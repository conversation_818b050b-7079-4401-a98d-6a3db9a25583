#!/usr/bin/env python3
"""
Debug Multi-Modal Implementation
Simple test to debug the multi-modal emotion detection flow.
"""
import os
import sys
import django
import asyncio
import time
from pathlib import Path

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service


async def setup_test_user() -> User:
    """Setup test user."""
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    return user


async def test_debug_multimodal():
    """Debug multi-modal emotion detection."""
    print("🔍 DEBUG MULTI-MODAL EMOTION DETECTION")
    print("=" * 60)
    
    user = await setup_test_user()
    user_id = str(user.id)
    
    # Test with a clear emotion case
    audio_file = "expressive_audio_library/07_anger.mp3"
    text = "I am absolutely furious about this situation!"
    
    print(f"🎵 Audio: {audio_file}")
    print(f"📝 Text: '{text}'")
    
    # Load audio
    audio_path = Path(audio_file)
    if not audio_path.exists():
        print(f"❌ Audio file not found: {audio_path}")
        return
    
    with open(audio_path, 'rb') as f:
        audio_data = f.read()
    
    session_id = f"debug_multimodal_{int(time.time())}"
    
    print(f"\n🔍 TESTING MULTI-MODAL ANALYSIS:")
    print(f"   Session ID: {session_id}")
    print(f"   User ID: {user_id}")
    print(f"   Audio size: {len(audio_data):,} bytes")
    print(f"   Text length: {len(text)} characters")
    
    # Test multi-modal analysis with debugging
    start_time = time.time()
    
    result = await expression_measurement_service.analyze_audio_chunk(
        audio_data, 
        f"{session_id}_debug", 
        session_id, 
        user_id, 
        text=text
    )
    
    analysis_time = (time.time() - start_time) * 1000
    
    print(f"\n📊 RESULTS:")
    print(f"   Analysis time: {analysis_time:.1f}ms")
    
    if result:
        print(f"   ✅ Dominant emotion: {result.dominant_emotion}")
        print(f"   ✅ Confidence: {result.confidence:.3f}")
        print(f"   ✅ Total emotions: {len(result.emotions)}")
        
        print(f"\n📈 Top 10 emotions:")
        for i, emotion in enumerate(result.emotions[:10]):
            name = emotion.get('name', 'Unknown')
            score = emotion.get('score', 0)
            prosody_score = emotion.get('prosody_score', 'N/A')
            language_score = emotion.get('language_score', 'N/A')
            
            if prosody_score != 'N/A' and language_score != 'N/A':
                print(f"      {i+1:2d}. {name:20s}: {score:.3f} (P:{prosody_score:.3f}, L:{language_score:.3f})")
            else:
                print(f"      {i+1:2d}. {name:20s}: {score:.3f}")
        
        # Check if we have multi-modal data
        has_multimodal = any(
            emotion.get('prosody_score', 'N/A') != 'N/A' and emotion.get('language_score', 'N/A') != 'N/A'
            for emotion in result.emotions[:5]
        )
        
        if has_multimodal:
            print(f"\n✅ MULTI-MODAL DATA DETECTED!")
        else:
            print(f"\n⚠️ NO MULTI-MODAL DATA - Only single modality detected")
            
    else:
        print(f"   ❌ No result returned")
    
    # Test audio-only for comparison
    print(f"\n🔍 TESTING AUDIO-ONLY FOR COMPARISON:")
    
    start_time = time.time()
    
    audio_only_result = await expression_measurement_service.analyze_audio_chunk(
        audio_data, 
        f"{session_id}_audio_only", 
        session_id, 
        user_id, 
        text=None
    )
    
    audio_only_time = (time.time() - start_time) * 1000
    
    print(f"   Analysis time: {audio_only_time:.1f}ms")
    
    if audio_only_result:
        print(f"   ✅ Dominant emotion: {audio_only_result.dominant_emotion}")
        print(f"   ✅ Confidence: {audio_only_result.confidence:.3f}")
        
        print(f"\n📈 Top 5 emotions (audio-only):")
        for i, emotion in enumerate(audio_only_result.emotions[:5]):
            name = emotion.get('name', 'Unknown')
            score = emotion.get('score', 0)
            print(f"      {i+1}. {name:20s}: {score:.3f}")
    else:
        print(f"   ❌ No audio-only result")
    
    # Compare results
    if result and audio_only_result:
        print(f"\n🔍 COMPARISON:")
        print(f"   Multi-modal: {result.dominant_emotion} ({result.confidence:.3f})")
        print(f"   Audio-only:  {audio_only_result.dominant_emotion} ({audio_only_result.confidence:.3f})")
        
        if result.dominant_emotion == audio_only_result.dominant_emotion and abs(result.confidence - audio_only_result.confidence) < 0.001:
            print(f"   ⚠️ IDENTICAL RESULTS - Multi-modal not working")
        else:
            print(f"   ✅ DIFFERENT RESULTS - Multi-modal working!")


async def main():
    """Run debug test."""
    print("🎯 DEBUG MULTI-MODAL EMOTION DETECTION")
    print("=" * 70)
    
    await test_debug_multimodal()
    
    print(f"\n✨ Debug test completed!")


if __name__ == "__main__":
    asyncio.run(main())
