#!/usr/bin/env python3
"""
ElevenLabs Expressive Audio Library Creator
Creates realistic emotional audio samples for accurate emotion detection testing.
"""
import os
import requests
import json
import time
from typing import Dict, List, Tuple
from pathlib import Path

# ElevenLabs Configuration
ELEVENLABS_API_KEY = "***************************************************"
VOICE_ID = "56AoDkrOh6qfVPDXZ7Pt"
ELEVENLABS_URL = "https://api.elevenlabs.io/v1/text-to-speech"

# Create audio library directory
AUDIO_LIBRARY_DIR = Path("expressive_audio_library")
AUDIO_LIBRARY_DIR.mkdir(exist_ok=True)


def create_expressive_audio(text: str, emotion_style: str, filename: str, voice_settings: Dict = None) -> bool:
    """Create expressive audio using ElevenLabs v3 with emotional prompting."""
    
    # Default voice settings optimized for emotional expression
    if voice_settings is None:
        voice_settings = {
            "stability": 0.5,  # Lower for more expressive variation
            "similarity_boost": 0.8,  # Higher to maintain voice consistency
            "style": 0.8,  # Higher for more expressive style
            "use_speaker_boost": True
        }
    
    # Construct the API request
    url = f"{ELEVENLABS_URL}/{VOICE_ID}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVENLABS_API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_turbo_v2_5",  # Use v3 for better emotional expression
        "voice_settings": voice_settings
    }
    
    try:
        print(f"🎭 Creating {emotion_style} audio: '{text[:50]}...'")
        
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            # Save the audio file
            filepath = AUDIO_LIBRARY_DIR / f"{filename}.mp3"
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"   ✅ Saved: {filepath}")
            return True
        else:
            print(f"   ❌ Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False


def create_emotion_library():
    """Create comprehensive library of emotional audio samples."""
    print("🎭 CREATING ELEVENLABS EXPRESSIVE AUDIO LIBRARY")
    print("=" * 60)
    
    # Natural emotional scenarios - let ElevenLabs v3 infer emotion from context (under 5 seconds)
    emotional_scenarios = [

        # HAPPINESS & JOY
        {
            "emotion": "joy",
            "text": "I just got the best news ever!",
            "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}
        },
        {
            "emotion": "excitement",
            "text": "This is going to be amazing!",
            "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}
        },
        {
            "emotion": "gratitude",
            "text": "Thank you so much for helping me.",
            "voice_settings": {"stability": 0.5, "similarity_boost": 0.9, "style": 0.8}
        },
        
        # SADNESS & DISTRESS
        {
            "emotion": "sadness",
            "text": "I'm feeling really down today.",
            "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}
        },
        {
            "emotion": "distress",
            "text": "I can't handle this anymore.",
            "voice_settings": {"stability": 0.3, "similarity_boost": 0.7, "style": 0.9}
        },
        {
            "emotion": "disappointment",
            "text": "This didn't work out at all.",
            "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}
        },
        
        # ANGER & FRUSTRATION
        {
            "emotion": "anger",
            "text": "This is absolutely unacceptable!",
            "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.9}
        },
        {
            "emotion": "frustration",
            "text": "Nothing is working right today!",
            "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}
        },
        {
            "emotion": "annoyance",
            "text": "This is really getting on my nerves.",
            "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}
        },
        
        # FEAR & ANXIETY
        {
            "emotion": "fear",
            "text": "I'm really scared about this.",
            "voice_settings": {"stability": 0.3, "similarity_boost": 0.7, "style": 0.8}
        },
        {
            "emotion": "anxiety",
            "text": "I'm so worried about tomorrow.",
            "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}
        },
        {
            "emotion": "panic",
            "text": "Oh no, what am I going to do?",
            "voice_settings": {"stability": 0.2, "similarity_boost": 0.7, "style": 0.9}
        },
        
        # SURPRISE & AWE
        {
            "emotion": "surprise",
            "text": "I can't believe this happened!",
            "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.9}
        },
        {
            "emotion": "shock",
            "text": "What? That's impossible!",
            "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}
        },
        {
            "emotion": "awe",
            "text": "This is absolutely breathtaking.",
            "voice_settings": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.8}
        },
        
        # CALM & PEACEFUL
        {
            "emotion": "calmness",
            "text": "I feel so peaceful right now.",
            "voice_settings": {"stability": 0.8, "similarity_boost": 0.9, "style": 0.6}
        },
        {
            "emotion": "relief",
            "text": "Thank goodness that's over.",
            "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
        },

        # COMPLEX EMOTIONS
        {
            "emotion": "determination",
            "text": "I'm going to make this work.",
            "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}
        },
        {
            "emotion": "contemplation",
            "text": "Let me think about this carefully.",
            "voice_settings": {"stability": 0.7, "similarity_boost": 0.9, "style": 0.6}
        },
        {
            "emotion": "embarrassment",
            "text": "That was so awkward.",
            "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.7}
        },
        {
            "emotion": "pride",
            "text": "I'm really proud of this achievement.",
            "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.8}
        }
    ]
    
    # Create audio files
    successful_creations = 0
    total_scenarios = len(emotional_scenarios)
    
    for i, scenario in enumerate(emotional_scenarios):
        print(f"\n📁 Creating {i+1}/{total_scenarios}: {scenario['emotion']}")
        
        success = create_expressive_audio(
            text=scenario['text'],
            emotion_style=scenario['emotion'],
            filename=f"{i+1:02d}_{scenario['emotion']}",
            voice_settings=scenario.get('voice_settings')
        )
        
        if success:
            successful_creations += 1
        
        # Rate limiting - ElevenLabs has API limits
        time.sleep(1)
    
    # Create metadata file
    metadata = {
        "library_info": {
            "total_scenarios": total_scenarios,
            "successful_creations": successful_creations,
            "voice_id": VOICE_ID,
            "model": "eleven_multilingual_v2",
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S")
        },
        "scenarios": emotional_scenarios
    }
    
    metadata_file = AUDIO_LIBRARY_DIR / "library_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"\n🏆 AUDIO LIBRARY CREATION COMPLETE")
    print(f"=" * 60)
    print(f"✅ Successfully created: {successful_creations}/{total_scenarios} audio files")
    print(f"📁 Library location: {AUDIO_LIBRARY_DIR}")
    print(f"📄 Metadata saved: {metadata_file}")
    
    if successful_creations == total_scenarios:
        print(f"🎉 Perfect! All audio files created successfully!")
    elif successful_creations > total_scenarios * 0.8:
        print(f"✅ Great! Most audio files created successfully!")
    else:
        print(f"⚠️ Some audio files failed to create. Check API key and limits.")
    
    return successful_creations, total_scenarios


def list_created_audio_files():
    """List all created audio files."""
    audio_files = list(AUDIO_LIBRARY_DIR.glob("*.mp3"))
    
    print(f"\n📚 CREATED AUDIO LIBRARY ({len(audio_files)} files):")
    print("=" * 60)
    
    for audio_file in sorted(audio_files):
        file_size = audio_file.stat().st_size / 1024  # KB
        print(f"🎵 {audio_file.name} ({file_size:.1f} KB)")
    
    return audio_files


if __name__ == "__main__":
    print("🎭 ELEVENLABS EXPRESSIVE AUDIO LIBRARY CREATOR")
    print("=" * 70)
    print(f"Voice ID: {VOICE_ID}")
    print(f"Output Directory: {AUDIO_LIBRARY_DIR}")
    
    # Create the library
    successful, total = create_emotion_library()
    
    # List created files
    audio_files = list_created_audio_files()
    
    print(f"\n🚀 Ready for emotion detection testing with realistic audio!")
    print(f"Use these {len(audio_files)} expressive audio files for accurate validation.")
