#!/usr/bin/env python3
"""
Hume Emotion Validation Test
Quick test to understand what emotions <PERSON> actually detects from our audio.
"""
import os
import sys
import django
import asyncio
import time
import tempfile
import subprocess

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service


def create_simple_audio(text: str) -> bytes:
    """Create simple audio for testing."""
    try:
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
        
        # Simple audio generation
        result = subprocess.run([
            'say', '-o', temp_path, '--data-format=LEI16@16000', text
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error generating speech: {result.stderr}")
            return None
        
        with open(temp_path, 'rb') as f:
            audio_data = f.read()
        
        os.unlink(temp_path)
        return audio_data
        
    except Exception as e:
        print(f"Error creating audio: {e}")
        return None


async def setup_test_user() -> User:
    """Setup test user."""
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    return user


async def test_basic_emotions():
    """Test basic emotions to see what Hume actually detects."""
    print("🔍 HUME EMOTION VALIDATION TEST")
    print("=" * 60)
    print("Testing what emotions Hume actually detects from our audio")
    
    user = await setup_test_user()
    session_id = f"validation_{int(time.time())}"
    user_id = str(user.id)
    
    # Simple test cases
    test_cases = [
        ("Happy", "I'm so happy and excited today!"),
        ("Sad", "I'm feeling really sad and down."),
        ("Angry", "I'm so angry and frustrated right now!"),
        ("Calm", "I feel peaceful and relaxed."),
        ("Surprised", "Oh wow, I can't believe this happened!"),
        ("Grateful", "Thank you so much, I'm really grateful."),
        ("Anxious", "I'm worried and anxious about this."),
        ("Excited", "This is so exciting, I can't wait!"),
    ]
    
    results = []
    
    for i, (emotion_label, text) in enumerate(test_cases):
        print(f"\n🎤 Test {i+1}: {emotion_label}")
        print(f"   Text: '{text}'")
        
        # Generate audio
        audio_data = create_simple_audio(text)
        if not audio_data:
            print(f"   ❌ Failed to generate audio")
            continue
        
        # Analyze emotion
        chunk_id = f"{session_id}_test_{i}"
        start_time = time.time()
        
        result = await expression_measurement_service.analyze_audio_chunk(
            audio_data, chunk_id, session_id, user_id
        )
        
        analysis_time = (time.time() - start_time) * 1000
        
        if result:
            print(f"   ✅ Detected: {result.dominant_emotion} ({result.confidence:.2f}) - {analysis_time:.1f}ms")
            
            # Show top 5 emotions
            print(f"   📊 Top emotions:")
            for j, emotion in enumerate(result.emotions[:5]):
                print(f"      {j+1}. {emotion.get('name', 'Unknown')}: {emotion.get('score', 0):.3f}")
            
            results.append({
                'label': emotion_label,
                'text': text,
                'detected': result.dominant_emotion,
                'confidence': result.confidence,
                'top_emotions': result.emotions[:5],
                'analysis_time': analysis_time
            })
        else:
            print(f"   ❌ No emotion detected - {analysis_time:.1f}ms")
        
        # Small delay
        await asyncio.sleep(0.5)
    
    return results


async def test_session_aggregation_simple():
    """Test session aggregation with simple progression."""
    print("\n🧠 SESSION AGGREGATION VALIDATION")
    print("=" * 60)
    
    user = await setup_test_user()
    session_id = f"aggregation_validation_{int(time.time())}"
    user_id = str(user.id)
    
    # Simple emotional progression
    progression = [
        "I'm feeling sad today.",
        "Maybe things will get better.",
        "Actually, I'm starting to feel a bit better.",
        "You know what, I'm feeling much happier now!",
    ]
    
    session_results = []
    
    for i, text in enumerate(progression):
        print(f"\n   Stage {i+1}: '{text}'")
        
        audio_data = create_simple_audio(text)
        if not audio_data:
            continue
        
        chunk_id = f"{session_id}_stage_{i}"
        result = await expression_measurement_service.analyze_audio_chunk(
            audio_data, chunk_id, session_id, user_id
        )
        
        # Get session profile
        session_profile = await expression_measurement_service.get_session_emotion_profile(session_id)
        
        if result and session_profile:
            print(f"   Individual: {result.dominant_emotion} ({result.confidence:.2f})")
            print(f"   Session: {session_profile.dominant_emotion} ({session_profile.overall_confidence:.2f})")
            print(f"   Trend: {session_profile.recent_trend}")
            
            session_results.append({
                'stage': i + 1,
                'individual': result.dominant_emotion,
                'session': session_profile.dominant_emotion,
                'trend': session_profile.recent_trend,
                'interactions': session_profile.total_interactions
            })
        
        await asyncio.sleep(0.3)
    
    return session_results


async def main():
    """Run Hume emotion validation tests."""
    print("🎯 HUME EMOTION DETECTION VALIDATION")
    print("=" * 70)
    
    # Test 1: Basic emotion detection
    basic_results = await test_basic_emotions()
    
    # Test 2: Session aggregation
    session_results = await test_session_aggregation_simple()
    
    # Analysis
    print("\n📊 VALIDATION RESULTS")
    print("=" * 70)
    
    if basic_results:
        print(f"🔍 Basic Emotion Detection:")
        print(f"   Total tests: {len(basic_results)}")
        successful_detections = [r for r in basic_results if r['confidence'] > 0.3]
        print(f"   Successful detections (>30% confidence): {len(successful_detections)}")
        
        print(f"\n   Emotion mapping:")
        for result in basic_results:
            confidence_indicator = "✅" if result['confidence'] > 0.5 else "⚠️" if result['confidence'] > 0.3 else "❌"
            print(f"      {result['label']} → {result['detected']} ({result['confidence']:.2f}) {confidence_indicator}")
        
        # Show unique emotions detected
        unique_emotions = set(r['detected'] for r in basic_results)
        print(f"\n   Unique emotions detected: {len(unique_emotions)}")
        for emotion in sorted(unique_emotions):
            print(f"      - {emotion}")
    
    if session_results:
        print(f"\n🧠 Session Aggregation:")
        print(f"   Stages: {len(session_results)}")
        if session_results:
            final_result = session_results[-1]
            print(f"   Final session emotion: {final_result['session']}")
            print(f"   Final trend: {final_result['trend']}")
            print(f"   Total interactions: {final_result['interactions']}")
            
            print(f"\n   Progression:")
            for result in session_results:
                print(f"      Stage {result['stage']}: {result['individual']} → {result['session']} ({result['trend']})")
    
    # Get service stats
    stats = expression_measurement_service.get_performance_stats()
    
    print(f"\n📈 Service Performance:")
    print(f"   Success rate: {stats.get('success_rate_percent', 0):.1f}%")
    print(f"   Average processing time: {stats.get('avg_processing_time_ms', 0):.1f}ms")
    
    session_stats = stats.get('session_aggregation', {})
    if session_stats:
        print(f"   Active sessions: {session_stats.get('active_sessions', 0)}")
        print(f"   Total session interactions: {session_stats.get('total_session_interactions', 0)}")
    
    # Recommendations
    print(f"\n💡 Validation Insights:")
    if basic_results:
        high_confidence_count = len([r for r in basic_results if r['confidence'] > 0.5])
        if high_confidence_count >= len(basic_results) * 0.6:
            print("   ✅ Good emotion detection accuracy")
        elif high_confidence_count >= len(basic_results) * 0.4:
            print("   ⚠️ Moderate emotion detection accuracy")
        else:
            print("   ❌ Low emotion detection accuracy - may need audio quality improvements")
    
    if session_results and len(session_results) > 2:
        trends = [r['trend'] for r in session_results]
        if 'improving' in trends or 'declining' in trends:
            print("   ✅ Session trend detection working")
        else:
            print("   ⚠️ Session trend detection may need tuning")
    
    print(f"\n✨ Hume emotion validation completed!")


if __name__ == "__main__":
    asyncio.run(main())
