{"library_info": {"total_scenarios": 17, "total_files": 89, "successful_creations": 89, "voice_id": "5l5f8iK3YPeGga21rQIX", "model": "eleven_turbo_v2_5", "created_at": "2025-07-24 19:04:38"}, "scenarios": [{"scenario": "work_stress_relief", "description": "User starts stressed about work, gradually finds relief", "interactions": [{"stage": "initial_stress", "text": "I'm so overwhelmed with this project deadline.", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "building_pressure", "text": "My boss keeps adding more requirements.", "expected_emotion": "frustration", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "seeking_help", "text": "Can you help me figure out how to prioritize this?", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "finding_solution", "text": "That's actually a really good approach.", "expected_emotion": "relief", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_better", "text": "I feel so much better about this now.", "expected_emotion": "relief", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}]}, {"scenario": "sad_to_happy", "description": "User starts sad, gradually becomes happier through conversation", "interactions": [{"stage": "initial_sadness", "text": "I've been feeling really down lately.", "expected_emotion": "sadness", "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "sharing_problems", "text": "Everything just seems to be going wrong.", "expected_emotion": "distress", "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "opening_up", "text": "Maybe talking about it will help.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "small_improvement", "text": "You know what, you're right about that.", "expected_emotion": "interest", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_hopeful", "text": "I'm starting to feel more optimistic.", "expected_emotion": "contentment", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "much_better", "text": "Thank you, I'm feeling so much better!", "expected_emotion": "gratitude", "voice_settings": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.8}}]}, {"scenario": "excitement_building", "description": "User gets increasingly excited about good news", "interactions": [{"stage": "good_news", "text": "I just got some really good news!", "expected_emotion": "excitement", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "sharing_details", "text": "I got the job I've been hoping for!", "expected_emotion": "joy", "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "overwhelming_joy", "text": "I can't believe this is actually happening!", "expected_emotion": "excitement", "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "grateful_excitement", "text": "This is the best day ever!", "expected_emotion": "joy", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}]}, {"scenario": "anger_to_calm", "description": "User starts angry, gradually calms down", "interactions": [{"stage": "initial_anger", "text": "I'm so angry about what happened today!", "expected_emotion": "anger", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "venting_frustration", "text": "This is completely unfair and ridiculous!", "expected_emotion": "anger", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "starting_to_process", "text": "I guess I need to think about this differently.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "finding_perspective", "text": "Maybe there's another way to handle this.", "expected_emotion": "determination", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_calmer", "text": "I feel much calmer about it now.", "expected_emotion": "calmness", "voice_settings": {"stability": 0.8, "similarity_boost": 0.9, "style": 0.6}}]}, {"scenario": "anxiety_recovery", "description": "User has anxiety spike, then recovers", "interactions": [{"stage": "anxiety_spike", "text": "I'm really worried about tomorrow's presentation.", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "panic_thoughts", "text": "What if everything goes wrong?", "expected_emotion": "fear", "voice_settings": {"stability": 0.3, "similarity_boost": 0.7, "style": 0.9}}, {"stage": "getting_support", "text": "Your advice is really helping me.", "expected_emotion": "gratitude", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_confident", "text": "I think I can actually do this well.", "expected_emotion": "determination", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "disappointment_acceptance", "description": "User deals with disappointment and finds acceptance", "interactions": [{"stage": "bad_news", "text": "I didn't get the promotion I was hoping for.", "expected_emotion": "disappointment", "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "processing_sadness", "text": "I'm really sad about this outcome.", "expected_emotion": "sadness", "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "reflecting", "text": "Maybe this is a chance to learn something.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "finding_silver_lining", "text": "Actually, this gives me time to improve my skills.", "expected_emotion": "realization", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "acceptance", "text": "I'm okay with how things turned out.", "expected_emotion": "contentment", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}]}, {"scenario": "surprise_joy", "description": "User receives unexpected good news", "interactions": [{"stage": "unexpected_news", "text": "Wait, what? I can't believe this!", "expected_emotion": "surprise", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "processing_joy", "text": "This is absolutely amazing!", "expected_emotion": "joy", "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "overwhelming_gratitude", "text": "I'm so grateful for this opportunity!", "expected_emotion": "gratitude", "voice_settings": {"stability": 0.5, "similarity_boost": 0.9, "style": 0.8}}]}, {"scenario": "embarrassment_recovery", "description": "User feels embarrassed but recovers confidence", "interactions": [{"stage": "embarrassing_moment", "text": "I made such a fool of myself in that meeting.", "expected_emotion": "embarrassment", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_awkward", "text": "Everyone was staring at me.", "expected_emotion": "awkwardness", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "getting_perspective", "text": "Maybe it wasn't as bad as I thought.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "moving_forward", "text": "I'll do better next time.", "expected_emotion": "determination", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "fear_to_courage", "description": "User overcomes fear with growing courage", "interactions": [{"stage": "initial_fear", "text": "I'm terrified of giving this speech.", "expected_emotion": "fear", "voice_settings": {"stability": 0.3, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "anxiety_building", "text": "My heart is racing just thinking about it.", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "seeking_courage", "text": "I need to find the courage to do this.", "expected_emotion": "determination", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "building_confidence", "text": "I've prepared well, I can handle this.", "expected_emotion": "confidence", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "feeling_brave", "text": "I'm ready to face this challenge.", "expected_emotion": "determination", "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "boredom_to_interest", "description": "User goes from bored to engaged", "interactions": [{"stage": "feeling_bored", "text": "This is so boring, I can't focus.", "expected_emotion": "boredom", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.5}}, {"stage": "losing_attention", "text": "I'm having trouble staying awake.", "expected_emotion": "tiredness", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.4}}, {"stage": "finding_interest", "text": "Wait, this part is actually interesting.", "expected_emotion": "interest", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "getting_engaged", "text": "Now I'm really curious to learn more.", "expected_emotion": "curiosity", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "pride_achievement", "description": "User feels proud of accomplishment", "interactions": [{"stage": "completing_task", "text": "I finally finished this difficult project.", "expected_emotion": "relief", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_proud", "text": "I'm really proud of what I accomplished.", "expected_emotion": "pride", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "celebrating_success", "text": "This feels like a real triumph!", "expected_emotion": "triumph", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.9}}]}, {"scenario": "confusion_understanding", "description": "User works through confusion to clarity", "interactions": [{"stage": "feeling_confused", "text": "I don't understand what's happening here.", "expected_emotion": "confusion", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "seeking_clarity", "text": "Let me think about this more carefully.", "expected_emotion": "concentration", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "starting_to_understand", "text": "Oh wait, I think I'm starting to get it.", "expected_emotion": "realization", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "full_understanding", "text": "Now it all makes perfect sense!", "expected_emotion": "satisfaction", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "envy_acceptance", "description": "User deals with envy and finds peace", "interactions": [{"stage": "feeling_envious", "text": "I wish I had what they have.", "expected_emotion": "envy", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "comparing_self", "text": "Why can't I be as successful as them?", "expected_emotion": "disappointment", "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "self_reflection", "text": "Maybe I should focus on my own journey.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "finding_peace", "text": "I'm content with my own progress.", "expected_emotion": "contentment", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}]}, {"scenario": "nostalgia_warmth", "description": "User experiences nostalgic feelings", "interactions": [{"stage": "remembering_past", "text": "This reminds me of when I was a child.", "expected_emotion": "nostalgia", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "warm_feelings", "text": "Those were such wonderful times.", "expected_emotion": "love", "voice_settings": {"stability": 0.7, "similarity_boost": 0.9, "style": 0.8}}, {"stage": "appreciating_memories", "text": "I'm grateful for those beautiful memories.", "expected_emotion": "gratitude", "voice_settings": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.8}}]}, {"scenario": "disgust_tolerance", "description": "User overcomes initial disgust", "interactions": [{"stage": "initial_disgust", "text": "That's absolutely disgusting!", "expected_emotion": "disgust", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "trying_to_cope", "text": "I guess I have to deal with this.", "expected_emotion": "resignation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "finding_tolerance", "text": "It's not as bad as I first thought.", "expected_emotion": "acceptance", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}]}, {"scenario": "job_interview_journey", "description": "Complete job interview process from anxiety to confidence to outcome", "interactions": [{"stage": "initial_anxiety", "text": "I have a really important job interview tomorrow and I'm so nervous.", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "building_worry", "text": "What if I mess up and say something stupid?", "expected_emotion": "fear", "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "seeking_help", "text": "I need to prepare properly for this interview.", "expected_emotion": "determination", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "preparation_focus", "text": "Let me research the company and practice my answers.", "expected_emotion": "concentration", "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "building_confidence", "text": "I've prepared well and I know my strengths.", "expected_emotion": "satisfaction", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "morning_nerves", "text": "It's the morning of the interview and my heart is racing.", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "arrival_stress", "text": "I'm here at the office and I feel like I might panic.", "expected_emotion": "panic", "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "calming_down", "text": "Take deep breaths, you've got this, stay calm.", "expected_emotion": "calmness", "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "interview_performance", "text": "I'm focusing on giving clear, thoughtful answers.", "expected_emotion": "concentration", "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "post_interview_relief", "text": "The interview is over and I think it went pretty well.", "expected_emotion": "relief", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "waiting_anxiety", "text": "Now I have to wait for their decision and it's killing me.", "expected_emotion": "anticipation", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "rejection_news", "text": "They called and said they went with someone else.", "expected_emotion": "disappointment", "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "processing_sadness", "text": "I'm really sad about this, I wanted that job so much.", "expected_emotion": "sadness", "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "finding_resolve", "text": "But I'm not giving up, I'll keep applying and improving.", "expected_emotion": "determination", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "future_optimism", "text": "The right opportunity will come along when it's meant to.", "expected_emotion": "hope", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}]}, {"scenario": "relationship_conflict_resolution", "description": "Working through a serious relationship conflict from anger to understanding", "interactions": [{"stage": "initial_anger", "text": "I can't believe they did this to me, I'm so angry right now!", "expected_emotion": "anger", "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "venting_frustration", "text": "This is so frustrating, why can't they understand how this affects me?", "expected_emotion": "frustration", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "feeling_hurt", "text": "It really hurts that someone I care about would do this.", "expected_emotion": "sadness", "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "questioning_relationship", "text": "I don't know what to think about our relationship anymore.", "expected_emotion": "confusion", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "deep_sadness", "text": "This whole situation is making me feel so lost and sad.", "expected_emotion": "distress", "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "starting_reflection", "text": "Maybe I need to try to understand their perspective too.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "seeking_understanding", "text": "I wonder what was going through their mind when this happened.", "expected_emotion": "curiosity", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "finding_empathy", "text": "I can see how they might have felt pressured in that situation.", "expected_emotion": "empathy", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "choosing_forgiveness", "text": "I think I'm ready to forgive them and move forward.", "expected_emotion": "relief", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "expressing_gratitude", "text": "I'm grateful we can work through difficult things like this together.", "expected_emotion": "gratitude", "voice_settings": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.8}}, {"stage": "renewed_love", "text": "This experience has actually made me love them even more.", "expected_emotion": "love", "voice_settings": {"stability": 0.7, "similarity_boost": 0.9, "style": 0.8}}, {"stage": "peaceful_resolution", "text": "I feel so much peace now that we've worked through this.", "expected_emotion": "contentment", "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.6}}]}, {"scenario": "creative_breakthrough_journey", "description": "Artist struggling with creative block who experiences breakthrough and artistic flow", "total_interactions": 13, "emotional_arc": "frustration → despair → curiosity → inspiration → excitement → flow → satisfaction → gratitude", "target_user_profile": "creative_perfectionist", "interactions": [{"stage": "initial_frustration", "text": "I've been staring at this blank canvas for hours and nothing is coming to me.", "expected_emotion": "frustration"}, {"stage": "building_despair", "text": "Maybe I'm just not cut out to be an artist anymore.", "expected_emotion": "despair"}, {"stage": "self_doubt", "text": "Everyone else seems to create such beautiful work so effortlessly.", "expected_emotion": "disappointment"}, {"stage": "seeking_inspiration", "text": "Let me try looking at some of my favorite artists for inspiration.", "expected_emotion": "curiosity"}, {"stage": "small_spark", "text": "Wait, there's something interesting about the way light hits this corner.", "expected_emotion": "interest"}, {"stage": "growing_excitement", "text": "I think I see what I want to create now, this could actually work!", "expected_emotion": "excitement"}, {"stage": "creative_flow_begins", "text": "The colors are flowing together perfectly, I can feel it happening.", "expected_emotion": "joy"}, {"stage": "deep_flow_state", "text": "Time seems to disappear when I'm in this creative zone.", "expected_emotion": "concentration"}, {"stage": "breakthrough_moment", "text": "This is it! This is exactly what I was trying to express!", "expected_emotion": "triumph"}, {"stage": "artistic_satisfaction", "text": "I can't believe how well this piece turned out.", "expected_emotion": "pride"}, {"stage": "sharing_excitement", "text": "I can't wait to show this to other people and see their reactions.", "expected_emotion": "anticipation"}, {"stage": "grateful_reflection", "text": "I'm so grateful for this creative gift and the ability to express myself.", "expected_emotion": "gratitude"}, {"stage": "peaceful_completion", "text": "There's such a deep sense of fulfillment when you create something meaningful.", "expected_emotion": "contentment"}]}, {"scenario": "family_reconciliation_journey", "description": "Adult child working through years of family conflict toward healing and understanding", "total_interactions": 14, "emotional_arc": "anger → resentment → sadness → reflection → empathy → hope → love → peace", "target_user_profile": "family_oriented_healer", "interactions": [{"stage": "initial_anger", "text": "I'm so tired of the same old arguments every time we talk.", "expected_emotion": "anger"}, {"stage": "deep_resentment", "text": "They never really listened to me or understood what I was going through.", "expected_emotion": "resentment"}, {"stage": "hurt_feelings", "text": "It hurts that they seem to care more about being right than about our relationship.", "expected_emotion": "sadness"}, {"stage": "childhood_memories", "text": "I remember feeling so alone as a child when they would fight.", "expected_emotion": "pain"}, {"stage": "seeking_understanding", "text": "Maybe I should try to understand their perspective and what they went through.", "expected_emotion": "contemplation"}, {"stage": "growing_empathy", "text": "They were probably doing the best they could with what they knew at the time.", "expected_emotion": "empathy"}, {"stage": "difficult_conversation", "text": "I need to have an honest conversation with them about how I feel.", "expected_emotion": "determination"}, {"stage": "vulnerable_sharing", "text": "I told them how much their words hurt me over the years.", "expected_emotion": "vulnerability"}, {"stage": "mutual_understanding", "text": "For the first time, I think they really heard what I was saying.", "expected_emotion": "relief"}, {"stage": "healing_begins", "text": "We both apologized and acknowledged the pain we caused each other.", "expected_emotion": "hope"}, {"stage": "rebuilding_connection", "text": "It feels so good to laugh together again like we used to.", "expected_emotion": "joy"}, {"stage": "deeper_love", "text": "I realize how much I love them despite all our struggles.", "expected_emotion": "love"}, {"stage": "grateful_healing", "text": "I'm so grateful we were able to work through this together.", "expected_emotion": "gratitude"}, {"stage": "peaceful_resolution", "text": "There's such peace in knowing our relationship is healing and growing stronger.", "expected_emotion": "peace"}]}]}