{"metadata": {"version": "3.0", "description": "Combined original and extended conversation scenarios", "total_scenarios": 17, "original_scenarios": 15, "extended_scenarios": 2, "total_interactions": 89}, "scenarios": [{"scenario": "work_stress_relief", "description": "User starts stressed about work, gradually finds relief", "interactions": [{"stage": "initial_stress", "text": "I'm so overwhelmed with this project deadline.", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "building_pressure", "text": "My boss keeps adding more requirements.", "expected_emotion": "frustration", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "seeking_help", "text": "Can you help me figure out how to prioritize this?", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "finding_solution", "text": "That's actually a really good approach.", "expected_emotion": "relief", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_better", "text": "I feel so much better about this now.", "expected_emotion": "relief", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}]}, {"scenario": "sad_to_happy", "description": "User starts sad, gradually becomes happier through conversation", "interactions": [{"stage": "initial_sadness", "text": "I've been feeling really down lately.", "expected_emotion": "sadness", "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "sharing_problems", "text": "Everything just seems to be going wrong.", "expected_emotion": "distress", "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "opening_up", "text": "Maybe talking about it will help.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "small_improvement", "text": "You know what, you're right about that.", "expected_emotion": "interest", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_hopeful", "text": "I'm starting to feel more optimistic.", "expected_emotion": "contentment", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "much_better", "text": "Thank you, I'm feeling so much better!", "expected_emotion": "gratitude", "voice_settings": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.8}}]}, {"scenario": "excitement_building", "description": "User gets increasingly excited about good news", "interactions": [{"stage": "good_news", "text": "I just got some really good news!", "expected_emotion": "excitement", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "sharing_details", "text": "I got the job I've been hoping for!", "expected_emotion": "joy", "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "overwhelming_joy", "text": "I can't believe this is actually happening!", "expected_emotion": "excitement", "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "grateful_excitement", "text": "This is the best day ever!", "expected_emotion": "joy", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}]}, {"scenario": "anger_to_calm", "description": "User starts angry, gradually calms down", "interactions": [{"stage": "initial_anger", "text": "I'm so angry about what happened today!", "expected_emotion": "anger", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "venting_frustration", "text": "This is completely unfair and ridiculous!", "expected_emotion": "anger", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "starting_to_process", "text": "I guess I need to think about this differently.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "finding_perspective", "text": "Maybe there's another way to handle this.", "expected_emotion": "determination", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_calmer", "text": "I feel much calmer about it now.", "expected_emotion": "calmness", "voice_settings": {"stability": 0.8, "similarity_boost": 0.9, "style": 0.6}}]}, {"scenario": "anxiety_recovery", "description": "User has anxiety spike, then recovers", "interactions": [{"stage": "anxiety_spike", "text": "I'm really worried about tomorrow's presentation.", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "panic_thoughts", "text": "What if everything goes wrong?", "expected_emotion": "fear", "voice_settings": {"stability": 0.3, "similarity_boost": 0.7, "style": 0.9}}, {"stage": "getting_support", "text": "Your advice is really helping me.", "expected_emotion": "gratitude", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_confident", "text": "I think I can actually do this well.", "expected_emotion": "determination", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "disappointment_acceptance", "description": "User deals with disappointment and finds acceptance", "interactions": [{"stage": "bad_news", "text": "I didn't get the promotion I was hoping for.", "expected_emotion": "disappointment", "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "processing_sadness", "text": "I'm really sad about this outcome.", "expected_emotion": "sadness", "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "reflecting", "text": "Maybe this is a chance to learn something.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "finding_silver_lining", "text": "Actually, this gives me time to improve my skills.", "expected_emotion": "realization", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "acceptance", "text": "I'm okay with how things turned out.", "expected_emotion": "contentment", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}]}, {"scenario": "surprise_joy", "description": "User receives unexpected good news", "interactions": [{"stage": "unexpected_news", "text": "Wait, what? I can't believe this!", "expected_emotion": "surprise", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "processing_joy", "text": "This is absolutely amazing!", "expected_emotion": "joy", "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "overwhelming_gratitude", "text": "I'm so grateful for this opportunity!", "expected_emotion": "gratitude", "voice_settings": {"stability": 0.5, "similarity_boost": 0.9, "style": 0.8}}]}, {"scenario": "embarrassment_recovery", "description": "User feels embarrassed but recovers confidence", "interactions": [{"stage": "embarrassing_moment", "text": "I made such a fool of myself in that meeting.", "expected_emotion": "embarrassment", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_awkward", "text": "Everyone was staring at me.", "expected_emotion": "awkwardness", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "getting_perspective", "text": "Maybe it wasn't as bad as I thought.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "moving_forward", "text": "I'll do better next time.", "expected_emotion": "determination", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "fear_to_courage", "description": "User overcomes fear with growing courage", "interactions": [{"stage": "initial_fear", "text": "I'm terrified of giving this speech.", "expected_emotion": "fear", "voice_settings": {"stability": 0.3, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "anxiety_building", "text": "My heart is racing just thinking about it.", "expected_emotion": "anxiety", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "seeking_courage", "text": "I need to find the courage to do this.", "expected_emotion": "determination", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "building_confidence", "text": "I've prepared well, I can handle this.", "expected_emotion": "confidence", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "feeling_brave", "text": "I'm ready to face this challenge.", "expected_emotion": "determination", "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "boredom_to_interest", "description": "User goes from bored to engaged", "interactions": [{"stage": "feeling_bored", "text": "This is so boring, I can't focus.", "expected_emotion": "boredom", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.5}}, {"stage": "losing_attention", "text": "I'm having trouble staying awake.", "expected_emotion": "tiredness", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.4}}, {"stage": "finding_interest", "text": "Wait, this part is actually interesting.", "expected_emotion": "interest", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "getting_engaged", "text": "Now I'm really curious to learn more.", "expected_emotion": "curiosity", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "pride_achievement", "description": "User feels proud of accomplishment", "interactions": [{"stage": "completing_task", "text": "I finally finished this difficult project.", "expected_emotion": "relief", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "feeling_proud", "text": "I'm really proud of what I accomplished.", "expected_emotion": "pride", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.8}}, {"stage": "celebrating_success", "text": "This feels like a real triumph!", "expected_emotion": "triumph", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.9}}]}, {"scenario": "confusion_understanding", "description": "User works through confusion to clarity", "interactions": [{"stage": "feeling_confused", "text": "I don't understand what's happening here.", "expected_emotion": "confusion", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "seeking_clarity", "text": "Let me think about this more carefully.", "expected_emotion": "concentration", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "starting_to_understand", "text": "Oh wait, I think I'm starting to get it.", "expected_emotion": "realization", "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "full_understanding", "text": "Now it all makes perfect sense!", "expected_emotion": "satisfaction", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}}]}, {"scenario": "envy_acceptance", "description": "User deals with envy and finds peace", "interactions": [{"stage": "feeling_envious", "text": "I wish I had what they have.", "expected_emotion": "envy", "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "comparing_self", "text": "Why can't I be as successful as them?", "expected_emotion": "disappointment", "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}}, {"stage": "self_reflection", "text": "Maybe I should focus on my own journey.", "expected_emotion": "contemplation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "finding_peace", "text": "I'm content with my own progress.", "expected_emotion": "contentment", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}]}, {"scenario": "nostalgia_warmth", "description": "User experiences nostalgic feelings", "interactions": [{"stage": "remembering_past", "text": "This reminds me of when I was a child.", "expected_emotion": "nostalgia", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}}, {"stage": "warm_feelings", "text": "Those were such wonderful times.", "expected_emotion": "love", "voice_settings": {"stability": 0.7, "similarity_boost": 0.9, "style": 0.8}}, {"stage": "appreciating_memories", "text": "I'm grateful for those beautiful memories.", "expected_emotion": "gratitude", "voice_settings": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.8}}]}, {"scenario": "disgust_tolerance", "description": "User overcomes initial disgust", "interactions": [{"stage": "initial_disgust", "text": "That's absolutely disgusting!", "expected_emotion": "disgust", "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}}, {"stage": "trying_to_cope", "text": "I guess I have to deal with this.", "expected_emotion": "resignation", "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}}, {"stage": "finding_tolerance", "text": "It's not as bad as I first thought.", "expected_emotion": "acceptance", "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}}]}, {"scenario": "job_interview_journey", "description": "Complete job interview process from anxiety to confidence to outcome", "interactions": [{"stage": "initial_anxiety", "expected_emotion": "anxiety", "text": "I have a really important job interview tomorrow and I'm so nervous.", "voice_style": "[nervously, with worry]", "context": "User just learned about interview"}, {"stage": "building_worry", "expected_emotion": "fear", "text": "What if I mess up and say something stupid?", "voice_style": "[with increasing anxiety]", "context": "Catastrophic thinking setting in"}, {"stage": "seeking_help", "expected_emotion": "determination", "text": "I need to prepare properly for this interview.", "voice_style": "[with resolve, determined]", "context": "Deciding to take action"}, {"stage": "preparation_focus", "expected_emotion": "concentration", "text": "Let me research the company and practice my answers.", "voice_style": "[focused, methodical]", "context": "Getting into preparation mode"}, {"stage": "building_confidence", "expected_emotion": "satisfaction", "text": "I've prepared well and I know my strengths.", "voice_style": "[with growing confidence]", "context": "Preparation paying off"}, {"stage": "morning_nerves", "expected_emotion": "anxiety", "text": "It's the morning of the interview and my heart is racing.", "voice_style": "[breathless, nervous energy]", "context": "Interview day anxiety spike"}, {"stage": "arrival_stress", "expected_emotion": "panic", "text": "I'm here at the office and I feel like I might panic.", "voice_style": "[rapid speech, panicked]", "context": "Peak stress moment"}, {"stage": "calming_down", "expected_emotion": "calmness", "text": "Take deep breaths, you've got this, stay calm.", "voice_style": "[slow, deliberate, calming]", "context": "Self-soothing technique"}, {"stage": "interview_performance", "expected_emotion": "concentration", "text": "I'm focusing on giving clear, thoughtful answers.", "voice_style": "[steady, professional]", "context": "During the interview"}, {"stage": "post_interview_relief", "expected_emotion": "relief", "text": "The interview is over and I think it went pretty well.", "voice_style": "[exhaling with relief]", "context": "Immediate post-interview"}, {"stage": "waiting_anxiety", "expected_emotion": "anticipation", "text": "Now I have to wait for their decision and it's killing me.", "voice_style": "[restless, anticipatory]", "context": "Waiting for results"}, {"stage": "rejection_news", "expected_emotion": "disappointment", "text": "They called and said they went with someone else.", "voice_style": "[deflated, disappointed]", "context": "Receiving bad news"}, {"stage": "processing_sadness", "expected_emotion": "sadness", "text": "I'm really sad about this, I wanted that job so much.", "voice_style": "[quietly sad, dejected]", "context": "Processing the rejection"}, {"stage": "finding_resolve", "expected_emotion": "determination", "text": "But I'm not giving up, I'll keep applying and improving.", "voice_style": "[with renewed determination]", "context": "Bouncing back with resolve"}, {"stage": "future_optimism", "expected_emotion": "hope", "text": "The right opportunity will come along when it's meant to.", "voice_style": "[hopeful, optimistic]", "context": "Looking forward with hope"}], "emotional_arc": "anxiety → nervousness → preparation → confidence → interview_stress → performance → relief → anticipation → disappointment → reflection → determination → hope → action → success → joy"}, {"scenario": "relationship_conflict_resolution", "description": "Working through a serious relationship conflict from anger to understanding", "interactions": [{"stage": "initial_anger", "expected_emotion": "anger", "text": "I can't believe they did this to me, I'm so angry right now!", "voice_style": "[furious, heated]", "context": "Discovery of the conflict"}, {"stage": "venting_frustration", "expected_emotion": "frustration", "text": "This is so frustrating, why can't they understand how this affects me?", "voice_style": "[exasperated, frustrated]", "context": "Expressing frustration"}, {"stage": "feeling_hurt", "expected_emotion": "sadness", "text": "It really hurts that someone I care about would do this.", "voice_style": "[voice breaking, hurt]", "context": "Deeper emotional pain emerging"}, {"stage": "questioning_relationship", "expected_emotion": "confusion", "text": "I don't know what to think about our relationship anymore.", "voice_style": "[uncertain, confused]", "context": "Questioning the relationship"}, {"stage": "deep_sadness", "expected_emotion": "distress", "text": "This whole situation is making me feel so lost and sad.", "voice_style": "[deeply sad, distressed]", "context": "Emotional low point"}, {"stage": "starting_reflection", "expected_emotion": "contemplation", "text": "Maybe I need to try to understand their perspective too.", "voice_style": "[thoughtful, reflective]", "context": "Beginning to consider other viewpoints"}, {"stage": "seeking_understanding", "expected_emotion": "curiosity", "text": "I wonder what was going through their mind when this happened.", "voice_style": "[genuinely curious]", "context": "Trying to understand their perspective"}, {"stage": "finding_empathy", "expected_emotion": "empathy", "text": "I can see how they might have felt pressured in that situation.", "voice_style": "[with understanding, empathetic]", "context": "Developing empathy"}, {"stage": "choosing_forgiveness", "expected_emotion": "relief", "text": "I think I'm ready to forgive them and move forward.", "voice_style": "[with relief, releasing tension]", "context": "Decision to forgive"}, {"stage": "expressing_gratitude", "expected_emotion": "gratitude", "text": "I'm grateful we can work through difficult things like this together.", "voice_style": "[warm, grateful]", "context": "Appreciating the relationship"}, {"stage": "renewed_love", "expected_emotion": "love", "text": "This experience has actually made me love them even more.", "voice_style": "[tender, loving]", "context": "Deeper connection through conflict"}, {"stage": "peaceful_resolution", "expected_emotion": "contentment", "text": "I feel so much peace now that we've worked through this.", "voice_style": "[calm, peaceful, content]", "context": "Final resolution and peace"}], "emotional_arc": "anger → frustration → hurt → sadness → reflection → understanding → empathy → forgiveness → gratitude → love → commitment → peace"}]}