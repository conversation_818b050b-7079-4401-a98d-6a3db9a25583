#!/usr/bin/env python3
"""
Test TTS reliability and circuit breaker functionality.
"""
import os
import sys
import django
import asyncio

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.services.hume_service import HumeTTSClient
from chat.services.error_recovery import error_recovery_manager

async def test_tts_reliability():
    print("🎵 TTS Reliability Test")
    print("=" * 50)
    
    # Check circuit breaker status
    status = error_recovery_manager.get_circuit_breaker_status()
    print(f"🔧 Circuit Breaker Status:")
    for service, info in status.items():
        print(f"  {service}: {info['state']} (failures: {info['failure_count']})")
    
    # Reset circuit breakers if needed
    if status['hume']['state'] != 'closed':
        print(f"\n🔄 Resetting Hume circuit breaker...")
        error_recovery_manager.reset_circuit_breaker('hume')
        print(f"✅ Hume circuit breaker reset")
    
    # Test TTS
    print(f"\n🎵 Testing TTS synthesis...")
    
    try:
        tts_client = HumeTTSClient()
        test_text = "Hello, this is a test of the TTS system. Can you hear me clearly?"
        
        print(f"📝 Test text: '{test_text}'")
        print(f"🎤 Starting TTS synthesis...")
        
        chunk_count = 0
        total_audio_bytes = 0
        
        async for chunk in tts_client.synthesize_streaming(
            text=test_text,
            voice_name="Ellah"
        ):
            chunk_count += 1
            audio_size = len(chunk.audio_data)
            total_audio_bytes += audio_size
            
            print(f"  📦 Chunk {chunk_count}: {audio_size} bytes, final: {chunk.is_final}")
            
            if chunk.is_final:
                break
        
        print(f"\n✅ TTS Test Results:")
        print(f"  📊 Total chunks: {chunk_count}")
        print(f"  📊 Total audio bytes: {total_audio_bytes}")
        
        if total_audio_bytes > 0:
            print(f"  🎉 TTS is working correctly!")
            return True
        else:
            print(f"  ❌ TTS generated empty audio")
            return False
            
    except Exception as e:
        print(f"\n❌ TTS test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

async def test_circuit_breaker_recovery():
    print(f"\n🔄 Testing Circuit Breaker Recovery")
    print("=" * 50)
    
    # Check if we can reset and recover
    try:
        error_recovery_manager.reset_all_circuit_breakers()
        print(f"✅ All circuit breakers reset successfully")
        
        # Wait a moment for the reset to take effect
        await asyncio.sleep(1)
        
        # Test TTS again
        return await test_tts_reliability()
        
    except Exception as e:
        print(f"❌ Circuit breaker recovery failed: {e}")
        return False

async def main():
    print("🚀 Starting TTS Reliability Tests")
    print("=" * 60)
    
    # Test 1: Basic TTS functionality
    tts_works = await test_tts_reliability()
    
    # Test 2: Circuit breaker recovery if TTS failed
    if not tts_works:
        print(f"\n🔧 TTS failed, testing recovery...")
        tts_works = await test_circuit_breaker_recovery()
    
    # Final status
    print(f"\n" + "=" * 60)
    if tts_works:
        print(f"🎉 TTS RELIABILITY TEST PASSED!")
        print(f"✅ TTS should work consistently now")
    else:
        print(f"❌ TTS RELIABILITY TEST FAILED!")
        print(f"🔧 Manual intervention may be required")
        print(f"💡 Try restarting the Django server")
    
    print(f"\n📋 Recommendations:")
    print(f"1. Restart your Django server to pick up new circuit breaker settings")
    print(f"2. Use the reset_circuit_breakers.py script if TTS fails")
    print(f"3. Check Hume API key and network connectivity")

if __name__ == "__main__":
    asyncio.run(main())
