#!/usr/bin/env python3
"""
Integrated Expression Measurement Test
Tests the full integration of optimized expression measurement with fast response service.
"""
import os
import sys
import django
import asyncio
import time
import uuid
import tempfile
import subprocess
import json
import base64

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from channels.testing import WebsocketCommunicator
from channels.routing import URLRouter
from django.urls import re_path
from chat.consumers import ChatConsumer
from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.fast_response_service import get_fast_response_service
from chat.services.expression_measurement_service import expression_measurement_service


def create_speech_audio(text: str) -> bytes:
    """Create speech audio for testing."""
    try:
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
        
        # Generate speech audio
        result = subprocess.run([
            'say', '-o', temp_path, '--data-format=LEI16@16000', text
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error generating speech: {result.stderr}")
            return None
        
        with open(temp_path, 'rb') as f:
            audio_data = f.read()
        
        os.unlink(temp_path)
        return audio_data
        
    except Exception as e:
        print(f"Error creating speech audio: {e}")
        return None


async def setup_test_user() -> User:
    """Setup test user."""
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    return user


async def test_fast_response_service_integration():
    """Test fast response service with expression measurement integration."""
    print("🚀 FAST RESPONSE SERVICE INTEGRATION TEST")
    print("=" * 60)
    
    # Setup
    user = await setup_test_user()
    fast_service = get_fast_response_service(user=user)
    
    # Test cases with different emotional content
    test_cases = [
        ("Happy message", "I'm so excited about this new project!", "excitement"),
        ("Frustrated message", "This is really frustrating and annoying.", "frustration"),
        ("Calm message", "I feel peaceful and relaxed today.", "calmness"),
    ]
    
    results = []
    
    for test_name, text, expected_emotion in test_cases:
        print(f"\n🎤 Testing: {test_name}")
        print(f"   Text: '{text}'")
        
        # Generate audio
        audio_data = create_speech_audio(text)
        if not audio_data:
            print(f"   ❌ Failed to generate audio")
            continue
        
        print(f"   📊 Audio size: {len(audio_data):,} bytes")
        
        # Set audio data for expression analysis
        fast_service.set_audio_data(audio_data)
        
        # Test fast response generation
        start_time = time.time()
        
        response_chunks = []
        tts_chunks = []
        
        try:
            async for chunk in fast_service.process_query_fast(
                user_input=text,
                user_id=str(user.id),
                conversation_history=[],
                emotion_context=None,
                streaming=True,
                enable_tts=True
            ):
                chunk_type = chunk.get('type')
                elapsed_ms = (time.time() - start_time) * 1000
                
                if chunk_type == 'response_chunk':
                    response_chunks.append(chunk)
                    response_time = elapsed_ms
                    print(f"   ✅ Text response: {elapsed_ms:.1f}ms")
                    print(f"      Content: '{chunk.get('content', '')[:50]}...'")
                
                elif chunk_type == 'tts_chunk':
                    tts_chunks.append(chunk)
                    if len(tts_chunks) == 1:  # First TTS chunk
                        first_tts_time = elapsed_ms
                        print(f"   🎵 First TTS chunk: {elapsed_ms:.1f}ms")
                
                elif chunk_type == 'tts_final':
                    total_tts_time = elapsed_ms
                    print(f"   🎵 TTS complete: {elapsed_ms:.1f}ms")
                    break
        
        except Exception as e:
            print(f"   ❌ Error: {e}")
            continue
        
        # Wait a moment for background expression analysis
        await asyncio.sleep(1.0)
        
        # Check if expression context was stored
        expression_context = await fast_service._get_expression_context(str(user.id))
        
        if expression_context:
            print(f"   😊 Expression detected: {expression_context['dominant_emotion']} "
                  f"({expression_context['confidence']:.2f})")
        else:
            print(f"   ⚠️  No expression context stored")
        
        # Test second interaction to see if cached expression is used
        print(f"   🔄 Testing cached expression usage...")
        
        second_start = time.time()
        second_response_chunks = []
        
        async for chunk in fast_service.process_query_fast(
            user_input="Thank you for that response.",
            user_id=str(user.id),
            conversation_history=[],
            emotion_context=None,
            streaming=True,
            enable_tts=False
        ):
            if chunk.get('type') == 'response_chunk':
                second_response_chunks.append(chunk)
                second_response_time = (time.time() - second_start) * 1000
                print(f"   ⚡ Second response: {second_response_time:.1f}ms (with cached expression)")
                break
        
        results.append({
            'test_name': test_name,
            'text': text,
            'audio_size': len(audio_data),
            'response_time_ms': response_time if 'response_time' in locals() else None,
            'first_tts_time_ms': first_tts_time if 'first_tts_time' in locals() else None,
            'expression_detected': expression_context is not None,
            'expression_emotion': expression_context['dominant_emotion'] if expression_context else None,
            'second_response_time_ms': second_response_time if 'second_response_time' in locals() else None,
            'success': len(response_chunks) > 0
        })
    
    return results


async def test_websocket_integration():
    """Test full WebSocket integration with expression measurement."""
    print("\n🔌 WEBSOCKET INTEGRATION TEST")
    print("=" * 60)
    
    user = await setup_test_user()
    
    try:
        # Create WebSocket application
        application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
        ])
        
        # Create communicator
        communicator = WebsocketCommunicator(application, "/ws/chat/")
        communicator.scope["user"] = user
        
        # Connect
        connected, _ = await communicator.connect()
        if not connected:
            print("❌ Failed to connect WebSocket")
            return None
        
        print("✅ WebSocket connected")
        
        # Generate test audio
        text = "I'm feeling really happy and excited about this test!"
        audio_data = create_speech_audio(text)
        
        if not audio_data:
            print("❌ Failed to generate test audio")
            return None
        
        audio_b64 = base64.b64encode(audio_data).decode('utf-8')
        chunk_id = str(uuid.uuid4())
        
        # Send audio chunk
        start_time = time.time()
        
        await communicator.send_json_to({
            'type': 'audio_chunk',
            'data': audio_b64,
            'chunk_id': chunk_id,
            'is_final': True,
            'timestamp': time.time() * 1000
        })
        
        # Collect responses
        responses = []
        text_response_time = None
        first_tts_time = None
        expression_detected = False
        
        timeout = 30
        while (time.time() - start_time) < timeout:
            try:
                response = await asyncio.wait_for(
                    communicator.receive_json_from(),
                    timeout=5.0
                )
                
                response_type = response.get('type')
                elapsed_ms = (time.time() - start_time) * 1000
                
                responses.append((response_type, elapsed_ms))
                
                if response_type == 'llm_response_chunk' and text_response_time is None:
                    text_response_time = elapsed_ms
                    print(f"   📝 Text response: {elapsed_ms:.1f}ms")
                
                elif response_type == 'tts_chunk' and first_tts_time is None:
                    first_tts_time = elapsed_ms
                    print(f"   🎵 First TTS: {elapsed_ms:.1f}ms")
                
                elif response_type == 'emotion_analysis':
                    expression_detected = True
                    print(f"   😊 Expression analysis: {elapsed_ms:.1f}ms")
                
                elif response_type == 'tts_final':
                    print(f"   🎵 TTS complete: {elapsed_ms:.1f}ms")
                    break
                    
            except asyncio.TimeoutError:
                break
        
        await communicator.disconnect()
        
        return {
            'websocket_connected': True,
            'text_response_time_ms': text_response_time,
            'first_tts_time_ms': first_tts_time,
            'expression_detected': expression_detected,
            'total_responses': len(responses),
            'success': text_response_time is not None
        }
        
    except Exception as e:
        print(f"❌ WebSocket error: {e}")
        return {'error': str(e)}


async def main():
    """Run comprehensive integration tests."""
    print("🎯 COMPREHENSIVE EXPRESSION MEASUREMENT INTEGRATION TEST")
    print("=" * 70)
    
    # Test 1: Fast response service integration
    fast_service_results = await test_fast_response_service_integration()
    
    # Test 2: WebSocket integration
    websocket_results = await test_websocket_integration()
    
    # Get service statistics
    expression_stats = expression_measurement_service.get_performance_stats()
    
    # Final analysis
    print("\n🏆 INTEGRATION TEST RESULTS")
    print("=" * 70)
    
    if fast_service_results:
        successful_tests = [r for r in fast_service_results if r.get('success')]
        
        if successful_tests:
            avg_response_time = sum(r['response_time_ms'] for r in successful_tests if r['response_time_ms']) / len(successful_tests)
            expression_detection_rate = sum(1 for r in successful_tests if r['expression_detected']) / len(successful_tests) * 100
            
            print(f"📊 Fast Response Service Integration:")
            print(f"   Successful tests: {len(successful_tests)}/{len(fast_service_results)}")
            print(f"   Average response time: {avg_response_time:.1f}ms")
            print(f"   Expression detection rate: {expression_detection_rate:.1f}%")
            
            # Performance assessment
            if avg_response_time <= 300:
                print(f"   ✅ EXCELLENT - Fast response target met!")
            elif avg_response_time <= 500:
                print(f"   ✅ GOOD - Acceptable for real-time use")
            else:
                print(f"   ⚠️  NEEDS OPTIMIZATION - Response time too slow")
    
    if websocket_results and websocket_results.get('success'):
        print(f"\n🔌 WebSocket Integration:")
        print(f"   Text response: {websocket_results['text_response_time_ms']:.1f}ms")
        print(f"   First TTS: {websocket_results['first_tts_time_ms']:.1f}ms")
        print(f"   Expression detected: {'✅' if websocket_results['expression_detected'] else '❌'}")
    
    print(f"\n📈 Expression Service Statistics:")
    print(f"   Total requests: {expression_stats['total_requests']}")
    print(f"   Success rate: {expression_stats['success_rate_percent']:.1f}%")
    print(f"   Average processing: {expression_stats['avg_processing_time_ms']:.1f}ms")
    print(f"   Cache hit rate: {expression_stats['cache_hit_rate_percent']:.1f}%")
    print(f"   Optimizations enabled: {'✅' if expression_stats['optimizations_enabled'] else '❌'}")
    
    # Overall verdict
    overall_success = (
        fast_service_results and len([r for r in fast_service_results if r.get('success')]) > 0 and
        websocket_results and websocket_results.get('success')
    )
    
    print(f"\n🏆 OVERALL INTEGRATION STATUS:")
    if overall_success:
        print("   ✅ EXCELLENT - Expression measurement fully integrated!")
        print("   🚀 Ready for production deployment with background emotion intelligence!")
    else:
        print("   ⚠️  NEEDS ATTENTION - Some integration issues detected")
    
    print(f"\n✨ Integration test completed!")


if __name__ == "__main__":
    asyncio.run(main())
