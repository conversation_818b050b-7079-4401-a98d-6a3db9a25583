#!/usr/bin/env python3
"""
Test Fixed Emotion Detection System
Tests the improvements made to memory integration and trajectory-aware metrics.
"""
import os
import sys
import django

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')

# Override settings to test both modes
os.environ['EMOTION_CRITIC_ENABLE_MEMORY'] = 'False'  # Test without memory first
os.environ['EMOTION_CRITIC_MEMORY_DEBUG'] = 'True'   # Enable debug logging

django.setup()

import asyncio
from test_memory_enhanced_emotion_detection import test_memory_enhanced_emotion_detection, analyze_memory_enhanced_results


async def test_both_modes():
    """Test emotion detection with and without memory features."""
    
    print("🔧 TESTING FIXED EMOTION DETECTION SYSTEM")
    print("=" * 80)
    
    # Test 1: Without memory features (should improve from baseline)
    print("\n📊 TEST 1: WITHOUT MEMORY FEATURES (Fixed baseline)")
    print("-" * 60)
    
    results_no_memory, user_comparisons_no_memory = await test_memory_enhanced_emotion_detection()
    
    print(f"\n📈 RESULTS WITHOUT MEMORY:")
    await analyze_memory_enhanced_results(results_no_memory, user_comparisons_no_memory)
    
    # Test 2: With memory features enabled (should be even better)
    print("\n" + "=" * 80)
    print("📊 TEST 2: WITH MEMORY FEATURES (Enhanced system)")
    print("-" * 60)
    
    # Enable memory features for second test
    os.environ['EMOTION_CRITIC_ENABLE_MEMORY'] = 'True'
    
    # Restart the emotion critic service to pick up new settings
    from chat.services.emotion_critic_service import emotion_critic_service
    emotion_critic_service.enable_memory_features = True
    
    results_with_memory, user_comparisons_with_memory = await test_memory_enhanced_emotion_detection()
    
    print(f"\n📈 RESULTS WITH MEMORY:")
    await analyze_memory_enhanced_results(results_with_memory, user_comparisons_with_memory)
    
    # Compare results
    print("\n" + "=" * 80)
    print("🔍 COMPARISON: MEMORY IMPACT ANALYSIS")
    print("-" * 60)
    
    if results_no_memory and results_with_memory:
        # Calculate family accuracy for both
        no_memory_family = sum(1 for r in results_no_memory if r['family_match']) / len(results_no_memory) * 100
        with_memory_family = sum(1 for r in results_with_memory if r['family_match']) / len(results_with_memory) * 100
        
        memory_improvement = with_memory_family - no_memory_family
        
        print(f"📊 Family Accuracy Comparison:")
        print(f"   Without Memory: {no_memory_family:.1f}%")
        print(f"   With Memory:    {with_memory_family:.1f}%")
        print(f"   Memory Impact:  {memory_improvement:+.1f}%")
        
        # Trajectory metrics comparison
        no_memory_trajectory = [r for r in results_no_memory if 'arc_similarity' in r]
        with_memory_trajectory = [r for r in results_with_memory if 'arc_similarity' in r]
        
        if no_memory_trajectory and with_memory_trajectory:
            no_memory_arc = sum(r['arc_similarity'] for r in no_memory_trajectory) / len(no_memory_trajectory)
            with_memory_arc = sum(r['arc_similarity'] for r in with_memory_trajectory) / len(with_memory_trajectory)
            
            print(f"\n🌀 Arc Similarity Comparison:")
            print(f"   Without Memory: {no_memory_arc:.3f}")
            print(f"   With Memory:    {with_memory_arc:.3f}")
            print(f"   Improvement:    {with_memory_arc - no_memory_arc:+.3f}")
        
        print(f"\n✅ SYSTEM STATUS:")
        if memory_improvement > 5:
            print("   🎯 EXCELLENT: Memory integration working well!")
        elif memory_improvement > 0:
            print("   ✅ GOOD: Memory provides positive improvement")
        elif memory_improvement > -5:
            print("   ⚠️ NEUTRAL: Memory impact minimal")
        else:
            print("   ❌ ISSUE: Memory causing degradation - needs refinement")
    
    print(f"\n🏁 Fixed emotion detection system test completed!")


if __name__ == "__main__":
    asyncio.run(test_both_modes())
