# Frontend Integration Guide
## EllahAI Flutter App → Django Backend Integration

This document provides comprehensive guidance for integrating the Flutter frontend with the Django backend. It covers all aspects of the integration including authentication, user management, chat functionality, gamification, shop system, and Unity integration.

## 📋 Overview

The Django backend is now ready for integration with the Flutter frontend. The backend implements all the required functionality as specified in the requirements document, including:

- REST API endpoints for all required functionality
- JWT authentication for both HTTP and WebSocket connections
- Real-time chat with WebSocket support
- Gamification system with XP, levels, achievements, and rewards
- Shop system with virtual currency and item purchases
- Unity integration for avatar customization

## 🔑 Authentication

### Authentication Endpoints

```
POST /api/auth/register/                # Register a new user
POST /api/auth/login/                   # Login with email/password
POST /api/auth/refresh/                 # Refresh JWT token
POST /api/auth/logout/                  # Logout and invalidate token
POST /api/auth/google/                  # Login with Google OAuth
POST /api/auth/apple/                   # Login with Apple Sign In
```

### Authentication Flow

1. **Registration/Login**: Send credentials to the appropriate endpoint
2. **Token Reception**: Receive JWT access and refresh tokens
3. **Token Storage**: Store tokens securely in the Flutter app
4. **API Requests**: Include the access token in the Authorization header
5. **Token Refresh**: Use the refresh token to get a new access token when expired

### Example Authentication Request

```dart
// Example Flutter code for login
final response = await http.post(
  Uri.parse('https://api.example.com/api/auth/login/'),
  headers: {'Content-Type': 'application/json'},
  body: jsonEncode({
    'email': '<EMAIL>',
    'password': 'password123'
  }),
);

if (response.statusCode == 200) {
  final data = jsonDecode(response.body);
  final accessToken = data['tokens']['access'];
  final refreshToken = data['tokens']['refresh'];
  
  // Store tokens securely
  await secureStorage.write(key: 'access_token', value: accessToken);
  await secureStorage.write(key: 'refresh_token', value: refreshToken);
}
```

### WebSocket Authentication

WebSocket connections require JWT authentication. Include the token in the connection URL:

```dart
// Example WebSocket connection with JWT
final token = await secureStorage.read(key: 'access_token');
final ws = WebSocketChannel.connect(
  Uri.parse('ws://api.example.com/ws/chat/?token=$token'),
);
```

## 👤 User Management

### User Management Endpoints

```
GET /api/user/profile/                  # Get user profile
PUT /api/user/profile/                  # Update user profile
GET /api/user/settings/                 # Get user settings
PUT /api/user/settings/                 # Update user settings
GET /api/user/progress/                 # Get user progress
POST /api/user/progress/update/         # Update user progress
```

### User Model

The backend User model includes all fields required by the Flutter app:

```json
{
  "id": "uuid",
  "username": "username",
  "email": "<EMAIL>",
  "first_name": "First",
  "last_name": "Last",
  "selected_personality": "caringFriend",
  "selected_environment": "cozy_room",
  "owned_environments": ["cozy_room", "beach"],
  "owned_outfits": ["casual", "formal"],
  "owned_pets": ["cat", "dog"],
  "preferences": {
    "theme": "dark",
    "notifications": true
  },
  "progress": {
    "xp": 150,
    "level": 3,
    "hearts": 5,
    "messages_count": 42,
    "voice_messages_count": 12,
    "achievements": ["first_chat", "daily_streak_7"],
    "total_time_spent": 3600
  }
}
```

## 💬 Chat System

### Chat Endpoints

```
GET /api/conversations/                 # List conversations
POST /api/conversations/                # Create a new conversation
GET /api/conversations/{id}/messages/   # Get messages in a conversation
POST /api/conversations/{id}/messages/  # Send a message
DELETE /api/conversations/{id}/         # Delete a conversation
```

### WebSocket Chat Protocol

The WebSocket connection supports real-time chat with the following message types:

#### Connection

```
ws://api.example.com/ws/chat/?token=<jwt_token>
```

#### Sending a Text Message

```json
{
  "type": "text_message",
  "content": "Hello, how are you?",
  "conversation_id": "uuid-here"
}
```

#### Sending an Audio Message

```json
{
  "type": "audio_chunk",
  "data": "base64-encoded-audio-data",
  "chunk_id": "uuid-here",
  "is_final": true,
  "conversation_id": "uuid-here"
}
```

#### Typing Indicators

```json
{
  "type": "typing_start"
}
```

```json
{
  "type": "typing_stop"
}
```

#### Receiving AI Responses

The backend will send streaming responses:

```json
{
  "type": "llm_response_chunk",
  "content": "Hello! I'm",
  "chunk_id": "uuid-here",
  "is_final": false
}
```

```json
{
  "type": "llm_response_chunk",
  "content": " doing well. How are you?",
  "chunk_id": "uuid-here",
  "is_final": true
}
```

#### Receiving Transcriptions

```json
{
  "type": "transcription_partial",
  "text": "Hello how are",
  "confidence": 0.95,
  "chunk_id": "uuid-here",
  "is_partial": true
}
```

```json
{
  "type": "transcription_partial",
  "text": "Hello how are you today?",
  "confidence": 0.98,
  "chunk_id": "uuid-here",
  "is_partial": false
}
```

#### Receiving Emotion Detection

```json
{
  "type": "emotion_detected",
  "emotions": [
    {"name": "happy", "score": 0.8},
    {"name": "neutral", "score": 0.15},
    {"name": "sad", "score": 0.05}
  ],
  "confidence_score": 0.9,
  "chunk_id": "uuid-here"
}
```

## 🎮 Gamification System

### Gamification Endpoints

```
GET /api/user/level/                    # Get user level info
GET /api/user/achievements/             # Get user achievements
POST /api/achievements/unlock/          # Unlock an achievement
GET /api/user/wallet/                   # Get user wallet
POST /api/user/wallet/add-currency/     # Add currency to wallet
GET /api/daily-rewards/                 # Get daily rewards
POST /api/daily-rewards/claim/          # Claim daily reward
```

### XP and Level System

The backend implements a progressive XP system:

- XP is awarded for various actions (chatting, completing achievements, etc.)
- Levels are calculated based on accumulated XP
- Each level requires more XP than the previous one
- Level-ups may unlock new features or items

### Achievement System

Achievements are tracked and can be unlocked based on various criteria:

```json
{
  "id": "uuid",
  "name": "Chatty Friend",
  "description": "Send 100 messages",
  "category": "chat",
  "rarity": "rare",
  "xp_reward": 50,
  "currency_reward": 20,
  "requirement_type": "message_count",
  "requirement_value": 100,
  "is_hidden": false
}
```

### Daily Rewards

The backend supports a daily reward system:

```json
{
  "day": 7,
  "xp_reward": 50,
  "currency_reward": 100,
  "is_bonus_day": true
}
```

## 🛒 Shop System

### Shop Endpoints

```
GET /api/shop/items/                    # List all shop items
GET /api/shop/items/{category}/         # List items by category
POST /api/shop/purchase/                # Purchase an item
GET /api/user/inventory/                # Get user inventory
POST /api/shop/preview/                 # Preview an item
```

### Shop Items

Shop items include environments, outfits, accessories, companions, and pets:

```json
{
  "id": "uuid",
  "name": "Beach Environment",
  "description": "A relaxing beach setting",
  "price": 500,
  "item_type": "environment",
  "image_url": "https://example.com/beach.png",
  "preview_data": {
    "unity_asset_id": "beach_env_01",
    "preview_image": "https://example.com/beach_preview.png"
  },
  "is_active": true,
  "is_featured": true,
  "level_requirement": 2,
  "relationship_level_requirement": 1
}
```

### Purchase Flow

1. Check if the user can purchase the item (`can_purchase` method)
2. Process the purchase (deduct currency, add to inventory)
3. Update the user's owned items list
4. Return the updated inventory and wallet

### Relationship Gates

Some items require specific relationship levels to unlock:

```json
{
  "level": 2,
  "unlocked_items": ["romantic_outfit", "special_environment"]
}
```

## 🤖 Agent System

### Agent System Overview

The backend includes a sophisticated agent system with specialized AI agents for different domains and comprehensive task management capabilities.

### Agent Domains

The system supports multiple specialized agents:

- **General Agent**: Warm, playful, emotionally intelligent companion
  - Natural conversation and emotional support
  - Personality adaptation based on user mood
  - Memory of previous conversations
  - Supportive and encouraging responses

- **Dev Agent**: Programming and technology expertise
  - Code examples and explanations
  - Debugging assistance
  - Best practices guidance
  - Multiple programming languages
  - Architecture and design patterns

- **Business Agent**: Professional and business topics
  - Business strategy and planning
  - Finance and investment advice
  - Marketing and growth strategies
  - Professional development guidance
  - Workplace communication tips

- **Learning Agent**: Education and knowledge sharing
  - Educational content and explanations
  - Learning path recommendations
  - Study techniques and strategies
  - Knowledge assessment and quizzes
  - Personalized learning experiences

- **Music Agent**: Music theory, recommendations, and analysis
  - Music theory explanations
  - Song recommendations
  - Genre exploration
  - Music analysis and insights
  - Playlist creation assistance

- **Trivia Agent**: Fun facts and knowledge quizzes
  - Interactive trivia games
  - Educational facts
  - Knowledge challenges
  - Fun learning experiences
  - Customizable difficulty levels

### Agent Endpoints

```
POST /api/agents/process-query/          # Process query with domain routing
GET /api/agents/domains/                 # Get available agent domains
POST /api/agents/switch-domain/          # Switch to specific agent domain
```

### Agent Query Processing

```json
{
  "query": "How do I implement authentication in Flutter?",
  "user_id": "uuid",
  "conversation_history": [...],
  "emotion_context": {
    "primary_emotion": "curious",
    "intensity": 0.7
  },
  "memory_context": {
    "relevant_memories": [...],
    "user_preferences": {...}
  },
  "streaming": true,
  "auto_create_task": true
}
```

### Agent Response Format

```json
{
  "type": "agent_response",
  "content": "To implement authentication in Flutter...",
  "domain": "dev",
  "confidence": 0.95,
  "suggested_actions": [
    "create_task",
    "show_code_example"
  ],
  "emotion_adaptation": {
    "detected_emotion": "curious",
    "response_tone": "helpful_and_encouraging"
  }
}
```

## 📋 Task Management System

### Task Management Overview

The backend includes a comprehensive task management system that tracks complex tasks with multiple phases, progress monitoring, and intervention handling.

### Task Model

Tasks support detailed tracking with phases, progress, and status management:

```json
{
  "id": "uuid",
  "user": "user-uuid",
  "title": "Build Flutter Authentication System",
  "description": "Implement secure authentication with JWT tokens",
  "status": "running",
  "progress": 65.5,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z",
  "phases": [
    {
      "phase": "initialization",
      "title": "Project Setup",
      "description": "Initialize Flutter project and dependencies",
      "is_completed": true,
      "is_current": false,
      "sub_tasks": [
        {"title": "Create new Flutter project", "completed": true},
        {"title": "Add authentication dependencies", "completed": true}
      ]
    },
    {
      "phase": "analysis",
      "title": "Requirements Analysis",
      "description": "Analyze authentication requirements",
      "is_completed": true,
      "is_current": false,
      "sub_tasks": [...]
    },
    {
      "phase": "execution",
      "title": "Implementation",
      "description": "Implement authentication logic",
      "is_completed": false,
      "is_current": true,
      "sub_tasks": [...]
    }
  ],
  "error_message": null,
  "intervention_message": null
}
```

### Task Status Options

- `pending`: Task is created but not started
- `running`: Task is currently being executed
- `completed`: Task has been successfully completed
- `failed`: Task encountered an error and failed
- `needs_intervention`: Task requires user intervention
- `cancelled`: Task was cancelled by user

### Task Phases

- `initialization`: Project setup and preparation
- `analysis`: Requirements and planning analysis
- `planning`: Detailed planning and design
- `execution`: Main implementation work
- `debugging`: Error fixing and optimization
- `testing`: Testing and validation
- `finalization`: Final touches and documentation

### Task Management Endpoints

```
GET /api/agents/tasks/                   # List all user tasks
POST /api/agents/tasks/                  # Create a new task
GET /api/agents/tasks/{id}/              # Get specific task details
PUT /api/agents/tasks/{id}/              # Update task
DELETE /api/agents/tasks/{id}/           # Delete task
PATCH /api/agents/tasks/{id}/update_status/ # Update task status
PATCH /api/agents/tasks/{id}/update_progress/ # Update task progress
POST /api/agents/tasks/{id}/add_phase/   # Add new phase to task
GET /api/agents/tasks/sync_with_orchestrator/ # Sync with agent orchestrator
```

### Creating a Task

```json
{
  "title": "Build Flutter Authentication System",
  "description": "Implement secure authentication with JWT tokens and biometric login",
  "phases": [
    {
      "phase": "initialization",
      "title": "Project Setup",
      "description": "Initialize Flutter project and add authentication dependencies",
      "sub_tasks": [
        {"title": "Create new Flutter project", "completed": false},
        {"title": "Add http and shared_preferences packages", "completed": false},
        {"title": "Set up project structure", "completed": false}
      ]
    },
    {
      "phase": "analysis",
      "title": "Requirements Analysis",
      "description": "Analyze authentication requirements and design API integration",
      "sub_tasks": [
        {"title": "Define authentication flow", "completed": false},
        {"title": "Design API integration", "completed": false}
      ]
    }
  ]
}
```

### Updating Task Progress

```json
{
  "progress": 75.0,
  "current_phase": "execution"
}
```

### Updating Task Status

```json
{
  "status": "needs_intervention",
  "intervention_message": "Need clarification on biometric authentication requirements"
}
```

### Adding a Phase to Task

```json
{
  "phase": "testing",
  "title": "Testing and Validation",
  "description": "Test authentication flow and validate security",
  "sub_tasks": [
    {"title": "Unit tests for auth service", "completed": false},
    {"title": "Integration tests", "completed": false},
    {"title": "Security validation", "completed": false}
  ]
}
```

### Task Error Handling and Intervention

Tasks can encounter errors or require user intervention:

#### Error Response
```json
{
  "type": "task_error",
  "task_id": "uuid",
  "error_message": "Failed to connect to authentication service",
  "error_code": "AUTH_SERVICE_UNAVAILABLE",
  "suggested_resolution": "Check network connection and retry",
  "can_retry": true,
  "retry_count": 2
}
```

#### Intervention Required
```json
{
  "type": "task_intervention",
  "task_id": "uuid",
  "intervention_message": "Need clarification on biometric authentication requirements",
  "intervention_type": "user_input_required",
  "required_fields": [
    "biometric_type",
    "security_level",
    "fallback_method"
  ],
  "options": {
    "biometric_type": ["fingerprint", "face_id", "touch_id"],
    "security_level": ["basic", "enhanced", "maximum"]
  }
}
```

#### Task Recovery
```json
{
  "type": "task_recovery",
  "task_id": "uuid",
  "recovery_action": "retry_with_fallback",
  "fallback_method": "password_only",
  "estimated_completion_time": "5 minutes",
  "progress_after_recovery": 85.0
}
```

### Task Orchestration Integration

Tasks can be automatically created from agent queries and synchronized with the agent orchestrator:

```json
{
  "type": "task_creation",
  "task_id": "uuid",
  "title": "Build Flutter Authentication System",
  "description": "Auto-generated from user query",
  "domain": "dev",
  "estimated_complexity": "medium",
  "suggested_phases": [...]
}
```

### Agent Orchestrator Integration

The backend includes a sophisticated agent orchestrator that manages:

- **Domain Classification**: Automatically routes queries to appropriate specialized agents
- **Emotion Context**: Adapts responses based on user emotional state
- **Memory Integration**: Uses conversation history and user preferences
- **Task Creation**: Automatically creates tasks from complex queries
- **Streaming Responses**: Provides real-time streaming responses

### Memory System Integration

The agent system integrates with a comprehensive memory system:

```json
{
  "memory_context": {
    "relevant_memories": [
      {
        "id": "memory-uuid",
        "content": "User prefers detailed code examples",
        "salience_score": 0.85,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "user_preferences": {
      "technical_level": "intermediate",
      "preferred_language": "python",
      "learning_style": "hands_on"
    },
    "conversation_context": {
      "recent_topics": ["flutter", "authentication"],
      "ongoing_tasks": ["task-uuid-1", "task-uuid-2"]
    }
  }
}
```

## 🧠 Memory Management System

### Memory Management Overview

The backend includes a comprehensive memory management system that allows users to store, retrieve, and manage their personal memories and preferences.

### Memory Types

The system supports multiple memory types:

- **semantic_profile**: User-specific stable preferences and characteristics
- **episodic_summary**: Summaries of conversations or interactions
- **general_knowledge**: General facts or domain knowledge
- **explicit_memory**: Specific facts or notes explicitly saved
- **personal_fact**: Personal information about the user
- **preference**: User preferences and choices
- **goal**: User goals and aspirations
- **relationship**: Information about user relationships
- **skill**: User skills and abilities
- **interest**: User interests and hobbies

### Memory Model

Memories include comprehensive tracking with salience scoring:

```json
{
  "id": "uuid",
  "user": "user-uuid",
  "content": "User prefers dark theme for UI",
  "memory_type": "preference",
  "memory_type_display": "User preferences and choices",
  "importance_score": 0.8,
  "personalness_score": 0.9,
  "actionability_score": 0.7,
  "salience_score": 0.79,
  "vector_id": "mem_abc123def456",
  "source_conversation": "conversation-uuid",
  "source_message": "message-uuid",
  "metadata": {
    "context": "UI discussion",
    "tags": ["theme", "preference"]
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z",
  "last_accessed": "2024-01-15T11:45:00Z",
  "is_active": true,
  "is_verified": false
}
```

### Memory Management Endpoints

```
GET /api/agents/memories/                    # List all user memories
POST /api/agents/memories/                   # Create a new memory
GET /api/agents/memories/{id}/               # Get specific memory details
PUT /api/agents/memories/{id}/               # Update memory
DELETE /api/agents/memories/{id}/            # Delete memory
GET /api/agents/memories/types/              # Get available memory types
GET /api/agents/memories/stats/              # Get memory statistics
GET /api/agents/memories/search/             # Search memories
POST /api/agents/memories/{id}/verify/       # Mark memory as verified
POST /api/agents/memories/{id}/deactivate/   # Deactivate memory
POST /api/agents/memories/{id}/reactivate/   # Reactivate memory
```

### Creating a Memory

```json
{
  "content": "User prefers dark theme for UI",
  "memory_type": "preference",
  "importance_score": 0.8,
  "personalness_score": 0.9,
  "actionability_score": 0.7,
  "metadata": {
    "context": "UI discussion",
    "tags": ["theme", "preference"]
  }
}
```

### Memory Search

Search memories with various filters:

```json
{
  "q": "dark theme",
  "type": "preference",
  "active": "true"
}
```

### Memory Statistics

Get comprehensive memory statistics:

```json
{
  "total_memories": 25,
  "active_memories": 23,
  "verified_memories": 15,
  "memory_types": {
    "preference": 8,
    "skill": 5,
    "personal_fact": 4,
    "goal": 3,
    "interest": 3,
    "relationship": 2
  },
  "average_salience_score": 0.72,
  "recent_memories_count": 5
}
```

### Memory Clusters

Organize memories into clusters for better management:

```json
{
  "id": "uuid",
  "user": "user-uuid",
  "name": "Programming Preferences",
  "description": "User's programming-related preferences and skills",
  "cluster_type": "topic",
  "memory_count": 5,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z"
}
```

### Memory Cluster Endpoints

```
GET /api/agents/memory-clusters/             # List all user clusters
POST /api/agents/memory-clusters/            # Create a new cluster
GET /api/agents/memory-clusters/{id}/        # Get specific cluster details
PUT /api/agents/memory-clusters/{id}/        # Update cluster
DELETE /api/agents/memory-clusters/{id}/     # Delete cluster
POST /api/agents/memory-clusters/{id}/add-memory/    # Add memory to cluster
POST /api/agents/memory-clusters/{id}/remove-memory/ # Remove memory from cluster
GET /api/agents/memory-clusters/{id}/memories/       # Get memories in cluster
```

### Memory Configuration

Configure memory system behavior:

```json
{
  "min_importance_threshold": 0.3,
  "min_personalness_threshold": 0.2,
  "min_actionability_threshold": 0.2,
  "max_memories_per_retrieval": 5,
  "similarity_threshold": 0.7,
  "allow_memory_storage": true,
  "allow_cross_conversation_memory": true,
  "auto_cleanup_enabled": true,
  "memory_retention_days": 365
}
```

### Memory Configuration Endpoints

```
GET /api/agents/memory/configuration/        # Get memory configuration
PUT /api/agents/memory/configuration/        # Update memory configuration
```

### Bulk Memory Operations

Perform bulk operations on memories:

```json
{
  "memories": [
    {
      "content": "User prefers dark theme",
      "memory_type": "preference",
      "importance_score": 0.8,
      "personalness_score": 0.9,
      "actionability_score": 0.7
    },
    {
      "content": "User knows Python programming",
      "memory_type": "skill",
      "importance_score": 0.9,
      "personalness_score": 0.8,
      "actionability_score": 0.9
    }
  ]
}
```

### Bulk Memory Endpoints

```
POST /api/agents/memory/bulk/               # Bulk create memories
DELETE /api/agents/memory/bulk/              # Bulk delete memories
```

### Memory Verification and Status

Manage memory verification and activation status:

```json
{
  "type": "memory_verification",
  "memory_id": "uuid",
  "is_verified": true,
  "verification_date": "2024-01-15T11:45:00Z"
}
```

```json
{
  "type": "memory_status_change",
  "memory_id": "uuid",
  "is_active": false,
  "deactivation_reason": "outdated_information"
}
```

### Agent Response Streaming

The agent system supports real-time streaming responses:

```json
{
  "type": "agent_response_chunk",
  "content": "To implement authentication in Flutter...",
  "domain": "dev",
  "confidence": 0.95,
  "is_final": false,
  "chunk_id": "uuid-here"
}
```

```json
{
  "type": "agent_response_chunk",
  "content": " you'll need to set up JWT tokens.",
  "domain": "dev",
  "confidence": 0.95,
  "is_final": true,
  "chunk_id": "uuid-here",
  "suggested_actions": [
    "create_task",
    "show_code_example"
  ]
}
```

## 🎭 Unity Integration

### Unity Integration Endpoints

```
POST /api/agents/avatar/outfit/          # Change avatar outfit
POST /api/agents/avatar/environment/     # Change environment
POST /api/agents/avatar/animation/       # Play an animation
GET /api/agents/avatar/settings/         # Get avatar settings
POST /api/agents/avatar/voice-command/   # Process voice command
```

### Avatar Customization

The backend supports changing avatar appearance:

```json
{
  "outfit_id": "casual_outfit",
  "accessories": ["glasses", "hat"]
}
```

### Environment Management

Change the 3D environment:

```json
{
  "environment_id": "beach",
  "time_of_day": "sunset"
}
```

### Animation Control

Trigger animations:

```json
{
  "animation_id": "wave",
  "intensity": 0.8,
  "duration": 2.5
}
```

## 📁 File Upload System

### File Upload Endpoints

```
POST /api/upload/avatar/                # Upload user avatar
POST /api/upload/audio/                 # Upload audio file
GET /api/media/{file_id}/               # Get uploaded file
```

### Avatar Upload

```dart
// Example Flutter code for avatar upload
final request = http.MultipartRequest(
  'POST',
  Uri.parse('https://api.example.com/api/upload/avatar/'),
);

request.files.add(await http.MultipartFile.fromPath(
  'avatar',
  imagePath,
));

request.headers['Authorization'] = 'Bearer $token';

final response = await request.send();
```

### Audio Upload

```dart
// Example Flutter code for audio upload
final request = http.MultipartRequest(
  'POST',
  Uri.parse('https://api.example.com/api/upload/audio/'),
);

request.files.add(await http.MultipartFile.fromPath(
  'audio',
  audioPath,
));

request.headers['Authorization'] = 'Bearer $token';

final response = await request.send();
```

## 🔄 Data Synchronization

### Initial Data Load

When the app starts, fetch the following data:

1. User profile and settings
2. User progress and achievements
3. Conversations list
4. Shop items
5. User inventory

### Real-time Updates

Use WebSocket for real-time updates:

1. New messages
2. Typing indicators
3. XP and level changes
4. Achievement unlocks

### Offline Support

The backend supports reconnection with session restoration:

```json
{
  "type": "reconnection_request",
  "previous_session_id": "uuid-here",
  "previous_connection_id": "uuid-here"
}
```

## 🔒 Security Considerations

### Token Security

- Store JWT tokens securely using Flutter's secure storage
- Implement token refresh mechanism
- Clear tokens on logout

### Input Validation

- Validate all user inputs before sending to the backend
- Handle error responses appropriately

### Error Handling

The backend returns standardized error responses:

```json
{
  "error": {
    "code": "validation_error",
    "message": "Invalid input data",
    "details": {
      "field": "error description"
    }
  }
}
```

## 📊 Performance Considerations

### Caching

- Cache frequently accessed data (user profile, conversations)
- Implement efficient image caching
- Use pagination for large data sets

### WebSocket Optimization

- Implement reconnection with exponential backoff
- Handle connection state changes gracefully
- Monitor connection health with heartbeats

```json
{
  "type": "connection_heartbeat"
}
```

## 🧪 Testing

### API Testing

Test all API endpoints with different scenarios:

1. Valid inputs
2. Invalid inputs
3. Edge cases
4. Authentication failures

### WebSocket Testing

Test WebSocket functionality:

1. Connection establishment
2. Message sending and receiving
3. Streaming responses
4. Connection loss and reconnection

## 📱 Integration Checklist

- [ ] Set up API client with JWT authentication
- [ ] Implement WebSocket connection with authentication
- [ ] Create models matching backend data structures
- [ ] Implement user authentication flow
- [ ] Set up real-time chat with streaming
- [ ] Integrate gamification features
- [ ] Implement shop and inventory system
- [ ] Connect Unity integration endpoints
- [ ] Set up file upload functionality
- [ ] Implement error handling and retry logic
- [ ] Implement WebSocket TTS streaming handler
- [ ] Support chunk-based audio playback
- [ ] Handle emotion-based voice modulation
- [ ] Manage voice configuration dynamically
- [ ] Implement error recovery strategies
- [ ] Track and log TTS performance metrics
- [ ] **Agent System Integration**
  - [ ] Implement agent domain routing
  - [ ] Create agent query processing
  - [ ] Handle multi-agent responses
  - [ ] Support emotion-aware agent responses
  - [ ] Implement agent switching functionality
- [ ] **Task Management Integration**
  - [ ] Implement task model in Flutter
  - [ ] Create task creation UI
  - [ ] Implement task status update functionality
  - [ ] Add progress tracking UI
  - [ ] Create phase management interface
  - [ ] Handle task error and intervention scenarios
  - [ ] Implement task list and detail views
  - [ ] Add task filtering and sorting
  - [ ] Support task orchestration integration
  - [ ] Implement automatic task creation from queries
- [ ] **Memory Management Integration**
  - [ ] Implement memory model in Flutter
  - [ ] Create memory creation and editing UI
  - [ ] Implement memory search and filtering
  - [ ] Add memory statistics dashboard
  - [ ] Create memory cluster management
  - [ ] Implement memory configuration settings
  - [ ] Add bulk memory operations
  - [ ] Support memory verification workflow
  - [ ] Create memory type selection interface
  - [ ] Implement memory salience score display
- [ ] Test all agent features
- [ ] Test all task tracking features
- [ ] Test all features with the backend

## 🚀 Next Steps

1. **Set up API client**: Create a Flutter service for API communication
2. **Implement authentication**: Connect login/registration to backend
3. **Set up WebSocket**: Implement real-time chat functionality
4. **Integrate gamification**: Connect XP, levels, and achievements
5. **Implement shop**: Connect virtual shop and inventory
6. **Connect Unity**: Integrate avatar customization and animations

## 📞 Support

For any questions or issues during integration, please contact the backend team.

## 🎙️ Hume TTS Streaming

### TTS Streaming Protocol

The backend supports advanced Text-to-Speech (TTS) streaming with emotion-aware voice modulation using Hume AI. This allows for dynamic, expressive audio generation.

#### TTS Streaming Message Types

##### Audio Chunk Streaming
```json
{
  "type": "audio_chunk",
  "data": "base64-encoded-audio-data",
  "chunk_id": "unique-chunk-identifier",
  "is_final": false,
  "voice_settings": {
    "voice_name": "nova",
    "emotion_context": {
      "primary_emotion": "happy",
      "intensity": 0.7
    },
    "speed": 1.0,
    "acting_instructions": "Speak with gentle excitement"
  },
  "timestamp": 1623456789000,
  "request_id": "optional-request-tracking-id"
}
```

##### Voice Configuration Options
- `voice_name`: Select from available voices (e.g., 'nova', 'shimmer', 'onyx')
- `emotion_context`: Modulate voice based on detected emotions
  - `primary_emotion`: Emotion to influence voice tone
  - `intensity`: Strength of emotional modulation
- `speed`: Speech rate (0.25 to 4.0)
- `acting_instructions`: Custom voice styling guidance

#### Emotion-Aware TTS Example

```dart
// Flutter WebSocket TTS Streaming Example
void streamTTSResponse(String text, EmotionContext emotionContext) {
  final socket = WebSocketChannel.connect(Uri.parse('ws://api.example.com/ws/tts'));
  
  socket.sink.add(jsonEncode({
    'type': 'tts_request',
    'text': text,
    'voice_settings': {
      'voice_name': 'nova',
      'emotion_context': {
        'primary_emotion': emotionContext.primaryEmotion,
        'intensity': emotionContext.emotionIntensity
      },
      'speed': 1.0
    }
  }));

  socket.stream.listen((chunk) {
    final audioChunk = AudioChunk.fromJson(jsonDecode(chunk));
    
    // Process audio chunk
    if (!audioChunk.isFinal) {
      playAudioChunk(audioChunk.audioData);
    } else {
      // Final chunk - complete audio playback
      finalizeAudioPlayback();
    }
  });
}
```

#### Performance and Error Handling

- Streaming supports partial audio generation
- Handles network interruptions
- Provides fallback mechanisms
- Tracks performance metrics (first chunk time, total generation time)

#### Integration Considerations

1. Implement chunk-based audio playback
2. Handle streaming interruptions
3. Manage voice settings dynamically
4. Use emotion context for voice modulation

### Voice Settings API

```json
{
  "available_voices": [
    {
      "name": "nova",
      "description": "Warm, friendly tone",
      "gender": "neutral",
      "languages": ["en"],
      "emotion_range": ["happy", "calm", "excited"]
    },
    {
      "name": "shimmer",
      "description": "Energetic, youthful voice",
      "gender": "female",
      "languages": ["en"],
      "emotion_range": ["playful", "enthusiastic"]
    }
  ]
}
```

### Error Handling

```json
{
  "type": "tts_error",
  "error_code": "STREAM_INTERRUPTED",
  "message": "TTS streaming was interrupted",
  "recovery_suggestion": "Retry the request or use text-only fallback"
}
```