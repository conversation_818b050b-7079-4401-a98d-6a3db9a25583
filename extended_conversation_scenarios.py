#!/usr/bin/env python3
"""
Extended Conversation Scenarios for Multi-Modal Emotion Detection
Creates detailed 10-15 interaction scenarios for testing accuracy over time.
"""
import json
from pathlib import Path

def create_extended_scenarios():
    """Create extended conversation scenarios with 10-15 interactions each."""
    
    extended_scenarios = {
        "metadata": {
            "version": "2.0",
            "description": "Extended conversation scenarios for testing emotion accuracy over time",
            "total_scenarios": 17,  # 15 original + 2 extended
            "extended_scenarios": 2,
            "total_interactions": 92  # Original 62 + 30 new
        },
        "scenarios": [
            # Extended Scenario 1: Job Interview Journey (15 interactions)
            {
                "scenario": "job_interview_journey",
                "description": "Complete job interview process from anxiety to confidence to outcome",
                "interactions": 15,
                "emotional_arc": "anxiety → nervousness → preparation → confidence → interview_stress → performance → relief → anticipation → disappointment → reflection → determination → hope → action → success → joy",
                "interactions": [
                    {
                        "stage": "initial_anxiety",
                        "expected_emotion": "anxiety",
                        "text": "I have a really important job interview tomorrow and I'm so nervous.",
                        "voice_style": "[nervously, with worry]",
                        "context": "User just learned about interview"
                    },
                    {
                        "stage": "building_worry",
                        "expected_emotion": "fear",
                        "text": "What if I mess up and say something stupid?",
                        "voice_style": "[with increasing anxiety]",
                        "context": "Catastrophic thinking setting in"
                    },
                    {
                        "stage": "seeking_help",
                        "expected_emotion": "determination",
                        "text": "I need to prepare properly for this interview.",
                        "voice_style": "[with resolve, determined]",
                        "context": "Deciding to take action"
                    },
                    {
                        "stage": "preparation_focus",
                        "expected_emotion": "concentration",
                        "text": "Let me research the company and practice my answers.",
                        "voice_style": "[focused, methodical]",
                        "context": "Getting into preparation mode"
                    },
                    {
                        "stage": "building_confidence",
                        "expected_emotion": "satisfaction",
                        "text": "I've prepared well and I know my strengths.",
                        "voice_style": "[with growing confidence]",
                        "context": "Preparation paying off"
                    },
                    {
                        "stage": "morning_nerves",
                        "expected_emotion": "anxiety",
                        "text": "It's the morning of the interview and my heart is racing.",
                        "voice_style": "[breathless, nervous energy]",
                        "context": "Interview day anxiety spike"
                    },
                    {
                        "stage": "arrival_stress",
                        "expected_emotion": "panic",
                        "text": "I'm here at the office and I feel like I might panic.",
                        "voice_style": "[rapid speech, panicked]",
                        "context": "Peak stress moment"
                    },
                    {
                        "stage": "calming_down",
                        "expected_emotion": "calmness",
                        "text": "Take deep breaths, you've got this, stay calm.",
                        "voice_style": "[slow, deliberate, calming]",
                        "context": "Self-soothing technique"
                    },
                    {
                        "stage": "interview_performance",
                        "expected_emotion": "concentration",
                        "text": "I'm focusing on giving clear, thoughtful answers.",
                        "voice_style": "[steady, professional]",
                        "context": "During the interview"
                    },
                    {
                        "stage": "post_interview_relief",
                        "expected_emotion": "relief",
                        "text": "The interview is over and I think it went pretty well.",
                        "voice_style": "[exhaling with relief]",
                        "context": "Immediate post-interview"
                    },
                    {
                        "stage": "waiting_anxiety",
                        "expected_emotion": "anticipation",
                        "text": "Now I have to wait for their decision and it's killing me.",
                        "voice_style": "[restless, anticipatory]",
                        "context": "Waiting for results"
                    },
                    {
                        "stage": "rejection_news",
                        "expected_emotion": "disappointment",
                        "text": "They called and said they went with someone else.",
                        "voice_style": "[deflated, disappointed]",
                        "context": "Receiving bad news"
                    },
                    {
                        "stage": "processing_sadness",
                        "expected_emotion": "sadness",
                        "text": "I'm really sad about this, I wanted that job so much.",
                        "voice_style": "[quietly sad, dejected]",
                        "context": "Processing the rejection"
                    },
                    {
                        "stage": "finding_resolve",
                        "expected_emotion": "determination",
                        "text": "But I'm not giving up, I'll keep applying and improving.",
                        "voice_style": "[with renewed determination]",
                        "context": "Bouncing back with resolve"
                    },
                    {
                        "stage": "future_optimism",
                        "expected_emotion": "hope",
                        "text": "The right opportunity will come along when it's meant to.",
                        "voice_style": "[hopeful, optimistic]",
                        "context": "Looking forward with hope"
                    }
                ]
            },
            
            # Extended Scenario 2: Relationship Conflict Resolution (12 interactions)
            {
                "scenario": "relationship_conflict_resolution",
                "description": "Working through a serious relationship conflict from anger to understanding",
                "interactions": 12,
                "emotional_arc": "anger → frustration → hurt → sadness → reflection → understanding → empathy → forgiveness → gratitude → love → commitment → peace",
                "interactions": [
                    {
                        "stage": "initial_anger",
                        "expected_emotion": "anger",
                        "text": "I can't believe they did this to me, I'm so angry right now!",
                        "voice_style": "[furious, heated]",
                        "context": "Discovery of the conflict"
                    },
                    {
                        "stage": "venting_frustration",
                        "expected_emotion": "frustration",
                        "text": "This is so frustrating, why can't they understand how this affects me?",
                        "voice_style": "[exasperated, frustrated]",
                        "context": "Expressing frustration"
                    },
                    {
                        "stage": "feeling_hurt",
                        "expected_emotion": "sadness",
                        "text": "It really hurts that someone I care about would do this.",
                        "voice_style": "[voice breaking, hurt]",
                        "context": "Deeper emotional pain emerging"
                    },
                    {
                        "stage": "questioning_relationship",
                        "expected_emotion": "confusion",
                        "text": "I don't know what to think about our relationship anymore.",
                        "voice_style": "[uncertain, confused]",
                        "context": "Questioning the relationship"
                    },
                    {
                        "stage": "deep_sadness",
                        "expected_emotion": "distress",
                        "text": "This whole situation is making me feel so lost and sad.",
                        "voice_style": "[deeply sad, distressed]",
                        "context": "Emotional low point"
                    },
                    {
                        "stage": "starting_reflection",
                        "expected_emotion": "contemplation",
                        "text": "Maybe I need to try to understand their perspective too.",
                        "voice_style": "[thoughtful, reflective]",
                        "context": "Beginning to consider other viewpoints"
                    },
                    {
                        "stage": "seeking_understanding",
                        "expected_emotion": "curiosity",
                        "text": "I wonder what was going through their mind when this happened.",
                        "voice_style": "[genuinely curious]",
                        "context": "Trying to understand their perspective"
                    },
                    {
                        "stage": "finding_empathy",
                        "expected_emotion": "empathy",
                        "text": "I can see how they might have felt pressured in that situation.",
                        "voice_style": "[with understanding, empathetic]",
                        "context": "Developing empathy"
                    },
                    {
                        "stage": "choosing_forgiveness",
                        "expected_emotion": "relief",
                        "text": "I think I'm ready to forgive them and move forward.",
                        "voice_style": "[with relief, releasing tension]",
                        "context": "Decision to forgive"
                    },
                    {
                        "stage": "expressing_gratitude",
                        "expected_emotion": "gratitude",
                        "text": "I'm grateful we can work through difficult things like this together.",
                        "voice_style": "[warm, grateful]",
                        "context": "Appreciating the relationship"
                    },
                    {
                        "stage": "renewed_love",
                        "expected_emotion": "love",
                        "text": "This experience has actually made me love them even more.",
                        "voice_style": "[tender, loving]",
                        "context": "Deeper connection through conflict"
                    },
                    {
                        "stage": "peaceful_resolution",
                        "expected_emotion": "contentment",
                        "text": "I feel so much peace now that we've worked through this.",
                        "voice_style": "[calm, peaceful, content]",
                        "context": "Final resolution and peace"
                    }
                ]
            }
        ]
    }
    
    return extended_scenarios

def save_extended_scenarios():
    """Save extended scenarios to JSON file."""
    scenarios = create_extended_scenarios()
    
    # Save to conversation_audio_library directory
    output_dir = Path("conversation_audio_library")
    output_dir.mkdir(exist_ok=True)
    
    output_file = output_dir / "extended_conversation_metadata.json"
    
    with open(output_file, 'w') as f:
        json.dump(scenarios, f, indent=2)
    
    print(f"✅ Extended scenarios saved to: {output_file}")
    print(f"📊 Created {len(scenarios['scenarios'])} extended scenarios")
    print(f"📊 Total interactions: {sum(len(s['interactions']) for s in scenarios['scenarios'])}")
    
    return output_file

if __name__ == "__main__":
    print("🎯 CREATING EXTENDED CONVERSATION SCENARIOS")
    print("=" * 60)
    
    output_file = save_extended_scenarios()
    
    print(f"\n📋 Scenario Summary:")
    scenarios = create_extended_scenarios()
    
    for scenario in scenarios['scenarios']:
        print(f"   🎭 {scenario['scenario']}: {len(scenario['interactions'])} interactions")
        print(f"      Arc: {scenario['emotional_arc']}")
        print()
    
    print(f"✨ Extended scenarios ready for audio generation!")
