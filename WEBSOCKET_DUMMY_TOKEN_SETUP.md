# WebSocket Dummy Token Configuration

This document explains how the WebSocket has been configured to support a dummy token for testing purposes.

## Summary

✅ **WebSocket now supports dummy token authentication for testing**
✅ **API fallback endpoints added for missing routes**
✅ **Server configured to run with daphne for WebSocket support**

## What Was Done

### 1. WebSocket Authentication Middleware Enhanced

**File: `chat/middleware.py`**

- Added support for `dummy_token_for_testing` token
- When this token is used, creates/gets a test user (`<EMAIL>`)
- Only enabled when `ENABLE_DUMMY_TOKEN` setting is `True` (default in DEBUG mode)

### 2. Settings Configuration

**File: `ellahai_backend/settings.py`**

- Added `ENABLE_DUMMY_TOKEN` setting that defaults to `DEBUG` mode
- This ensures dummy token only works in development

### 3. ASGI Configuration Fixed

**File: `ellahai_backend/asgi.py`**

- Temporarily removed `AllowedHostsOriginValidator` to allow local testing
- This was blocking WebSocket connections during development

### 4. Fallback API Endpoints Added

**File: `ellahai_backend/urls.py`**

- Added fallback endpoint for `/api/conversations/`
- Added fallback endpoint for `/users/{user_id}/progress`
- These return test data to prevent 404 errors

### 5. Test Scripts Created

**Files: `test_websocket_dummy_token.py` and `test_api_endpoints.py`**

- WebSocket test script to verify dummy token authentication
- API test script to verify fallback endpoints

## How to Use

### 1. Start the Server with WebSocket Support

```bash
# Use daphne instead of regular Django runserver for WebSocket support
daphne -b 0.0.0.0 -p 8000 ellahai_backend.asgi:application
```

### 2. Connect to WebSocket with Dummy Token

```javascript
// WebSocket URL with dummy token
const ws = new WebSocket('ws://localhost:8000/ws/chat/?token=dummy_token_for_testing');

ws.onopen = function(event) {
    console.log('WebSocket connected!');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};
```

### 3. Test with Python Script

```bash
# Test WebSocket connection
python test_websocket_dummy_token.py

# Test API endpoints
python test_api_endpoints.py
```

## Test Results

### WebSocket Connection Test
```
✅ WebSocket connected successfully!
✅ Connection established successfully!
✅ Test message sent and received
```

### API Endpoints Test
```
✅ /api/conversations/ - 200 OK (fallback endpoint)
✅ /users/anonymous_1753289824336/progress - 200 OK (fallback endpoint)
🔒 /api/chat/conversations/ - 401 Unauthorized (expected, needs auth)
🔒 /api/auth/progress/ - 401 Unauthorized (expected, needs auth)
🔒 /api/gamification/progress/ - 401 Unauthorized (expected, needs auth)
```

## Security Notes

- Dummy token only works when `ENABLE_DUMMY_TOKEN=True`
- This setting defaults to `DEBUG` mode value
- In production (`DEBUG=False`), dummy token will be rejected
- Test user created: `<EMAIL>` with username `testuser`

## Server Logs

When dummy token is used, you'll see these logs:
```
INFO Using dummy token for testing - creating/getting test user
INFO Created new test user for dummy token
INFO WebSocket authenticated for user: <EMAIL>
INFO Enhanced chat WebSocket connected for user: <EMAIL>
```

## Troubleshooting

### WebSocket Connection Fails
1. Make sure you're using `daphne` not `python manage.py runserver`
2. Check that `ENABLE_DUMMY_TOKEN=True` in settings
3. Verify the token is exactly `dummy_token_for_testing`

### 404 Errors on API Endpoints
- Fallback endpoints have been added for common missing routes
- Check `ellahai_backend/urls.py` for available endpoints

### Database Errors
- Run `python manage.py migrate` to ensure database is up to date
- Test user will be created automatically on first dummy token use

## Next Steps

1. **For Production**: Remove or disable dummy token support
2. **For Frontend Integration**: Use the dummy token for development/testing
3. **For Real Authentication**: Implement proper JWT token generation and validation
