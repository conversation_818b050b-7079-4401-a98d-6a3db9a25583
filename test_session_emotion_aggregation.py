#!/usr/bin/env python3
"""
Session-Based Emotion Aggregation Test
Tests the new session-level emotion context and aggregation features.
"""
import os
import sys
import django
import asyncio
import time
import uuid
import tempfile
import subprocess

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.fast_response_service import get_fast_response_service
from chat.services.expression_measurement_service import expression_measurement_service


def create_speech_audio(text: str) -> bytes:
    """Create speech audio for testing."""
    try:
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
        
        result = subprocess.run([
            'say', '-o', temp_path, '--data-format=LEI16@16000', text
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error generating speech: {result.stderr}")
            return None
        
        with open(temp_path, 'rb') as f:
            audio_data = f.read()
        
        os.unlink(temp_path)
        return audio_data
        
    except Exception as e:
        print(f"Error creating speech audio: {e}")
        return None


async def setup_test_user() -> User:
    """Setup test user."""
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    return user


async def test_session_emotion_aggregation():
    """Test session-based emotion aggregation with multiple interactions."""
    print("🧠 SESSION-BASED EMOTION AGGREGATION TEST")
    print("=" * 60)
    
    # Setup
    user = await setup_test_user()
    session_id = f"test_session_{int(time.time())}"
    user_id = str(user.id)
    
    # Test sequence: simulate a conversation with changing emotions
    conversation_sequence = [
        ("Initial excitement", "I'm so excited to start this new project!", "excitement"),
        ("Growing frustration", "This is getting really frustrating and difficult.", "frustration"),
        ("More frustration", "I can't figure this out, it's so annoying!", "anger"),
        ("Calming down", "Okay, let me take a deep breath and try again.", "calmness"),
        ("Success", "Great! I finally got it working, I'm so happy!", "joy"),
    ]
    
    print(f"📊 Testing session: {session_id}")
    print(f"👤 User: {user_id}")
    print(f"🎭 Conversation sequence: {len(conversation_sequence)} interactions")
    
    session_results = []
    
    for i, (stage, text, expected_emotion) in enumerate(conversation_sequence):
        print(f"\n🎤 Interaction {i+1}: {stage}")
        print(f"   Text: '{text}'")
        
        # Generate audio
        audio_data = create_speech_audio(text)
        if not audio_data:
            print(f"   ❌ Failed to generate audio")
            continue
        
        # Analyze with session aggregation
        chunk_id = f"{session_id}_interaction_{i+1}"
        
        start_time = time.time()
        result = await expression_measurement_service.analyze_audio_chunk(
            audio_data, chunk_id, session_id, user_id
        )
        analysis_time = (time.time() - start_time) * 1000
        
        if result:
            print(f"   ✅ Individual result: {result.dominant_emotion} ({result.confidence:.2f}) - {analysis_time:.1f}ms")
        else:
            print(f"   ❌ No individual result - {analysis_time:.1f}ms")
        
        # Get session profile
        session_profile = await expression_measurement_service.get_session_emotion_profile(session_id)
        
        if session_profile:
            print(f"   📊 Session profile: {session_profile.dominant_emotion} ({session_profile.overall_confidence:.2f})")
            print(f"      Interactions: {session_profile.total_interactions}")
            print(f"      Trend: {session_profile.recent_trend}")
            print(f"      Top emotions: {list(session_profile.emotion_frequency.keys())[:3]}")
            
            session_results.append({
                'interaction': i + 1,
                'stage': stage,
                'individual_emotion': result.dominant_emotion if result else None,
                'individual_confidence': result.confidence if result else 0,
                'session_emotion': session_profile.dominant_emotion,
                'session_confidence': session_profile.overall_confidence,
                'total_interactions': session_profile.total_interactions,
                'trend': session_profile.recent_trend,
                'emotion_frequency': session_profile.emotion_frequency.copy()
            })
        else:
            print(f"   ⚠️  No session profile available")
        
        # Small delay between interactions
        await asyncio.sleep(0.5)
    
    return session_results, session_id


async def test_session_emotion_context():
    """Test session emotion context integration with fast response service."""
    print("\n🚀 SESSION EMOTION CONTEXT INTEGRATION TEST")
    print("=" * 60)
    
    # Setup
    user = await setup_test_user()
    session_id = f"context_test_{int(time.time())}"
    user_id = str(user.id)
    
    # Create some emotion history first
    emotion_texts = [
        "I'm feeling really excited about this!",
        "This is getting quite frustrating though.",
        "I'm feeling much calmer now."
    ]
    
    print(f"📊 Building emotion context for session: {session_id}")
    
    for i, text in enumerate(emotion_texts):
        audio_data = create_speech_audio(text)
        if audio_data:
            chunk_id = f"{session_id}_context_{i}"
            await expression_measurement_service.analyze_audio_chunk(
                audio_data, chunk_id, session_id, user_id
            )
            print(f"   ✅ Added emotion context: '{text[:30]}...'")
    
    # Test fast response service integration
    fast_service = get_fast_response_service(user=user)
    
    # Set audio data
    test_audio = create_speech_audio("How can you help me with this?")
    if test_audio:
        fast_service.set_audio_data(test_audio)
    
    print(f"\n🤖 Testing fast response with session emotion context...")
    
    start_time = time.time()
    response_chunks = []
    
    try:
        async for chunk in fast_service.process_query_fast(
            user_input="How can you help me with this?",
            user_id=user_id,
            conversation_history=[],
            emotion_context=None,
            streaming=True,
            enable_tts=False
        ):
            if chunk.get('type') == 'response_chunk':
                response_chunks.append(chunk)
                response_time = (time.time() - start_time) * 1000
                print(f"   ✅ Response: {response_time:.1f}ms")
                print(f"      Content: '{chunk.get('content', '')[:80]}...'")
                break
    
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Get final session context
    session_context = await expression_measurement_service.get_session_emotion_context(session_id)
    
    if session_context:
        print(f"\n📊 Final session emotion context:")
        session_profile = session_context.get('session_emotion_profile', {})
        if session_profile:
            print(f"   Dominant emotion: {session_profile.get('dominant_emotion')}")
            print(f"   Confidence: {session_profile.get('confidence', 0):.2f}")
            print(f"   Total interactions: {session_profile.get('total_interactions', 0)}")
            print(f"   Trend: {session_profile.get('recent_trend', 'unknown')}")
            print(f"   Duration: {session_profile.get('session_duration_minutes', 0):.1f} minutes")
        
        recent_emotion = session_context.get('recent_emotion', {})
        if recent_emotion:
            print(f"   Recent emotion: {recent_emotion.get('dominant_emotion')} ({recent_emotion.get('confidence', 0):.2f})")
    
    return session_context


async def test_emotion_trend_analysis():
    """Test emotion trend analysis over a session."""
    print("\n📈 EMOTION TREND ANALYSIS TEST")
    print("=" * 60)
    
    user = await setup_test_user()
    session_id = f"trend_test_{int(time.time())}"
    user_id = str(user.id)
    
    # Simulate emotional journey: happy -> frustrated -> angry -> calm -> happy
    emotional_journey = [
        ("Happy start", "I'm so excited to begin this project!", "joy"),
        ("First challenge", "This is a bit tricky, but I can handle it.", "determination"),
        ("Growing frustration", "This is really getting frustrating now.", "frustration"),
        ("Peak frustration", "I'm so angry, nothing is working!", "anger"),
        ("Calming down", "Okay, let me take a break and breathe.", "calmness"),
        ("Problem solving", "Let me approach this differently.", "focus"),
        ("Success", "Yes! I finally figured it out!", "joy"),
        ("Satisfaction", "I'm so proud of what I accomplished.", "satisfaction"),
    ]
    
    print(f"🎭 Emotional journey: {len(emotional_journey)} stages")
    
    trend_results = []
    
    for i, (stage, text, expected_emotion) in enumerate(emotional_journey):
        print(f"\n📊 Stage {i+1}: {stage}")
        
        audio_data = create_speech_audio(text)
        if not audio_data:
            continue
        
        chunk_id = f"{session_id}_trend_{i}"
        result = await expression_measurement_service.analyze_audio_chunk(
            audio_data, chunk_id, session_id, user_id
        )
        
        session_profile = await expression_measurement_service.get_session_emotion_profile(session_id)
        
        if session_profile:
            trend_results.append({
                'stage': i + 1,
                'stage_name': stage,
                'individual_emotion': result.dominant_emotion if result else None,
                'session_emotion': session_profile.dominant_emotion,
                'trend': session_profile.recent_trend,
                'confidence': session_profile.overall_confidence,
                'interactions': session_profile.total_interactions
            })
            
            print(f"   Individual: {result.dominant_emotion if result else 'None'}")
            print(f"   Session: {session_profile.dominant_emotion} ({session_profile.overall_confidence:.2f})")
            print(f"   Trend: {session_profile.recent_trend}")
    
    # Analyze trend progression
    print(f"\n📈 Trend Analysis Summary:")
    trends = [r['trend'] for r in trend_results]
    trend_changes = []
    
    for i in range(1, len(trends)):
        if trends[i] != trends[i-1]:
            trend_changes.append(f"Stage {i+1}: {trends[i-1]} → {trends[i]}")
    
    print(f"   Trend changes: {len(trend_changes)}")
    for change in trend_changes:
        print(f"      {change}")
    
    return trend_results


async def main():
    """Run comprehensive session emotion aggregation tests."""
    print("🎯 COMPREHENSIVE SESSION EMOTION AGGREGATION TEST")
    print("=" * 70)
    
    # Test 1: Basic session aggregation
    session_results, session_id = await test_session_emotion_aggregation()
    
    # Test 2: Context integration
    context_results = await test_session_emotion_context()
    
    # Test 3: Trend analysis
    trend_results = await test_emotion_trend_analysis()
    
    # Get service statistics
    stats = expression_measurement_service.get_performance_stats()
    
    # Final analysis
    print("\n🏆 SESSION AGGREGATION TEST RESULTS")
    print("=" * 70)
    
    if session_results:
        print(f"📊 Session Aggregation Test:")
        print(f"   Total interactions: {len(session_results)}")
        print(f"   Final session emotion: {session_results[-1]['session_emotion']}")
        print(f"   Final confidence: {session_results[-1]['session_confidence']:.2f}")
        print(f"   Final trend: {session_results[-1]['trend']}")
        
        # Show emotion evolution
        print(f"   Emotion evolution:")
        for result in session_results:
            print(f"      {result['interaction']}: {result['individual_emotion']} → {result['session_emotion']} ({result['trend']})")
    
    if context_results:
        print(f"\n🤖 Context Integration Test:")
        session_profile = context_results.get('session_emotion_profile', {})
        if session_profile:
            print(f"   ✅ Session context successfully integrated")
            print(f"   Dominant emotion: {session_profile.get('dominant_emotion')}")
            print(f"   Total interactions: {session_profile.get('total_interactions')}")
        else:
            print(f"   ❌ Session context integration failed")
    
    if trend_results:
        print(f"\n📈 Trend Analysis Test:")
        print(f"   Stages analyzed: {len(trend_results)}")
        final_trend = trend_results[-1]['trend'] if trend_results else 'unknown'
        print(f"   Final trend: {final_trend}")
        
        # Count trend types
        trend_counts = {}
        for result in trend_results:
            trend = result['trend']
            trend_counts[trend] = trend_counts.get(trend, 0) + 1
        
        print(f"   Trend distribution: {trend_counts}")
    
    print(f"\n📈 Service Statistics:")
    session_stats = stats.get('session_aggregation', {})
    print(f"   Active sessions: {session_stats.get('active_sessions', 0)}")
    print(f"   Total session interactions: {session_stats.get('total_session_interactions', 0)}")
    print(f"   Avg interactions per session: {session_stats.get('avg_interactions_per_session', 0):.1f}")
    print(f"   Overall success rate: {stats.get('success_rate_percent', 0):.1f}%")
    
    # Overall verdict
    overall_success = (
        session_results and len(session_results) > 0 and
        context_results and
        trend_results and len(trend_results) > 0
    )
    
    print(f"\n🏆 OVERALL SESSION AGGREGATION STATUS:")
    if overall_success:
        print("   ✅ EXCELLENT - Session-based emotion aggregation working perfectly!")
        print("   🧠 Advanced emotion intelligence with trend analysis enabled!")
        print("   🚀 Ready for production with comprehensive emotion context!")
    else:
        print("   ⚠️  NEEDS ATTENTION - Some session aggregation features need work")
    
    print(f"\n✨ Session emotion aggregation test completed!")


if __name__ == "__main__":
    asyncio.run(main())
