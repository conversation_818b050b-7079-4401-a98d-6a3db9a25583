# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
DB_NAME=ellahai_db
DB_USER=postgres
DB_PASSWORD=your-db-password
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Groq API Configuration (for real-time LLM)
GROQ_API_KEY=your-groq-api-key-here
GROQ_BASE_URL=https://api.groq.com
GROQ_DEFAULT_MODEL=llama-3.3-70b-versatile
GROQ_MAX_RETRIES=3
GROQ_TIMEOUT=30.0
GROQ_CONNECTION_POOL_SIZE=10

# Hume AI Configuration (for emotion detection and TTS)
HUME_API_KEY=your-hume-api-key-here
HUME_TIMEOUT=30.0
HUME_DEFAULT_VOICE=
HUME_ENABLE_PROSODY=True
HUME_ENABLE_LANGUAGE=True

# Performance Targets (milliseconds)
PERFORMANCE_TARGET_RESPONSE_TIME_MS=450
PERFORMANCE_TARGET_FIRST_TOKEN_MS=200
PERFORMANCE_TARGET_TTS_FIRST_CHUNK_MS=100

# Vector Store Configuration
VECTOR_STORE_PERSIST_DIRECTORY=./vector_store_db
EMBEDDING_MODEL=text-embedding-3-small

# Audio Processing Configuration
AUDIO_UPLOAD_PATH=./media/audio
MAX_AUDIO_FILE_SIZE=10485760

# Backblaze B2 Storage Configuration
USE_B2_STORAGE=False
B2_ACCESS_KEY=your-b2-access-key
B2_SECRET_KEY=your-b2-secret-key
B2_BUCKET_NAME=your-b2-bucket-name
B2_REGION=us-west-002
B2_CUSTOM_DOMAIN=
B2_FILE_OVERWRITE=False
B2_DEFAULT_ACL=private
