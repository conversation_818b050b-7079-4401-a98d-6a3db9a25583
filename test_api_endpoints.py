#!/usr/bin/env python3
"""
Test script to verify API endpoints that were showing 404 errors.
"""
import requests
import json

def test_api_endpoint(url, description):
    """Test a single API endpoint."""
    print(f"\n🧪 Testing {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                data = response.json()
                print(f"Response: {json.dumps(data, indent=2)}")
            except json.JSONDecodeError:
                print(f"Response (text): {response.text[:200]}...")
        elif response.status_code == 404:
            print("❌ 404 Not Found")
        elif response.status_code == 401:
            print("🔒 401 Unauthorized (expected for authenticated endpoints)")
        elif response.status_code == 403:
            print("🔒 403 Forbidden (expected for authenticated endpoints)")
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error. Is the Django server running?")
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Test the API endpoints that were showing 404 errors."""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing API endpoints that were showing 404 errors...")
    print("Make sure Django server is running: python manage.py runserver")
    
    # Test the endpoints that were failing
    endpoints = [
        ("/api/conversations/", "Conversations endpoint (fallback)"),
        ("/api/chat/conversations/", "Chat conversations endpoint (main)"),
        ("/users/anonymous_1753289824336/progress", "User progress endpoint (fallback)"),
        ("/api/auth/progress/", "Auth progress endpoint"),
        ("/api/gamification/progress/", "Gamification progress endpoint"),
        ("/ws/chat/?token=dummy_token_for_testing", "WebSocket endpoint (will fail with HTTP)"),
    ]
    
    for endpoint, description in endpoints:
        test_api_endpoint(f"{base_url}{endpoint}", description)
    
    print("\n" + "="*60)
    print("📝 Summary:")
    print("- Fallback endpoints should return 200 with test data")
    print("- Main endpoints may return 401/403 (authentication required)")
    print("- WebSocket endpoint will fail with HTTP (use WebSocket client)")
    print("- Run test_websocket_dummy_token.py to test WebSocket")

if __name__ == "__main__":
    main()
