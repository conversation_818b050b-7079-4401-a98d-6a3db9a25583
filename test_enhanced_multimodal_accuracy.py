#!/usr/bin/env python3
"""
Enhanced Multi-Modal Accuracy Test
Tests with emotion family tolerance and advanced accuracy metrics.
"""
import os
import sys
import django
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service


def get_emotion_families() -> Dict[str, List[str]]:
    """Enhanced emotion families for tolerance matching."""
    return {
        # Joy & Excitement family
        "joy": ["Joy", "Happiness", "Excitement", "Enthusiasm", "Ecstasy", "Satisfaction", "Contentment", "Amusement"],
        "excitement": ["Excitement", "Joy", "Enthusiasm", "Anticipation", "Thrill", "Exhilaration"],
        "satisfaction": ["Satisfaction", "Contentment", "Joy", "Pride", "Accomplishment", "Fulfillment"],
        
        # Anger family
        "anger": ["Anger", "Rage", "Fury", "Annoyance", "Irritation", "Frustration", "Contempt"],
        "frustration": ["Frustration", "Annoyance", "Anger", "Irritation", "Exasperation"],
        "annoyance": ["Annoyance", "Irritation", "Anger", "Frustration", "Vexation"],
        
        # Sadness family
        "sadness": ["Sadness", "Sorrow", "Grief", "Melancholy", "Disappointment", "Distress", "Empathic Pain"],
        "disappointment": ["Disappointment", "Sadness", "Letdown", "Dissatisfaction", "Disillusionment"],
        "distress": ["Distress", "Anguish", "Sadness", "Anxiety", "Worry", "Suffering"],
        
        # Fear & Anxiety family
        "fear": ["Fear", "Terror", "Fright", "Anxiety", "Worry", "Panic", "Horror"],
        "anxiety": ["Anxiety", "Worry", "Fear", "Nervousness", "Apprehension", "Unease"],
        "panic": ["Panic", "Terror", "Fear", "Anxiety", "Alarm"],
        
        # Cognitive family
        "interest": ["Interest", "Curiosity", "Fascination", "Engagement", "Attention"],
        "curiosity": ["Curiosity", "Interest", "Wonder", "Inquisitiveness", "Fascination"],
        "concentration": ["Concentration", "Focus", "Attention", "Contemplation", "Mindfulness"],
        "contemplation": ["Contemplation", "Reflection", "Thoughtfulness", "Concentration", "Meditation"],
        "realization": ["Realization", "Understanding", "Insight", "Comprehension", "Enlightenment"],
        "confusion": ["Confusion", "Bewilderment", "Perplexity", "Uncertainty", "Puzzlement"],
        
        # Achievement family
        "pride": ["Pride", "Accomplishment", "Achievement", "Satisfaction", "Triumph", "Success"],
        "triumph": ["Triumph", "Victory", "Success", "Pride", "Accomplishment", "Conquest"],
        "determination": ["Determination", "Resolve", "Persistence", "Commitment", "Tenacity"],
        
        # Social emotions
        "gratitude": ["Gratitude", "Thankfulness", "Appreciation", "Recognition", "Acknowledgment"],
        "love": ["Love", "Affection", "Adoration", "Fondness", "Devotion"],
        "admiration": ["Admiration", "Respect", "Appreciation", "Esteem", "Reverence"],
        "embarrassment": ["Embarrassment", "Shame", "Awkwardness", "Humiliation", "Mortification"],
        "shame": ["Shame", "Embarrassment", "Guilt", "Humiliation", "Disgrace"],
        "awkwardness": ["Awkwardness", "Embarrassment", "Discomfort", "Unease"],
        
        # Calm emotions
        "calmness": ["Calmness", "Serenity", "Peace", "Tranquility", "Relaxation", "Composure"],
        "relief": ["Relief", "Ease", "Comfort", "Calmness", "Respite"],
        "contentment": ["Contentment", "Satisfaction", "Peace", "Fulfillment", "Serenity"],
        
        # Surprise family
        "surprise": ["Surprise (positive)", "Surprise (negative)", "Amazement", "Wonder", "Astonishment", "Shock"],
        "shock": ["Surprise (negative)", "Shock", "Startle", "Alarm"],
        "awe": ["Awe", "Wonder", "Amazement", "Reverence", "Aesthetic Appreciation"],
        
        # Other emotions
        "boredom": ["Boredom", "Tedium", "Disinterest", "Apathy", "Ennui"],
        "tiredness": ["Tiredness", "Fatigue", "Exhaustion", "Weariness"],
        "disgust": ["Disgust", "Revulsion", "Aversion", "Repugnance", "Loathing"],
        "envy": ["Envy", "Jealousy", "Resentment", "Covetousness"],
        "nostalgia": ["Nostalgia", "Longing", "Wistfulness", "Reminiscence", "Sentimentality"]
    }


def calculate_enhanced_accuracy(results: List[Dict]) -> Dict:
    """Calculate accuracy with emotion family tolerance."""
    if not results:
        return {"exact": 0, "family": 0, "semantic": 0, "total": 0}
    
    exact_matches = 0
    family_matches = 0
    semantic_matches = 0
    total = len(results)
    
    emotion_families = get_emotion_families()
    
    for result in results:
        detected = result.get('detected_emotion', '').lower()
        expected = result.get('expected_emotion', '').lower()
        
        if not detected or not expected:
            continue
        
        # Exact match
        if detected.lower() == expected.lower():
            exact_matches += 1
            family_matches += 1
            semantic_matches += 1
            continue
        
        # Family match
        family_found = False
        if expected in emotion_families:
            expected_family = [e.lower() for e in emotion_families[expected]]
            if detected.lower() in expected_family:
                family_matches += 1
                semantic_matches += 1
                family_found = True
                continue
        
        # Reverse family match
        if not family_found:
            for family_emotion, family_list in emotion_families.items():
                if detected.lower() in [e.lower() for e in family_list]:
                    if expected in [e.lower() for e in family_list]:
                        family_matches += 1
                        semantic_matches += 1
                        family_found = True
                        break
        
        # Semantic similarity (broader matching)
        if not family_found:
            semantic_pairs = [
                (["excitement", "joy", "enthusiasm"], ["satisfaction", "contentment", "happiness"]),
                (["anger", "frustration"], ["annoyance", "irritation"]),
                (["fear", "anxiety"], ["worry", "nervousness"]),
                (["sadness", "disappointment"], ["distress", "sorrow"]),
                (["interest", "curiosity"], ["fascination", "engagement"]),
                (["pride", "triumph"], ["accomplishment", "achievement"]),
                (["confusion", "uncertainty"], ["bewilderment", "perplexity"]),
                (["surprise", "shock"], ["amazement", "astonishment"]),
                (["calmness", "peace"], ["serenity", "tranquility"]),
                (["embarrassment", "shame"], ["awkwardness", "humiliation"])
            ]
            
            for group1, group2 in semantic_pairs:
                if (expected in group1 and detected.lower() in [e.lower() for e in group2]) or \
                   (expected in group2 and detected.lower() in [e.lower() for e in group1]):
                    semantic_matches += 1
                    break
    
    return {
        "exact": exact_matches,
        "family": family_matches,
        "semantic": semantic_matches,
        "total": total,
        "exact_percent": (exact_matches / total * 100) if total > 0 else 0,
        "family_percent": (family_matches / total * 100) if total > 0 else 0,
        "semantic_percent": (semantic_matches / total * 100) if total > 0 else 0
    }


async def test_conversation_scenarios_enhanced():
    """Test conversation scenarios with enhanced accuracy analysis."""
    print("🎯 ENHANCED MULTI-MODAL ACCURACY TEST")
    print("=" * 70)
    
    # Load conversation metadata
    metadata_file = Path("conversation_audio_library/conversation_metadata.json")
    if not metadata_file.exists():
        print(f"❌ Conversation metadata not found: {metadata_file}")
        return []
    
    with open(metadata_file, 'r') as f:
        metadata = json.load(f)
    
    scenarios = metadata['scenarios']
    
    # Setup user
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    
    user_id = str(user.id)
    results = []
    
    print(f"📊 Testing {len(scenarios)} scenarios with enhanced accuracy analysis")
    
    for scenario in scenarios:
        scenario_name = scenario['scenario']
        interactions = scenario['interactions']
        
        print(f"\n🎭 {scenario_name}: {len(interactions)} interactions")
        
        for i, interaction in enumerate(interactions):
            expected_emotion = interaction['expected_emotion']
            interaction_text = interaction['text']
            
            # Load audio
            audio_filename = f"{scenario_name}_{i+1:02d}_{interaction['stage']}.mp3"
            audio_path = Path("conversation_audio_library") / audio_filename
            
            if not audio_path.exists():
                continue
            
            with open(audio_path, 'rb') as f:
                audio_data = f.read()
            
            # Multi-modal analysis
            session_id = f"enhanced_{scenario_name}_{int(time.time())}"
            chunk_id = f"{session_id}_interaction_{i+1}"
            
            result = await expression_measurement_service.analyze_audio_chunk(
                audio_data, chunk_id, session_id, user_id, text=interaction_text
            )
            
            if result:
                detected_emotion = result.dominant_emotion
                confidence = result.confidence
                
                # Check accuracy levels
                exact_match = detected_emotion.lower() == expected_emotion.lower()
                
                # Family match
                emotion_families = get_emotion_families()
                family_match = False
                if expected_emotion in emotion_families:
                    family_emotions = [e.lower() for e in emotion_families[expected_emotion]]
                    family_match = detected_emotion.lower() in family_emotions
                
                match_type = "EXACT" if exact_match else "FAMILY" if family_match else "MISS"
                match_icon = "✅" if exact_match else "🟡" if family_match else "❌"
                
                print(f"   {match_icon} {expected_emotion} → {detected_emotion} ({confidence:.2f}) [{match_type}]")
                
                results.append({
                    'scenario': scenario_name,
                    'expected_emotion': expected_emotion,
                    'detected_emotion': detected_emotion,
                    'confidence': confidence,
                    'exact_match': exact_match,
                    'family_match': family_match
                })
            
            await asyncio.sleep(0.1)  # Small delay
    
    return results


async def main():
    """Run enhanced accuracy test."""
    print("🎯 ENHANCED MULTI-MODAL EMOTION ACCURACY TEST")
    print("=" * 80)
    
    results = await test_conversation_scenarios_enhanced()
    
    if not results:
        print("❌ No results to analyze")
        return
    
    # Calculate enhanced accuracy
    accuracy = calculate_enhanced_accuracy(results)
    
    print(f"\n🏆 ENHANCED ACCURACY ANALYSIS")
    print("=" * 80)
    
    print(f"📊 Results Summary:")
    print(f"   Total tests: {accuracy['total']}")
    print(f"   Exact matches: {accuracy['exact']}/{accuracy['total']} ({accuracy['exact_percent']:.1f}%)")
    print(f"   Family matches: {accuracy['family']}/{accuracy['total']} ({accuracy['family_percent']:.1f}%)")
    print(f"   Semantic matches: {accuracy['semantic']}/{accuracy['total']} ({accuracy['semantic_percent']:.1f}%)")
    
    # Show improvement
    print(f"\n📈 Accuracy Levels:")
    print(f"   🎯 Exact: {accuracy['exact_percent']:.1f}% (Perfect matches)")
    print(f"   🟡 Family: {accuracy['family_percent']:.1f}% (Emotion family matches)")
    print(f"   🔍 Semantic: {accuracy['semantic_percent']:.1f}% (Semantically similar)")
    
    # Production assessment
    if accuracy['family_percent'] >= 80:
        print(f"\n✅ EXCELLENT - Ready for production!")
    elif accuracy['family_percent'] >= 70:
        print(f"\n✅ VERY GOOD - Strong performance")
    elif accuracy['family_percent'] >= 60:
        print(f"\n⚠️ GOOD - Acceptable for production")
    else:
        print(f"\n❌ NEEDS IMPROVEMENT")
    
    print(f"\n✨ Enhanced accuracy test completed!")


if __name__ == "__main__":
    asyncio.run(main())
