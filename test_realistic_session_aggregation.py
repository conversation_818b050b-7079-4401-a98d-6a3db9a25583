#!/usr/bin/env python3
"""
Realistic Session-Based Emotion Aggregation Test
Tests session-level emotion aggregation using realistic ElevenLabs conversation audio.
"""
import os
import sys
import django
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service

# Conversation library path
CONVERSATION_LIBRARY_DIR = Path("conversation_audio_library")


async def setup_test_user() -> User:
    """Setup test user."""
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    return user


def load_conversation_metadata() -> Dict:
    """Load conversation metadata."""
    metadata_file = CONVERSATION_LIBRARY_DIR / "conversation_metadata.json"
    if not metadata_file.exists():
        raise FileNotFoundError(f"Conversation metadata not found: {metadata_file}")
    
    with open(metadata_file, 'r') as f:
        return json.load(f)


async def test_single_conversation_scenario(scenario: Dict, user_id: str) -> Dict:
    """Test a single conversation scenario with session aggregation."""
    scenario_name = scenario['scenario']
    interactions = scenario['interactions']
    
    print(f"\n🎭 SCENARIO: {scenario_name}")
    print(f"   Description: {scenario['description']}")
    print(f"   Interactions: {len(interactions)}")
    
    session_id = f"realistic_{scenario_name}_{int(time.time())}"
    session_results = []
    
    for i, interaction in enumerate(interactions):
        stage = interaction['stage']
        expected_emotion = interaction['expected_emotion']
        
        # Load audio file
        audio_filename = f"{scenario_name}_{i+1:02d}_{stage}.mp3"
        audio_path = CONVERSATION_LIBRARY_DIR / audio_filename
        
        if not audio_path.exists():
            print(f"   ❌ Audio file not found: {audio_filename}")
            continue
        
        print(f"\n   🎤 Interaction {i+1}: {stage}")
        print(f"      Expected: {expected_emotion}")
        print(f"      Text: '{interaction['text']}'")
        
        # Load audio data
        try:
            with open(audio_path, 'rb') as f:
                audio_data = f.read()
        except Exception as e:
            print(f"      ❌ Failed to load audio: {e}")
            continue
        
        # Multi-modal analysis with session aggregation (audio + text)
        chunk_id = f"{session_id}_interaction_{i+1}"
        start_time = time.time()

        # Use the interaction text for multi-modal analysis
        interaction_text = interaction['text']

        result = await expression_measurement_service.analyze_audio_chunk(
            audio_data, chunk_id, session_id, user_id, text=interaction_text
        )
        
        analysis_time = (time.time() - start_time) * 1000
        
        # Multi-modal result
        if result:
            individual_match = result.dominant_emotion.lower() == expected_emotion.lower()
            match_icon = "✅" if individual_match else "❌"
            print(f"      {match_icon} Multi-modal: {result.dominant_emotion} ({result.confidence:.2f}) - {analysis_time:.1f}ms")

            # Show if we have multi-modal data
            has_multimodal = any(
                emotion.get('prosody_score', 'N/A') != 'N/A' and emotion.get('language_score', 'N/A') != 'N/A'
                for emotion in result.emotions[:3]
            )
            if has_multimodal:
                print(f"         📊 Multi-modal data: Language + Prosody combined")
            else:
                print(f"         📊 Single modality data")
        else:
            print(f"      ❌ No multi-modal result - {analysis_time:.1f}ms")
        
        # Session profile
        session_profile = await expression_measurement_service.get_session_emotion_profile(session_id)
        
        if session_profile:
            session_match = session_profile.dominant_emotion.lower() == expected_emotion.lower()
            session_icon = "✅" if session_match else "📊"
            
            print(f"      {session_icon} Session: {session_profile.dominant_emotion} ({session_profile.overall_confidence:.2f})")
            print(f"         Interactions: {session_profile.total_interactions}")
            print(f"         Trend: {session_profile.recent_trend}")
            
            # Show top 3 emotions in session
            top_emotions = list(session_profile.emotion_frequency.keys())[:3]
            print(f"         Top emotions: {top_emotions}")
            
            session_results.append({
                'interaction': i + 1,
                'stage': stage,
                'expected_emotion': expected_emotion,
                'multimodal_emotion': result.dominant_emotion if result else None,
                'multimodal_confidence': result.confidence if result else 0,
                'multimodal_match': individual_match if result else False,
                'session_emotion': session_profile.dominant_emotion,
                'session_confidence': session_profile.overall_confidence,
                'session_match': session_match,
                'total_interactions': session_profile.total_interactions,
                'trend': session_profile.recent_trend,
                'emotion_frequency': session_profile.emotion_frequency.copy(),
                'analysis_time_ms': analysis_time
            })
        else:
            print(f"      ⚠️  No session profile available")
        
        # Small delay between interactions
        await asyncio.sleep(0.3)
    
    return {
        'scenario_name': scenario_name,
        'description': scenario['description'],
        'session_id': session_id,
        'results': session_results
    }


def analyze_session_accuracy(scenario_results: List[Dict]) -> Dict:
    """Analyze accuracy improvements from session aggregation."""
    total_interactions = 0
    individual_correct = 0
    session_correct = 0
    
    accuracy_by_scenario = {}
    
    for scenario in scenario_results:
        scenario_name = scenario['scenario_name']
        results = scenario['results']
        
        scenario_individual_correct = 0
        scenario_session_correct = 0
        scenario_total = len(results)
        
        for result in results:
            total_interactions += 1
            
            if result.get('multimodal_match', False):
                individual_correct += 1
                scenario_individual_correct += 1
            
            if result.get('session_match', False):
                session_correct += 1
                scenario_session_correct += 1
        
        accuracy_by_scenario[scenario_name] = {
            'multimodal_accuracy': (scenario_individual_correct / scenario_total * 100) if scenario_total > 0 else 0,
            'session_accuracy': (scenario_session_correct / scenario_total * 100) if scenario_total > 0 else 0,
            'total_interactions': scenario_total,
            'improvement': ((scenario_session_correct - scenario_individual_correct) / scenario_total * 100) if scenario_total > 0 else 0
        }
    
    overall_individual_accuracy = (individual_correct / total_interactions * 100) if total_interactions > 0 else 0
    overall_session_accuracy = (session_correct / total_interactions * 100) if total_interactions > 0 else 0
    overall_improvement = overall_session_accuracy - overall_individual_accuracy
    
    return {
        'overall': {
            'multimodal_accuracy': overall_individual_accuracy,
            'session_accuracy': overall_session_accuracy,
            'improvement': overall_improvement,
            'total_interactions': total_interactions
        },
        'by_scenario': accuracy_by_scenario
    }


async def test_all_conversation_scenarios():
    """Test all conversation scenarios with session aggregation."""
    print("🎭 MULTI-MODAL SESSION-BASED EMOTION AGGREGATION TEST")
    print("=" * 70)
    print("Testing with ElevenLabs realistic conversation audio + transcribed text")
    
    # Setup
    user = await setup_test_user()
    user_id = str(user.id)
    
    # Load conversation metadata
    try:
        metadata = load_conversation_metadata()
        scenarios = metadata['scenarios']
    except Exception as e:
        print(f"❌ Failed to load conversation metadata: {e}")
        return
    
    print(f"\n📊 Testing {len(scenarios)} conversation scenarios")
    print(f"👤 User: {user_id}")
    
    # Test each scenario
    scenario_results = []
    
    for scenario in scenarios:
        try:
            result = await test_single_conversation_scenario(scenario, user_id)
            scenario_results.append(result)
        except Exception as e:
            print(f"❌ Error testing scenario {scenario['scenario']}: {e}")
            continue
    
    return scenario_results


async def main():
    """Run comprehensive realistic session aggregation test."""
    print("🎯 COMPREHENSIVE REALISTIC SESSION AGGREGATION TEST")
    print("=" * 80)
    
    # Test all scenarios
    scenario_results = await test_all_conversation_scenarios()
    
    if not scenario_results:
        print("❌ No scenario results to analyze")
        return
    
    # Analyze accuracy improvements
    accuracy_analysis = analyze_session_accuracy(scenario_results)
    
    print(f"\n🏆 SESSION AGGREGATION ACCURACY ANALYSIS")
    print("=" * 80)
    
    overall = accuracy_analysis['overall']
    print(f"📊 Overall Results:")
    print(f"   Total interactions: {overall['total_interactions']}")
    print(f"   Multi-modal accuracy: {overall['multimodal_accuracy']:.1f}%")
    print(f"   Session accuracy: {overall['session_accuracy']:.1f}%")
    print(f"   Improvement: {overall['improvement']:+.1f}%")
    
    print(f"\n📈 Accuracy by Scenario:")
    for scenario_name, stats in accuracy_analysis['by_scenario'].items():
        improvement_icon = "📈" if stats['improvement'] > 0 else "📉" if stats['improvement'] < 0 else "➡️"
        print(f"   {improvement_icon} {scenario_name}:")
        print(f"      Multi-modal: {stats['multimodal_accuracy']:.1f}%")
        print(f"      Session: {stats['session_accuracy']:.1f}%")
        print(f"      Improvement: {stats['improvement']:+.1f}%")
        print(f"      Interactions: {stats['total_interactions']}")
    
    # Performance analysis
    all_results = []
    for scenario in scenario_results:
        all_results.extend(scenario['results'])
    
    if all_results:
        avg_time = sum(r['analysis_time_ms'] for r in all_results) / len(all_results)
        print(f"\n⏱️  Performance:")
        print(f"   Average processing time: {avg_time:.1f}ms")
    
    # Session aggregation insights
    print(f"\n🧠 Session Aggregation Insights:")
    
    # Find scenarios with biggest improvements
    improvements = [(name, stats['improvement']) for name, stats in accuracy_analysis['by_scenario'].items()]
    improvements.sort(key=lambda x: x[1], reverse=True)
    
    if improvements:
        best_scenario, best_improvement = improvements[0]
        print(f"   🏆 Best improvement: {best_scenario} (+{best_improvement:.1f}%)")
        
        if best_improvement > 10:
            print(f"   ✅ Session aggregation provides significant value!")
        elif best_improvement > 0:
            print(f"   ✅ Session aggregation provides moderate improvement")
        else:
            print(f"   ⚠️ Session aggregation needs optimization")
    
    # Final assessment
    print(f"\n🎯 PRODUCTION ASSESSMENT:")
    if overall['session_accuracy'] >= 60:
        print("   ✅ EXCELLENT - Session aggregation ready for production")
    elif overall['session_accuracy'] >= 45:
        print("   ✅ GOOD - Session aggregation provides value")
    elif overall['improvement'] > 5:
        print("   ⚠️ FAIR - Session aggregation shows promise")
    else:
        print("   ❌ NEEDS WORK - Session aggregation requires optimization")
    
    print(f"\n✨ Realistic session aggregation test completed!")
    print(f"Session-based emotion intelligence tested with {overall['total_interactions']} realistic interactions.")


if __name__ == "__main__":
    asyncio.run(main())
