# Flutter Integration Guide - Voice AI Assistant

## Overview

This guide shows how to integrate your Flutter app with the EllaHAI backend's voice-to-voice AI pipeline featuring <PERSON><PERSON>q Whisper and Hume AI.

## Architecture

```
Flutter App ↔ WebSocket ↔ Django Backend
    ↓              ↓           ↓
Audio Recording → Groq Whisper → Hume Emotion → Agent LLM → Hume TTS → Audio Playback
```

## Dependencies

Add these to your `pubspec.yaml`:

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # WebSocket & HTTP
  web_socket_channel: ^2.4.0
  http: ^1.1.0
  
  # Audio Recording & Playback
  record: ^5.0.4
  audioplayers: ^5.2.1
  permission_handler: ^11.0.1
  
  # State Management
  provider: ^6.1.1
  # OR bloc: ^8.1.2
  
  # Utilities
  path_provider: ^2.1.1
  convert: ^3.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
```

## Permissions

### Android (`android/app/src/main/AndroidManifest.xml`)
```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### iOS (`ios/Runner/Info.plist`)
```xml
<key>NSMicrophoneUsageDescription</key>
<string>This app needs microphone access for voice chat</string>
```

## Core Implementation

### 1. WebSocket Service

```dart
// lib/services/websocket_service.dart
import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:web_socket_channel/web_socket_channel.dart';

class WebSocketService {
  WebSocketChannel? _channel;
  String? _sessionId;
  String? _connectionId;
  
  // Event streams
  final _transcriptionController = StreamController<TranscriptionResult>.broadcast();
  final _emotionController = StreamController<EmotionResult>.broadcast();
  final _aiResponseController = StreamController<String>.broadcast();
  final _audioChunkController = StreamController<Uint8List>.broadcast();
  final _connectionController = StreamController<ConnectionState>.broadcast();
  
  // Getters for streams
  Stream<TranscriptionResult> get transcriptionStream => _transcriptionController.stream;
  Stream<EmotionResult> get emotionStream => _emotionController.stream;
  Stream<String> get aiResponseStream => _aiResponseController.stream;
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;
  Stream<ConnectionState> get connectionStream => _connectionController.stream;
  
  Future<void> connect(String token) async {
    try {
      final uri = Uri.parse('ws://your-backend-url/ws/chat/?token=$token');
      _channel = WebSocketChannel.connect(uri);
      
      _connectionController.add(ConnectionState.connecting);
      
      _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnect,
      );
      
    } catch (e) {
      _connectionController.add(ConnectionState.error);
      print('WebSocket connection error: $e');
    }
  }
  
  void _handleMessage(dynamic message) {
    final data = jsonDecode(message);
    final type = data['type'];
    
    switch (type) {
      case 'connection_established':
        _sessionId = data['session_id'];
        _connectionId = data['connection_id'];
        _connectionController.add(ConnectionState.connected);
        break;
        
      case 'transcription_partial':
        _transcriptionController.add(TranscriptionResult.fromJson(data));
        break;
        
      case 'emotion_detected':
        _emotionController.add(EmotionResult.fromJson(data));
        break;
        
      case 'llm_response_chunk':
        _aiResponseController.add(data['content'] ?? '');
        break;
        
      case 'audio_chunk':
        final audioData = base64Decode(data['data']);
        _audioChunkController.add(audioData);
        break;
        
      case 'ai_typing':
        // Handle typing indicators
        break;
    }
  }
  
  void sendAudioChunk(Uint8List audioData, {bool isFinal = false}) {
    if (_channel == null) return;
    
    final message = {
      'type': 'audio_chunk',
      'data': base64Encode(audioData),
      'chunk_id': _generateChunkId(),
      'is_final': isFinal,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    
    _channel!.sink.add(jsonEncode(message));
  }
  
  void sendTextMessage(String text) {
    if (_channel == null) return;
    
    final message = {
      'type': 'text_message',
      'content': text,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    
    _channel!.sink.add(jsonEncode(message));
  }
  
  String _generateChunkId() {
    return 'chunk_${DateTime.now().millisecondsSinceEpoch}';
  }
  
  void _handleError(error) {
    _connectionController.add(ConnectionState.error);
    print('WebSocket error: $error');
  }
  
  void _handleDisconnect() {
    _connectionController.add(ConnectionState.disconnected);
  }
  
  void disconnect() {
    _channel?.sink.close();
    _channel = null;
  }
  
  void dispose() {
    disconnect();
    _transcriptionController.close();
    _emotionController.close();
    _aiResponseController.close();
    _audioChunkController.close();
    _connectionController.close();
  }
}
```

### 2. Audio Recording Service

```dart
// lib/services/audio_service.dart
import 'dart:async';
import 'dart:typed_data';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';

class AudioService {
  final AudioRecorder _recorder = AudioRecorder();
  StreamSubscription<Uint8List>? _audioStreamSubscription;
  
  final _audioChunkController = StreamController<Uint8List>.broadcast();
  Stream<Uint8List> get audioChunkStream => _audioChunkController.stream;
  
  bool _isRecording = false;
  bool get isRecording => _isRecording;
  
  Future<bool> requestPermissions() async {
    final status = await Permission.microphone.request();
    return status == PermissionStatus.granted;
  }
  
  Future<void> startRecording() async {
    if (_isRecording) return;
    
    final hasPermission = await requestPermissions();
    if (!hasPermission) {
      throw Exception('Microphone permission denied');
    }
    
    try {
      // Configure for real-time streaming
      const config = RecordConfig(
        encoder: AudioEncoder.pcm16bits,
        sampleRate: 48000,
        numChannels: 1,
        bitRate: 768000,
      );
      
      // Start streaming recording
      final stream = await _recorder.startStream(config);
      _isRecording = true;
      
      _audioStreamSubscription = stream.listen(
        (audioChunk) {
          _audioChunkController.add(audioChunk);
        },
        onError: (error) {
          print('Audio recording error: $error');
          stopRecording();
        },
      );
      
    } catch (e) {
      print('Failed to start recording: $e');
      throw e;
    }
  }
  
  Future<void> stopRecording() async {
    if (!_isRecording) return;
    
    await _audioStreamSubscription?.cancel();
    await _recorder.stop();
    _isRecording = false;
  }
  
  void dispose() {
    stopRecording();
    _audioChunkController.close();
    _recorder.dispose();
  }
}
```

### 3. Audio Playback Service

```dart
// lib/services/audio_playback_service.dart
import 'dart:async';
import 'dart:typed_data';
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class AudioPlaybackService {
  final AudioPlayer _player = AudioPlayer();
  final List<Uint8List> _audioQueue = [];
  bool _isPlaying = false;
  
  Future<void> playAudioChunk(Uint8List audioData) async {
    _audioQueue.add(audioData);
    
    if (!_isPlaying) {
      _processAudioQueue();
    }
  }
  
  Future<void> _processAudioQueue() async {
    if (_audioQueue.isEmpty) {
      _isPlaying = false;
      return;
    }
    
    _isPlaying = true;
    
    while (_audioQueue.isNotEmpty) {
      final audioData = _audioQueue.removeAt(0);
      await _playChunk(audioData);
    }
    
    _isPlaying = false;
  }
  
  Future<void> _playChunk(Uint8List audioData) async {
    try {
      // Save audio data to temporary file
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/audio_chunk_${DateTime.now().millisecondsSinceEpoch}.wav');
      await tempFile.writeAsBytes(audioData);
      
      // Play the audio file
      await _player.play(DeviceFileSource(tempFile.path));
      
      // Wait for playback to complete
      await _player.onPlayerComplete.first;
      
      // Clean up temp file
      await tempFile.delete();
      
    } catch (e) {
      print('Audio playback error: $e');
    }
  }
  
  Future<void> stop() async {
    await _player.stop();
    _audioQueue.clear();
    _isPlaying = false;
  }
  
  void dispose() {
    stop();
    _player.dispose();
  }
}

### 4. Data Models

```dart
// lib/models/transcription_result.dart
class TranscriptionResult {
  final String text;
  final double confidence;
  final String chunkId;
  final bool isPartial;
  final double processingTimeMs;
  final int timestamp;
  final String requestId;

  TranscriptionResult({
    required this.text,
    required this.confidence,
    required this.chunkId,
    required this.isPartial,
    required this.processingTimeMs,
    required this.timestamp,
    required this.requestId,
  });

  factory TranscriptionResult.fromJson(Map<String, dynamic> json) {
    return TranscriptionResult(
      text: json['text'] ?? '',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      chunkId: json['chunk_id'] ?? '',
      isPartial: json['is_partial'] ?? false,
      processingTimeMs: (json['processing_time_ms'] ?? 0.0).toDouble(),
      timestamp: json['timestamp'] ?? 0,
      requestId: json['request_id'] ?? '',
    );
  }
}

// lib/models/emotion_result.dart
class EmotionResult {
  final List<Emotion> emotions;
  final double confidenceScore;
  final String chunkId;
  final int timestamp;
  final String requestId;

  EmotionResult({
    required this.emotions,
    required this.confidenceScore,
    required this.chunkId,
    required this.timestamp,
    required this.requestId,
  });

  factory EmotionResult.fromJson(Map<String, dynamic> json) {
    final emotionsJson = json['emotions'] as List? ?? [];
    final emotions = emotionsJson
        .map((e) => Emotion.fromJson(e))
        .toList();

    return EmotionResult(
      emotions: emotions,
      confidenceScore: (json['confidence_score'] ?? 0.0).toDouble(),
      chunkId: json['chunk_id'] ?? '',
      timestamp: json['timestamp'] ?? 0,
      requestId: json['request_id'] ?? '',
    );
  }

  Emotion? get primaryEmotion {
    if (emotions.isEmpty) return null;
    return emotions.reduce((a, b) => a.score > b.score ? a : b);
  }
}

class Emotion {
  final String name;
  final double score;

  Emotion({required this.name, required this.score});

  factory Emotion.fromJson(Map<String, dynamic> json) {
    return Emotion(
      name: json['name'] ?? '',
      score: (json['score'] ?? 0.0).toDouble(),
    );
  }
}

enum ConnectionState {
  disconnected,
  connecting,
  connected,
  error,
}
```

### 5. Voice Chat Provider (State Management)

```dart
// lib/providers/voice_chat_provider.dart
import 'package:flutter/foundation.dart';
import '../services/websocket_service.dart';
import '../services/audio_service.dart';
import '../services/audio_playback_service.dart';
import '../models/transcription_result.dart';
import '../models/emotion_result.dart';

class VoiceChatProvider extends ChangeNotifier {
  final WebSocketService _webSocketService = WebSocketService();
  final AudioService _audioService = AudioService();
  final AudioPlaybackService _playbackService = AudioPlaybackService();

  // State
  ConnectionState _connectionState = ConnectionState.disconnected;
  bool _isRecording = false;
  String _currentTranscription = '';
  String _aiResponse = '';
  EmotionResult? _currentEmotion;
  List<String> _conversationHistory = [];

  // Getters
  ConnectionState get connectionState => _connectionState;
  bool get isRecording => _isRecording;
  String get currentTranscription => _currentTranscription;
  String get aiResponse => _aiResponse;
  EmotionResult? get currentEmotion => _currentEmotion;
  List<String> get conversationHistory => _conversationHistory;

  VoiceChatProvider() {
    _initializeListeners();
  }

  void _initializeListeners() {
    // WebSocket connection state
    _webSocketService.connectionStream.listen((state) {
      _connectionState = state;
      notifyListeners();
    });

    // Transcription results
    _webSocketService.transcriptionStream.listen((result) {
      _currentTranscription = result.text;
      notifyListeners();

      // Add to conversation history when final
      if (!result.isPartial) {
        _conversationHistory.add('You: ${result.text}');
        notifyListeners();
      }
    });

    // Emotion detection
    _webSocketService.emotionStream.listen((emotion) {
      _currentEmotion = emotion;
      notifyListeners();
    });

    // AI response chunks
    _webSocketService.aiResponseStream.listen((chunk) {
      _aiResponse += chunk;
      notifyListeners();
    });

    // Audio playback
    _webSocketService.audioChunkStream.listen((audioData) {
      _playbackService.playAudioChunk(audioData);
    });

    // Audio recording stream
    _audioService.audioChunkStream.listen((audioChunk) {
      _webSocketService.sendAudioChunk(audioChunk);
    });
  }

  Future<void> connect(String token) async {
    await _webSocketService.connect(token);
  }

  Future<void> startVoiceRecording() async {
    try {
      await _audioService.startRecording();
      _isRecording = true;
      _currentTranscription = '';
      _aiResponse = '';
      notifyListeners();
    } catch (e) {
      print('Failed to start recording: $e');
    }
  }

  Future<void> stopVoiceRecording() async {
    await _audioService.stopRecording();
    _isRecording = false;
    notifyListeners();

    // Send final audio chunk
    _webSocketService.sendAudioChunk(Uint8List(0), isFinal: true);
  }

  void sendTextMessage(String message) {
    _webSocketService.sendTextMessage(message);
    _conversationHistory.add('You: $message');
    _aiResponse = '';
    notifyListeners();
  }

  void clearConversation() {
    _conversationHistory.clear();
    _currentTranscription = '';
    _aiResponse = '';
    _currentEmotion = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _webSocketService.dispose();
    _audioService.dispose();
    _playbackService.dispose();
    super.dispose();
  }
}

### 6. Voice Chat UI Widget

```dart
// lib/widgets/voice_chat_widget.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/voice_chat_provider.dart';
import '../models/emotion_result.dart';

class VoiceChatWidget extends StatefulWidget {
  @override
  _VoiceChatWidgetState createState() => _VoiceChatWidgetState();
}

class _VoiceChatWidgetState extends State<VoiceChatWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _emotionController;
  final TextEditingController _textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: Duration(seconds: 1),
      vsync: this,
    )..repeat(reverse: true);

    _emotionController = AnimationController(
      duration: Duration(milliseconds: 500),
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VoiceChatProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text('Voice AI Assistant'),
            actions: [
              _buildConnectionIndicator(provider.connectionState),
            ],
          ),
          body: Column(
            children: [
              // Emotion Display
              _buildEmotionDisplay(provider.currentEmotion),

              // Conversation History
              Expanded(
                child: _buildConversationList(provider.conversationHistory),
              ),

              // Current Transcription
              _buildTranscriptionDisplay(provider.currentTranscription),

              // AI Response
              _buildAIResponseDisplay(provider.aiResponse),

              // Controls
              _buildControlsPanel(provider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConnectionIndicator(ConnectionState state) {
    Color color;
    IconData icon;

    switch (state) {
      case ConnectionState.connected:
        color = Colors.green;
        icon = Icons.wifi;
        break;
      case ConnectionState.connecting:
        color = Colors.orange;
        icon = Icons.wifi_tethering;
        break;
      case ConnectionState.error:
        color = Colors.red;
        icon = Icons.wifi_off;
        break;
      default:
        color = Colors.grey;
        icon = Icons.wifi_off;
    }

    return Padding(
      padding: EdgeInsets.all(8.0),
      child: Icon(icon, color: color),
    );
  }

  Widget _buildEmotionDisplay(EmotionResult? emotion) {
    if (emotion == null) return SizedBox.shrink();

    final primaryEmotion = emotion.primaryEmotion;
    if (primaryEmotion == null) return SizedBox.shrink();

    return AnimatedBuilder(
      animation: _emotionController,
      builder: (context, child) {
        return Container(
          padding: EdgeInsets.all(16),
          margin: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getEmotionColor(primaryEmotion.name).withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getEmotionColor(primaryEmotion.name),
              width: 2,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getEmotionIcon(primaryEmotion.name),
                color: _getEmotionColor(primaryEmotion.name),
                size: 24,
              ),
              SizedBox(width: 8),
              Text(
                '${primaryEmotion.name} (${(primaryEmotion.score * 100).toInt()}%)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: _getEmotionColor(primaryEmotion.name),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConversationList(List<String> history) {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: history.length,
      itemBuilder: (context, index) {
        final message = history[index];
        final isUser = message.startsWith('You:');

        return Align(
          alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: EdgeInsets.symmetric(vertical: 4),
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isUser ? Colors.blue[100] : Colors.grey[200],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              message.substring(message.indexOf(':') + 2),
              style: TextStyle(fontSize: 16),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTranscriptionDisplay(String transcription) {
    if (transcription.isEmpty) return SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      margin: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Text(
        'Listening: $transcription',
        style: TextStyle(
          fontStyle: FontStyle.italic,
          color: Colors.blue[800],
        ),
      ),
    );
  }

  Widget _buildAIResponseDisplay(String response) {
    if (response.isEmpty) return SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      margin: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Text(
        'AI: $response',
        style: TextStyle(color: Colors.green[800]),
      ),
    );
  }

  Widget _buildControlsPanel(VoiceChatProvider provider) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          // Voice Recording Button
          GestureDetector(
            onTapDown: (_) => provider.startVoiceRecording(),
            onTapUp: (_) => provider.stopVoiceRecording(),
            onTapCancel: () => provider.stopVoiceRecording(),
            child: AnimatedBuilder(
              animation: _pulseController,
              builder: (context, child) {
                return Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: provider.isRecording
                        ? Colors.red.withOpacity(0.7 + 0.3 * _pulseController.value)
                        : Colors.blue,
                    boxShadow: provider.isRecording
                        ? [
                            BoxShadow(
                              color: Colors.red.withOpacity(0.3),
                              blurRadius: 20 * _pulseController.value,
                              spreadRadius: 10 * _pulseController.value,
                            ),
                          ]
                        : null,
                  ),
                  child: Icon(
                    provider.isRecording ? Icons.mic : Icons.mic_none,
                    color: Colors.white,
                    size: 40,
                  ),
                );
              },
            ),
          ),

          SizedBox(height: 16),

          // Text Input
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _textController,
                  decoration: InputDecoration(
                    hintText: 'Type a message...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  onSubmitted: (text) {
                    if (text.isNotEmpty) {
                      provider.sendTextMessage(text);
                      _textController.clear();
                    }
                  },
                ),
              ),
              SizedBox(width: 8),
              IconButton(
                onPressed: () {
                  final text = _textController.text;
                  if (text.isNotEmpty) {
                    provider.sendTextMessage(text);
                    _textController.clear();
                  }
                },
                icon: Icon(Icons.send),
                color: Colors.blue,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getEmotionColor(String emotion) {
    switch (emotion.toLowerCase()) {
      case 'joy':
      case 'happiness':
        return Colors.yellow[700]!;
      case 'sadness':
        return Colors.blue[700]!;
      case 'anger':
        return Colors.red[700]!;
      case 'fear':
        return Colors.purple[700]!;
      case 'surprise':
        return Colors.orange[700]!;
      case 'calm':
      case 'neutral':
        return Colors.green[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  IconData _getEmotionIcon(String emotion) {
    switch (emotion.toLowerCase()) {
      case 'joy':
      case 'happiness':
        return Icons.sentiment_very_satisfied;
      case 'sadness':
        return Icons.sentiment_very_dissatisfied;
      case 'anger':
        return Icons.sentiment_very_dissatisfied;
      case 'fear':
        return Icons.sentiment_neutral;
      case 'surprise':
        return Icons.sentiment_satisfied;
      case 'calm':
      case 'neutral':
        return Icons.sentiment_neutral;
      default:
        return Icons.sentiment_neutral;
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _emotionController.dispose();
    _textController.dispose();
    super.dispose();
  }
}

### 7. Main App Setup

```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/voice_chat_provider.dart';
import 'widgets/voice_chat_widget.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Voice AI Assistant',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: ChangeNotifierProvider(
        create: (context) => VoiceChatProvider(),
        child: VoiceChatScreen(),
      ),
    );
  }
}

class VoiceChatScreen extends StatefulWidget {
  @override
  _VoiceChatScreenState createState() => _VoiceChatScreenState();
}

class _VoiceChatScreenState extends State<VoiceChatScreen> {
  @override
  void initState() {
    super.initState();
    // Connect to WebSocket when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _connectToBackend();
    });
  }

  Future<void> _connectToBackend() async {
    final provider = Provider.of<VoiceChatProvider>(context, listen: false);

    // Get your JWT token (from login, storage, etc.)
    final token = await _getAuthToken();

    if (token != null) {
      await provider.connect(token);
    } else {
      // Handle authentication error
      _showAuthError();
    }
  }

  Future<String?> _getAuthToken() async {
    // Implement your token retrieval logic
    // This could be from SharedPreferences, secure storage, etc.
    return 'your-jwt-token-here';
  }

  void _showAuthError() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Authentication failed. Please login.'),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return VoiceChatWidget();
  }
}
```

## Usage Examples

### Basic Voice Interaction

```dart
// Start voice recording (hold to talk)
await provider.startVoiceRecording();

// Stop recording (release to send)
await provider.stopVoiceRecording();

// Send text message
provider.sendTextMessage("Hello, how are you?");
```

### Listening to Real-time Updates

```dart
// Listen to transcription updates
provider.addListener(() {
  if (provider.currentTranscription.isNotEmpty) {
    print('User said: ${provider.currentTranscription}');
  }
});

// Listen to emotion detection
provider.addListener(() {
  final emotion = provider.currentEmotion?.primaryEmotion;
  if (emotion != null) {
    print('Detected emotion: ${emotion.name} (${emotion.score})');
  }
});

// Listen to AI responses
provider.addListener(() {
  if (provider.aiResponse.isNotEmpty) {
    print('AI response: ${provider.aiResponse}');
  }
});
```

### Custom Audio Processing

```dart
// Custom audio chunk processing
audioService.audioChunkStream.listen((audioChunk) {
  // Apply custom audio processing
  final processedAudio = applyNoiseReduction(audioChunk);

  // Send to backend
  webSocketService.sendAudioChunk(processedAudio);
});
```

## Configuration

### Backend URL Configuration

```dart
// lib/config/app_config.dart
class AppConfig {
  static const String baseUrl = 'https://your-backend-domain.com';
  static const String wsUrl = 'wss://your-backend-domain.com';

  // For development
  static const String devBaseUrl = 'http://localhost:8000';
  static const String devWsUrl = 'ws://localhost:8000';

  static String get webSocketUrl {
    return kDebugMode ? '$devWsUrl/ws/chat/' : '$wsUrl/ws/chat/';
  }
}
```

### Audio Quality Settings

```dart
// lib/config/audio_config.dart
class AudioConfig {
  static const int sampleRate = 16000;  // 16kHz for optimal speech recognition
  static const int channels = 1;
  static const int bitRate = 256000;    // Reduced bitrate for 16kHz
  static const AudioEncoder encoder = AudioEncoder.pcm16bits;

  // Chunk size for streaming (in milliseconds)
  static const int chunkDurationMs = 100;

  // Audio quality thresholds
  static const double minQualityScore = 0.3;
  static const double goodQualityScore = 0.7;
}
```

## Error Handling

### Connection Management

```dart
// Handle connection errors
provider.connectionStream.listen((state) {
  switch (state) {
    case ConnectionState.error:
      _showErrorDialog('Connection failed. Please check your internet.');
      break;
    case ConnectionState.disconnected:
      _attemptReconnection();
      break;
  }
});

Future<void> _attemptReconnection() async {
  await Future.delayed(Duration(seconds: 2));
  final token = await _getAuthToken();
  if (token != null) {
    await provider.connect(token);
  }
}
```

### Audio Permission Handling

```dart
Future<void> _handleAudioPermissions() async {
  final hasPermission = await audioService.requestPermissions();

  if (!hasPermission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Microphone Permission Required'),
        content: Text('Please grant microphone permission to use voice chat.'),
        actions: [
          TextButton(
            onPressed: () => openAppSettings(),
            child: Text('Open Settings'),
          ),
        ],
      ),
    );
  }
}
```

## Performance Optimization

### Memory Management

```dart
// Dispose resources properly
@override
void dispose() {
  provider.dispose();
  super.dispose();
}

// Clear audio buffers periodically
Timer.periodic(Duration(minutes: 5), (timer) {
  audioService.clearBuffers();
});
```

### Battery Optimization

```dart
// Pause audio processing when app is in background
@override
void didChangeAppLifecycleState(AppLifecycleState state) {
  switch (state) {
    case AppLifecycleState.paused:
      provider.pauseAudioProcessing();
      break;
    case AppLifecycleState.resumed:
      provider.resumeAudioProcessing();
      break;
  }
}
```

## Testing

### Unit Tests

```dart
// test/providers/voice_chat_provider_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import '../lib/providers/voice_chat_provider.dart';

void main() {
  group('VoiceChatProvider', () {
    test('should connect to WebSocket', () async {
      final provider = VoiceChatProvider();
      await provider.connect('test-token');

      expect(provider.connectionState, ConnectionState.connected);
    });

    test('should handle transcription updates', () {
      final provider = VoiceChatProvider();
      // Test transcription handling
    });
  });
}
```

This comprehensive Flutter integration guide provides everything you need to build a voice-to-voice AI assistant that works seamlessly with your EllaHAI backend!
```
```
