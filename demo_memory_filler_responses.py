#!/usr/bin/env python3
"""
Demonstration of memory-based filler responses in action.
Shows how the system uses personal memories to create engaging conversation
while processing complex requests in the background.
"""
import asyncio
import os
import sys
from unittest.mock import Mock, patch, AsyncMock

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')

import django
django.setup()

from chat.services.fast_response_service import FastResponseService


class MockUser:
    """Mock user for testing."""
    def __init__(self, personality='caringFriend', companion_name='<PERSON>', first_name='<PERSON>'):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = first_name
        self.id = f"demo_{personality}"


async def demonstrate_memory_filler_responses():
    """Demonstrate memory-based filler responses with realistic scenarios."""
    print("🎭 Memory-Based Filler Response Demonstration")
    print("=" * 60)
    print("This demo shows how the AI uses personal memories to create")
    print("engaging conversation while processing complex requests.")
    print()
    
    # Create user and service
    user = MockUser('caringFriend', 'Ella', 'Sarah')
    service = FastResponseService(user=user)
    
    if not service.memory_manager:
        print("❌ Memory manager not available for demo")
        return
    
    # Set up realistic user memories
    print("📝 Setting up Sarah's personal memories...")
    
    memories = [
        {
            "text": "Sarah works as a product manager at a fintech startup called PayFlow",
            "type": "semantic_profile",
            "importance": 0.9,
            "personalness": 0.8,
            "actionability": 0.3
        },
        {
            "text": "Sarah mentioned being stressed about the upcoming product launch next month",
            "type": "episodic_summary",
            "importance": 0.8,
            "personalness": 0.7,
            "actionability": 0.8
        },
        {
            "text": "Sarah loves rock climbing and goes to the climbing gym twice a week",
            "type": "semantic_profile",
            "importance": 0.6,
            "personalness": 0.9,
            "actionability": 0.2
        },
        {
            "text": "Had a conversation about planning a weekend trip to Yosemite for climbing",
            "type": "episodic_summary",
            "importance": 0.7,
            "personalness": 0.8,
            "actionability": 0.6
        },
        {
            "text": "Sarah is learning Spanish and practices with a language exchange partner",
            "type": "semantic_profile",
            "importance": 0.6,
            "personalness": 0.7,
            "actionability": 0.4
        },
        {
            "text": "Sarah's cat Whiskers had surgery last week and is recovering well",
            "type": "episodic_summary",
            "importance": 0.7,
            "personalness": 0.9,
            "actionability": 0.5
        }
    ]
    
    # Store memories
    for memory in memories:
        service.memory_manager.store_memory(
            text=memory["text"],
            memory_type=memory["type"],
            user_id="demo_sarah",
            importance_score=memory["importance"],
            personalness_score=memory["personalness"],
            actionability_score=memory["actionability"]
        )
    
    print(f"   ✅ Stored {len(memories)} personal memories for Sarah")
    print()
    
    # Demonstrate different scenarios
    scenarios = [
        {
            "query": "Can you help me analyze this market research report for our product launch?",
            "description": "Work-related analysis request",
            "expected_memory_refs": ["product launch", "fintech", "PayFlow"]
        },
        {
            "query": "I need help planning a comprehensive training program for my team",
            "description": "Complex planning request",
            "expected_memory_refs": ["product manager", "work", "team"]
        },
        {
            "query": "Can you research the best climbing gear for outdoor adventures?",
            "description": "Hobby-related research",
            "expected_memory_refs": ["rock climbing", "Yosemite", "climbing"]
        },
        {
            "query": "Help me create a detailed budget for my upcoming vacation",
            "description": "Personal planning request",
            "expected_memory_refs": ["weekend trip", "Yosemite", "vacation"]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"🎬 Scenario {i}: {scenario['description']}")
        print(f"   💬 User Query: \"{scenario['query']}\"")
        print()
        
        # Check if this needs agent processing
        needs_agent = service._needs_agent_processing(scenario['query'])
        print(f"   🤖 Needs Agent Processing: {needs_agent}")
        
        if needs_agent:
            # Get contextual memories
            memories = service._get_contextual_memories("demo_sarah", limit=3)
            print(f"   🧠 Retrieved {len(memories)} contextual memories:")
            
            for j, memory in enumerate(memories, 1):
                mem_type = memory.get('memory_type', 'unknown')
                text = memory.get('text', '')
                print(f"      {j}. [{mem_type}] {text[:60]}...")
            
            # Generate system prompt with memory context
            prompt = service._build_fast_system_prompt(
                emotion_context=None,
                needs_agent=True,
                user_id="demo_sarah"
            )
            
            # Extract memory context from prompt
            if 'CONTEXTUAL MEMORIES' in prompt:
                lines = prompt.split('\n')
                memory_section = []
                in_memory_section = False
                
                for line in lines:
                    if 'CONTEXTUAL MEMORIES' in line:
                        in_memory_section = True
                        continue
                    elif in_memory_section and line.strip().startswith('•'):
                        memory_section.append(line.strip())
                    elif in_memory_section and not line.strip().startswith('•') and line.strip():
                        break
                
                print(f"   💡 Memory Context in Prompt:")
                for mem_line in memory_section:
                    print(f"      {mem_line}")
            
            # Simulate the filler response
            print(f"   ⚡ Expected Filler Response Style:")
            print(f"      \"I'll analyze that for you! Let me dive into the details.")
            
            # Find relevant memory references
            query_lower = scenario['query'].lower()
            prompt_lower = prompt.lower()
            
            relevant_refs = []
            for memory in memories:
                mem_text = memory.get('text', '').lower()
                if any(word in mem_text for word in query_lower.split() if len(word) > 3):
                    relevant_refs.append(memory.get('text', '')[:50] + "...")
            
            if relevant_refs:
                print(f"       By the way, how's {relevant_refs[0].split()[0].lower()} going?\"")
            else:
                print(f"       By the way, how was your day?\"")
        
        else:
            print(f"   ⚡ Fast Response (No Memory Context Needed)")
        
        print()
        print("   " + "─" * 50)
        print()
    
    # Show the difference between memory-enhanced and basic prompts
    print("📊 Prompt Comparison")
    print("=" * 30)
    
    basic_prompt = service._build_fast_system_prompt(
        emotion_context=None,
        needs_agent=True,
        user_id=None  # No user ID = no memories
    )
    
    memory_prompt = service._build_fast_system_prompt(
        emotion_context=None,
        needs_agent=True,
        user_id="demo_sarah"  # With user ID = memory context
    )
    
    print(f"📏 Basic prompt length: {len(basic_prompt)} characters")
    print(f"🧠 Memory-enhanced prompt length: {len(memory_prompt)} characters")
    print(f"📈 Enhancement: {len(memory_prompt) - len(basic_prompt)} additional characters")
    print()
    
    # Show memory context section
    if 'CONTEXTUAL MEMORIES' in memory_prompt:
        print("🎯 Memory Context Section:")
        lines = memory_prompt.split('\n')
        in_memory_section = False
        
        for line in lines:
            if 'CONTEXTUAL MEMORIES' in line:
                in_memory_section = True
                print(f"   {line}")
            elif in_memory_section and (line.strip().startswith('•') or 'Use the contextual' in line):
                print(f"   {line}")
            elif in_memory_section and line.strip() and not line.strip().startswith('•'):
                break
    
    print()
    print("🎉 Memory-Based Filler Response Demo Complete!")
    print()
    print("✅ Key Benefits Demonstrated:")
    print("  • 🧠 Personal context retrieval from user memories")
    print("  • 💬 Natural conversation continuation while processing")
    print("  • 🎯 Relevant memory references in filler responses")
    print("  • ⚡ Fast acknowledgment with personalized touch")
    print("  • 🤖 Seamless transition to background agent processing")


if __name__ == "__main__":
    asyncio.run(demonstrate_memory_filler_responses())
