#!/usr/bin/env python3
"""
Test script for Fast Response Service TTS Integration.
Tests the sub-450ms first TTS chunk delivery target.
"""

import asyncio
import time
import logging
import os
import sys
from typing import Dict, Any

# Add the project root to Python path
sys.path.insert(0, '/Users/<USER>/dev/pythonprojects/ellahai-backend')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')

import django
django.setup()

from chat.services.fast_response_service import FastResponseService
from authentication.models import User

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_fast_tts_integration():
    """Test the fast response service with TTS integration."""
    
    print("🚀 Testing Fast Response Service TTS Integration")
    print("=" * 60)
    
    # Create a test user (or get existing one)
    try:
        from asgiref.sync import sync_to_async
        user = await sync_to_async(User.objects.filter(email__icontains='test').first)()
        if not user:
            print("❌ No test user found. Please create a test user first.")
            return

        print(f"👤 Using test user: {user.email}")

    except Exception as e:
        print(f"❌ Error getting test user: {e}")
        return
    
    # Initialize fast response service
    fast_service = FastResponseService(user=user)
    
    # Test cases
    test_cases = [
        {
            'name': 'Simple Greeting',
            'input': 'Hello! How are you today?',
            'expected_tts': True
        },
        {
            'name': 'Question',
            'input': 'What\'s the weather like?',
            'expected_tts': True
        },
        {
            'name': 'Emotional Context',
            'input': 'I\'m feeling a bit stressed about work',
            'expected_tts': True,
            'emotion_context': {
                'primary_emotion': 'stress',
                'intensity': 0.7,
                'emotions': [
                    {'name': 'stress', 'score': 0.7},
                    {'name': 'anxiety', 'score': 0.5}
                ]
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test Case {i}: {test_case['name']}")
        print(f"📝 Input: {test_case['input']}")
        
        start_time = time.time()
        first_text_chunk_time = None
        first_tts_chunk_time = None
        text_chunks = []
        tts_chunks = []
        
        try:
            # Process the query
            async for response_chunk in fast_service.process_query_fast(
                user_input=test_case['input'],
                user_id=str(user.id),
                conversation_history=[],
                emotion_context=test_case.get('emotion_context'),
                streaming=True,
                enable_tts=True
            ):
                current_time = time.time()
                chunk_time = (current_time - start_time) * 1000
                
                chunk_type = response_chunk.get('type')
                
                if chunk_type == 'response_chunk':
                    if first_text_chunk_time is None:
                        first_text_chunk_time = chunk_time
                        print(f"📝 First text chunk: {first_text_chunk_time:.1f}ms")
                    
                    content = response_chunk.get('content', '')
                    text_chunks.append(content)
                    print(f"   Text: {content}")
                
                elif chunk_type == 'tts_chunk':
                    if first_tts_chunk_time is None:
                        first_tts_chunk_time = chunk_time
                        print(f"🎵 First TTS chunk: {first_tts_chunk_time:.1f}ms")
                    
                    chunk_info = response_chunk.get('metadata', {})
                    target_time = chunk_info.get('target_first_chunk_ms', 450)
                    
                    tts_chunks.append(response_chunk)
                    print(f"   TTS chunk {response_chunk.get('chunk_index', 0)}: {len(response_chunk.get('audio_data', ''))} bytes")
                    
                    if response_chunk.get('is_final'):
                        print(f"   TTS streaming complete")
                
                elif chunk_type == 'tts_error':
                    print(f"❌ TTS Error: {response_chunk.get('error')}")
                
                elif chunk_type == 'response_complete':
                    print(f"✅ Response complete")
                    break
            
            # Analyze results
            total_time = (time.time() - start_time) * 1000
            
            print(f"\n📊 Results for {test_case['name']}:")
            print(f"   Total time: {total_time:.1f}ms")
            print(f"   Text chunks: {len(text_chunks)}")
            print(f"   TTS chunks: {len(tts_chunks)}")
            
            if first_text_chunk_time:
                print(f"   First text chunk: {first_text_chunk_time:.1f}ms")
                if first_text_chunk_time <= 450:
                    print(f"   ✅ Text response target achieved!")
                else:
                    print(f"   ⚠️ Text response exceeded 450ms target")
            
            if first_tts_chunk_time:
                print(f"   First TTS chunk: {first_tts_chunk_time:.1f}ms")
                if first_tts_chunk_time <= 450:
                    print(f"   ✅ TTS response target achieved!")
                else:
                    print(f"   ⚠️ TTS response exceeded 450ms target")
            else:
                print(f"   ❌ No TTS chunks received")
            
            # Show full text response
            full_text = ''.join(text_chunks)
            print(f"   Full response: {full_text}")
            
        except Exception as e:
            print(f"❌ Error in test case: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🏁 Fast TTS Integration Test Complete")


if __name__ == "__main__":
    asyncio.run(test_fast_tts_integration())
