# Audio Debugging Guide

This guide explains how to use the audio debugging feature to save and analyze audio chunks received from the frontend for transcription debugging.

## Setup

### 1. Enable Audio Debugging

In your `.env` file, set:
```
DEBUG_SAVE_AUDIO_CHUNKS=True
DEBUG_AUDIO_PATH=./debug_audio
```

### 2. Restart the Server

After updating the `.env` file, restart your Django development server:
```bash
python manage.py runserver
```

## How It Works

When audio debugging is enabled, every audio chunk received from the frontend will be saved to the `debug_audio` directory with the following files:

### File Types Created

For each audio chunk, three files are created:

1. **WAV file** (`audio_{user_id}_{timestamp}_{chunk_id}.wav`)
   - Playable audio file in WAV format
   - Uses 16-bit PCM, mono, 16kHz (matching frontend format)
   - Can be played with any audio player

2. **RAW file** (`audio_{user_id}_{timestamp}_{chunk_id}.raw`)
   - Raw binary audio data as received from frontend
   - Useful for analyzing the exact data format

3. **JSON metadata** (`audio_{user_id}_{timestamp}_{chunk_id}.json`)
   - Contains metadata about the audio chunk
   - Includes chunk ID, timestamp, size, format info

### File Naming Convention

```
audio_{user_id}_{timestamp}_{chunk_id}[_final].wav
```

- `user_id`: UUID of the authenticated user
- `timestamp`: Unix timestamp in milliseconds
- `chunk_id`: Unique identifier for the audio chunk
- `_final`: Added if this is marked as the final chunk

## Example Files

```
debug_audio/
├── audio_90646d73-a3b5-4b85-82d8-64525dea49ff_1753954370146_vad_1753954369071.wav
├── audio_90646d73-a3b5-4b85-82d8-64525dea49ff_1753954370146_vad_1753954369071.raw
├── audio_90646d73-a3b5-4b85-82d8-64525dea49ff_1753954370146_vad_1753954369071.json
├── audio_90646d73-a3b5-4b85-82d8-64525dea49ff_1753954370147_final_1753954369305_final.wav
├── audio_90646d73-a3b5-4b85-82d8-64525dea49ff_1753954370147_final_1753954369305_final.raw
└── audio_90646d73-a3b5-4b85-82d8-64525dea49ff_1753954370147_final_1753954369305_final.json
```

## Analyzing the Audio

### 1. Listen to the Audio
Open the `.wav` files in any audio player (VLC, QuickTime, etc.) to hear what was actually received.

### 2. Check Metadata
Open the `.json` files to see:
```json
{
  "chunk_id": "vad_1753954369071",
  "is_final": false,
  "user_id": "90646d73-a3b5-4b85-82d8-64525dea49ff",
  "timestamp": 1753954370146,
  "audio_size_bytes": 57600,
  "sample_rate": 16000,
  "channels": 1,
  "bit_depth": 16,
  "format": "PCM",
  "wav_file": "audio_90646d73-a3b5-4b85-82d8-64525dea49ff_1753954370146_vad_1753954369071.wav",
  "raw_file": "audio_90646d73-a3b5-4b85-82d8-64525dea49ff_1753954370146_vad_1753954369071.raw"
}
```

### 3. Debug Transcription Issues

Common issues to check:
- **Silent audio**: If the WAV file is silent, the frontend might not be capturing audio properly
- **Distorted audio**: Audio format mismatch between frontend and backend expectations
- **Short chunks**: Very short audio chunks might not contain enough speech for transcription
- **Background noise**: Excessive noise might interfere with transcription

## Log Messages

When audio debugging is enabled, you'll see log messages like:
```
INFO 🎤 Audio chunk saved for debugging: /path/to/debug_audio/audio_user_timestamp_chunk.wav (57600 bytes)
INFO 🎤 Audio files saved for debugging:
INFO    WAV: /path/to/debug_audio/audio_user_timestamp_chunk.wav
INFO    RAW: /path/to/debug_audio/audio_user_timestamp_chunk.raw
INFO    META: /path/to/debug_audio/audio_user_timestamp_chunk.json
```

## Disabling Audio Debugging

To disable audio debugging:

1. Set `DEBUG_SAVE_AUDIO_CHUNKS=False` in your `.env` file
2. Restart the server
3. Optionally, clean up the debug_audio directory

## Security Note

⚠️ **Important**: Audio debugging saves actual user voice recordings to disk. Only enable this feature in development environments and ensure you comply with privacy regulations. Never enable this in production without proper user consent and data handling procedures.

## Troubleshooting

### Audio Files Won't Play
- Check if the audio format assumptions (16kHz, 16-bit, mono) match your frontend
- Try opening the RAW file with audio analysis tools like Audacity

### No Audio Files Created
- Verify `DEBUG_SAVE_AUDIO_CHUNKS=True` in `.env`
- Check server logs for any error messages
- Ensure the debug directory is writable

### Large File Sizes
- Audio files can be large (especially for longer recordings)
- Consider periodically cleaning up old debug files
- Monitor disk space usage
