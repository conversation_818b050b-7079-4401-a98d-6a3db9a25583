#!/usr/bin/env python3
"""
Test script to validate the new API endpoints we created for frontend compatibility.
"""

import requests
import json

def test_endpoint(url, description):
    """Test a single API endpoint."""
    print(f"\n🔍 Testing {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                data = response.json()
                print(f"Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            except json.JSONDecodeError:
                print("Response is not JSON")
        elif response.status_code == 401:
            print("🔒 Authentication required (expected)")
        elif response.status_code == 404:
            print("❌ Not Found - endpoint may not exist")
        else:
            print(f"⚠️ Unexpected status: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the server running?")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Test the new endpoints we created."""
    base_url = "http://127.0.0.1:8000"
    
    print("🚀 Testing New Backend API Endpoints")
    print("=" * 45)
    
    # Test Unity endpoints (aliases)
    print("\n🎮 Unity Integration Endpoints")
    print("-" * 35)
    endpoints = [
        ("/api/unity/avatar/settings/", "Unity Avatar Settings (alias)"),
        ("/api/agents/avatar/settings/", "Agents Avatar Settings (original)"),
        ("/api/unity/avatar/change-outfit/", "Unity Change Outfit (alias)"),
        ("/api/agents/avatar/outfit/", "Agents Change Outfit (original)"),
        ("/api/unity/avatar/change-environment/", "Unity Change Environment (alias)"),
        ("/api/agents/avatar/environment/", "Agents Change Environment (original)"),
    ]
    
    for endpoint, description in endpoints:
        test_endpoint(f"{base_url}{endpoint}", description)
    
    # Test Shop endpoints (aliases)
    print("\n🛒 Shop System Endpoints")
    print("-" * 25)
    endpoints = [
        ("/api/shop/items/", "Shop Items (alias)"),
        ("/api/gamification/shop/", "Gamification Shop (original)"),
        ("/api/shop/relationship-gates/", "Shop Relationship Gates (alias)"),
    ]
    
    for endpoint, description in endpoints:
        test_endpoint(f"{base_url}{endpoint}", description)
    
    # Test User endpoints (aliases)
    print("\n👤 User Management Endpoints")
    print("-" * 30)
    endpoints = [
        ("/api/user/profile/", "User Profile"),
        ("/api/user/settings/", "User Settings (alias)"),
        ("/api/user/preferences/", "User Preferences (original)"),
        ("/api/user/level/", "User Level (alias)"),
        ("/api/user/achievements/", "User Achievements (alias)"),
        ("/api/user/wallet/", "User Wallet (alias)"),
        ("/api/user/inventory/", "User Inventory (alias)"),
    ]
    
    for endpoint, description in endpoints:
        test_endpoint(f"{base_url}{endpoint}", description)
    
    # Test Gamification endpoints (aliases)
    print("\n🎯 Gamification Endpoints")
    print("-" * 25)
    endpoints = [
        ("/api/daily-rewards/", "Daily Rewards (alias)"),
        ("/api/achievements/unlock/", "Unlock Achievement (alias)"),
        ("/api/daily-rewards/claim/", "Claim Daily Reward (alias)"),
        ("/api/gamification/daily-rewards/", "Daily Rewards (original)"),
        ("/api/gamification/achievements/unlock/", "Unlock Achievement (original)"),
    ]
    
    for endpoint, description in endpoints:
        test_endpoint(f"{base_url}{endpoint}", description)
    
    print("\n" + "=" * 45)
    print("✅ Testing complete!")
    print("\nNote: 401 (Unauthorized) responses are expected for protected endpoints.")
    print("404 (Not Found) responses indicate missing endpoints that need to be fixed.")

if __name__ == "__main__":
    main()
