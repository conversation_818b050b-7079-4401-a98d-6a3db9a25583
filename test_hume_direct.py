#!/usr/bin/env python3
"""
Direct Hume API Test
Test Hume API directly to understand the correct format.
"""
import asyncio
import websockets
import json
import base64
import ssl
from pathlib import Path

# Hume Configuration
HUME_API_KEY = "VcaXvkuAbmtrQTCI29D449EuMt2uYHWCFMs8rY4ior6zhFQ5"
WEBSOCKET_URL = "wss://api.hume.ai/v0/stream/models"


async def test_hume_direct():
    """Test Hume API directly with WebSocket."""
    print("🔗 DIRECT HUME API TEST")
    print("=" * 60)
    
    # Get test audio file
    audio_files = list(Path("expressive_audio_library").glob("*.mp3"))
    if not audio_files:
        print("❌ No audio files found")
        return
    
    test_file = audio_files[0]
    print(f"🎵 Testing with: {test_file.name}")
    
    # Load and encode audio
    with open(test_file, 'rb') as f:
        audio_data = f.read()
    
    audio_b64 = base64.b64encode(audio_data).decode('utf-8')
    print(f"📊 Audio size: {len(audio_data):,} bytes")
    print(f"📊 Base64 size: {len(audio_b64):,} characters")
    
    try:
        # Connect to Hume WebSocket
        headers = {"X-Hume-Api-Key": HUME_API_KEY}
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        # Try with query parameter instead of header
        websocket_url_with_key = f"{WEBSOCKET_URL}?apiKey={HUME_API_KEY}"
        print(f"🔗 Connecting to: {websocket_url_with_key}")

        async with websockets.connect(
            websocket_url_with_key,
            ssl=ssl_context,
            ping_interval=None
        ) as websocket:
            print("✅ WebSocket connected successfully")
            
            # Send models and data in single message (correct Hume format)
            audio_message = {
                "models": {
                    "prosody": {}
                },
                "data": audio_b64
            }
            
            print(f"📤 Sending audio data...")
            await websocket.send(json.dumps(audio_message))
            
            # Wait for audio response
            try:
                audio_response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"📥 Audio response: {audio_response[:200]}...")
                
                # Parse response
                try:
                    response_data = json.loads(audio_response)
                    if 'prosody' in response_data:
                        prosody_data = response_data['prosody']
                        if 'predictions' in prosody_data:
                            predictions = prosody_data['predictions']
                            if predictions:
                                emotions = predictions[0].get('emotions', [])
                                if emotions:
                                    print(f"✅ SUCCESS! Found {len(emotions)} emotions")
                                    for i, emotion in enumerate(emotions[:5]):
                                        name = emotion.get('name', 'Unknown')
                                        score = emotion.get('score', 0)
                                        print(f"   {i+1}. {name}: {score:.3f}")
                                else:
                                    print("⚠️ No emotions in predictions")
                            else:
                                print("⚠️ No predictions in prosody data")
                        else:
                            print("⚠️ No predictions key in prosody")
                    else:
                        print("⚠️ No prosody key in response")
                        print(f"Response keys: {list(response_data.keys())}")
                except json.JSONDecodeError as e:
                    print(f"❌ JSON decode error: {e}")
                    
            except asyncio.TimeoutError:
                print("❌ No audio response (timeout)")
            
    except Exception as e:
        print(f"❌ WebSocket error: {e}")
        import traceback
        traceback.print_exc()


async def test_simple_text():
    """Test with simple text first to verify connection."""
    print(f"\n📝 SIMPLE TEXT TEST")
    print("=" * 60)
    
    try:
        headers = {"X-Hume-Api-Key": HUME_API_KEY}
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        websocket_url_with_key = f"{WEBSOCKET_URL}?apiKey={HUME_API_KEY}"

        async with websockets.connect(
            websocket_url_with_key,
            ssl=ssl_context,
            ping_interval=None
        ) as websocket:
            print("✅ WebSocket connected for text test")
            
            # Test with language model (text)
            text_message = {
                "models": {
                    "language": {}
                },
                "raw_text": True,
                "data": "I'm feeling really happy and excited today!"
            }
            
            print(f"📤 Sending text: {json.dumps(text_message)}")
            await websocket.send(json.dumps(text_message))
            
            try:
                text_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📥 Text response: {text_response[:200]}...")
                
                response_data = json.loads(text_response)
                if 'language' in response_data:
                    print("✅ Language model working!")
                else:
                    print("⚠️ Unexpected text response format")
                    
            except asyncio.TimeoutError:
                print("❌ No text response (timeout)")
                
    except Exception as e:
        print(f"❌ Text test error: {e}")


async def main():
    """Run direct Hume API tests."""
    print("🎯 DIRECT HUME API TESTING")
    print("=" * 70)
    
    # Test 1: Simple text to verify connection
    await test_simple_text()
    
    # Test 2: Audio prosody
    await test_hume_direct()
    
    print(f"\n💡 INSIGHTS:")
    print(f"   - Test if text model works (language)")
    print(f"   - Compare with prosody model behavior")
    print(f"   - Check exact response format")
    
    print(f"\n✨ Direct Hume API test completed!")


if __name__ == "__main__":
    asyncio.run(main())
