#!/usr/bin/env python3
"""
Optimized Expression Measurement Performance Test
Tests all the performance optimizations for expression measurement.
"""
import os
import sys
import django
import asyncio
import time
import uuid
import tempfile
import subprocess
import statistics

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.services.expression_measurement_service import expression_measurement_service


def create_speech_audio(text: str, duration_hint: str = "normal") -> bytes:
    """Create speech audio with different durations."""
    try:
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
        
        # Adjust speech rate based on duration hint
        rate_param = ""
        if duration_hint == "fast":
            rate_param = "-r 200"  # Faster speech
        elif duration_hint == "slow":
            rate_param = "-r 150"  # Slower speech
        
        # Generate speech audio
        cmd = f'say {rate_param} -o {temp_path} --data-format=LEI16@16000 "{text}"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error generating speech: {result.stderr}")
            return None
        
        with open(temp_path, 'rb') as f:
            audio_data = f.read()
        
        os.unlink(temp_path)
        return audio_data
        
    except Exception as e:
        print(f"Error creating speech audio: {e}")
        return None


async def test_optimization_performance():
    """Test the performance of all optimizations."""
    print("🚀 OPTIMIZED EXPRESSION MEASUREMENT PERFORMANCE TEST")
    print("=" * 60)
    
    # Test different audio sizes to measure preprocessing optimization
    test_cases = [
        ("Short phrase", "Hello there!", "fast"),
        ("Medium sentence", "I am feeling quite excited about this new project today.", "normal"),
        ("Long speech", "This is a longer piece of text that will test how well our audio preprocessing optimization works when dealing with larger audio files that need to be processed efficiently.", "slow"),
    ]
    
    results = []
    
    for name, text, duration_hint in test_cases:
        print(f"\n🎤 Testing: {name}")
        print(f"   Text: '{text[:50]}{'...' if len(text) > 50 else ''}'")
        
        # Generate audio
        audio_data = create_speech_audio(text, duration_hint)
        if not audio_data:
            print(f"   ❌ Failed to generate audio")
            continue
        
        print(f"   📊 Audio size: {len(audio_data):,} bytes")
        
        # Test multiple times to measure consistency
        latencies = []
        emotions = []
        
        for i in range(3):
            chunk_id = f"{name.lower().replace(' ', '_')}_{i}_{uuid.uuid4()}"
            
            start_time = time.time()
            result = await expression_measurement_service.analyze_audio_chunk(audio_data, chunk_id)
            latency = (time.time() - start_time) * 1000
            
            latencies.append(latency)
            
            if result:
                emotions.append(result.dominant_emotion)
                print(f"   ✅ Run {i+1}: {result.dominant_emotion} ({result.confidence:.2f}) - {latency:.1f}ms")
            else:
                print(f"   ❌ Run {i+1}: Failed - {latency:.1f}ms")
        
        # Calculate statistics
        if latencies:
            avg_latency = statistics.mean(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)
            
            results.append({
                'name': name,
                'audio_size': len(audio_data),
                'avg_latency_ms': avg_latency,
                'min_latency_ms': min_latency,
                'max_latency_ms': max_latency,
                'emotions': emotions,
                'success_rate': len([e for e in emotions if e]) / len(latencies) * 100
            })
            
            print(f"   📈 Average: {avg_latency:.1f}ms (range: {min_latency:.1f}-{max_latency:.1f}ms)")
    
    return results


async def test_caching_optimization():
    """Test the caching optimization with repeated audio."""
    print("\n🔄 CACHING OPTIMIZATION TEST")
    print("=" * 60)
    
    # Create test audio
    text = "This is a test for caching optimization."
    audio_data = create_speech_audio(text)
    
    if not audio_data:
        print("❌ Failed to generate test audio")
        return
    
    print(f"📊 Testing caching with {len(audio_data):,} bytes audio")
    
    # First request (should be slow - no cache)
    print("\n🔍 First request (no cache):")
    start_time = time.time()
    result1 = await expression_measurement_service.analyze_audio_chunk(audio_data, "cache_test_1")
    first_latency = (time.time() - start_time) * 1000
    
    if result1:
        print(f"   ✅ {result1.dominant_emotion} ({result1.confidence:.2f}) - {first_latency:.1f}ms")
    else:
        print(f"   ❌ Failed - {first_latency:.1f}ms")
    
    # Second request (should be faster - cache hit)
    print("\n⚡ Second request (cache hit expected):")
    start_time = time.time()
    result2 = await expression_measurement_service.analyze_audio_chunk(audio_data, "cache_test_2")
    second_latency = (time.time() - start_time) * 1000
    
    if result2:
        print(f"   ✅ {result2.dominant_emotion} ({result2.confidence:.2f}) - {second_latency:.1f}ms")
    else:
        print(f"   ❌ Failed - {second_latency:.1f}ms")
    
    # Calculate cache performance
    if first_latency > 0 and second_latency > 0:
        speedup = first_latency / second_latency
        improvement = ((first_latency - second_latency) / first_latency) * 100
        
        print(f"\n📊 Cache Performance:")
        print(f"   First request: {first_latency:.1f}ms")
        print(f"   Second request: {second_latency:.1f}ms")
        print(f"   Speedup: {speedup:.1f}x")
        print(f"   Improvement: {improvement:.1f}%")
        
        return {
            'first_latency_ms': first_latency,
            'second_latency_ms': second_latency,
            'speedup': speedup,
            'improvement_percent': improvement
        }
    
    return None


async def test_parallel_processing():
    """Test parallel processing optimization."""
    print("\n🔄 PARALLEL PROCESSING TEST")
    print("=" * 60)
    
    # Create multiple different audio samples
    texts = [
        "I am very happy today!",
        "This is quite frustrating.",
        "What a wonderful surprise!",
        "I feel calm and peaceful.",
        "That's really exciting news!"
    ]
    
    # Generate audio chunks
    audio_chunks = []
    for i, text in enumerate(texts):
        audio_data = create_speech_audio(text)
        if audio_data:
            audio_chunks.append((audio_data, f"parallel_test_{i}"))
    
    if not audio_chunks:
        print("❌ Failed to generate audio chunks")
        return
    
    print(f"🎵 Testing parallel processing with {len(audio_chunks)} audio chunks")
    
    # Test sequential processing
    print("\n📊 Sequential processing:")
    sequential_start = time.time()
    sequential_results = []
    
    for audio_data, chunk_id in audio_chunks:
        result = await expression_measurement_service.analyze_audio_chunk(audio_data, f"seq_{chunk_id}")
        sequential_results.append(result)
    
    sequential_time = (time.time() - sequential_start) * 1000
    sequential_success = len([r for r in sequential_results if r])
    
    print(f"   ⏱️  Total time: {sequential_time:.1f}ms")
    print(f"   ✅ Success: {sequential_success}/{len(audio_chunks)}")
    
    # Test parallel processing
    print("\n🚀 Parallel processing:")
    parallel_start = time.time()
    parallel_results = await expression_measurement_service.analyze_audio_batch(audio_chunks)
    parallel_time = (time.time() - parallel_start) * 1000
    
    print(f"   ⏱️  Total time: {parallel_time:.1f}ms")
    print(f"   ✅ Success: {len(parallel_results)}/{len(audio_chunks)}")
    
    # Calculate performance improvement
    if sequential_time > 0 and parallel_time > 0:
        speedup = sequential_time / parallel_time
        improvement = ((sequential_time - parallel_time) / sequential_time) * 100
        
        print(f"\n📈 Parallel Performance:")
        print(f"   Sequential: {sequential_time:.1f}ms")
        print(f"   Parallel: {parallel_time:.1f}ms")
        print(f"   Speedup: {speedup:.1f}x")
        print(f"   Improvement: {improvement:.1f}%")
        
        return {
            'sequential_time_ms': sequential_time,
            'parallel_time_ms': parallel_time,
            'speedup': speedup,
            'improvement_percent': improvement,
            'chunks_processed': len(audio_chunks)
        }
    
    return None


async def main():
    """Run all optimization tests."""
    print("🎯 COMPREHENSIVE OPTIMIZATION PERFORMANCE TEST")
    print("=" * 70)
    
    # Test 1: Basic optimization performance
    basic_results = await test_optimization_performance()
    
    # Test 2: Caching optimization
    cache_results = await test_caching_optimization()
    
    # Test 3: Parallel processing optimization
    parallel_results = await test_parallel_processing()
    
    # Get service statistics
    stats = expression_measurement_service.get_performance_stats()
    
    # Final analysis
    print("\n🏆 OPTIMIZATION PERFORMANCE ANALYSIS")
    print("=" * 70)
    
    if basic_results:
        avg_latencies = [r['avg_latency_ms'] for r in basic_results]
        overall_avg = statistics.mean(avg_latencies)
        
        print(f"📊 Basic Performance:")
        print(f"   Overall average latency: {overall_avg:.1f}ms")
        print(f"   Best performance: {min(avg_latencies):.1f}ms")
        print(f"   Range: {min(avg_latencies):.1f}-{max(avg_latencies):.1f}ms")
        
        # Performance assessment
        if overall_avg <= 100:
            print(f"   🎉 EXCELLENT - Within 100ms target!")
        elif overall_avg <= 300:
            print(f"   ✅ GOOD - Suitable for real-time use")
        elif overall_avg <= 500:
            print(f"   ⚠️  ACCEPTABLE - May impact user experience")
        else:
            print(f"   ❌ NEEDS IMPROVEMENT - Too slow for real-time")
    
    if cache_results:
        print(f"\n🔄 Caching Optimization:")
        print(f"   Speedup: {cache_results['speedup']:.1f}x")
        print(f"   Improvement: {cache_results['improvement_percent']:.1f}%")
    
    if parallel_results:
        print(f"\n🚀 Parallel Processing:")
        print(f"   Speedup: {parallel_results['speedup']:.1f}x")
        print(f"   Improvement: {parallel_results['improvement_percent']:.1f}%")
    
    print(f"\n📈 Service Statistics:")
    print(f"   Total requests: {stats['total_requests']}")
    print(f"   Success rate: {stats['success_rate_percent']:.1f}%")
    print(f"   Average processing: {stats['avg_processing_time_ms']:.1f}ms")
    print(f"   Cache hit rate: {stats['cache_hit_rate_percent']:.1f}%")
    print(f"   Connection pool size: {stats['connection_pool_size']}")
    print(f"   Optimizations enabled: {'✅' if stats['optimizations_enabled'] else '❌'}")
    
    print(f"\n✨ Optimization test completed!")


if __name__ == "__main__":
    asyncio.run(main())
