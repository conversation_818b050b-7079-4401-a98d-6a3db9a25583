#!/usr/bin/env python3
"""
Real-World Emotion Accuracy Stress Test
Tests emotion detection accuracy with realistic emotional scenarios and sudden mood shifts.
"""
import os
import sys
import django
import asyncio
import time
import uuid
import tempfile
import subprocess
from typing import List, Dict, Tuple

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service


def create_emotional_speech(text: str, emotion_hint: str = "normal") -> bytes:
    """Create speech audio with emotional inflection using macOS say command."""
    try:
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
        
        # Adjust voice parameters based on emotion (using default voice)
        voice_params = []

        if emotion_hint == "sad":
            voice_params = ["-r", "140"]  # Slower speech
        elif emotion_hint == "happy":
            voice_params = ["-r", "180"]  # Faster, brighter speech
        elif emotion_hint == "angry":
            voice_params = ["-r", "160"]  # Moderate speed, assertive
        elif emotion_hint == "excited":
            voice_params = ["-r", "200"]  # Fast, energetic
        elif emotion_hint == "calm":
            voice_params = ["-r", "150"]  # Steady, measured
        elif emotion_hint == "shocked":
            voice_params = ["-r", "120"]  # Slow, surprised
        else:
            voice_params = ["-r", "160"]  # Normal
        
        # Generate speech with emotional parameters
        cmd = ['say'] + voice_params + ['-o', temp_path, '--data-format=LEI16@16000', text]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error generating speech: {result.stderr}")
            return None
        
        with open(temp_path, 'rb') as f:
            audio_data = f.read()
        
        os.unlink(temp_path)
        return audio_data
        
    except Exception as e:
        print(f"Error creating emotional speech: {e}")
        return None


async def setup_test_user() -> User:
    """Setup test user."""
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    return user


async def test_gradual_mood_improvement():
    """Test scenario: User starts sad, gradually gets happier through AI conversation."""
    print("😢➡️😊 GRADUAL MOOD IMPROVEMENT TEST")
    print("=" * 60)
    print("Scenario: User starts sad, AI helps them feel better over time")
    
    user = await setup_test_user()
    session_id = f"mood_improvement_{int(time.time())}"
    user_id = str(user.id)
    
    # Emotional journey using ACTUAL Hume emotion categories
    # From Hume docs: 48 emotions for Speech Prosody
    conversation = [
        ("Initial sadness", "I'm feeling really down today, everything seems to be going wrong.", "sad", "Sadness"),
        ("Slight opening", "I guess talking about it might help a little bit.", "sad", "Distress"),
        ("Small improvement", "You know what, maybe you're right about that.", "calm", "Interest"),
        ("Getting better", "That's actually a really good point, I hadn't thought of it that way.", "calm", "Contemplation"),
        ("Feeling hopeful", "I'm starting to feel a bit more optimistic about things.", "happy", "Relief"),
        ("Much better", "Thank you so much, I'm feeling so much better now!", "happy", "Gratitude"),
        ("Grateful", "You've really helped me turn my day around, I'm genuinely happy now!", "excited", "Joy"),
    ]
    
    results = await run_emotion_scenario(conversation, session_id, user_id, "Gradual Improvement")
    
    # Analyze accuracy using Hume's actual emotion categories
    expected_progression = ["Sadness", "Distress", "Interest", "Contemplation", "Relief", "Gratitude", "Joy"]
    accuracy_score = analyze_emotion_accuracy(results, expected_progression)
    
    print(f"\n📊 Mood Improvement Analysis:")
    print(f"   Expected progression: Sadness → Distress → Interest → Contemplation → Relief → Gratitude → Joy")
    print(f"   Accuracy score: {accuracy_score:.1f}%")
    
    return results, accuracy_score


async def test_sudden_mood_crash():
    """Test scenario: User is happy, then receives terrible news and mood crashes."""
    print("\n😊➡️😭 SUDDEN MOOD CRASH TEST")
    print("=" * 60)
    print("Scenario: User is happy, then gets terrible news about their dog")
    
    user = await setup_test_user()
    session_id = f"mood_crash_{int(time.time())}"
    user_id = str(user.id)
    
    # Emotional journey using Hume's actual emotion categories
    conversation = [
        ("Happy start", "I'm having such a wonderful day today!", "happy", "Joy"),
        ("Very happy", "Everything is going perfectly, I couldn't be happier!", "excited", "Excitement"),
        ("Great mood", "Life is just amazing right now, I feel so grateful!", "excited", "Gratitude"),
        ("TERRIBLE NEWS", "Oh no... I just got a call... my dog is really sick and might not make it.", "shocked", "Surprise (negative)"),
        ("Devastation", "I can't believe this is happening, I'm so scared and heartbroken.", "sad", "Fear"),
        ("Deep grief", "I don't know what I'll do if I lose him, he's everything to me.", "sad", "Sadness"),
        ("Overwhelming sadness", "I'm just... I'm falling apart right now.", "sad", "Distress"),
    ]
    
    results = await run_emotion_scenario(conversation, session_id, user_id, "Sudden Crash")
    
    # Analyze the sudden shift using Hume's actual emotions
    expected_progression = ["Joy", "Excitement", "Gratitude", "Surprise (negative)", "Fear", "Sadness", "Distress"]
    accuracy_score = analyze_emotion_accuracy(results, expected_progression)

    # Check for sudden shift detection (positive to negative emotions)
    shift_detected = False
    for i in range(1, len(results)):
        if ('session_emotion' in results[i-1] and 'session_emotion' in results[i] and
            results[i-1]['session_emotion'] in ['Joy', 'Excitement', 'Gratitude'] and
            results[i]['session_emotion'] in ['Surprise (negative)', 'Fear', 'Sadness', 'Distress']):
            shift_detected = True
            break

    print(f"\n📊 Sudden Mood Crash Analysis:")
    print(f"   Expected progression: Joy → Excitement → Gratitude → CRASH → Fear → Sadness → Distress")
    print(f"   Accuracy score: {accuracy_score:.1f}%")
    print(f"   Sudden shift detected: {'✅' if shift_detected else '❌'}")
    
    return results, accuracy_score, shift_detected


async def test_emotional_rollercoaster():
    """Test scenario: Multiple rapid mood changes (work stress, good news, bad news, resolution)."""
    print("\n🎢 EMOTIONAL ROLLERCOASTER TEST")
    print("=" * 60)
    print("Scenario: Rapid mood changes - stress, relief, shock, anger, resolution")
    
    user = await setup_test_user()
    session_id = f"rollercoaster_{int(time.time())}"
    user_id = str(user.id)
    
    # Complex emotional journey using Hume's actual emotion categories
    conversation = [
        ("Work stress", "I'm so stressed about this deadline, I don't think I can finish in time.", "sad", "Anxiety"),
        ("Panic", "Oh god, my boss is going to be furious if I don't get this done!", "angry", "Fear"),
        ("GOOD NEWS", "Wait, I just got an email - the deadline has been extended by a week!", "excited", "Surprise (positive)"),
        ("Relief", "Thank goodness! I can actually breathe now, what a relief!", "happy", "Relief"),
        ("BAD NEWS", "But wait... they also said they're cutting the budget in half.", "shocked", "Disappointment"),
        ("Anger", "This is ridiculous! How am I supposed to do quality work with no resources?", "angry", "Anger"),
        ("Frustration", "I'm so fed up with this company and their constant changes!", "angry", "Annoyance"),
        ("Resolution", "You know what, I'm going to talk to my manager and work this out professionally.", "calm", "Determination"),
        ("Acceptance", "I feel much calmer now that I have a plan to address this.", "calm", "Calmness"),
    ]
    
    results = await run_emotion_scenario(conversation, session_id, user_id, "Rollercoaster")
    
    # Analyze emotional volatility using Hume's actual emotions
    expected_progression = ["Anxiety", "Fear", "Surprise (positive)", "Relief", "Disappointment", "Anger", "Annoyance", "Determination", "Calmness"]
    accuracy_score = analyze_emotion_accuracy(results, expected_progression)
    
    # Count emotional shifts
    emotion_shifts = 0
    for i in range(1, len(results)):
        if ('session_emotion' in results[i] and 'session_emotion' in results[i-1] and
            results[i]['session_emotion'] != results[i-1]['session_emotion']):
            emotion_shifts += 1
    
    print(f"\n📊 Emotional Rollercoaster Analysis:")
    print(f"   Expected: Anxiety → Fear → Surprise+ → Relief → Disappointment → Anger → Annoyance → Determination → Calmness")
    print(f"   Accuracy score: {accuracy_score:.1f}%")
    print(f"   Emotional shifts detected: {emotion_shifts}")
    print(f"   Volatility handling: {'✅ Good' if emotion_shifts >= 4 else '⚠️ May be too stable'}")
    
    return results, accuracy_score, emotion_shifts


async def test_subtle_emotion_detection():
    """Test scenario: Subtle emotional cues and mixed emotions."""
    print("\n🔍 SUBTLE EMOTION DETECTION TEST")
    print("=" * 60)
    print("Scenario: Subtle emotional cues, sarcasm, mixed feelings")
    
    user = await setup_test_user()
    session_id = f"subtle_{int(time.time())}"
    user_id = str(user.id)
    
    # Subtle and complex emotions using Hume's actual categories
    conversation = [
        ("Subtle sadness", "I'm fine, really. Just one of those days, you know?", "sad", "Sadness"),
        ("Hidden anxiety", "Everything's under control, totally manageable.", "calm", "Anxiety"),
        ("Sarcasm/frustration", "Oh great, another meeting. Just what I needed today.", "angry", "Sarcasm"),
        ("Mixed feelings", "I'm happy for them, but I can't help feeling a bit jealous too.", "calm", "Envy"),
        ("Forced positivity", "I'm trying to stay positive, but it's really hard sometimes.", "happy", "Distress"),
        ("Genuine relief", "Actually, talking about this is helping me feel better.", "calm", "Relief"),
        ("Authentic happiness", "You know what? I think I'm actually okay. Thank you.", "happy", "Contentment"),
    ]
    
    results = await run_emotion_scenario(conversation, session_id, user_id, "Subtle Emotions")
    
    # This test is about detecting nuanced emotions using Hume's actual categories
    expected_progression = ["Sadness", "Anxiety", "Sarcasm", "Envy", "Distress", "Relief", "Contentment"]
    accuracy_score = analyze_emotion_accuracy(results, expected_progression, tolerance=True)
    
    print(f"\n📊 Subtle Emotion Analysis:")
    print(f"   Expected: Complex mix of subtle emotions")
    print(f"   Accuracy score (with tolerance): {accuracy_score:.1f}%")
    print(f"   Note: Subtle emotions are harder to detect accurately")
    
    return results, accuracy_score


async def run_emotion_scenario(conversation: List[Tuple], session_id: str, user_id: str, scenario_name: str) -> List[Dict]:
    """Run an emotion detection scenario and return results."""
    print(f"\n🎭 Running {scenario_name} scenario...")
    
    results = []
    
    for i, (stage, text, emotion_hint, expected_emotion) in enumerate(conversation):
        print(f"\n   Stage {i+1}: {stage}")
        print(f"   Text: '{text[:60]}{'...' if len(text) > 60 else ''}'")
        print(f"   Expected: {expected_emotion}")
        
        # Generate emotional speech
        audio_data = create_emotional_speech(text, emotion_hint)
        if not audio_data:
            print(f"   ❌ Failed to generate audio")
            continue
        
        # Analyze emotion
        chunk_id = f"{session_id}_stage_{i}"
        start_time = time.time()
        
        result = await expression_measurement_service.analyze_audio_chunk(
            audio_data, chunk_id, session_id, user_id
        )
        
        analysis_time = (time.time() - start_time) * 1000
        
        # Get session profile
        session_profile = await expression_measurement_service.get_session_emotion_profile(session_id)
        
        if result and session_profile:
            individual_emotion = result.dominant_emotion
            session_emotion = session_profile.dominant_emotion
            confidence = session_profile.overall_confidence
            trend = session_profile.recent_trend
            
            print(f"   Individual: {individual_emotion} ({result.confidence:.2f})")
            print(f"   Session: {session_emotion} ({confidence:.2f}) - Trend: {trend}")
            
            # Check accuracy
            accuracy = "✅" if session_emotion == expected_emotion else "⚠️"
            print(f"   Accuracy: {accuracy} ({'Match' if session_emotion == expected_emotion else 'Different'})")
            
            results.append({
                'stage': i + 1,
                'stage_name': stage,
                'text': text,
                'expected_emotion': expected_emotion,
                'individual_emotion': individual_emotion,
                'individual_confidence': result.confidence,
                'session_emotion': session_emotion,
                'session_confidence': confidence,
                'trend': trend,
                'analysis_time_ms': analysis_time,
                'accurate': session_emotion == expected_emotion
            })
        else:
            print(f"   ❌ No emotion detected - {analysis_time:.1f}ms")
            results.append({
                'stage': i + 1,
                'stage_name': stage,
                'text': text,
                'expected_emotion': expected_emotion,
                'analysis_time_ms': analysis_time,
                'accurate': False
            })
        
        # Small delay between interactions
        await asyncio.sleep(0.3)
    
    return results


def analyze_emotion_accuracy(results: List[Dict], expected_progression: List[str], tolerance: bool = False) -> float:
    """Analyze the accuracy of emotion detection."""
    if not results:
        return 0.0
    
    correct = 0
    total = 0
    
    for i, result in enumerate(results):
        if 'session_emotion' in result and i < len(expected_progression):
            total += 1
            detected = result['session_emotion']
            expected = expected_progression[i]
            
            if tolerance:
                # For subtle emotions, allow related emotions to count as correct
                # Based on Hume's actual 48 emotion categories
                emotion_families = {
                    'Sadness': ['Sadness', 'Distress', 'Disappointment', 'Empathic Pain'],
                    'Joy': ['Joy', 'Contentment', 'Satisfaction', 'Amusement'],
                    'Anger': ['Anger', 'Annoyance', 'Contempt'],
                    'Fear': ['Fear', 'Anxiety', 'Horror'],
                    'Surprise': ['Surprise (positive)', 'Surprise (negative)', 'Awe'],
                    'Interest': ['Interest', 'Contemplation', 'Concentration'],
                    'Relief': ['Relief', 'Calmness'],
                    'Excitement': ['Excitement', 'Enthusiasm', 'Triumph'],
                    'Gratitude': ['Gratitude', 'Admiration', 'Love'],
                }
                
                is_correct = detected == expected
                if not is_correct:
                    for family_emotions in emotion_families.values():
                        if detected in family_emotions and expected in family_emotions:
                            is_correct = True
                            break
                
                if is_correct:
                    correct += 1
            else:
                if detected == expected:
                    correct += 1
    
    return (correct / total * 100) if total > 0 else 0.0


async def main():
    """Run comprehensive emotion accuracy stress tests."""
    print("🧪 REAL-WORLD EMOTION ACCURACY STRESS TEST")
    print("=" * 70)
    print("Testing emotion detection accuracy with realistic scenarios")
    
    # Test 1: Gradual mood improvement
    improvement_results, improvement_accuracy = await test_gradual_mood_improvement()
    
    # Test 2: Sudden mood crash
    crash_results, crash_accuracy, shift_detected = await test_sudden_mood_crash()
    
    # Test 3: Emotional rollercoaster
    rollercoaster_results, rollercoaster_accuracy, emotion_shifts = await test_emotional_rollercoaster()
    
    # Test 4: Subtle emotions
    subtle_results, subtle_accuracy = await test_subtle_emotion_detection()
    
    # Overall analysis
    print("\n🏆 COMPREHENSIVE ACCURACY ANALYSIS")
    print("=" * 70)
    
    all_accuracies = [improvement_accuracy, crash_accuracy, rollercoaster_accuracy, subtle_accuracy]
    overall_accuracy = sum(all_accuracies) / len(all_accuracies)
    
    print(f"📊 Test Results Summary:")
    print(f"   Gradual Improvement: {improvement_accuracy:.1f}% accuracy")
    print(f"   Sudden Mood Crash: {crash_accuracy:.1f}% accuracy")
    print(f"   Emotional Rollercoaster: {rollercoaster_accuracy:.1f}% accuracy")
    print(f"   Subtle Emotions: {subtle_accuracy:.1f}% accuracy")
    print(f"   OVERALL ACCURACY: {overall_accuracy:.1f}%")
    
    # Specific capabilities assessment
    print(f"\n🎯 Capability Assessment:")
    print(f"   Sudden mood shift detection: {'✅ Working' if shift_detected else '❌ Not detected'}")
    print(f"   Emotional volatility handling: {'✅ Good' if emotion_shifts >= 4 else '⚠️ May be too stable'}")
    print(f"   Session aggregation stability: {'✅ Good' if overall_accuracy > 60 else '⚠️ Needs improvement'}")
    
    # Production readiness assessment
    print(f"\n🚀 Production Readiness Assessment:")
    if overall_accuracy >= 70:
        print("   ✅ EXCELLENT - High accuracy, ready for production")
    elif overall_accuracy >= 50:
        print("   ✅ GOOD - Acceptable accuracy for production with monitoring")
    elif overall_accuracy >= 30:
        print("   ⚠️ FAIR - Needs improvement before production")
    else:
        print("   ❌ POOR - Significant improvements needed")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if improvement_accuracy < 50:
        print("   - Improve gradual emotion change detection")
    if crash_accuracy < 50:
        print("   - Enhance sudden mood shift sensitivity")
    if rollercoaster_accuracy < 50:
        print("   - Better handling of rapid emotion changes")
    if subtle_accuracy < 30:
        print("   - Subtle emotion detection needs work (expected - this is very difficult)")
    
    if overall_accuracy >= 60:
        print("   - System shows good emotion intelligence capabilities")
        print("   - Ready for production deployment with real-world scenarios")
    
    print(f"\n✨ Emotion accuracy stress test completed!")
    print(f"Overall system performance: {overall_accuracy:.1f}% accuracy across realistic scenarios")


if __name__ == "__main__":
    asyncio.run(main())
