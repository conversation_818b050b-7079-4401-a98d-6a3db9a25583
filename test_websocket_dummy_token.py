#!/usr/bin/env python3
"""
Test script to verify WebSocket connection with dummy token.
"""
import asyncio
import websockets
import json
import sys

async def test_websocket_connection():
    """Test WebSocket connection with dummy token."""
    
    # WebSocket URL with dummy token
    url = "ws://localhost:8000/ws/chat/?token=dummy_token_for_testing"
    
    try:
        print(f"Connecting to: {url}")
        
        async with websockets.connect(url) as websocket:
            print("✅ WebSocket connected successfully!")
            
            # Wait for connection established message
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                print(f"📨 Received: {json.dumps(data, indent=2)}")
                
                if data.get('type') == 'connection_established':
                    print("✅ Connection established successfully!")
                    
                    # Send a test message
                    test_message = {
                        "type": "text_message",
                        "content": "Hello from test script!",
                        "conversation_id": None
                    }
                    
                    print(f"📤 Sending test message: {test_message}")
                    await websocket.send(json.dumps(test_message))
                    
                    # Wait for response
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    data = json.loads(response)
                    print(f"📨 Received response: {json.dumps(data, indent=2)}")
                    
                else:
                    print(f"❌ Unexpected message type: {data.get('type')}")
                    
            except asyncio.TimeoutError:
                print("⏰ Timeout waiting for server response")
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ WebSocket connection closed: {e}")
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ WebSocket connection failed with status: {e}")
    except ConnectionRefusedError:
        print("❌ Connection refused. Is the Django server running?")
        print("   Run: python manage.py runserver")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🧪 Testing WebSocket connection with dummy token...")
    print("Make sure Django server is running: python manage.py runserver")
    print()
    
    asyncio.run(test_websocket_connection())
