#!/usr/bin/env python3
"""
Script to test JWT token configuration and lifetime.
"""
import os
import sys
import django
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from django.conf import settings
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from authentication.models import User

def test_jwt_configuration():
    print("🔐 JWT Configuration Test")
    print("=" * 50)
    
    # Print current JWT settings
    jwt_settings = settings.SIMPLE_JWT
    print(f"📅 Access Token Lifetime: {jwt_settings['ACCESS_TOKEN_LIFETIME']}")
    print(f"📅 Refresh Token Lifetime: {jwt_settings['REFRESH_TOKEN_LIFETIME']}")
    print(f"🔄 Rotate Refresh Tokens: {jwt_settings['ROTATE_REFRESH_TOKENS']}")
    print(f"🚫 Blacklist After Rotation: {jwt_settings['BLACKLIST_AFTER_ROTATION']}")
    
    # Get or create a test user
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f"\n👤 Using existing test user: {user.email}")
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword',
            first_name='Test',
            last_name='User'
        )
        print(f"\n👤 Created new test user: {user.email}")
    
    # Generate tokens
    refresh = RefreshToken.for_user(user)
    access_token = refresh.access_token
    
    print(f"\n🎫 Generated Tokens:")
    print(f"Access Token: {str(access_token)[:50]}...")
    print(f"Refresh Token: {str(refresh)[:50]}...")
    
    # Check token expiration times
    access_exp = datetime.fromtimestamp(access_token.payload['exp'])
    refresh_exp = datetime.fromtimestamp(refresh.payload['exp'])
    now = datetime.now()
    
    print(f"\n⏰ Token Expiration Times:")
    print(f"Current Time: {now}")
    print(f"Access Token Expires: {access_exp}")
    print(f"Refresh Token Expires: {refresh_exp}")
    print(f"Access Token Valid For: {access_exp - now}")
    print(f"Refresh Token Valid For: {refresh_exp - now}")
    
    # Test token validation
    try:
        # Validate access token
        validated_token = AccessToken(str(access_token))
        user_id = validated_token.payload.get('user_id')
        print(f"\n✅ Access token validation successful!")
        print(f"User ID from token: {user_id}")
        
        # Check if user exists
        token_user = User.objects.get(id=user_id)
        print(f"Token belongs to: {token_user.email}")
        
    except Exception as e:
        print(f"\n❌ Access token validation failed: {e}")
    
    print(f"\n🎯 Summary:")
    access_hours = (access_exp - now).total_seconds() / 3600
    refresh_days = (refresh_exp - now).total_seconds() / (3600 * 24)
    print(f"• Access tokens will be valid for {access_hours:.1f} hours")
    print(f"• Refresh tokens will be valid for {refresh_days:.1f} days")
    
    if access_hours > 720:  # 30 days
        print("✅ Access tokens are configured for long-term use (30+ days)")
    elif access_hours > 24:
        print("⚠️ Access tokens are valid for more than 24 hours")
    else:
        print("❌ Access tokens may expire too quickly for your needs")

if __name__ == "__main__":
    test_jwt_configuration()
