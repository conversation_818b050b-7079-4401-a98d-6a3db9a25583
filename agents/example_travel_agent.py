#!/usr/bin/env python3
"""
Travel Planning Agent - Specialized worker for travel-related tasks.
Processes structured requests from orchestrator and returns actionable travel plans.
"""
import logging
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class TravelRequest(BaseModel):
    """Structured input for travel planning requests."""
    destination: Optional[str] = None
    budget_range: Optional[str] = None
    duration: Optional[str] = None
    travel_style: Optional[str] = None
    interests: List[str] = Field(default_factory=list)
    constraints: List[str] = Field(default_factory=list)
    user_context: Dict[str, Any] = Field(default_factory=dict)


class TravelPlan(BaseModel):
    """Structured output for travel plans."""
    destinations: List[str]
    itinerary: Dict[str, Any]
    budget_breakdown: Dict[str, float]
    accommodations: List[Dict[str, Any]]
    activities: List[Dict[str, Any]]
    transportation: Dict[str, Any]
    recommendations: List[str]


class TravelPlanningAgent:
    """
    Specialized agent for travel planning tasks.
    Focuses on creating actionable travel plans based on structured input.
    """

    def __init__(self):
        self.agent_name = "Travel Planning Agent"
        self.version = "2.0"
        self.domain = "travel"

    def process_request(self, request_data: Dict[str, Any]) -> str:
        """
        Process travel planning request - provides conversational or comprehensive responses.

        Args:
            request_data: Structured request from orchestrator

        Returns:
            Conversational response or comprehensive travel plan based on context
        """
        start_time = time.time()

        logger.info(f"🧳 {self.agent_name} processing request")

        # Parse structured input
        travel_request = self._parse_request(request_data)
        conversation_history = request_data.get('conversation_history', [])
        user_input = request_data.get('user_input', '')

        logger.info(f"📍 Destination: {travel_request.destination}")
        logger.info(f"💰 Budget: {travel_request.budget_range}")
        logger.info(f"🎯 Interests: {travel_request.interests}")

        # Determine if this should be conversational or comprehensive
        should_be_comprehensive = self._should_provide_comprehensive_response(
            travel_request, conversation_history, user_input
        )

        if should_be_comprehensive:
            # Generate comprehensive travel plan
            logger.info(f"🎯 Providing comprehensive travel plan")
            return self._generate_comprehensive_response(travel_request)
        else:
            # Generate conversational response to gather more information
            logger.info(f"💬 Providing conversational response")
            return self._generate_conversational_response(travel_request, user_input, conversation_history)

    def _should_provide_comprehensive_response(
        self,
        travel_request: TravelRequest,
        conversation_history: List[Dict],
        user_input: str
    ) -> bool:
        """Determine if we should provide comprehensive response or continue conversation."""
        # Check if we have enough information for a comprehensive plan
        has_sufficient_info = (
            travel_request.destination and
            travel_request.budget_range and
            travel_request.duration and
            len(travel_request.interests) > 0
        )

        # Check if user is asking for final plan
        user_lower = user_input.lower()
        asking_for_plan = any(phrase in user_lower for phrase in [
            'put together', 'create', 'plan', 'itinerary', 'ready', 'sounds good', 'perfect'
        ])

        # Provide comprehensive response if we have info AND user is ready
        return has_sufficient_info and asking_for_plan

    def _generate_comprehensive_response(self, travel_request: TravelRequest) -> str:
        """Generate comprehensive travel plan response."""
        # Generate focused travel plan
        travel_plan = self._create_travel_plan(travel_request)

        # Format as comprehensive response
        response = f"""🧳 **TRAVEL PLAN CREATED**

📍 **DESTINATIONS:** {', '.join(travel_plan.destinations)}

💰 **BUDGET BREAKDOWN:**
• Total: ${travel_plan.budget_breakdown['total']:.0f}
• Daily Average: ${travel_plan.budget_breakdown['daily_average']:.0f}
• Accommodation: ${travel_plan.budget_breakdown['accommodation']:.0f}
• Food: ${travel_plan.budget_breakdown['food']:.0f}
• Transportation: ${travel_plan.budget_breakdown['transportation']:.0f}
• Activities: ${travel_plan.budget_breakdown['activities']:.0f}

🏨 **ACCOMMODATIONS:**
{self._format_accommodations(travel_plan.accommodations)}

🎯 **ACTIVITIES:**
{self._format_activities(travel_plan.activities)}

🚗 **TRANSPORTATION:**
{self._format_transportation(travel_plan.transportation)}

💡 **RECOMMENDATIONS:**
{self._format_recommendations(travel_plan.recommendations)}

📋 **NEXT STEPS:**
{self._format_next_steps(self._suggest_next_steps(travel_plan))}"""

        return response

    def _generate_conversational_response(
        self,
        travel_request: TravelRequest,
        user_input: str,
        conversation_history: List[Dict]
    ) -> str:
        """Generate conversational response to gather more information."""
        missing_info = []

        if not travel_request.destination:
            missing_info.append('destination')
        if not travel_request.budget_range:
            missing_info.append('budget')
        if not travel_request.duration:
            missing_info.append('duration')
        if not travel_request.interests:
            missing_info.append('interests')

        logger.info(f"🔍 Missing info: {missing_info}")
        logger.info(f"📊 Travel request: dest={travel_request.destination}, budget={travel_request.budget_range}, duration={travel_request.duration}, interests={travel_request.interests}")

        # Generate personalized conversational response
        # Always try early conversation response first if it's the user's first detailed input
        if len(missing_info) >= 2:
            # Early in conversation - ask about basics with personalization
            return self._generate_early_conversation_response(user_input, missing_info)
        elif len(missing_info) > 0:
            # Mid conversation - ask about specific missing info
            return self._generate_mid_conversation_response(travel_request, missing_info)
        else:
            # Have all info but user hasn't asked for plan yet
            return self._generate_ready_response(travel_request)

    def refine_response(self, original_result: Dict[str, Any], refinement_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Refine travel plan based on new conversation context.

        Args:
            original_result: Previous travel plan result
            refinement_context: New context from conversation

        Returns:
            Updated travel plan with refinements
        """
        start_time = time.time()

        logger.info(f"🔄 {self.agent_name} applying refinements")

        # Parse refinement context
        refinement_data = self._parse_refinement_context(refinement_context)

        # Load original plan
        original_plan = TravelPlan(**original_result['travel_plan'])

        # Apply targeted refinements
        refined_plan = self._apply_targeted_refinements(original_plan, refinement_data)

        processing_time = (time.time() - start_time) * 1000

        result = {
            **original_result,
            'travel_plan': refined_plan.dict(),
            'refinements_applied': refinement_data,
            'refinement_processing_time_ms': processing_time,
            'refinement_timestamp': datetime.now().isoformat(),
            'confidence_score': self._calculate_confidence_after_refinement(refined_plan, refinement_data)
        }

        logger.info(f"✅ Refinements applied in {processing_time:.1f}ms")
        return result

    def _parse_request(self, request_data: Dict[str, Any]) -> TravelRequest:
        """Parse orchestrator request into structured travel request."""
        user_input = request_data.get('user_input', '')
        memories = request_data.get('memories', [])
        conversation_context = request_data.get('conversation_context', {})

        # Extract travel parameters from input
        destination = self._extract_destination(user_input)
        budget_range = self._extract_budget(user_input, memories)
        duration = self._extract_duration(user_input)
        travel_style = self._extract_travel_style(user_input, memories)
        interests = self._extract_interests(user_input, memories)
        constraints = self._extract_constraints(user_input, conversation_context)

        return TravelRequest(
            destination=destination,
            budget_range=budget_range,
            duration=duration,
            travel_style=travel_style,
            interests=interests,
            constraints=constraints,
            user_context={
                'memories': memories,
                'conversation_context': conversation_context
            }
        )

    def _create_travel_plan(self, request: TravelRequest) -> TravelPlan:
        """Create focused travel plan based on structured request."""
        # Determine destinations
        destinations = self._select_destinations(request.destination, request.interests)

        # Build itinerary
        itinerary = self._build_itinerary(destinations, request.duration, request.interests)

        # Calculate budget
        budget_breakdown = self._calculate_budget(request.budget_range, request.duration, request.travel_style)

        # Find accommodations
        accommodations = self._find_accommodations(destinations, request.budget_range, request.travel_style)

        # Suggest activities
        activities = self._suggest_activities(destinations, request.interests, request.constraints)

        # Plan transportation
        transportation = self._plan_transportation(destinations, request.budget_range)

        # Generate recommendations
        recommendations = self._generate_recommendations(request)

        return TravelPlan(
            destinations=destinations,
            itinerary=itinerary,
            budget_breakdown=budget_breakdown,
            accommodations=accommodations,
            activities=activities,
            transportation=transportation,
            recommendations=recommendations
        )

    def _extract_destination(self, user_input: str) -> Optional[str]:
        """Extract destination from user input."""
        user_lower = user_input.lower()

        # Common destinations
        destinations = {
            'southeast asia': ['thailand', 'vietnam', 'cambodia', 'laos'],
            'europe': ['france', 'italy', 'spain', 'germany', 'uk'],
            'japan': ['tokyo', 'kyoto', 'osaka'],
            'south america': ['peru', 'chile', 'argentina', 'brazil']
        }

        for region, countries in destinations.items():
            if region in user_lower or any(country in user_lower for country in countries):
                return region.title()

        return None

    def _extract_budget(self, user_input: str, memories: List[Dict]) -> Optional[str]:
        """Extract budget range from input and memories."""
        user_lower = user_input.lower()

        if any(word in user_lower for word in ['budget', 'cheap', 'backpack']):
            return 'budget'
        elif any(word in user_lower for word in ['luxury', 'high-end', 'expensive']):
            return 'luxury'
        elif any(word in user_lower for word in ['mid-range', 'moderate']):
            return 'mid-range'

        # Check memories for budget preferences
        for memory in memories:
            memory_text = memory.get('text', '').lower()
            if 'budget' in memory_text or 'hostel' in memory_text:
                return 'budget'
            elif 'luxury' in memory_text or 'hotel' in memory_text:
                return 'luxury'

        return 'mid-range'  # Default

    def _extract_duration(self, user_input: str) -> Optional[str]:
        """Extract trip duration from user input."""
        user_lower = user_input.lower()

        if 'week' in user_lower:
            if 'two' in user_lower or '2' in user_lower:
                return '2 weeks'
            elif 'three' in user_lower or '3' in user_lower:
                return '3 weeks'
            else:
                return '1 week'
        elif 'month' in user_lower:
            return '1 month'
        elif 'day' in user_lower:
            if any(num in user_lower for num in ['10', 'ten']):
                return '10 days'
            else:
                return '1 week'

        return '2 weeks'  # Default

    def _extract_travel_style(self, user_input: str, memories: List[Dict]) -> Optional[str]:
        """Extract travel style from input and memories."""
        user_lower = user_input.lower()

        if any(word in user_lower for word in ['backpack', 'budget', 'hostel']):
            return 'backpacking'
        elif any(word in user_lower for word in ['luxury', 'resort', 'high-end']):
            return 'luxury'
        elif any(word in user_lower for word in ['family', 'kids', 'children']):
            return 'family'
        elif any(word in user_lower for word in ['business', 'work']):
            return 'business'

        return 'leisure'  # Default

    def _extract_interests(self, user_input: str, memories: List[Dict]) -> List[str]:
        """Extract interests from input and memories."""
        interests = []
        user_lower = user_input.lower()

        interest_keywords = {
            'food': ['food', 'culinary', 'restaurant', 'cuisine'],
            'adventure': ['adventure', 'hiking', 'climbing', 'outdoor'],
            'culture': ['culture', 'history', 'museum', 'art'],
            'nightlife': ['nightlife', 'bar', 'club', 'party'],
            'nature': ['nature', 'wildlife', 'beach', 'mountain'],
            'shopping': ['shopping', 'market', 'souvenir']
        }

        for interest, keywords in interest_keywords.items():
            if any(keyword in user_lower for keyword in keywords):
                interests.append(interest)

        # Check memories for additional interests
        for memory in memories:
            memory_text = memory.get('text', '').lower()
            for interest, keywords in interest_keywords.items():
                if any(keyword in memory_text for keyword in keywords) and interest not in interests:
                    interests.append(interest)

        return interests

    def _extract_constraints(self, user_input: str, conversation_context: Dict) -> List[str]:
        """Extract travel constraints from input and context."""
        constraints = []
        user_lower = user_input.lower()

        if any(word in user_lower for word in ['budget', 'cheap', 'limited money']):
            constraints.append('budget_limited')
        if any(word in user_lower for word in ['time', 'short', 'quick']):
            constraints.append('time_limited')
        if any(word in user_lower for word in ['visa', 'passport']):
            constraints.append('visa_requirements')
        if any(word in user_lower for word in ['health', 'medical', 'disability']):
            constraints.append('health_considerations')

        return constraints

    def _select_destinations(self, destination: Optional[str], interests: List[str]) -> List[str]:
        """Select specific destinations based on region and interests."""
        if not destination:
            return ['Thailand', 'Vietnam']  # Default

        destination_map = {
            'Southeast Asia': ['Thailand', 'Vietnam', 'Cambodia'],
            'Europe': ['France', 'Italy', 'Spain'],
            'Japan': ['Tokyo', 'Kyoto', 'Osaka'],
            'South America': ['Peru', 'Chile', 'Argentina']
        }

        return destination_map.get(destination, [destination])

    def _build_itinerary(self, destinations: List[str], duration: Optional[str], interests: List[str]) -> Dict[str, Any]:
        """Build day-by-day itinerary."""
        days = self._parse_duration_to_days(duration or '2 weeks')

        itinerary = {}
        days_per_destination = max(1, days // len(destinations))

        for i, dest in enumerate(destinations):
            start_day = i * days_per_destination + 1
            end_day = min((i + 1) * days_per_destination, days)

            itinerary[f'days_{start_day}_{end_day}'] = {
                'destination': dest,
                'activities': self._get_destination_activities(dest, interests),
                'highlights': self._get_destination_highlights(dest)
            }

        return itinerary

    def _calculate_budget(self, budget_range: Optional[str], duration: Optional[str], travel_style: Optional[str]) -> Dict[str, float]:
        """Calculate detailed budget breakdown."""
        days = self._parse_duration_to_days(duration or '2 weeks')

        budget_multipliers = {
            'budget': 1.0,
            'mid-range': 2.0,
            'luxury': 4.0
        }

        base_daily = 50  # Base daily budget in USD
        multiplier = budget_multipliers.get(budget_range or 'mid-range', 2.0)
        daily_budget = base_daily * multiplier

        return {
            'total': daily_budget * days,
            'daily_average': daily_budget,
            'accommodation': daily_budget * 0.4,
            'food': daily_budget * 0.3,
            'transportation': daily_budget * 0.2,
            'activities': daily_budget * 0.1
        }

    def _find_accommodations(self, destinations: List[str], budget_range: Optional[str], travel_style: Optional[str]) -> List[Dict[str, Any]]:
        """Find suitable accommodations."""
        accommodations = []

        for dest in destinations:
            if budget_range == 'budget':
                accommodations.append({
                    'destination': dest,
                    'type': 'hostel',
                    'price_range': '$15-30/night',
                    'amenities': ['WiFi', 'Shared kitchen', 'Common area']
                })
            elif budget_range == 'luxury':
                accommodations.append({
                    'destination': dest,
                    'type': 'luxury_hotel',
                    'price_range': '$150-300/night',
                    'amenities': ['Spa', 'Pool', 'Concierge', 'Fine dining']
                })
            else:
                accommodations.append({
                    'destination': dest,
                    'type': 'mid_range_hotel',
                    'price_range': '$50-100/night',
                    'amenities': ['WiFi', 'Breakfast', 'Gym', 'Restaurant']
                })

        return accommodations

    def _suggest_activities(self, destinations: List[str], interests: List[str], constraints: List[str]) -> List[Dict[str, Any]]:
        """Suggest activities based on interests and constraints."""
        activities = []

        for dest in destinations:
            dest_activities = self._get_destination_activities(dest, interests)

            for activity in dest_activities:
                activity_data = {
                    'destination': dest,
                    'activity': activity,
                    'duration': '2-4 hours',
                    'cost': 'Varies',
                    'suitable_for': interests
                }

                # Adjust for constraints
                if 'budget_limited' in constraints:
                    activity_data['cost'] = 'Free or low-cost'
                if 'time_limited' in constraints:
                    activity_data['duration'] = '1-2 hours'

                activities.append(activity_data)

        return activities

    def _plan_transportation(self, destinations: List[str], budget_range: Optional[str]) -> Dict[str, Any]:
        """Plan transportation between destinations."""
        if budget_range == 'budget':
            return {
                'international': 'Budget airlines',
                'local': ['Buses', 'Trains', 'Local transport'],
                'between_cities': 'Overland buses',
                'estimated_cost': '$200-400'
            }
        elif budget_range == 'luxury':
            return {
                'international': 'Full-service airlines',
                'local': ['Private transfers', 'Taxis', 'Car rental'],
                'between_cities': 'Domestic flights',
                'estimated_cost': '$800-1500'
            }
        else:
            return {
                'international': 'Standard airlines',
                'local': ['Trains', 'Buses', 'Taxis'],
                'between_cities': 'Trains or buses',
                'estimated_cost': '$400-800'
            }

    def _generate_recommendations(self, request: TravelRequest) -> List[str]:
        """Generate personalized recommendations."""
        recommendations = []

        if 'food' in request.interests:
            recommendations.append('Try local street food and cooking classes')
        if 'adventure' in request.interests:
            recommendations.append('Book outdoor activities in advance')
        if 'culture' in request.interests:
            recommendations.append('Visit museums and historical sites')

        if 'budget_limited' in request.constraints:
            recommendations.append('Look for free walking tours and activities')
        if 'time_limited' in request.constraints:
            recommendations.append('Focus on must-see highlights')

        recommendations.extend([
            'Check visa requirements well in advance',
            'Get travel insurance',
            'Download offline maps and translation apps'
        ])

        return recommendations

    def _parse_duration_to_days(self, duration: str) -> int:
        """Convert duration string to number of days."""
        duration_lower = duration.lower()

        if 'week' in duration_lower:
            if '2' in duration_lower or 'two' in duration_lower:
                return 14
            elif '3' in duration_lower or 'three' in duration_lower:
                return 21
            else:
                return 7
        elif 'month' in duration_lower:
            return 30
        elif 'day' in duration_lower:
            if '10' in duration_lower:
                return 10
            else:
                return 7

        return 14  # Default 2 weeks

    def _get_destination_activities(self, destination: str, interests: List[str]) -> List[str]:
        """Get activities for a destination based on interests."""
        activity_map = {
            'Thailand': {
                'food': ['Street food tours', 'Cooking classes', 'Night markets'],
                'adventure': ['Rock climbing', 'Jungle trekking', 'Scuba diving'],
                'culture': ['Temple visits', 'Traditional shows', 'Museums'],
                'default': ['Temple visits', 'Street food tours', 'Island hopping']
            },
            'Vietnam': {
                'food': ['Pho tours', 'Market visits', 'Cooking workshops'],
                'adventure': ['Ha Long Bay cruise', 'Motorbike tours', 'Hiking'],
                'culture': ['Historical sites', 'War museums', 'Traditional villages'],
                'default': ['Ha Long Bay cruise', 'Old Quarter walk', 'Cu Chi Tunnels']
            }
        }

        dest_activities = activity_map.get(destination, {})
        activities = []

        for interest in interests:
            activities.extend(dest_activities.get(interest, []))

        if not activities:
            activities = dest_activities.get('default', ['Sightseeing', 'Local exploration'])

        return list(set(activities))  # Remove duplicates

    def _get_destination_highlights(self, destination: str) -> List[str]:
        """Get main highlights for a destination."""
        highlights_map = {
            'Thailand': ['Grand Palace Bangkok', 'Phi Phi Islands', 'Chiang Mai temples'],
            'Vietnam': ['Ha Long Bay', 'Hoi An Ancient Town', 'Ho Chi Minh City'],
            'Cambodia': ['Angkor Wat', 'Phnom Penh', 'Siem Reap'],
            'France': ['Eiffel Tower', 'Louvre Museum', 'Palace of Versailles'],
            'Italy': ['Colosseum', 'Venice canals', 'Tuscany countryside']
        }

        return highlights_map.get(destination, [f'{destination} main attractions'])

    def _calculate_confidence(self, request: TravelRequest) -> float:
        """Calculate confidence score for the travel plan."""
        confidence = 0.7  # Base confidence

        if request.destination:
            confidence += 0.1
        if request.budget_range:
            confidence += 0.1
        if request.interests:
            confidence += 0.05 * len(request.interests)
        if request.duration:
            confidence += 0.05

        return min(confidence, 1.0)

    def _generate_early_conversation_response(self, user_input: str, missing_info: List[str]) -> str:
        """Generate response for early conversation when we need basic info."""
        user_lower = user_input.lower()

        # Check for specific mentions in user input to personalize response
        mentions = []
        if 'student' in user_lower or 'college' in user_lower or 'school' in user_lower:
            mentions.append('student')
        if 'spring break' in user_lower:
            mentions.append('spring_break')
        if 'k-pop' in user_lower or 'kpop' in user_lower:
            mentions.append('kpop')
        if 'hiking' in user_lower:
            mentions.append('hiking')
        if 'bubble tea' in user_lower or 'boba' in user_lower:
            mentions.append('bubble_tea')
        if 'la' in user_lower or 'los angeles' in user_lower:
            mentions.append('la')
        if 'affordable' in user_lower or 'budget' in user_lower or 'cheap' in user_lower:
            mentions.append('budget')

        # Generate personalized response based on mentions
        if 'student' in mentions and 'spring_break' in mentions:
            if 'kpop' in mentions and 'hiking' in mentions:
                return "I'm so stoked you're thinking of planning a trip for spring break! K-pop and hiking sounds like an amazing combination. Since you're a student, I totally get wanting to keep costs down. What you mean by 'affordable' - are we talking like under $1000 for the whole trip?"
            elif 'kpop' in mentions:
                return "Spring break with a K-pop focus sounds incredible! I love that you're into the music scene. Since you mentioned being a student, what's your budget looking like for this trip?"
            elif 'hiking' in mentions:
                return "A spring break trip with hiking sounds perfect! I can definitely help you find some amazing trails. What's your budget range, and how long of a trip are you thinking?"
            else:
                return "Spring break planning - so exciting! Since you're a student, I want to make sure we find something that fits your budget. What are you thinking cost-wise?"
        elif 'kpop' in mentions and 'hiking' in mentions:
            return "K-pop and hiking - what a cool combination! I can definitely help you find places that have both great music scenes and amazing trails. What's your budget looking like?"
        elif 'la' in mentions and 'bubble_tea' in mentions:
            return "I love that you mentioned that bubble tea shop in LA! Sounds like you've got some great taste. Are you thinking of staying in California or exploring other places too?"
        else:
            # Fallback to generic but still personalized
            responses = [
                "I'm excited to help you plan an amazing trip! To get started, could you tell me where you're thinking of going and what your budget looks like?",
                "That sounds like a great adventure! I'd love to help you plan this. What destinations are you considering, and do you have a budget in mind?",
                "Planning a trip is so exciting! Let me help you create something amazing. What's your dream destination, and what kind of budget are you working with?"
            ]
            import random
            return random.choice(responses)

    def _generate_mid_conversation_response(self, travel_request: TravelRequest, missing_info: List[str]) -> str:
        """Generate response when we have some info but need more details."""
        if 'duration' in missing_info:
            if travel_request.destination:
                return f"Great choice on {travel_request.destination}! How long are you planning to travel for? A week, two weeks, or longer?"
            else:
                return "How long are you planning to travel for? A week for spring break, or are you thinking longer?"
        elif 'interests' in missing_info:
            budget_text = f"your budget of {travel_request.budget_range}" if travel_request.budget_range else "keeping costs affordable"
            return f"Perfect! With {budget_text}, we can definitely make something work. What kind of experiences are you most interested in - adventure activities, cultural sites, food scenes, or relaxation?"
        elif 'budget' in missing_info:
            if travel_request.destination:
                return f"I love that you want to explore {travel_request.destination}! To help plan the perfect trip, what's your budget range looking like?"
            else:
                return "To help plan the perfect trip, what's your budget range looking like? Since you mentioned being a student, I want to make sure we keep it affordable!"
        else:
            # We have most info but user hasn't asked for plan yet
            interests_text = f"focusing on {', '.join(travel_request.interests)}" if travel_request.interests else "with your interests"
            destination_text = travel_request.destination or "your chosen destination"
            return f"That all sounds fantastic! I'm getting a great picture of what you're looking for - {destination_text} {interests_text}. Any other preferences or requirements I should know about?"

    def _generate_ready_response(self, travel_request: TravelRequest) -> str:
        """Generate response when we have all info but user hasn't asked for plan yet."""
        return f"Perfect! I have everything I need - {travel_request.destination} for {travel_request.duration} with a {travel_request.budget_range} budget, focusing on {', '.join(travel_request.interests)}. This is going to be an amazing trip! Ready for me to put together your detailed itinerary?"

    def _format_accommodations(self, accommodations: List[Dict[str, Any]]) -> str:
        """Format accommodations for display."""
        if not accommodations:
            return "• Mid-range hotels and guesthouses"

        formatted = []
        for acc in accommodations:
            formatted.append(f"• {acc.get('type', 'Hotel')}: {acc.get('description', 'Comfortable accommodations')}")
        return '\n'.join(formatted)

    def _format_activities(self, activities: List[Dict[str, Any]]) -> str:
        """Format activities for display."""
        if not activities:
            return "• Cultural site visits\n• Local food experiences\n• Adventure activities"

        formatted = []
        for activity in activities:
            formatted.append(f"• {activity.get('name', 'Activity')}: {activity.get('description', 'Exciting experience')}")
        return '\n'.join(formatted)

    def _format_transportation(self, transportation: Dict[str, Any]) -> str:
        """Format transportation for display."""
        lines = []
        if transportation.get('international'):
            lines.append(f"• International: {transportation['international']}")
        if transportation.get('local'):
            local_transport = transportation['local']
            if isinstance(local_transport, list):
                lines.append(f"• Local: {', '.join(local_transport)}")
            else:
                lines.append(f"• Local: {local_transport}")
        if transportation.get('estimated_cost'):
            lines.append(f"• Estimated cost: {transportation['estimated_cost']}")
        return '\n'.join(lines)

    def _format_recommendations(self, recommendations: List[str]) -> str:
        """Format recommendations for display."""
        return '\n'.join([f"• {rec}" for rec in recommendations])

    def _format_next_steps(self, next_steps: List[str]) -> str:
        """Format next steps for display."""
        return '\n'.join([f"• {step}" for step in next_steps])

    def _suggest_next_steps(self, plan: TravelPlan) -> List[str]:
        """Suggest next steps for trip planning."""
        return [
            'Book flights and accommodations',
            'Apply for visas if required',
            'Get travel insurance',
            'Research local customs and etiquette',
            'Download useful travel apps',
            'Notify bank of travel plans'
        ]

    def _refine_with_context(self, plan: TravelPlan, conversation_context: Dict) -> TravelPlan:
        """Apply conversation context to refine the plan."""
        # This would be called during initial processing if context is available
        return plan

    def _parse_refinement_context(self, refinement_context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse refinement context from conversation."""
        follow_up = refinement_context.get('follow_up_input', '')

        refinements = {}
        follow_up_lower = follow_up.lower()

        # Detect refinement needs
        if any(word in follow_up_lower for word in ['budget', 'cost', 'expensive', 'cheap']):
            refinements['budget_change'] = self._extract_budget_change(follow_up)

        if any(word in follow_up_lower for word in ['activity', 'adventure', 'relax']):
            refinements['activity_change'] = self._extract_activity_preferences(follow_up)

        if any(word in follow_up_lower for word in ['accommodation', 'hotel', 'hostel']):
            refinements['accommodation_change'] = self._extract_accommodation_preferences(follow_up)

        if any(word in follow_up_lower for word in ['destination', 'place', 'country']):
            refinements['destination_change'] = self._extract_destination_change(follow_up)

        return refinements

    def _apply_targeted_refinements(self, plan: TravelPlan, refinements: Dict[str, Any]) -> TravelPlan:
        """Apply specific refinements to the travel plan."""
        refined_plan = plan.copy()

        if 'budget_change' in refinements:
            refined_plan.budget_breakdown = self._adjust_budget(plan.budget_breakdown, refinements['budget_change'])
            refined_plan.accommodations = self._adjust_accommodations_for_budget(plan.accommodations, refinements['budget_change'])

        if 'activity_change' in refinements:
            refined_plan.activities = self._adjust_activities(plan.activities, refinements['activity_change'])

        if 'accommodation_change' in refinements:
            refined_plan.accommodations = self._adjust_accommodations(plan.accommodations, refinements['accommodation_change'])

        if 'destination_change' in refinements:
            # This would require more significant changes
            refined_plan.destinations = refinements['destination_change']
            refined_plan.itinerary = self._rebuild_itinerary_for_destinations(refined_plan.destinations)

        return refined_plan

    def _calculate_confidence_after_refinement(self, plan: TravelPlan, refinements: Dict[str, Any]) -> float:
        """Calculate confidence after applying refinements."""
        base_confidence = 0.8  # Higher after refinement

        # Increase confidence based on refinements applied
        refinement_boost = len(refinements) * 0.05

        return min(base_confidence + refinement_boost, 1.0)

    # Helper methods for refinements
    def _extract_budget_change(self, follow_up: str) -> str:
        """Extract budget change from follow-up."""
        follow_up_lower = follow_up.lower()
        if any(word in follow_up_lower for word in ['luxury', 'expensive', 'high-end']):
            return 'luxury'
        elif any(word in follow_up_lower for word in ['budget', 'cheap', 'save']):
            return 'budget'
        return 'mid-range'

    def _extract_activity_preferences(self, follow_up: str) -> List[str]:
        """Extract activity preferences from follow-up."""
        preferences = []
        follow_up_lower = follow_up.lower()

        if any(word in follow_up_lower for word in ['adventure', 'active', 'hiking']):
            preferences.append('adventure')
        if any(word in follow_up_lower for word in ['relax', 'spa', 'beach']):
            preferences.append('relaxation')
        if any(word in follow_up_lower for word in ['culture', 'museum', 'history']):
            preferences.append('culture')

        return preferences

    def _extract_accommodation_preferences(self, follow_up: str) -> str:
        """Extract accommodation preferences from follow-up."""
        follow_up_lower = follow_up.lower()
        if 'luxury' in follow_up_lower or 'hotel' in follow_up_lower:
            return 'luxury'
        elif 'hostel' in follow_up_lower or 'budget' in follow_up_lower:
            return 'budget'
        return 'mid-range'

    def _extract_destination_change(self, follow_up: str) -> List[str]:
        """Extract destination changes from follow-up."""
        # This would parse new destinations from the follow-up
        return []  # Simplified for now

    def _adjust_budget(self, current_budget: Dict[str, float], new_budget_level: str) -> Dict[str, float]:
        """Adjust budget breakdown for new budget level."""
        multipliers = {'budget': 0.5, 'mid-range': 1.0, 'luxury': 2.5}
        multiplier = multipliers.get(new_budget_level, 1.0)

        return {key: value * multiplier for key, value in current_budget.items()}

    def _adjust_accommodations_for_budget(self, accommodations: List[Dict], budget_level: str) -> List[Dict]:
        """Adjust accommodations for new budget level."""
        # Update accommodation types and prices based on budget
        return accommodations  # Simplified for now

    def _adjust_activities(self, activities: List[Dict], preferences: List[str]) -> List[Dict]:
        """Adjust activities based on new preferences."""
        # Filter and add activities based on preferences
        return activities  # Simplified for now

    def _adjust_accommodations(self, accommodations: List[Dict], preference: str) -> List[Dict]:
        """Adjust accommodations based on preference."""
        # Update accommodation types based on preference
        return accommodations  # Simplified for now

    def _rebuild_itinerary_for_destinations(self, destinations: List[str]) -> Dict[str, Any]:
        """Rebuild itinerary for new destinations."""
        # This would rebuild the entire itinerary
        return {}  # Simplified for now
