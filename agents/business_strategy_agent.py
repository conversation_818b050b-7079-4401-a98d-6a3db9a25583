#!/usr/bin/env python3
"""
Business Strategy Agent with semantic refinement capabilities.
Develops business plans and pivots based on market feedback.
"""
import logging
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class BusinessStrategyAgent:
    """
    Agent that develops business strategies and adapts to market changes.
    """
    
    def __init__(self):
        self.agent_name = "Business Strategy Agent"
        self.version = "1.0"
        self.capabilities = [
            "market_analysis",
            "business_planning",
            "competitive_analysis",
            "financial_modeling",
            "growth_strategy"
        ]
        
    def process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process initial business strategy request."""
        start_time = time.time()
        
        logger.info(f"📊 {self.agent_name} processing request")
        
        user_input = request_data.get('user_input', '')
        user_id = request_data.get('user_id', 'unknown')
        memories = request_data.get('memories', [])
        
        # Analyze business request
        business_details = self._analyze_business_request(user_input)
        logger.info(f"💼 Business analysis: {business_details}")
        
        # Apply memory context
        personalization = self._apply_memory_context(memories, business_details)
        
        # Generate business strategy
        business_strategy = self._generate_business_strategy(business_details, personalization)
        
        processing_time = (time.time() - start_time) * 1000
        
        result = {
            'agent_name': self.agent_name,
            'business_strategy': business_strategy,
            'personalization_used': personalization,
            'extracted_details': business_details,
            'processing_time_ms': processing_time,
            'timestamp': datetime.now().isoformat(),
            'refinement_suggestions': self._generate_refinement_suggestions(),
            'user_id': user_id
        }
        
        logger.info(f"✅ Business strategy completed in {processing_time:.1f}ms")
        return result
    
    def refine_response(self, original_result: Dict[str, Any], refinement_context: Dict[str, Any]) -> Dict[str, Any]:
        """Refine business strategy based on market feedback."""
        start_time = time.time()
        
        logger.info(f"🔄 {self.agent_name} refining response")
        
        follow_up_input = refinement_context.get('follow_up_input', '')
        semantic_gaps = refinement_context.get('semantic_gaps', [])
        
        # Analyze refinement needs
        refinement_needs = self._analyze_business_refinement_needs(
            original_result, follow_up_input, semantic_gaps
        )
        
        # Apply refinements
        refined_strategy = self._apply_business_refinements(
            original_result['business_strategy'], refinement_needs, follow_up_input
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        refined_result = {
            **original_result,
            'business_strategy': refined_strategy,
            'refinement_applied': refinement_needs,
            'refinement_processing_time_ms': processing_time,
            'refinement_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ Business strategy refined in {processing_time:.1f}ms")
        return refined_result
    
    def _analyze_business_request(self, user_input: str) -> Dict[str, Any]:
        """Analyze the business strategy request."""
        details = {
            'business_type': 'startup',
            'industry': 'technology',
            'stage': 'idea',
            'target_market': 'general',
            'business_model': 'b2c',
            'funding_stage': 'bootstrap'
        }
        
        user_lower = user_input.lower()
        
        # Detect business type
        if 'startup' in user_lower:
            details['business_type'] = 'startup'
        elif 'small business' in user_lower:
            details['business_type'] = 'small_business'
        elif 'enterprise' in user_lower:
            details['business_type'] = 'enterprise'
        
        # Detect industry
        industries = ['technology', 'healthcare', 'finance', 'retail', 'manufacturing', 'education', 'food']
        for industry in industries:
            if industry in user_lower:
                details['industry'] = industry
                break
        
        # Detect stage
        if 'idea' in user_lower or 'concept' in user_lower:
            details['stage'] = 'idea'
        elif 'mvp' in user_lower or 'prototype' in user_lower:
            details['stage'] = 'mvp'
        elif 'launch' in user_lower or 'launching' in user_lower:
            details['stage'] = 'launch'
        elif 'growth' in user_lower or 'scaling' in user_lower:
            details['stage'] = 'growth'
        
        # Detect business model
        if 'b2b' in user_lower or 'business to business' in user_lower:
            details['business_model'] = 'b2b'
        elif 'b2c' in user_lower or 'consumer' in user_lower:
            details['business_model'] = 'b2c'
        elif 'marketplace' in user_lower:
            details['business_model'] = 'marketplace'
        
        return details
    
    def _apply_memory_context(self, memories: List[Dict], business_details: Dict) -> Dict[str, Any]:
        """Apply memory context for personalized business strategy."""
        personalization = {
            'experience_level': 'first_time',
            'industry_knowledge': [],
            'previous_ventures': [],
            'network_strength': 'limited',
            'risk_tolerance': 'moderate'
        }
        
        for memory in memories:
            memory_text = memory.get('text', '').lower()
            
            if 'founded' in memory_text or 'started' in memory_text:
                personalization['experience_level'] = 'experienced'
            elif 'worked in' in memory_text or 'experience in' in memory_text:
                personalization['experience_level'] = 'some_experience'
            
            if 'industry' in memory_text or 'sector' in memory_text:
                personalization['industry_knowledge'].append(memory_text)
            
            if 'network' in memory_text or 'connections' in memory_text:
                personalization['network_strength'] = 'strong'
            
            if 'risk' in memory_text:
                if 'averse' in memory_text:
                    personalization['risk_tolerance'] = 'low'
                elif 'aggressive' in memory_text:
                    personalization['risk_tolerance'] = 'high'
        
        return personalization
    
    def _generate_business_strategy(self, details: Dict, personalization: Dict) -> Dict[str, Any]:
        """Generate comprehensive business strategy."""
        strategy = {
            'executive_summary': {
                'business_concept': f"{details['business_type'].title()} in {details['industry']} industry",
                'target_market': 'Identified customer segments with specific needs',
                'competitive_advantage': 'Unique value proposition and market positioning',
                'financial_projections': '3-year growth and profitability timeline'
            },
            'market_analysis': {
                'market_size': {
                    'tam': 'Total Addressable Market analysis',
                    'sam': 'Serviceable Addressable Market',
                    'som': 'Serviceable Obtainable Market'
                },
                'target_customers': {
                    'primary_segment': 'Early adopters and innovators',
                    'secondary_segment': 'Early majority market',
                    'customer_personas': 'Detailed buyer profiles'
                },
                'competitive_landscape': {
                    'direct_competitors': 'Companies offering similar solutions',
                    'indirect_competitors': 'Alternative solutions to same problem',
                    'competitive_advantages': 'Unique differentiators'
                }
            },
            'business_model': {
                'revenue_streams': self._get_revenue_model(details['business_model']),
                'cost_structure': 'Fixed and variable cost breakdown',
                'key_partnerships': 'Strategic alliances and vendor relationships',
                'distribution_channels': 'Customer acquisition and delivery methods'
            },
            'marketing_strategy': {
                'positioning': 'Brand positioning and messaging',
                'marketing_mix': '4Ps: Product, Price, Place, Promotion',
                'customer_acquisition': 'Lead generation and conversion strategies',
                'retention_strategy': 'Customer loyalty and lifetime value'
            },
            'operational_plan': {
                'team_structure': 'Key roles and hiring timeline',
                'technology_stack': 'Required systems and infrastructure',
                'supply_chain': 'Vendor and supplier relationships',
                'quality_control': 'Standards and processes'
            },
            'financial_projections': {
                'year_1': {'revenue': '$100K', 'expenses': '$80K', 'profit': '$20K'},
                'year_2': {'revenue': '$300K', 'expenses': '$200K', 'profit': '$100K'},
                'year_3': {'revenue': '$750K', 'expenses': '$450K', 'profit': '$300K'},
                'funding_requirements': 'Capital needs and use of funds'
            }
        }
        
        # Adjust based on stage
        if details['stage'] == 'idea':
            strategy['immediate_next_steps'] = {
                'market_validation': 'Customer interviews and surveys',
                'mvp_development': 'Minimum viable product creation',
                'team_building': 'Co-founder and early employee recruitment'
            }
        elif details['stage'] == 'growth':
            strategy['scaling_strategy'] = {
                'market_expansion': 'Geographic and demographic growth',
                'product_development': 'Feature enhancement and new products',
                'team_scaling': 'Department growth and management structure'
            }
        
        # Adjust for experience level
        if personalization['experience_level'] == 'first_time':
            strategy['founder_guidance'] = {
                'learning_resources': 'Recommended books, courses, and mentors',
                'common_pitfalls': 'Mistakes to avoid in early stages',
                'milestone_tracking': 'Key metrics and progress indicators'
            }
        
        return strategy
    
    def _get_revenue_model(self, business_model: str) -> Dict[str, str]:
        """Get appropriate revenue model based on business type."""
        models = {
            'b2b': {
                'primary': 'Subscription/SaaS model',
                'secondary': 'Professional services',
                'tertiary': 'Enterprise licensing'
            },
            'b2c': {
                'primary': 'Direct sales',
                'secondary': 'Subscription model',
                'tertiary': 'Advertising revenue'
            },
            'marketplace': {
                'primary': 'Transaction fees',
                'secondary': 'Listing fees',
                'tertiary': 'Premium services'
            }
        }
        return models.get(business_model, models['b2c'])
    
    def _generate_refinement_suggestions(self) -> List[str]:
        """Generate suggestions for potential refinements."""
        return [
            "Ask about specific market conditions and competition",
            "Inquire about funding requirements and timeline",
            "Check for regulatory or compliance considerations",
            "Verify target customer segments and needs",
            "Confirm business model and revenue assumptions"
        ]
    
    def _analyze_business_refinement_needs(
        self, 
        original_result: Dict, 
        follow_up: str, 
        semantic_gaps: List
    ) -> Dict[str, Any]:
        """Analyze what aspects of the business strategy need refinement."""
        needs = {}
        follow_up_lower = follow_up.lower()
        
        # Check for market changes
        if any(word in follow_up_lower for word in ['market', 'competition', 'competitor']):
            needs['market_adjustment'] = 'User mentioned market or competitive changes'
        
        # Check for funding changes
        if any(word in follow_up_lower for word in ['funding', 'investment', 'budget', 'money']):
            needs['funding_adjustment'] = 'User specified funding considerations'
        
        # Check for target market changes
        if any(word in follow_up_lower for word in ['customer', 'target', 'audience', 'segment']):
            needs['target_adjustment'] = 'User refined target market'
        
        # Check for business model changes
        if any(word in follow_up_lower for word in ['model', 'revenue', 'pricing', 'subscription']):
            needs['model_adjustment'] = 'User changed business model approach'
        
        # Check for timeline changes
        if any(word in follow_up_lower for word in ['timeline', 'schedule', 'launch', 'deadline']):
            needs['timeline_adjustment'] = 'User specified timeline requirements'
        
        return needs
    
    def _apply_business_refinements(
        self, 
        original_strategy: Dict, 
        refinement_needs: Dict, 
        follow_up: str
    ) -> Dict[str, Any]:
        """Apply specific refinements to the business strategy."""
        refined_strategy = original_strategy.copy()
        
        # Apply market adjustments
        if 'market_adjustment' in refinement_needs:
            refined_strategy['competitive_response'] = {
                'market_positioning': 'Adjusted positioning based on competitive landscape',
                'differentiation': 'Enhanced unique value propositions',
                'pricing_strategy': 'Competitive pricing analysis and recommendations'
            }
        
        # Apply funding adjustments
        if 'funding_adjustment' in refinement_needs:
            if 'bootstrap' in follow_up.lower():
                refined_strategy['bootstrap_strategy'] = {
                    'lean_operations': 'Minimal viable team and infrastructure',
                    'revenue_focus': 'Early monetization and cash flow positive',
                    'growth_pace': 'Sustainable growth without external funding'
                }
            elif 'investment' in follow_up.lower():
                refined_strategy['investment_strategy'] = {
                    'funding_rounds': 'Seed, Series A, B timeline and requirements',
                    'investor_targeting': 'VC firms and angel investors in industry',
                    'valuation_strategy': 'Company valuation and equity considerations'
                }
        
        # Apply target market adjustments
        if 'target_adjustment' in refinement_needs:
            refined_strategy['refined_targeting'] = {
                'customer_segmentation': 'Updated customer personas and segments',
                'market_sizing': 'Revised TAM/SAM/SOM calculations',
                'go_to_market': 'Adjusted customer acquisition strategies'
            }
        
        return refined_strategy
