from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON>out<PERSON>
from .views import (
    AvatarSettingsView, 
    AvatarOutfitView, 
    EnvironmentChangeView, 
    AnimationPlayView, 
    VoiceCommandView,
    TaskModelViewSet,
    MemoryViewSet,
    MemoryClusterViewSet,
    MemoryConfigurationView,
    MemoryBulkView
)

app_name = 'agents'

router = DefaultRouter()
router.register(r'tasks', TaskModelViewSet, basename='task')
router.register(r'memories', MemoryViewSet, basename='memory')
router.register(r'memory-clusters', MemoryClusterViewSet, basename='memory-cluster')

urlpatterns = [
    # Existing avatar-related URLs
    path('avatar/settings/', AvatarSettingsView.as_view(), name='avatar_settings'),
    path('avatar/outfit/', AvatarOutfitView.as_view(), name='avatar_outfit'),
    path('avatar/environment/', EnvironmentChangeView.as_view(), name='avatar_environment'),
    path('avatar/animation/', AnimationPlayView.as_view(), name='avatar_animation'),
    path('avatar/voice-command/', VoiceCommandView.as_view(), name='avatar_voice_command'),
    
    # Memory management URLs
    path('memory/configuration/', MemoryConfigurationView.as_view(), name='memory_configuration'),
    path('memory/bulk/', MemoryBulkView.as_view(), name='memory_bulk'),
    
    # Tasks and other URLs
    path('', include(router.urls)),
]