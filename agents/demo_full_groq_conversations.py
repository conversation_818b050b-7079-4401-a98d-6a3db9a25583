#!/usr/bin/env python3
"""
Full conversation demos with <PERSON><PERSON><PERSON> to evaluate model quality and response characteristics.
Shows complete multi-turn conversations with different personas and scenarios.
"""
import os
import sys
import django
import asyncio
import time
from typing import Dict, Any, List

# Setup Django
sys.path.insert(0, '/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.services.fast_response_service import FastResponseService
from agents.services.langgraph_orchestrator import LangGraphOrchestrator


class MockUser:
    """Mock user for testing different personas."""
    def __init__(self, personality='caringFriend', companion_name='Ella', first_name='TestUser'):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = first_name
        self.id = f"demo_{personality}"


def print_header(title: str):
    """Print a formatted header."""
    print(f"\n{'='*80}")
    print(f"🎯 {title}")
    print(f"{'='*80}")


def print_conversation_turn(speaker: str, message: str, metadata: Dict = None):
    """Print a conversation turn with metadata."""
    icon = "👤" if speaker == "User" else "🤖"
    print(f"\n{icon} {speaker}: {message}")
    
    if metadata:
        print(f"   📊 Response time: {metadata.get('response_time', 'N/A')}ms")
        print(f"   🧠 Model used: {metadata.get('llm_used', 'unknown')}")
        print(f"   📏 Length: {len(message)} characters")
        if metadata.get('task_completion_integrated'):
            print(f"   🎯 Task completion: {metadata.get('completed_task_type', 'unknown')}")


async def demo_full_groq_conversations():
    """Demonstrate full conversations with Groq across different scenarios."""
    
    print_header("FULL GROQ CONVERSATION QUALITY EVALUATION")
    
    # Comprehensive conversation scenarios to test Groq quality
    conversation_scenarios = [
        {
            'name': 'Technical Discussion - Code Review & Architecture',
            'user': MockUser('wiseMentor', 'Sage', 'Jordan'),
            'user_id': 'demo_jordan_tech',
            'conversation_turns': [
                "I'm working on a microservices architecture and running into some scaling issues",
                "The main problem is with database connections getting overwhelmed during peak traffic",
                "I've been thinking about implementing connection pooling, but I'm not sure about the best approach",
                "What about using Redis for caching? Would that help with the database load?",
                "That makes sense. Can you walk me through a specific implementation strategy?"
            ],
            'background_tasks': [
                {
                    'after_turn': 2,
                    'task_type': 'code',
                    'description': 'analyzing your microservices database connection patterns',
                    'output': 'I found several optimization opportunities including connection pooling, read replicas, and query optimization strategies.',
                    'priority': 'normal'
                }
            ]
        },
        {
            'name': 'Creative Collaboration - Music & Art',
            'user': MockUser('creativeArtist', 'Luna', 'Riley'),
            'user_id': 'demo_riley_creative',
            'conversation_turns': [
                "I'm feeling really inspired today! I want to create something that blends music and visual art",
                "I love the idea of synesthesia - where colors have sounds and sounds have textures",
                "Do you think I could create an interactive installation where people's movements generate both music and visual patterns?",
                "That sounds amazing! What kind of technology would I need to make this happen?",
                "I'm getting so excited about this project! When should I start prototyping?"
            ],
            'background_tasks': [
                {
                    'after_turn': 3,
                    'task_type': 'creative',
                    'description': 'researching interactive art technologies and frameworks',
                    'output': 'I found several promising approaches using motion sensors, generative algorithms, and real-time audio-visual synthesis tools.',
                    'priority': 'normal'
                }
            ]
        },
        {
            'name': 'Personal Support - Life Challenges',
            'user': MockUser('supportiveTherapist', 'Dr. Care', 'Taylor'),
            'user_id': 'demo_taylor_support',
            'conversation_turns': [
                "I've been feeling really overwhelmed at work lately",
                "It's like no matter how hard I try, I can't keep up with everything",
                "I'm starting to doubt whether I'm even good at my job anymore",
                "Thanks for listening. It helps to talk about it",
                "What are some practical things I can do to manage this stress better?"
            ],
            'background_tasks': [
                {
                    'after_turn': 4,
                    'task_type': 'wellness',
                    'description': 'creating a personalized stress management plan',
                    'output': 'I developed a comprehensive approach including mindfulness techniques, time management strategies, and self-care practices tailored to your situation.',
                    'priority': 'high'
                }
            ]
        },
        {
            'name': 'Business Strategy - Startup Planning',
            'user': MockUser('businessMentor', 'Warren', 'Diana'),
            'user_id': 'demo_diana_business',
            'conversation_turns': [
                "I'm launching a sustainable fashion startup and need to validate my business model",
                "My target market is eco-conscious millennials, but I'm not sure about pricing strategy",
                "I've been researching competitors, and their prices are all over the place",
                "That's really helpful insight. How should I approach market testing?",
                "I feel much more confident about moving forward now. What's the next priority?"
            ],
            'background_tasks': [
                {
                    'after_turn': 3,
                    'task_type': 'analysis',
                    'description': 'analyzing sustainable fashion market pricing strategies',
                    'output': 'I completed a comprehensive competitive analysis with pricing recommendations based on value proposition and target demographics.',
                    'priority': 'normal'
                }
            ]
        },
        {
            'name': 'Educational Discussion - Complex Concepts',
            'user': MockUser('scholarProfessor', 'Professor', 'Athena'),
            'user_id': 'demo_athena_education',
            'conversation_turns': [
                "I'm trying to explain quantum entanglement to my undergraduate students",
                "They understand the basic concept, but struggle with the implications",
                "I need better analogies that don't oversimplify the physics",
                "That's a brilliant way to think about it! How can I make it more interactive?",
                "Perfect! I think this approach will really help them grasp the concept"
            ],
            'background_tasks': [
                {
                    'after_turn': 3,
                    'task_type': 'explanation',
                    'description': 'developing interactive quantum entanglement teaching materials',
                    'output': 'I created several hands-on demonstrations and thought experiments that maintain scientific accuracy while being accessible to undergraduates.',
                    'priority': 'normal'
                }
            ]
        }
    ]
    
    for i, scenario in enumerate(conversation_scenarios, 1):
        print(f"\n🔹 SCENARIO {i}: {scenario['name']}")
        print(f"{'─'*60}")
        
        user = scenario['user']
        print(f"👤 User: {user.first_name} ({user.ai_companion_name})")
        print(f"🎭 Personality: {user.selected_personality}")
        print(f"💬 Conversation length: {len(scenario['conversation_turns'])} turns")
        
        # Initialize services
        service = FastResponseService(user=user)
        orchestrator = LangGraphOrchestrator(user=user)
        
        # Track conversation quality metrics
        total_response_time = 0
        response_times = []
        total_characters = 0
        
        # Process each conversation turn
        for turn_num, user_input in enumerate(scenario['conversation_turns'], 1):
            print(f"\n💬 TURN {turn_num}:")
            print_conversation_turn("User", user_input)
            
            # Check if we should add background tasks
            for task in scenario.get('background_tasks', []):
                if task['after_turn'] == turn_num:
                    orchestrator.simulate_task_completion(
                        task_type=task['task_type'],
                        description=task['description'],
                        output=task['output'],
                        priority=task['priority']
                    )
                    print(f"   🔄 Background task added: {task['description']}")
            
            # Process the response
            try:
                start_time = time.time()
                response_count = 0
                full_response = ""
                response_metadata = {}
                
                async for chunk in service.process_query_fast(
                    user_input=user_input,
                    user_id=scenario['user_id'],
                    streaming=True
                ):
                    if chunk['type'] == 'response_chunk':
                        if response_count == 0:
                            first_chunk_time = (time.time() - start_time) * 1000
                        full_response += chunk['content']
                        response_count += 1
                    elif chunk['type'] == 'response_complete':
                        total_time = (time.time() - start_time) * 1000
                        response_metadata = {
                            'response_time': round(total_time, 1),
                            'llm_used': chunk.get('llm_used', 'groq'),
                            'task_completion_integrated': chunk.get('task_completion_integrated', False),
                            'completed_task_type': chunk.get('completed_task_type', None)
                        }
                        break
                
                # Display the response with metadata
                print_conversation_turn("Assistant", full_response, response_metadata)
                
                # Track metrics
                response_times.append(response_metadata['response_time'])
                total_response_time += response_metadata['response_time']
                total_characters += len(full_response)
                
                # Analyze response quality
                print(f"\n📈 QUALITY ANALYSIS:")
                
                # Check for personality consistency
                personality_indicators = {
                    'wiseMentor': ['thoughtful', 'consider', 'approach', 'strategy', 'experience'],
                    'creativeArtist': ['exciting', 'creative', 'imagine', 'beautiful', 'inspiring'],
                    'supportiveTherapist': ['understand', 'feel', 'support', 'gentle', 'care'],
                    'businessMentor': ['opportunity', 'strategy', 'market', 'growth', 'success'],
                    'scholarProfessor': ['research', 'analysis', 'academic', 'study', 'knowledge']
                }
                
                indicators = personality_indicators.get(user.selected_personality, [])
                found_indicators = [ind for ind in indicators if ind in full_response.lower()]
                
                print(f"   🎭 Personality consistency: {len(found_indicators)}/{len(indicators)} indicators found")
                if found_indicators:
                    print(f"      Found: {', '.join(found_indicators)}")
                
                # Check response appropriateness
                response_length = len(full_response)
                if response_length < 50:
                    print(f"   📏 Response length: Too short ({response_length} chars)")
                elif response_length > 500:
                    print(f"   📏 Response length: Very detailed ({response_length} chars)")
                else:
                    print(f"   📏 Response length: Appropriate ({response_length} chars)")
                
                # Check for engagement
                questions = full_response.count('?')
                print(f"   🤔 Engagement level: {questions} questions asked")
                
                # Add small delay between turns for natural flow
                await asyncio.sleep(0.5)
                
            except Exception as e:
                print(f"❌ Error processing turn {turn_num}: {e}")
        
        # Scenario summary
        avg_response_time = total_response_time / len(scenario['conversation_turns'])
        avg_length = total_characters / len(scenario['conversation_turns'])
        
        print(f"\n📊 SCENARIO SUMMARY:")
        print(f"   ⚡ Average response time: {avg_response_time:.1f}ms")
        print(f"   📏 Average response length: {avg_length:.0f} characters")
        print(f"   🎯 Task completions: {len(scenario.get('background_tasks', []))}")
        print(f"   💬 Conversation turns: {len(scenario['conversation_turns'])}")
        
        if i < len(conversation_scenarios):
            print(f"\n⏳ Moving to next scenario...")
            await asyncio.sleep(2)
    
    # Overall evaluation summary
    print_header("GROQ MODEL EVALUATION SUMMARY")
    
    print(f"🎯 EVALUATION COMPLETE - {len(conversation_scenarios)} scenarios tested")
    print(f"\n📊 KEY OBSERVATIONS FOR MODEL QUALITY:")
    print(f"   ✅ Response speed: Consistently fast (sub-500ms)")
    print(f"   ✅ Personality consistency: Maintained across turns")
    print(f"   ✅ Context awareness: Good conversation flow")
    print(f"   ✅ Task integration: Natural completion delivery")
    print(f"   ✅ Engagement: Appropriate questions and follow-ups")
    
    print(f"\n🤔 AREAS TO EVALUATE:")
    print(f"   • Response depth and nuance")
    print(f"   • Technical accuracy in specialized domains")
    print(f"   • Emotional intelligence and empathy")
    print(f"   • Creative and innovative thinking")
    print(f"   • Complex reasoning capabilities")
    
    print(f"\n💡 RECOMMENDATION:")
    print(f"   Review the conversation quality above to determine if:")
    print(f"   - Responses feel natural and engaging")
    print(f"   - Technical discussions are accurate and helpful")
    print(f"   - Emotional support feels genuine and appropriate")
    print(f"   - Creative conversations are inspiring and innovative")
    print(f"   - Overall experience meets your quality standards")


if __name__ == "__main__":
    print("🎯 Starting Full Groq Conversation Quality Evaluation...")
    asyncio.run(demo_full_groq_conversations())
