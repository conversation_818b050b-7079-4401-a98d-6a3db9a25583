"""
Simple tests for LangGraph integration.
"""
import pytest
import pytest
from unittest.mock import Mock, patch
from django.test import TestCase

from agents.services.domain_router import Domain
from agents.services.agent_state import AgentState
from agents.services.memory_manager import MemoryManager
from agents.services.langgraph_orchestrator import LangGraphOrchestrator


@pytest.mark.unit
class TestAgentState(TestCase):
    """Test AgentState functionality."""
    
    def test_agent_state_creation(self):
        """Test creating an AgentState instance."""
        state = AgentState(
            user_id="test_user",
            original_input="Hello world",
            current_question="Hello world"
        )
        
        self.assertEqual(state["user_id"], "test_user")
        self.assertEqual(state["original_input"], "Hello world")
        self.assertEqual(state["current_question"], "Hello world")
    
    def test_agent_state_with_messages(self):
        """Test AgentState with messages."""
        from langchain_core.messages import HumanMessage, AIMessage
        
        messages = [
            HumanMessage(content="Hello"),
            AIMessage(content="Hi there!")
        ]
        
        state = AgentState(
            messages=messages,
            user_id="test_user"
        )
        
        self.assertEqual(len(state["messages"]), 2)
        self.assertEqual(state["messages"][0].content, "Hello")
        self.assertEqual(state["messages"][1].content, "Hi there!")


class TestMemoryManager(TestCase):
    """Test MemoryManager functionality."""
    
    def setUp(self):
        self.memory_manager = MemoryManager()
    
    def test_memory_manager_initialization(self):
        """Test memory manager initializes correctly."""
        self.assertIsNotNone(self.memory_manager.vectorstore)
        self.assertIsNotNone(self.memory_manager.memory_types)
        self.assertIn("semantic_profile", self.memory_manager.memory_types)
    
    def test_memory_types_defined(self):
        """Test that all expected memory types are defined."""
        expected_types = [
            "semantic_profile",
            "episodic_summary", 
            "general_knowledge",
            "explicit_memory",
            "task_related"
        ]
        
        for mem_type in expected_types:
            self.assertIn(mem_type, self.memory_manager.memory_types)
    
    def test_store_memory_validation(self):
        """Test memory storage validation."""
        # Test empty text
        result = self.memory_manager.store_memory(
            text="",
            memory_type="semantic_profile",
            user_id="test_user"
        )
        self.assertEqual(result, "")
        
        # Test invalid memory type
        result = self.memory_manager.store_memory(
            text="Valid text",
            memory_type="invalid_type",
            user_id="test_user"
        )
        # Should still work but with warning (defaulted to explicit_memory)
        self.assertIsInstance(result, str)


class TestLangGraphOrchestrator(TestCase):
    """Test LangGraph Orchestrator functionality."""
    
    def setUp(self):
        self.orchestrator = LangGraphOrchestrator()
    
    def test_orchestrator_initialization(self):
        """Test orchestrator initializes correctly."""
        self.assertIsNotNone(self.orchestrator.llm)
        self.assertIsNotNone(self.orchestrator.memory_manager)
        self.assertIsNotNone(self.orchestrator.graph)
    
    def test_graph_compilation(self):
        """Test that the LangGraph compiles successfully."""
        # The graph should be compiled during initialization
        self.assertIsNotNone(self.orchestrator.graph)
        
        # Test that we can get the graph structure
        graph_dict = self.orchestrator.graph.get_graph()
        self.assertIsNotNone(graph_dict)
    
    def test_initialize_turn_node(self):
        """Test the initialize turn node."""
        from langchain_core.messages import HumanMessage
        
        state = AgentState(
            messages=[HumanMessage(content="Test message")],
            user_id=None
        )
        
        result = self.orchestrator._initialize_turn_node(state)
        
        # Should set user_id if missing
        self.assertEqual(result["user_id"], "default_user")
        self.assertEqual(result["original_input"], "Test message")
        self.assertEqual(result["current_question"], "Test message")
    
    def test_flag_salient_input_node(self):
        """Test the flag salient input node."""
        state = AgentState(
            original_input="I love jazz music",
            user_id="test_user"
        )
        
        result = self.orchestrator._flag_salient_input_node(state)
        
        # Should process without error (may or may not flag depending on scorer availability)
        self.assertIsInstance(result, dict)
    
    def test_retrieve_memories_node(self):
        """Test the retrieve memories node."""
        state = AgentState(
            current_question="What music do I like?",
            user_id="test_user"
        )
        
        result = self.orchestrator._retrieve_memories_node(state)
        
        # Should process without error
        self.assertIsInstance(result, dict)
        # Should have retrieved_long_term_memories key (may be empty list)
        self.assertTrue("retrieved_long_term_memories" in result or len(result.get("retrieved_long_term_memories", [])) >= 0)


class TestDomainRouter(TestCase):
    """Test Domain Router functionality."""
    
    def test_domain_enum(self):
        """Test Domain enum values."""
        self.assertEqual(Domain.GENERAL, "general")
        self.assertEqual(Domain.DEV, "dev")
        self.assertEqual(Domain.MUSIC, "music")
        self.assertEqual(Domain.BUSINESS, "business")
        self.assertEqual(Domain.LEARNING, "learning")
        self.assertEqual(Domain.TRIVIA, "trivia")
        self.assertEqual(Domain.WEB, "web")


# Integration test that requires API keys
@pytest.mark.skip(reason="Requires OpenAI API key for full integration test")
class TestIntegration(TestCase):
    """Integration tests requiring API keys."""
    
    def setUp(self):
        """Set up test orchestrator."""
        self.orchestrator = LangGraphOrchestrator()
    
    @patch('agents.services.langgraph_orchestrator.LangGraphOrchestrator.llm')
    async def test_full_orchestration_flow(self, mock_llm):
        """Test complete orchestration flow."""
        orchestrator = LangGraphOrchestrator()
        
        # Mock the process_query to return a simple response
        orchestrator.process_query = Mock(return_value=[
            {'type': 'response_complete', 'content': 'Hello!', 'domain': 'general'}
        ])
        
        responses = []
        for response in orchestrator.process_query(
            user_input="Hello, how are you?",
            user_id="test_user",
            conversation_history=[],
            streaming=True
        ):
            responses.append(response)
            # Break after first response to avoid hanging
            break
        
        # Should have at least one response
        self.assertGreater(len(responses), 0)
        
        # Should have response structure
        self.assertIn('type', responses[0])
        
    @patch('agents.services.langgraph_orchestrator.LangGraphOrchestrator.graph')
    def test_simple_orchestration_flow(self, mock_graph):
        """Test simple orchestration flow without streaming."""
        orchestrator = LangGraphOrchestrator()
        
        # Test with a simple music query
        from langchain_core.messages import HumanMessage, AIMessage
        
        initial_state = AgentState(
            messages=[HumanMessage(content="I like jazz music")],
            user_id="test_user",
            original_input="I like jazz music",
            current_question="I like jazz music"
        )
        
        # Mock the graph invoke method
        mock_result = {
            "messages": [
                HumanMessage(content="I like jazz music"),
                AIMessage(content="That's great! Jazz is a wonderful genre.")
            ]
        }
        mock_graph.invoke.return_value = mock_result
        
        # Execute the graph
        result = mock_graph.invoke(initial_state)
        
        # Should have processed successfully
        self.assertIsNotNone(result)
        self.assertIn("messages", result)
        
        # Should have at least the original message
        self.assertGreater(len(result["messages"]), 0)
    
    @patch('agents.services.langgraph_orchestrator.LangGraphOrchestrator.llm')
    def test_memory_tools_integration(self, mock_llm):
        """Test that memory tools are properly integrated."""
        orchestrator = LangGraphOrchestrator()
        
        # Mock memory tools
        from agents.services.memory_tools import QueryMemoryTool
        query_tool = QueryMemoryTool(memory_manager=orchestrator.memory_manager)
        orchestrator.memory_tools = [query_tool]
        
        # Should have memory tools initialized
        self.assertIsNotNone(orchestrator.memory_tools)
        self.assertGreater(len(orchestrator.memory_tools), 0)
        
        # Should have query tool at minimum
        tool_names = [tool.name for tool in orchestrator.memory_tools]
        self.assertIn("query_memory", tool_names)