#!/usr/bin/env python3
"""
User Persona Simulator - Uses Groq LLM to simulate realistic user personas
with varied backgrounds, personalities, and communication styles.
"""
import os
import json
import random
import asyncio
from typing import Dict, List, Any, Optional
from groq import AsyncGroq

class UserPersona:
    """Represents a user persona with background, personality, and communication style."""
    
    def __init__(self, persona_data: Dict[str, Any]):
        self.name = persona_data['name']
        self.age = persona_data['age']
        self.occupation = persona_data['occupation']
        self.background = persona_data['background']
        self.personality_traits = persona_data['personality_traits']
        self.communication_style = persona_data['communication_style']
        self.interests = persona_data['interests']
        self.pain_points = persona_data['pain_points']
        self.goals = persona_data['goals']
        self.tech_savviness = persona_data['tech_savviness']
        self.conversation_patterns = persona_data['conversation_patterns']
        
    def get_system_prompt(self) -> str:
        """Generate system prompt for this persona."""
        return f"""You are {self.name}, a {self.age}-year-old {self.occupation}.

BACKGROUND: {self.background}

PERSONALITY TRAITS: {', '.join(self.personality_traits)}

COMMUNICATION STYLE: {self.communication_style}

INTERESTS: {', '.join(self.interests)}

CURRENT PAIN POINTS: {', '.join(self.pain_points)}

GOALS: {', '.join(self.goals)}

TECH SAVVINESS: {self.tech_savviness}

CONVERSATION PATTERNS: {', '.join(self.conversation_patterns)}

You are having a conversation with an AI assistant. Respond naturally as this character would, using their communication style, referencing their background when relevant, and expressing their personality. Keep responses conversational and authentic to this persona. Don't break character or mention that you're roleplaying."""


class UserPersonaSimulator:
    """Simulates realistic user personas using Groq LLM."""
    
    def __init__(self):
        api_key = os.getenv('GROQ_API_KEY')
        if not api_key:
            print("⚠️ Warning: GROQ_API_KEY not found. Using fallback responses only.")
            self.client = None
        else:
            self.client = AsyncGroq(api_key=api_key)
        self.personas = self._create_diverse_personas()
        
    def _create_diverse_personas(self) -> List[UserPersona]:
        """Create a diverse set of user personas."""
        persona_configs = [
            {
                'name': 'Emma',
                'age': 20,
                'occupation': 'college student',
                'background': 'Junior at UC Berkeley studying computer science. Lives in dorms, tight budget, very social. From suburban Seattle, middle-class family.',
                'personality_traits': ['curious', 'energetic', 'slightly anxious', 'optimistic', 'social'],
                'communication_style': 'Casual, uses some slang, asks lots of questions, tends to overthink, uses "like" and "um" occasionally',
                'interests': ['coding', 'bubble tea', 'K-pop', 'hiking', 'video games', 'study abroad'],
                'pain_points': ['student debt stress', 'imposter syndrome', 'time management', 'dating anxiety'],
                'goals': ['land a good internship', 'maintain GPA', 'travel more', 'build cool projects'],
                'tech_savviness': 'High - CS major, comfortable with most tech',
                'conversation_patterns': ['asks follow-up questions', 'shares personal anecdotes', 'seeks validation', 'changes topics quickly']
            },
            {
                'name': 'Marcus',
                'age': 28,
                'occupation': 'fitness trainer and gym owner',
                'background': 'Former college football player, opened his own gym 3 years ago. Lives in Austin, Texas. Very disciplined lifestyle.',
                'personality_traits': ['motivated', 'disciplined', 'encouraging', 'straightforward', 'competitive'],
                'communication_style': 'Direct and motivational, uses fitness metaphors, encouraging tone, occasionally uses bro-speak',
                'interests': ['powerlifting', 'nutrition', 'business growth', 'football', 'meal prep', 'outdoor workouts'],
                'pain_points': ['business cash flow', 'work-life balance', 'client retention', 'equipment costs'],
                'goals': ['expand gym locations', 'help more people get fit', 'compete in powerlifting', 'financial stability'],
                'tech_savviness': 'Medium - uses apps for business but not super technical',
                'conversation_patterns': ['gives motivational advice', 'relates everything to fitness', 'asks about goals', 'shares success stories']
            },
            {
                'name': 'Sarah',
                'age': 35,
                'occupation': 'working mother and marketing manager',
                'background': 'Marketing manager at a mid-size company, mother of 7-year-old twins. Married, lives in suburbs of Chicago. Always juggling responsibilities.',
                'personality_traits': ['organized', 'caring', 'stressed', 'efficient', 'practical'],
                'communication_style': 'Efficient and to-the-point, mentions family often, multitasking mindset, occasionally frazzled',
                'interests': ['family activities', 'wine', 'yoga', 'cooking', 'home organization', 'weekend getaways'],
                'pain_points': ['time management', 'mom guilt', 'work-life balance', 'finding me-time', 'kids activities scheduling'],
                'goals': ['be present for kids', 'advance career', 'maintain health', 'plan family vacations', 'organize home better'],
                'tech_savviness': 'Medium-high - uses productivity apps, social media savvy',
                'conversation_patterns': ['mentions time constraints', 'asks for efficient solutions', 'shares parenting challenges', 'seeks practical advice']
            },
            {
                'name': 'Tyler',
                'age': 26,
                'occupation': 'investment banker',
                'background': 'Works 80+ hour weeks at a top investment bank in NYC. Ivy League graduate, high achiever, expensive lifestyle.',
                'personality_traits': ['ambitious', 'analytical', 'impatient', 'competitive', 'status-conscious'],
                'communication_style': 'Fast-paced, uses financial jargon, direct, time-conscious, occasionally arrogant',
                'interests': ['stock market', 'expensive cars', 'fine dining', 'networking', 'luxury travel', 'watches'],
                'pain_points': ['work stress', 'long hours', 'relationship struggles', 'health neglect', 'burnout'],
                'goals': ['make MD by 30', 'buy a Porsche', 'network with high-net-worth individuals', 'optimize everything'],
                'tech_savviness': 'High - uses financial tools, productivity apps, latest gadgets',
                'conversation_patterns': ['mentions being busy', 'asks about ROI/efficiency', 'name-drops', 'wants premium options']
            },
            {
                'name': 'David',
                'age': 42,
                'occupation': 'senior software engineer',
                'background': 'Senior engineer at a tech company, 15+ years experience. Lives in Bay Area, owns a home, married with teenage kids.',
                'personality_traits': ['logical', 'detail-oriented', 'introverted', 'helpful', 'perfectionist'],
                'communication_style': 'Technical and precise, asks detailed questions, methodical, uses tech analogies',
                'interests': ['programming', 'open source', 'home automation', 'board games', 'sci-fi', 'coffee'],
                'pain_points': ['keeping up with new tech', 'work-life balance', 'team management', 'legacy code'],
                'goals': ['mentor junior developers', 'contribute to open source', 'optimize home setup', 'plan retirement'],
                'tech_savviness': 'Very high - expert level, builds own tools',
                'conversation_patterns': ['asks technical details', 'suggests optimizations', 'shares technical insights', 'methodical problem-solving']
            },
            {
                'name': 'Jessica',
                'age': 29,
                'occupation': 'freelance graphic designer',
                'background': 'Creative freelancer working from home in Portland. Art school graduate, values creativity and flexibility over high income.',
                'personality_traits': ['creative', 'independent', 'artistic', 'flexible', 'environmentally conscious'],
                'communication_style': 'Expressive and creative, uses visual metaphors, thoughtful, values aesthetics',
                'interests': ['design trends', 'sustainability', 'coffee culture', 'indie music', 'vintage fashion', 'local art'],
                'pain_points': ['inconsistent income', 'client management', 'creative blocks', 'isolation'],
                'goals': ['build consistent client base', 'create meaningful work', 'travel for inspiration', 'sustainable lifestyle'],
                'tech_savviness': 'High for design tools, medium for general tech',
                'conversation_patterns': ['discusses aesthetics', 'values authenticity', 'asks about creative aspects', 'environmentally conscious choices']
            },
            # NEW PERSONAS - 10 additional diverse characters
            {
                'name': 'Dr. Amara',
                'age': 38,
                'occupation': 'emergency room physician',
                'background': 'ER doctor at a major hospital in Miami. Medical school graduate, works night shifts, high-stress environment. Single, dedicated to her career.',
                'personality_traits': ['decisive', 'compassionate', 'analytical', 'resilient', 'direct'],
                'communication_style': 'Clear and concise, medical terminology, time-sensitive mindset, empathetic but professional',
                'interests': ['medical research', 'salsa dancing', 'travel medicine', 'fitness', 'medical conferences', 'volunteer work'],
                'pain_points': ['burnout', 'work-life balance', 'emotional toll', 'long hours', 'administrative burden'],
                'goals': ['advance to department head', 'publish research', 'maintain physical health', 'help underserved communities'],
                'tech_savviness': 'High - uses medical software, research databases, telemedicine',
                'conversation_patterns': ['asks diagnostic questions', 'seeks evidence-based solutions', 'time-conscious', 'focuses on outcomes']
            },
            {
                'name': 'Carlos',
                'age': 45,
                'occupation': 'restaurant owner and chef',
                'background': 'Owns a family Mexican restaurant in San Antonio. Immigrated from Mexico 20 years ago, built business from scratch. Married with three kids.',
                'personality_traits': ['passionate', 'hardworking', 'family-oriented', 'traditional', 'proud'],
                'communication_style': 'Warm and expressive, uses food metaphors, family-focused, occasionally mixes Spanish phrases',
                'interests': ['authentic cuisine', 'family traditions', 'soccer', 'community events', 'cooking techniques', 'local suppliers'],
                'pain_points': ['rising food costs', 'staff turnover', 'competition', 'work-life balance', 'health regulations'],
                'goals': ['expand restaurant', 'pass business to kids', 'preserve family recipes', 'support local community'],
                'tech_savviness': 'Medium - uses POS systems, social media for business, learning digital marketing',
                'conversation_patterns': ['relates to family experiences', 'uses food analogies', 'values tradition', 'community-minded']
            },
            {
                'name': 'Luna',
                'age': 24,
                'occupation': 'environmental science graduate student',
                'background': 'PhD student at UC Davis studying climate change. Vegan, activist, lives in shared housing. Passionate about sustainability.',
                'personality_traits': ['idealistic', 'analytical', 'passionate', 'environmentally conscious', 'collaborative'],
                'communication_style': 'Academic but accessible, uses environmental terminology, passionate about causes, data-driven',
                'interests': ['climate research', 'renewable energy', 'zero waste living', 'hiking', 'environmental policy', 'plant-based cooking'],
                'pain_points': ['climate anxiety', 'funding stress', 'research pressure', 'feeling overwhelmed by global issues'],
                'goals': ['complete PhD', 'influence policy', 'reduce carbon footprint', 'educate others', 'find sustainable career'],
                'tech_savviness': 'High - research tools, data analysis, environmental monitoring apps',
                'conversation_patterns': ['cites research', 'asks about environmental impact', 'seeks sustainable solutions', 'collaborative approach']
            },
            {
                'name': 'Robert',
                'age': 67,
                'occupation': 'retired teacher and grandfather',
                'background': 'Recently retired high school history teacher from small town in Ohio. Married 40+ years, four grandchildren. Learning to use technology.',
                'personality_traits': ['patient', 'wise', 'curious', 'traditional', 'nurturing'],
                'communication_style': 'Thoughtful and measured, uses teaching analogies, asks clarifying questions, values learning',
                'interests': ['history', 'gardening', 'woodworking', 'grandchildren', 'reading', 'community volunteering'],
                'pain_points': ['technology learning curve', 'health concerns', 'staying relevant', 'fixed income'],
                'goals': ['stay connected with family', 'learn new skills', 'maintain health', 'share knowledge', 'travel with spouse'],
                'tech_savviness': 'Low-medium - learning smartphones, video calls, basic internet',
                'conversation_patterns': ['asks for step-by-step guidance', 'relates to teaching experience', 'values patience', 'seeks understanding']
            },
            {
                'name': 'Zoe',
                'age': 19,
                'occupation': 'social media influencer and college student',
                'background': 'Fashion and lifestyle influencer with 500K followers. Studies communications at NYU. Gen Z digital native.',
                'personality_traits': ['confident', 'creative', 'trend-aware', 'social', 'ambitious'],
                'communication_style': 'Trendy language, uses emojis conceptually, fast-paced, visual thinking, social media savvy',
                'interests': ['fashion trends', 'content creation', 'social media strategy', 'photography', 'brand partnerships', 'travel'],
                'pain_points': ['algorithm changes', 'content burnout', 'online criticism', 'balancing school and work'],
                'goals': ['grow following', 'graduate college', 'build personal brand', 'secure brand deals', 'stay authentic'],
                'tech_savviness': 'Very high - expert in social platforms, content creation tools, analytics',
                'conversation_patterns': ['thinks in content opportunities', 'asks about trends', 'visual-first approach', 'engagement-focused']
            },
            {
                'name': 'Alex',
                'age': 33,
                'occupation': 'cybersecurity analyst',
                'background': 'Works for a Fortune 500 company in Denver. Former military, methodical and security-focused. Lives alone, enjoys outdoor activities.',
                'personality_traits': ['analytical', 'cautious', 'detail-oriented', 'independent', 'logical'],
                'communication_style': 'Precise and technical, security-minded, asks about risks and safeguards, methodical approach',
                'interests': ['cybersecurity', 'hiking', 'home automation', 'privacy tools', 'mountain biking', 'craft beer'],
                'pain_points': ['constant threat landscape changes', 'work stress', 'explaining security to non-tech people'],
                'goals': ['advance to security architect', 'build secure home setup', 'stay current with threats', 'work-life balance'],
                'tech_savviness': 'Very high - expert level, builds security tools, privacy-focused',
                'conversation_patterns': ['asks about security implications', 'methodical problem-solving', 'risk assessment mindset', 'technical precision']
            },
            {
                'name': 'Maya',
                'age': 31,
                'occupation': 'yoga instructor and wellness coach',
                'background': 'Certified yoga instructor in Boulder, Colorado. Former corporate lawyer who changed careers. Focuses on mindfulness and holistic wellness.',
                'personality_traits': ['calm', 'mindful', 'empathetic', 'spiritual', 'balanced'],
                'communication_style': 'Gentle and mindful, uses wellness terminology, holistic thinking, encouraging and supportive',
                'interests': ['yoga practice', 'meditation', 'holistic health', 'nature', 'mindfulness', 'plant-based nutrition'],
                'pain_points': ['irregular income', 'building client base', 'balancing business and practice', 'seasonal fluctuations'],
                'goals': ['grow wellness practice', 'help others find balance', 'deepen spiritual practice', 'create online courses'],
                'tech_savviness': 'Medium - uses wellness apps, online teaching platforms, social media for business',
                'conversation_patterns': ['focuses on well-being', 'asks about balance', 'holistic approach', 'mindful communication']
            },
            {
                'name': 'Jordan',
                'age': 27,
                'occupation': 'startup founder',
                'background': 'Founded a fintech startup in San Francisco. MBA from Wharton, former consultant. High-pressure environment, seeking rapid growth.',
                'personality_traits': ['ambitious', 'innovative', 'risk-taking', 'strategic', 'intense'],
                'communication_style': 'Fast-paced and strategic, uses business jargon, growth-focused, data-driven, time-conscious',
                'interests': ['entrepreneurship', 'venture capital', 'technology trends', 'networking', 'productivity hacks', 'market analysis'],
                'pain_points': ['funding pressure', 'scaling challenges', 'competition', 'work-life balance', 'team building'],
                'goals': ['achieve product-market fit', 'raise Series A', 'scale team', 'disrupt financial services', 'eventual IPO'],
                'tech_savviness': 'Very high - uses cutting-edge tools, analytics platforms, automation',
                'conversation_patterns': ['thinks strategically', 'asks about scalability', 'ROI-focused', 'fast decision making']
            },
            {
                'name': 'Grace',
                'age': 52,
                'occupation': 'nonprofit director',
                'background': 'Runs a homeless shelter in Seattle. Social work background, dedicated to helping others. Divorced, adult children.',
                'personality_traits': ['compassionate', 'dedicated', 'resourceful', 'patient', 'advocacy-minded'],
                'communication_style': 'Empathetic and mission-focused, uses social work terminology, collaborative approach, values-driven',
                'interests': ['social justice', 'community organizing', 'grant writing', 'volunteer coordination', 'policy advocacy', 'reading'],
                'pain_points': ['funding challenges', 'bureaucracy', 'emotional toll', 'resource limitations', 'staff burnout'],
                'goals': ['expand shelter capacity', 'secure sustainable funding', 'influence policy', 'support staff well-being'],
                'tech_savviness': 'Medium - uses nonprofit management software, grant databases, basic social media',
                'conversation_patterns': ['focuses on impact', 'collaborative problem-solving', 'resource-conscious', 'mission-driven']
            },
            {
                'name': 'Kai',
                'age': 22,
                'occupation': 'gaming streamer and esports player',
                'background': 'Professional gamer and Twitch streamer from Los Angeles. Dropped out of college to pursue gaming career. Lives with roommates.',
                'personality_traits': ['competitive', 'entertaining', 'tech-savvy', 'social', 'perfectionist'],
                'communication_style': 'Gaming terminology, energetic, uses internet slang, performance-focused, community-oriented',
                'interests': ['competitive gaming', 'streaming technology', 'game development', 'anime', 'tech hardware', 'online communities'],
                'pain_points': ['income volatility', 'performance pressure', 'online toxicity', 'health from long gaming sessions'],
                'goals': ['join professional esports team', 'grow streaming audience', 'diversify income', 'maintain peak performance'],
                'tech_savviness': 'Very high - expert in gaming tech, streaming software, hardware optimization',
                'conversation_patterns': ['uses gaming analogies', 'performance optimization mindset', 'community-focused', 'tech enthusiast']
            }
        ]
        
        return [UserPersona(config) for config in persona_configs]
    
    def get_random_persona(self) -> UserPersona:
        """Get a random persona for simulation."""
        return random.choice(self.personas)
    
    def get_persona_by_name(self, name: str) -> Optional[UserPersona]:
        """Get a specific persona by name."""
        for persona in self.personas:
            if persona.name.lower() == name.lower():
                return persona
        return None
    
    async def simulate_user_response(
        self, 
        persona: UserPersona, 
        ai_message: str, 
        conversation_context: str = "",
        scenario_context: str = ""
    ) -> str:
        """Simulate how this persona would respond to an AI message."""
        
        system_prompt = persona.get_system_prompt()
        
        if scenario_context:
            system_prompt += f"\n\nSCENARIO CONTEXT: {scenario_context}"
        
        messages = [
            {"role": "system", "content": system_prompt},
        ]
        
        if conversation_context:
            messages.append({"role": "user", "content": f"Previous conversation context: {conversation_context}"})
        
        messages.append({
            "role": "user", 
            "content": f"The AI assistant just said: '{ai_message}'\n\nRespond naturally as {persona.name} would. Keep it conversational and authentic to your character."
        })
        
        # If no API client, use fallback immediately
        if not self.client:
            print(f"🎭 Using fallback response for {persona.name} (no API key)")
        else:
            try:
                response = await self.client.chat.completions.create(
                    model="llama-3.3-70b-versatile",  # Production model with better quality
                    messages=messages,
                    temperature=0.9,  # Higher temperature for more variety
                    max_tokens=150,
                    top_p=0.9
                )

                return response.choices[0].message.content.strip()

            except Exception as e:
                print(f"⚠️ Groq API Error for {persona.name}: {e}")

        # Fallback responses with more variety
            import random

            # Add conversation context to responses
            turn_count = len(conversation_context.split("->")) if conversation_context else 1

            fallback_sets = {
                'Emma': [
                    "Oh wow, that's really cool! Can you explain more?",
                    "I'm super curious about this - what else should I know?",
                    "That sounds amazing! Like, what would be the next steps?",
                    "Hmm, I'm trying to wrap my head around this. Can you break it down?",
                    "This is so interesting! I have like a million questions now.",
                    "Wait, so if I understand correctly...",
                    "Actually, I'm a bit confused about one thing...",
                    "Oh! That reminds me of something I learned in class!"
                ] if turn_count <= 3 else [
                    "Okay, I think I'm getting it now. So basically...",
                    "This is starting to make sense! Can we move forward?",
                    "Alright, I'm convinced. What do we do next?",
                    "Perfect! This sounds like exactly what I need."
                ],
                'Marcus': [
                    "That sounds solid, bro. What's the game plan?",
                    "I'm feeling this approach. How do we execute?",
                    "Alright, that's what I'm talking about! What's next?",
                    "This could be a game-changer. Walk me through it.",
                    "I like where this is headed. Let's push forward.",
                    "Yeah, I can see the potential here. Break it down for me.",
                    "That's the kind of thinking I respect. Keep going."
                ] if turn_count <= 3 else [
                    "Alright, let's stop talking and start doing. What's the plan?",
                    "I'm ready to commit. Let's make this happen.",
                    "Perfect. Let's get this program rolling.",
                    "Sounds like we have a winner. Let's execute."
                ],
                'Sarah': [
                    "That could work, but I need to think about timing with everything else.",
                    "Interesting idea, but how does this fit with my schedule?",
                    "I like it, but I need to consider the family impact.",
                    "That's promising, though I'll need to juggle some priorities.",
                    "Good point - let me think about how to make this work efficiently."
                ],
                'Tyler': [
                    "What's the ROI on this? Show me the numbers.",
                    "Time is money - what's the bottom line here?",
                    "I need to see the metrics before I commit.",
                    "What's the upside potential? Give me the analysis.",
                    "Cut to the chase - is this worth my investment?"
                ],
                'David': [
                    "Can you provide more technical details about the implementation?",
                    "What's the architecture behind this solution?",
                    "I need to understand the technical specifications better.",
                    "How does this integrate with existing systems?",
                    "What are the performance implications of this approach?"
                ],
                'Jessica': [
                    "I love the creative potential! How do we make it visually stunning?",
                    "This has such artistic possibilities - what's the aesthetic vision?",
                    "How can we make this more authentic and meaningful?",
                    "What about the visual storytelling aspect?",
                    "I'm excited about this! How do we bring the creative vision to life?"
                ],
                # New personas fallback responses
                'Dr. Amara': [
                    "What's the evidence base for this approach?",
                    "How does this impact patient outcomes?",
                    "I need to understand the clinical implications.",
                    "What are the potential risks and benefits?",
                    "Can you provide more specific medical details?"
                ],
                'Carlos': [
                    "How does this help my family and restaurant?",
                    "What's the cost-benefit for a small business like mine?",
                    "Will this work with traditional approaches?",
                    "How can I implement this without disrupting operations?",
                    "Does this respect our family values and traditions?"
                ],
                'Luna': [
                    "What's the environmental impact of this solution?",
                    "How does this align with sustainability goals?",
                    "Can you share the research behind this?",
                    "What are the long-term ecological implications?",
                    "How can we make this more environmentally friendly?"
                ],
                'Robert': [
                    "Can you explain this step-by-step?",
                    "How does this compare to traditional methods?",
                    "I need more time to understand this fully.",
                    "What would you recommend for someone my age?",
                    "Can you break this down in simpler terms?"
                ],
                'Zoe': [
                    "How would this look on social media?",
                    "What's the visual potential for content creation?",
                    "How can I make this engaging for my audience?",
                    "What are the trending aspects of this?",
                    "How does this fit with current social media trends?"
                ],
                'Alex': [
                    "What are the security implications of this?",
                    "How do we protect against potential vulnerabilities?",
                    "What's the risk assessment for this approach?",
                    "Can you detail the technical security measures?",
                    "How does this maintain privacy and data protection?"
                ],
                'Maya': [
                    "How does this support overall well-being?",
                    "What's the mindful approach to this?",
                    "How can we maintain balance while implementing this?",
                    "What are the holistic benefits?",
                    "How does this align with wellness principles?"
                ],
                'Jordan': [
                    "What's the scalability potential here?",
                    "How does this impact our growth metrics?",
                    "What's the competitive advantage?",
                    "Can you show me the business case?",
                    "How quickly can we implement and see ROI?"
                ],
                'Grace': [
                    "How does this serve our community mission?",
                    "What's the social impact of this approach?",
                    "How can we make this accessible to those in need?",
                    "What resources would this require?",
                    "How does this align with our values and goals?"
                ],
                'Kai': [
                    "How does this optimize performance?",
                    "What's the competitive edge this gives?",
                    "How can I level up with this?",
                    "What's the meta strategy here?",
                    "How does this improve my game/content?"
                ]
            }

            responses = fallback_sets.get(persona.name, ["That's interesting, tell me more."])
            return random.choice(responses)
    
    async def simulate_initial_request(
        self, 
        persona: UserPersona, 
        topic: str,
        agent_type: str = "general"
    ) -> str:
        """Simulate how this persona would initially request help with a topic."""
        
        system_prompt = persona.get_system_prompt()
        
        topic_contexts = {
            'travel': f"You want to plan a trip or vacation. Consider your budget, interests, and lifestyle.",
            'fitness': f"You want help with fitness, exercise, or health goals. Consider your current fitness level and goals.",
            'code': f"You need help with programming, code review, or technical issues. Consider your technical background.",
            'writing': f"You want help with creative writing, storytelling, or content creation.",
            'business': f"You need help with business strategy, planning, or professional development."
        }
        
        context = topic_contexts.get(agent_type, f"You need help with {topic}")
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"{context}. Make an initial request to an AI assistant for help. Be natural and authentic to your character. Don't be overly detailed - just make a realistic opening request like a real person would."}
        ]
        
        # If no API client, use fallback immediately
        if not self.client:
            print(f"🎭 Using fallback initial request for {persona.name} (no API key)")
        else:
            try:
                response = await self.client.chat.completions.create(
                    model="llama-3.3-70b-versatile",  # Production model with better quality
                    messages=messages,
                    temperature=0.9,
                    max_tokens=120,
                    top_p=0.9
                )

                return response.choices[0].message.content.strip()

            except Exception as e:
                print(f"⚠️ Groq API Error for {persona.name} initial request: {e}")

        # Persona-specific fallback requests
            import random

            fallback_requests = {
                'travel': [
                    "Hi! I'm looking to plan a trip and could use some help figuring out the details.",
                    "I want to plan a vacation but I'm not sure where to start.",
                    "Can you help me organize a trip? I have some ideas but need guidance.",
                    "I'm thinking about traveling somewhere new - any suggestions?"
                ],
                'fitness': [
                    "Hey, I want to get in better shape and need some guidance on where to start.",
                    "I'm looking to improve my fitness routine - can you help?",
                    "I want to start working out but don't know the best approach.",
                    "Can you help me design a workout plan that actually works?"
                ],
                'code': [
                    "I need help reviewing some code I've been working on.",
                    "Can you take a look at my code and give me feedback?",
                    "I'm working on a programming project and could use a second opinion.",
                    "I need someone to review my code for potential issues."
                ],
                'writing': [
                    "I'm working on a creative writing project and could use some assistance.",
                    "Can you help me with a writing project I'm developing?",
                    "I need guidance on a creative piece I'm working on.",
                    "I'm stuck on a writing project - can you help me brainstorm?"
                ],
                'business': [
                    "I need help developing a business strategy for my company.",
                    "Can you assist with creating a business plan?",
                    "I'm looking for guidance on business strategy and growth.",
                    "I need help with strategic planning for my business."
                ]
            }

            requests = fallback_requests.get(agent_type, [f"I need help with {topic}."])
            return random.choice(requests)
    
    def get_persona_summary(self, persona: UserPersona) -> str:
        """Get a brief summary of a persona for display."""
        return f"{persona.name} ({persona.age}) - {persona.occupation}: {', '.join(persona.personality_traits[:3])}"
