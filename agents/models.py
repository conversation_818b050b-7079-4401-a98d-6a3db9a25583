from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()

class TaskModel(models.Model):
    """
    Represents a task with detailed tracking and phase management.
    """
    TASK_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('needs_intervention', 'Needs Intervention'),
        ('cancelled', 'Cancelled'),
    ]

    TASK_PHASE_CHOICES = [
        ('initialization', 'Initialization'),
        ('analysis', 'Analysis'),
        ('planning', 'Planning'),
        ('execution', 'Execution'),
        ('debugging', 'Debugging'),
        ('testing', 'Testing'),
        ('finalization', 'Finalization'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tasks')
    
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    
    status = models.CharField(
        max_length=30, 
        choices=TASK_STATUS_CHOICES, 
        default='pending'
    )
    progress = models.FloatField(default=0.0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    phases = models.JSONField(default=list)
    
    error_message = models.TextField(blank=True, null=True)
    intervention_message = models.TextField(blank=True, null=True)
    
    class Meta:
        verbose_name = 'Task'
        verbose_name_plural = 'Tasks'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.title} ({self.status})"
    
    def update_status(self, new_status, error_message=None, intervention_message=None):
        """
        Update task status with optional error or intervention messages.
        """
        self.status = new_status
        
        if error_message:
            self.error_message = error_message
        
        if intervention_message:
            self.intervention_message = intervention_message
        
        self.save()
    
    def update_progress(self, progress, current_phase=None):
        """
        Update task progress and optionally mark current phase.
        
        Args:
            progress (float): Progress percentage (0.0 - 100.0)
            current_phase (str, optional): Current task phase
        """
        self.progress = max(0.0, min(100.0, progress))
        
        if current_phase and self.phases:
            # Update phases to mark current phase
            updated_phases = []
            for phase in self.phases:
                phase_copy = phase.copy()
                
                # Mark current phase
                phase_copy['is_current'] = phase['phase'] == current_phase
                
                # Mark previous phases as completed
                phase_copy['is_completed'] = (
                    phase['phase'] != current_phase and 
                    self.phases.index(phase) < self.phases.index(
                        next(p for p in self.phases if p['phase'] == current_phase)
                    )
                )
                
                updated_phases.append(phase_copy)
            
            self.phases = updated_phases
        
        self.save()
    
    @property
    def completed_phases(self):
        """
        Returns a list of completed phase names.
        """
        return [
            phase['phase'] 
            for phase in self.phases 
            if phase.get('is_completed', False)
        ]
    
    def add_phase(self, phase, title, description, sub_tasks=None):
        """
        Add a new phase to the task.
        
        Args:
            phase (str): Phase name
            title (str): Phase title
            description (str): Phase description
            sub_tasks (list, optional): List of sub-tasks
        """
        new_phase = {
            'phase': phase,
            'title': title,
            'description': description,
            'is_completed': False,
            'is_current': False,
            'sub_tasks': sub_tasks or []
        }
        
        self.phases.append(new_phase)
        self.save()
