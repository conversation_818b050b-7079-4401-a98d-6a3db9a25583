#!/usr/bin/env python3
"""
Simple demo to show natural task completion handling working.
"""
import os
import sys
import django
import asyncio
import time

# Setup Django
sys.path.insert(0, '/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from agents.services.langgraph_orchestrator import LangGraphOrchestrator


class MockUser:
    def __init__(self, personality='caringFriend', companion_name='<PERSON>', first_name='TestUser'):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = first_name
        self.id = f"demo_{personality}"


async def demo_simple_task_completion():
    """Simple demonstration of task completion integration."""
    
    print("🎯 SIMPLE TASK COMPLETION DEMO")
    print("="*50)
    
    # Create user and orchestrator
    user = MockUser('wiseMentor', 'Sage', '<PERSON>')
    orchestrator = LangGraphOrchestrator(user=user)
    
    print(f"👤 User: {user.first_name} ({user.ai_companion_name})")
    print(f"🎭 Personality: {user.selected_personality}")
    
    # Simulate a completed task
    print(f"\n🔄 Simulating completed task...")
    orchestrator.simulate_task_completion(
        task_type="code",
        description="reviewing your Python debugging code",
        output="I found 3 potential issues and created optimized solutions with detailed explanations. Would you like me to walk you through them?",
        priority="normal"
    )
    
    # Test conversation that should trigger task delivery
    test_inputs = [
        "That sounds fascinating!",  # Natural pause indicator
        "Thanks for the help!",      # Natural pause indicator  
        "I'd love to see that.",     # Natural pause indicator
        "Okay, got it."              # Natural pause indicator
    ]
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\n💬 TEST {i}: User says: \"{user_input}\"")
        
        # Update conversation state
        orchestrator.task_completion_queue.update_conversation_state(user_input, {})
        
        # Check if we should deliver completions
        should_deliver = orchestrator.task_completion_queue.should_deliver_completions()
        print(f"🤔 Should deliver completions: {should_deliver}")
        
        if should_deliver:
            # Get the completion
            completion = orchestrator.task_completion_queue.get_next_completion()
            if completion:
                print(f"🎯 TASK COMPLETION DELIVERED!")
                
                # Generate natural response
                natural_response = orchestrator.completion_handler.generate_natural_completion_response(
                    current_response="That's a great question! I'd love to help you explore that further.",
                    task_completion=completion,
                    conversation_context={'domain': 'general'},
                    memories=[]
                )
                
                print(f"\n🤖 NATURAL RESPONSE WITH TASK COMPLETION:")
                print(f"{'─'*60}")
                print(natural_response)
                print(f"{'─'*60}")
                
                # Check for transition patterns
                response_lower = natural_response.lower()
                patterns_found = []
                
                transition_patterns = [
                    "by the way", "oh, and", "i just finished", "i completed",
                    "i've got", "while we were", "i wanted to let you know"
                ]
                
                for pattern in transition_patterns:
                    if pattern in response_lower:
                        patterns_found.append(pattern)
                
                if patterns_found:
                    print(f"✅ Natural transition patterns found: {patterns_found}")
                else:
                    print(f"ℹ️ Task completion integrated without explicit transition pattern")
                
                break
        else:
            conv_state = orchestrator.task_completion_queue.conversation_state
            pending = len(orchestrator.task_completion_queue.pending_completions)
            print(f"⏸️ Not delivered - State: {conv_state}, Pending: {pending}")
    
    print(f"\n✅ Demo completed!")


if __name__ == "__main__":
    asyncio.run(demo_simple_task_completion())
