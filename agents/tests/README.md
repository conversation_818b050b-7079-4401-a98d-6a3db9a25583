# Unity Integration API Tests

This directory contains tests for the Unity Integration API implemented in the agents app.

## Test Files

- `test_unity_integration_api.py`: Tests the API endpoints for avatar settings, outfit changes, environment changes, animation playback, and voice commands.
- `test_serializers.py`: Tests the AvatarSettingsSerializer to ensure it correctly serializes user data.
- `test_urls.py`: Tests the URL configuration to ensure that all endpoints are correctly mapped to their respective views.
- `test_basic.py`: A simple test that doesn't require database access.

## Running the Tests

To run all tests for the agents app:

```bash
python manage.py test agents.tests
```

To run a specific test file:

```bash
python manage.py test agents.tests.test_urls
```

## Known Issues

If you encounter an error related to `libmagic` when running the tests:

```
ImportError: failed to find libmagic. Check your installation
```

This is because the `python-magic` library is used in the chat app and requires the `libmagic` system library to be installed.

### Fix for macOS:

```bash
brew install libmagic
```

### Fix for Ubuntu/Debian:

```bash
sudo apt-get install libmagic-dev
```

### Fix for Windows:

For Windows, you need to install the DLLs for the magic library. See the [python-magic documentation](https://github.com/ahupp/python-magic#windows) for details.

## Test Coverage

These tests cover:
- Serializer functionality and edge cases
- API endpoint behavior and responses
- Authentication requirements
- URL configuration

The tests ensure that:
1. The serializer correctly handles avatar settings and customization options
2. The API endpoints function as expected for changing outfits, environments, playing animations, and processing voice commands
3. Authentication is properly enforced for all endpoints
4. The URL patterns are correctly configured