import json
import uuid
from unittest import mock
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from gamification.models import ShopItem, UserInventory, Pet

User = get_user_model()


class UnityIntegrationAPITestCase(TestCase):
    """Test case for the Unity Integration API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123'
        )
        
        # Set some avatar preferences
        self.user.selected_personality = 'caringFriend'
        self.user.selected_environment = 'cozy_room'
        self.user.owned_environments = ['cozy_room', 'beach', 'forest']
        self.user.owned_outfits = ['casual', 'formal', 'sporty']
        self.user.owned_pets = ['cat', 'dog']
        self.user.save()
        
        # Generate UUIDs for test items
        self.outfit_id = uuid.uuid4()
        self.environment_id = uuid.uuid4()
        self.pet_id = uuid.uuid4()
        
        # Create test shop items
        self.outfit_item = ShopItem.objects.create(
            id=self.outfit_id,
            name='casual',
            description='Casual outfit',
            price=100,
            item_type='outfit',
            image_url='https://example.com/casual.png',
            preview_data={'color': 'blue'}
        )
        
        self.environment_item = ShopItem.objects.create(
            id=self.environment_id,
            name='beach',
            description='Beach environment',
            price=200,
            item_type='environment',
            image_url='https://example.com/beach.png',
            preview_data={'lighting': 'bright'}
        )
        
        self.pet_item = ShopItem.objects.create(
            id=self.pet_id,
            name='cat',
            description='Cat pet',
            price=300,
            item_type='pet',
            image_url='https://example.com/cat.png',
            preview_data={'behavior': 'playful'}
        )
        
        # Add items to user inventory
        self.outfit_inventory = UserInventory.objects.create(
            user=self.user,
            item=self.outfit_item,
            is_equipped=True
        )
        
        self.environment_inventory = UserInventory.objects.create(
            user=self.user,
            item=self.environment_item,
            is_equipped=False
        )
        
        self.pet_inventory = UserInventory.objects.create(
            user=self.user,
            item=self.pet_item,
            is_equipped=True
        )
        
        # Create a pet in the Pet model
        self.pet = Pet.objects.create(
            name='cat',
            pet_type='feline',
            behavior='playful',
            sound='meow',
            animation_data={'idle': 'cat_idle', 'play': 'cat_play'}
        )
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_avatar_settings_view(self):
        """Test retrieving avatar settings."""
        url = reverse('agents:avatar_settings')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Check basic fields
        self.assertEqual(data['selected_personality'], 'caringFriend')
        self.assertEqual(data['selected_environment'], 'cozy_room')
        self.assertEqual(data['owned_environments'], ['cozy_room', 'beach', 'forest'])
        self.assertEqual(data['owned_outfits'], ['casual', 'formal', 'sporty'])
        self.assertEqual(data['owned_pets'], ['cat', 'dog'])
        
        # Check current outfit
        self.assertIsNotNone(data['current_outfit'])
        self.assertEqual(data['current_outfit']['name'], 'casual')
        
        # Check current pet
        self.assertIsNotNone(data['current_pet'])
        self.assertEqual(data['current_pet']['name'], 'cat')
        self.assertEqual(data['current_pet']['pet_type'], 'feline')
        self.assertEqual(data['current_pet']['behavior'], 'playful')
    
    def test_avatar_outfit_view(self):
        """Test changing avatar outfit."""
        url = reverse('agents:avatar_outfit')
        
        # Create another outfit
        new_outfit = ShopItem.objects.create(
            id=uuid.uuid4(),
            name='formal',
            description='Formal outfit',
            price=150,
            item_type='outfit',
            image_url='https://example.com/formal.png',
            preview_data={'color': 'black'}
        )
        
        new_outfit_inventory = UserInventory.objects.create(
            user=self.user,
            item=new_outfit,
            is_equipped=False
        )
        
        # Change outfit
        response = self.client.post(
            url,
            {'outfit_id': str(new_outfit.id)},
            format='json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Check that the new outfit is equipped
        new_outfit_inventory.refresh_from_db()
        self.assertTrue(new_outfit_inventory.is_equipped)
        
        # Check that the old outfit is unequipped
        self.outfit_inventory.refresh_from_db()
        self.assertFalse(self.outfit_inventory.is_equipped)
    
    def test_environment_change_view(self):
        """Test changing avatar environment."""
        url = reverse('agents:avatar_environment')
        
        # Change environment
        response = self.client.post(
            url,
            {'environment_id': str(self.environment_item.id)},
            format='json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Check that the environment is equipped
        self.environment_inventory.refresh_from_db()
        self.assertTrue(self.environment_inventory.is_equipped)
        
        # Check that the user's selected environment is updated
        self.user.refresh_from_db()
        self.assertEqual(self.user.selected_environment, 'beach')
    
    def test_animation_play_view(self):
        """Test playing avatar animations."""
        url = reverse('agents:avatar_animation')
        
        # Test valid animation
        response = self.client.post(
            url,
            {
                'animation_name': 'wave',
                'animation_parameters': {'speed': 1.0}
            },
            format='json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['animation_name'], 'wave')
        
        # Test invalid animation
        response = self.client.post(
            url,
            {'animation_name': 'invalid_animation'},
            format='json'
        )
        
        self.assertEqual(response.status_code, 400)
        data = response.json()
        self.assertIn('error', data)
    
    def test_voice_command_view(self):
        """Test processing voice commands."""
        url = reverse('agents:avatar_voice_command')
        
        # Test wave command
        response = self.client.post(
            url,
            {'command': 'wave hello'},
            format='json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['action'], 'animation')
        self.assertEqual(data['animation_name'], 'wave')
        
        # Test dance command
        response = self.client.post(
            url,
            {'command': 'please dance for me'},
            format='json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['action'], 'animation')
        self.assertEqual(data['animation_name'], 'dance')
        
        # Test change outfit command
        response = self.client.post(
            url,
            {'command': 'change outfit please'},
            format='json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['action'], 'suggest_outfit_change')
        
        # Test unknown command
        response = self.client.post(
            url,
            {'command': 'do something completely random and nonsensical that would never be a valid command'},
            format='json'
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['success'])
        self.assertEqual(data['action'], 'unknown')
        self.assertIn('message', data)
    
    def test_authentication_required(self):
        """Test that authentication is required for all endpoints."""
        # Create an unauthenticated client
        client = APIClient()
        
        # Test avatar settings endpoint
        url = reverse('agents:avatar_settings')
        response = client.get(url)
        self.assertEqual(response.status_code, 401)
        
        # Test avatar outfit endpoint
        url = reverse('agents:avatar_outfit')
        response = client.post(url, {'outfit_id': str(uuid.uuid4())}, format='json')
        self.assertEqual(response.status_code, 401)
        
        # Test environment change endpoint
        url = reverse('agents:avatar_environment')
        response = client.post(url, {'environment_id': str(uuid.uuid4())}, format='json')
        self.assertEqual(response.status_code, 401)
        
        # Test animation play endpoint
        url = reverse('agents:avatar_animation')
        response = client.post(url, {'animation_name': 'wave'}, format='json')
        self.assertEqual(response.status_code, 401)
        
        # Test voice command endpoint
        url = reverse('agents:avatar_voice_command')
        response = client.post(url, {'command': 'wave'}, format='json')
        self.assertEqual(response.status_code, 401)