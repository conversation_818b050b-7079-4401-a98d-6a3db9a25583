from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from agents.models import TaskModel

User = get_user_model()

class TaskModelTestCase(TestCase):
    """
    Test cases for TaskModel functionality.
    """
    def setUp(self):
        """
        Set up test environment with a user and a task.
        """
        self.user = User.objects.create_user(
            username='testuser', 
            email='<EMAIL>', 
            password='testpassword'
        )
        
        self.task = TaskModel.objects.create(
            user=self.user,
            title='Test Task',
            description='A task for testing',
            status='pending',
            progress=0.0
        )
    
    def test_task_creation(self):
        """
        Test basic task creation.
        """
        self.assertEqual(self.task.title, 'Test Task')
        self.assertEqual(self.task.status, 'pending')
        self.assertEqual(self.task.progress, 0.0)
    
    def test_update_status(self):
        """
        Test updating task status.
        """
        self.task.update_status('running', error_message='Test error')
        
        self.assertEqual(self.task.status, 'running')
        self.assertEqual(self.task.error_message, 'Test error')
    
    def test_update_progress(self):
        """
        Test updating task progress.
        """
        # Add some phases first
        self.task.add_phase(
            phase='initialization', 
            title='Project Start', 
            description='Initial project setup'
        )
        self.task.add_phase(
            phase='analysis', 
            title='Requirements Analysis', 
            description='Gather and document requirements'
        )
        
        # Update progress with current phase
        self.task.update_progress(50.0, current_phase='analysis')
        
        self.assertEqual(self.task.progress, 50.0)
        
        # Check phase updates
        analysis_phase = [p for p in self.task.phases if p['phase'] == 'analysis'][0]
        initialization_phase = [p for p in self.task.phases if p['phase'] == 'initialization'][0]
        
        self.assertTrue(analysis_phase['is_current'])
        self.assertTrue(initialization_phase['is_completed'])
    
    def test_add_phase(self):
        """
        Test adding phases to a task.
        """
        self.task.add_phase(
            phase='testing', 
            title='System Testing', 
            description='Comprehensive system testing',
            sub_tasks=['Unit Tests', 'Integration Tests', 'User Acceptance Testing']
        )
        
        # Verify phase was added
        testing_phase = [p for p in self.task.phases if p['phase'] == 'testing'][0]
        
        self.assertEqual(testing_phase['title'], 'System Testing')
        self.assertEqual(testing_phase['description'], 'Comprehensive system testing')
        self.assertEqual(testing_phase['sub_tasks'], ['Unit Tests', 'Integration Tests', 'User Acceptance Testing'])
        self.assertFalse(testing_phase['is_completed'])
        self.assertFalse(testing_phase['is_current'])


class TaskModelViewSetTestCase(TestCase):
    """
    Test cases for TaskModelViewSet API endpoints.
    """
    def setUp(self):
        """
        Set up test environment with a user and API client.
        """
        self.user = User.objects.create_user(
            username='testuser', 
            email='<EMAIL>', 
            password='testpassword'
        )
        
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # Create a task for testing
        self.task = TaskModel.objects.create(
            user=self.user,
            title='Test Task',
            description='A task for testing',
            status='pending',
            progress=0.0
        )
    
    def test_create_task(self):
        """
        Test creating a new task via API.
        """
        task_data = {
            'title': 'New Test Task',
            'description': 'A new task for testing',
            'status': 'pending',
            'progress': 0.0,
            'phases': [
                {
                    'phase': 'initialization',
                    'title': 'Project Start',
                    'description': 'Initial project setup',
                    'sub_tasks': ['Define scope', 'Create project plan']
                }
            ]
        }
        
        # Manually set the user for the request
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post('/api/agents/tasks/', task_data, format='json')
        
        # Print response details for debugging
        print("Response status code:", response.status_code)
        print("Response content:", response.content)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], 'New Test Task')
        self.assertEqual(len(response.data['phases']), 1)
    
    def test_update_task_status(self):
        """
        Test updating task status via custom action.
        """
        response = self.client.patch(
            f'/api/agents/tasks/{self.task.id}/update_status/', 
            {
                'status': 'running', 
                'error_message': 'Test error'
            }, 
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'running')
        self.assertEqual(response.data['error_message'], 'Test error')
    
    def test_update_task_progress(self):
        """
        Test updating task progress via custom action.
        """
        # First add a phase
        self.client.post(
            f'/api/agents/tasks/{self.task.id}/add_phase/', 
            {
                'phase': 'analysis', 
                'title': 'Requirements Analysis', 
                'description': 'Gather and document requirements'
            }, 
            format='json'
        )
        
        # Then update progress
        response = self.client.patch(
            f'/api/agents/tasks/{self.task.id}/update_progress/', 
            {
                'progress': 50.0, 
                'current_phase': 'analysis'
            }, 
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['progress'], 50.0)
        
        # Check that the current phase is marked correctly
        analysis_phase = [p for p in response.data['phases'] if p['phase'] == 'analysis'][0]
        self.assertTrue(analysis_phase['is_current'])
    
    def test_add_task_phase(self):
        """
        Test adding a phase to a task via custom action.
        """
        response = self.client.post(
            f'/api/agents/tasks/{self.task.id}/add_phase/', 
            {
                'phase': 'testing', 
                'title': 'System Testing', 
                'description': 'Comprehensive system testing',
                'sub_tasks': ['Unit Tests', 'Integration Tests']
            }, 
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify phase was added
        testing_phase = [p for p in response.data['phases'] if p['phase'] == 'testing'][0]
        
        self.assertEqual(testing_phase['title'], 'System Testing')
        self.assertEqual(testing_phase['sub_tasks'], ['Unit Tests', 'Integration Tests']) 