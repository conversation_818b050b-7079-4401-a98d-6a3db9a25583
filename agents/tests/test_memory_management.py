"""
Tests for memory management functionality in the agents app.
"""
import json
import uuid
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from memory.models import Memory, MemoryType, MemoryConfiguration, MemoryCluster
from agents.serializers import (
    MemorySerializer, MemoryCreateSerializer, MemoryUpdateSerializer,
    MemoryClusterSerializer, MemoryConfigurationSerializer
)

User = get_user_model()


class MemoryModelTest(TestCase):
    """Test Memory model functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.memory = Memory.objects.create(
            user=self.user,
            content="User prefers dark theme",
            memory_type=MemoryType.PREFERENCE,
            importance_score=0.8,
            personalness_score=0.9,
            actionability_score=0.7,
            vector_id="test_vector_123"
        )
    
    def test_memory_creation(self):
        """Test memory creation with all fields."""
        self.assertEqual(self.memory.user, self.user)
        self.assertEqual(self.memory.content, "User prefers dark theme")
        self.assertEqual(self.memory.memory_type, MemoryType.PREFERENCE)
        self.assertEqual(self.memory.importance_score, 0.8)
        self.assertEqual(self.memory.personalness_score, 0.9)
        self.assertEqual(self.memory.actionability_score, 0.7)
        self.assertTrue(self.memory.is_active)
        self.assertFalse(self.memory.is_verified)
    
    def test_memory_salience_score(self):
        """Test salience score calculation."""
        # Test default weights
        salience_score = self.memory.get_salience_score()
        expected_score = 0.8 * 0.5 + 0.9 * 0.3 + 0.7 * 0.2
        self.assertAlmostEqual(salience_score, expected_score, places=2)
        
        # Test custom weights
        custom_weights = {'importance': 0.6, 'personalness': 0.3, 'actionability': 0.1}
        salience_score = self.memory.get_salience_score(custom_weights)
        expected_score = 0.8 * 0.6 + 0.9 * 0.3 + 0.7 * 0.1
        self.assertAlmostEqual(salience_score, expected_score, places=2)
    
    def test_memory_update_access_time(self):
        """Test that access time is updated when memory is accessed."""
        original_access_time = self.memory.last_accessed
        self.memory.update_access_time()
        self.memory.refresh_from_db()
        
        self.assertGreater(self.memory.last_accessed, original_access_time)
    
    def test_memory_string_representation(self):
        """Test memory string representation."""
        expected_str = f"{self.user.email} - preference - User prefers dark theme..."
        self.assertEqual(str(self.memory), expected_str)


class MemorySerializerTest(TestCase):
    """Test Memory serializers."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.memory = Memory.objects.create(
            user=self.user,
            content="User prefers dark theme",
            memory_type=MemoryType.PREFERENCE,
            importance_score=0.8,
            personalness_score=0.9,
            actionability_score=0.7,
            vector_id="test_vector_123"
        )
    
    def test_memory_serializer(self):
        """Test MemorySerializer serialization."""
        serializer = MemorySerializer(self.memory)
        data = serializer.data
        
        self.assertEqual(data['id'], str(self.memory.id))
        self.assertEqual(data['content'], "User prefers dark theme")
        self.assertEqual(data['memory_type'], 'preference')
        self.assertEqual(data['memory_type_display'], 'User preferences and choices')
        self.assertEqual(data['importance_score'], 0.8)
        self.assertEqual(data['personalness_score'], 0.9)
        self.assertEqual(data['actionability_score'], 0.7)
        self.assertIn('salience_score', data)
        self.assertTrue(data['is_active'])
        self.assertFalse(data['is_verified'])
    
    def test_memory_create_serializer(self):
        """Test MemoryCreateSerializer."""
        data = {
            'content': 'User likes programming',
            'memory_type': 'skill',
            'importance_score': 0.9,
            'personalness_score': 0.8,
            'actionability_score': 0.7
        }
        
        serializer = MemoryCreateSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        
        # Test creation with user context
        request = type('Request', (), {'user': self.user})()
        context = {'request': request}
        serializer = MemoryCreateSerializer(data=data, context=context)
        self.assertTrue(serializer.is_valid())
        
        memory = serializer.save()
        self.assertEqual(memory.user, self.user)
        self.assertEqual(memory.content, 'User likes programming')
        self.assertEqual(memory.memory_type, 'skill')
        self.assertIsNotNone(memory.vector_id)
    
    def test_memory_update_serializer(self):
        """Test MemoryUpdateSerializer."""
        data = {
            'content': 'Updated content',
            'importance_score': 0.9,
            'is_verified': True
        }
        
        serializer = MemoryUpdateSerializer(self.memory, data=data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        updated_memory = serializer.save()
        self.assertEqual(updated_memory.content, 'Updated content')
        self.assertEqual(updated_memory.importance_score, 0.9)
        self.assertTrue(updated_memory.is_verified)


class MemoryAPITest(APITestCase):
    """Test memory API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        # Create test memories
        self.memory1 = Memory.objects.create(
            user=self.user,
            content="User prefers dark theme",
            memory_type=MemoryType.PREFERENCE,
            importance_score=0.8,
            personalness_score=0.9,
            actionability_score=0.7,
            vector_id="test_vector_1"
        )
        
        self.memory2 = Memory.objects.create(
            user=self.user,
            content="User knows Python programming",
            memory_type=MemoryType.SKILL,
            importance_score=0.9,
            personalness_score=0.8,
            actionability_score=0.9,
            vector_id="test_vector_2"
        )
    
    def test_list_memories(self):
        """Test listing user memories."""
        url = reverse('agents:memory-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Handle pagination
        if hasattr(response, 'data') and isinstance(response.data, dict) and 'results' in response.data:
            memories = response.data['results']
        else:
            memories = response.data
            
        # Check that user's memories are returned (may be more than 2 due to existing data)
        memory_ids = [memory['id'] for memory in memories]
        self.assertIn(str(self.memory1.id), memory_ids)
        self.assertIn(str(self.memory2.id), memory_ids)
        # Verify all memories belong to the user
        for memory in memories:
            self.assertEqual(str(memory['user']), str(self.user.id))
    
    def test_create_memory(self):
        """Test creating a new memory."""
        url = reverse('agents:memory-list')
        data = {
            'content': 'User likes Flutter development',
            'memory_type': 'skill',
            'importance_score': 0.8,
            'personalness_score': 0.7,
            'actionability_score': 0.9
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Memory.objects.count(), 3)
        
        memory = Memory.objects.latest('created_at')
        self.assertEqual(memory.user, self.user)
        self.assertEqual(memory.content, 'User likes Flutter development')
        self.assertEqual(memory.memory_type, 'skill')
        self.assertIsNotNone(memory.vector_id)
    
    def test_retrieve_memory(self):
        """Test retrieving a specific memory."""
        url = reverse('agents:memory-detail', args=[self.memory1.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['content'], 'User prefers dark theme')
        self.assertEqual(response.data['memory_type'], 'preference')
    
    def test_update_memory(self):
        """Test updating a memory."""
        url = reverse('agents:memory-detail', args=[self.memory1.id])
        data = {
            'content': 'Updated content',
            'importance_score': 0.9,
            'is_verified': True
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        self.memory1.refresh_from_db()
        self.assertEqual(self.memory1.content, 'Updated content')
        self.assertEqual(self.memory1.importance_score, 0.9)
        self.assertTrue(self.memory1.is_verified)
    
    def test_delete_memory(self):
        """Test deleting a memory."""
        url = reverse('agents:memory-detail', args=[self.memory1.id])
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Memory.objects.count(), 1)
        self.assertFalse(Memory.objects.filter(id=self.memory1.id).exists())
    
    def test_memory_types_endpoint(self):
        """Test getting available memory types."""
        url = reverse('agents:memory-types')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)
        
        # Check that all memory types are included
        memory_types = [item['value'] for item in response.data]
        expected_types = [choice[0] for choice in MemoryType.choices]
        self.assertEqual(set(memory_types), set(expected_types))
    
    def test_memory_stats_endpoint(self):
        """Test getting memory statistics."""
        url = reverse('agents:memory-stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data
        
        self.assertEqual(data['total_memories'], 2)
        self.assertEqual(data['active_memories'], 2)
        self.assertEqual(data['verified_memories'], 0)
        self.assertIn('preference', data['memory_types'])
        self.assertIn('skill', data['memory_types'])
        self.assertEqual(data['memory_types']['preference'], 1)
        self.assertEqual(data['memory_types']['skill'], 1)
        self.assertIsInstance(data['average_salience_score'], float)
        self.assertEqual(data['recent_memories_count'], 2)
    
    def test_memory_search(self):
        """Test memory search functionality."""
        url = reverse('agents:memory-search')
        
        # Search by content
        response = self.client.get(url, {'q': 'dark theme'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Handle pagination
        if hasattr(response, 'data') and isinstance(response.data, dict) and 'results' in response.data:
            memories = response.data['results']
        else:
            memories = response.data
            
        # Should find at least our test memory
        self.assertGreaterEqual(len(memories), 1)
        memory_contents = [memory['content'] for memory in memories]
        self.assertIn('User prefers dark theme', memory_contents)
        
        # Search by type
        response = self.client.get(url, {'type': 'skill'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Handle pagination
        if hasattr(response, 'data') and isinstance(response.data, dict) and 'results' in response.data:
            memories = response.data['results']
        else:
            memories = response.data
            
        # Should find at least our test memory
        self.assertGreaterEqual(len(memories), 1)
        memory_types = [memory['memory_type'] for memory in memories]
        self.assertIn('skill', memory_types)
        
        # Search by active status
        response = self.client.get(url, {'active': 'true'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Handle pagination
        if hasattr(response, 'data') and isinstance(response.data, dict) and 'results' in response.data:
            memories = response.data['results']
        else:
            memories = response.data
            
        # Should find at least our test memories
        self.assertGreaterEqual(len(memories), 2)
    
    def test_memory_verify(self):
        """Test verifying a memory."""
        url = reverse('agents:memory-verify', args=[self.memory1.id])
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        self.memory1.refresh_from_db()
        self.assertTrue(self.memory1.is_verified)
    
    def test_memory_deactivate_reactivate(self):
        """Test deactivating and reactivating a memory."""
        # Deactivate
        url = reverse('agents:memory-deactivate', args=[self.memory1.id])
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        self.memory1.refresh_from_db()
        self.assertFalse(self.memory1.is_active)
        
        # Reactivate
        url = reverse('agents:memory-reactivate', args=[self.memory1.id])
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        self.memory1.refresh_from_db()
        self.assertTrue(self.memory1.is_active)


class MemoryClusterAPITest(APITestCase):
    """Test memory cluster API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
        
        # Create test memories
        self.memory1 = Memory.objects.create(
            user=self.user,
            content="User prefers dark theme",
            memory_type=MemoryType.PREFERENCE,
            vector_id="test_vector_1"
        )
        
        self.memory2 = Memory.objects.create(
            user=self.user,
            content="User knows Python programming",
            memory_type=MemoryType.SKILL,
            vector_id="test_vector_2"
        )
        
        # Create test cluster
        self.cluster = MemoryCluster.objects.create(
            user=self.user,
            name="Programming Preferences",
            description="User's programming-related preferences and skills",
            cluster_type="topic"
        )
    
    def test_list_clusters(self):
        """Test listing user memory clusters."""
        url = reverse('agents:memory-cluster-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Handle pagination
        if hasattr(response, 'data') and isinstance(response.data, dict) and 'results' in response.data:
            clusters = response.data['results']
        else:
            clusters = response.data
            
        # Should find at least our test cluster
        self.assertGreaterEqual(len(clusters), 1)
        cluster_names = [cluster['name'] for cluster in clusters]
        self.assertIn("Programming Preferences", cluster_names)
        # Verify all clusters belong to the user
        for cluster in clusters:
            self.assertEqual(str(cluster['user']), str(self.user.id))
    
    def test_create_cluster(self):
        """Test creating a new memory cluster."""
        url = reverse('agents:memory-cluster-list')
        data = {
            'name': 'Personal Facts',
            'description': 'Personal information about the user',
            'cluster_type': 'topic'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(MemoryCluster.objects.count(), 2)
        
        cluster = MemoryCluster.objects.latest('created_at')
        self.assertEqual(cluster.user, self.user)
        self.assertEqual(cluster.name, 'Personal Facts')
    
    def test_add_memory_to_cluster(self):
        """Test adding a memory to a cluster."""
        url = reverse('agents:memory-cluster-add-memory', args=[self.cluster.id])
        data = {'memory_id': str(self.memory1.id)}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(self.cluster.memories.count(), 1)
        self.assertIn(self.memory1, self.cluster.memories.all())
    
    def test_remove_memory_from_cluster(self):
        """Test removing a memory from a cluster."""
        # First add memory to cluster
        self.cluster.memories.add(self.memory1)
        
        url = reverse('agents:memory-cluster-remove-memory', args=[self.cluster.id])
        data = {'memory_id': str(self.memory1.id)}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(self.cluster.memories.count(), 0)
    
    def test_get_cluster_memories(self):
        """Test getting memories in a cluster."""
        # Add memories to cluster
        self.cluster.memories.add(self.memory1, self.memory2)
        
        url = reverse('agents:memory-cluster-memories', args=[self.cluster.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)


class MemoryConfigurationAPITest(APITestCase):
    """Test memory configuration API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_get_memory_configuration(self):
        """Test getting memory configuration."""
        url = reverse('agents:memory_configuration')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should create default configuration if none exists
        self.assertTrue(MemoryConfiguration.objects.filter(user=self.user).exists())
        
        config = MemoryConfiguration.objects.get(user=self.user)
        self.assertEqual(response.data['min_importance_threshold'], config.min_importance_threshold)
        self.assertEqual(response.data['max_memories_per_retrieval'], config.max_memories_per_retrieval)
    
    def test_update_memory_configuration(self):
        """Test updating memory configuration."""
        url = reverse('agents:memory_configuration')
        data = {
            'min_importance_threshold': 0.4,
            'max_memories_per_retrieval': 10,
            'similarity_threshold': 0.8,
            'allow_memory_storage': False
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        config = MemoryConfiguration.objects.get(user=self.user)
        self.assertEqual(config.min_importance_threshold, 0.4)
        self.assertEqual(config.max_memories_per_retrieval, 10)
        self.assertEqual(config.similarity_threshold, 0.8)
        self.assertFalse(config.allow_memory_storage)


class MemoryBulkAPITest(APITestCase):
    """Test bulk memory operations."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_bulk_create_memories(self):
        """Test bulk creating memories."""
        url = reverse('agents:memory_bulk')
        data = {
            'memories': [
                {
                    'content': 'User prefers dark theme',
                    'memory_type': 'preference',
                    'importance_score': 0.8,
                    'personalness_score': 0.9,
                    'actionability_score': 0.7
                },
                {
                    'content': 'User knows Python programming',
                    'memory_type': 'skill',
                    'importance_score': 0.9,
                    'personalness_score': 0.8,
                    'actionability_score': 0.9
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['created_count'], 2)
        self.assertEqual(Memory.objects.count(), 2)
        
        # Check that memories belong to user
        memories = Memory.objects.filter(user=self.user)
        self.assertEqual(memories.count(), 2)
    
    def test_bulk_delete_memories(self):
        """Test bulk deleting memories."""
        # Create test memories
        memory1 = Memory.objects.create(
            user=self.user,
            content="Test memory 1",
            memory_type=MemoryType.PREFERENCE,
            vector_id="test_vector_1"
        )
        
        memory2 = Memory.objects.create(
            user=self.user,
            content="Test memory 2",
            memory_type=MemoryType.SKILL,
            vector_id="test_vector_2"
        )
        
        url = reverse('agents:memory_bulk')
        data = {
            'memory_ids': [str(memory1.id), str(memory2.id)]
        }
        
        response = self.client.delete(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['deleted_count'], 2)
        self.assertEqual(Memory.objects.count(), 0)
    
    def test_bulk_create_with_errors(self):
        """Test bulk create with some invalid data."""
        url = reverse('agents:memory_bulk')
        data = {
            'memories': [
                {
                    'content': 'Valid memory',
                    'memory_type': 'preference',
                    'importance_score': 0.8,
                    'personalness_score': 0.9,
                    'actionability_score': 0.7
                },
                {
                    'content': '',  # Invalid - empty content
                    'memory_type': 'invalid_type',  # Invalid type
                    'importance_score': 1.5  # Invalid score
                }
            ]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Should create at least 1 valid memory
        self.assertGreaterEqual(response.data['created_count'], 1)
        # Should have at least 1 memory created
        self.assertGreaterEqual(Memory.objects.count(), 1)
        # Check if there are any errors (may be 0 if validation is handled differently)
        if 'errors' in response.data:
            self.assertIsInstance(response.data['errors'], list)


class MemorySecurityTest(APITestCase):
    """Test memory API security and permissions."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        self.user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create memories for user1
        self.memory1 = Memory.objects.create(
            user=self.user1,
            content="User1's memory",
            memory_type=MemoryType.PREFERENCE,
            vector_id="test_vector_1"
        )
        
        # Create cluster for user1
        self.cluster1 = MemoryCluster.objects.create(
            user=self.user1,
            name="User1's cluster",
            cluster_type="topic"
        )
    
    def test_user_cannot_access_other_user_memories(self):
        """Test that users cannot access memories of other users."""
        self.client.force_authenticate(user=self.user2)
        
        url = reverse('agents:memory-detail', args=[self.memory1.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_user_cannot_access_other_user_clusters(self):
        """Test that users cannot access clusters of other users."""
        self.client.force_authenticate(user=self.user2)
        
        url = reverse('agents:memory-cluster-detail', args=[self.cluster1.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_user_cannot_modify_other_user_memories(self):
        """Test that users cannot modify memories of other users."""
        self.client.force_authenticate(user=self.user2)
        
        url = reverse('agents:memory-detail', args=[self.memory1.id])
        data = {'content': 'Modified by user2'}
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Verify memory was not modified
        self.memory1.refresh_from_db()
        self.assertEqual(self.memory1.content, "User1's memory")
    
    def test_unauthenticated_access_denied(self):
        """Test that unauthenticated users cannot access memory endpoints."""
        url = reverse('agents:memory-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED) 