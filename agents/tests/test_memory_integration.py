"""
Integration tests for the complete memory system.
"""
from django.test import TestCase
from unittest.mock import patch, Mock

from agents.services.langgraph_orchestrator import LangGraphOrchestrator
from agents.services.memory_manager import MemoryManager
from agents.services.salience_scorer import MemorySalienceScorer
from agents.services.memory_tools import SaveMemoryTool, QueryMemoryTool


class MemoryIntegrationTests(TestCase):
    """Integration tests for the complete memory system."""
    
    def setUp(self):
        self.orchestrator = LangGraphOrchestrator()
        self.memory_manager = MemoryManager()
        
    def test_memory_manager_basic_operations(self):
        """Test basic memory manager operations."""
        # Store a memory
        memory_id = self.memory_manager.store_memory(
            text="User likes jazz music",
            memory_type="semantic_profile",
            user_id="test_user",
            importance_score=0.8,
            personalness_score=0.7,
            actionability_score=0.2
        )
        
        self.assertIsNotNone(memory_id)
        self.assertNotEqual(memory_id, "")
        
        # Search for the memory
        results = self.memory_manager.search_memories(
            query="music preferences",
            user_id="test_user",
            k=5
        )
        
        # Should find the stored memory
        self.assertGreater(len(results), 0)
        found_memory = any("jazz" in result.get("text", "").lower() for result in results)
        self.assertTrue(found_memory)
    
    @patch('agents.services.salience_scorer.MemorySalienceScorer.score_text')
    def test_salience_scorer_functionality(self, mock_score_text):
        """Test salience scorer with different types of input."""
        # Mock the score_text method to return a simple response
        mock_score_text.return_value = {
            'importance_score': 0.8,
            'personalness_score': 0.7,
            'actionability_score': 0.2,
            'reasoning': 'This is personal information about the user'
        }
        
        # Create a scorer with the mocked method
        scorer = MemorySalienceScorer()
        
        # Test high importance/personal statement
        result = scorer.score_text("My name is Alice and I work as a software engineer")
        
        self.assertIn("importance_score", result)
        self.assertIn("personalness_score", result)
        self.assertIn("actionability_score", result)
        self.assertIn("reasoning", result)
        
        # Should have reasonable personalness for name/job info
        self.assertGreater(result["personalness_score"], 0.3)
        
        # Update mock for second call
        mock_score_text.return_value = {
            'importance_score': 0.3,
            'personalness_score': 0.2,
            'actionability_score': 0.1,
            'reasoning': 'This is a generic statement'
        }
        
        # Test low salience statement
        result2 = scorer.score_text("The weather is nice today")
        
        # Should have lower scores for generic weather comment
        self.assertLess(result2["personalness_score"], result["personalness_score"])
    
    @patch('agents.services.salience_scorer.MemorySalienceScorer.score_text')
    def test_memory_tools_functionality(self, mock_score_text):
        """Test memory tools work correctly."""
        # Mock the score_text method to return a simple response
        mock_score_text.return_value = {
            'importance_score': 0.8,
            'personalness_score': 0.7,
            'actionability_score': 0.2,
            'reasoning': 'This is personal information about the user'
        }
        
        # Create a scorer with the mocked method
        scorer = MemorySalienceScorer()
        
        # Create tools
        save_tool = SaveMemoryTool(
            memory_manager=self.memory_manager,
            salience_scorer=scorer
        )
        
        query_tool = QueryMemoryTool(
            memory_manager=self.memory_manager
        )
        
        # Test saving a memory with higher importance
        save_result = save_tool._run(
            text_to_save="I am a professional software engineer specializing in machine learning and I have been playing classical guitar for 15 years",
            memory_type="semantic_profile",
            user_id="test_user_2"
        )
        
        self.assertIn("Successfully saved", save_result)
        
        # Test querying the memory
        query_result = query_tool._run(
            query="hobbies and interests",
            user_id="test_user_2",
            top_k=3
        )
        
        self.assertNotEqual(query_result, "No relevant memories found for the query.")
        self.assertIn("guitar", query_result.lower())
    
    def test_orchestrator_memory_integration(self):
        """Test that orchestrator properly integrates memory tools."""
        # Check that memory tools are initialized
        self.assertIsNotNone(self.orchestrator.memory_tools)
        self.assertGreater(len(self.orchestrator.memory_tools), 0)
        
        # Check tool names
        tool_names = [tool.name for tool in self.orchestrator.memory_tools]
        self.assertIn("query_memory", tool_names)
    
    def test_memory_workflow_nodes(self):
        """Test memory workflow nodes in the orchestrator."""
        from langchain_core.messages import HumanMessage
        from agents.services.agent_state import AgentState
        
        # Test flag salient input node
        state = AgentState(
            original_input="I love classical music and play piano",
            user_id="test_user_3"
        )
        
        result = self.orchestrator._flag_salient_input_node(state)
        
        # Should process without error
        self.assertIsInstance(result, dict)
        
        # Test retrieve memories node
        state2 = AgentState(
            current_question="What are my musical interests?",
            user_id="test_user_3"
        )
        
        result2 = self.orchestrator._retrieve_memories_node(state2)
        
        # Should have retrieved_long_term_memories key
        self.assertIn("retrieved_long_term_memories", result2)
        self.assertIsInstance(result2["retrieved_long_term_memories"], list)
    
    @patch('agents.services.memory_manager.MemoryManager.store_memory')
    @patch('agents.services.memory_manager.MemoryManager.search_memories')
    @patch('agents.services.salience_scorer.MemorySalienceScorer.score_text')
    def test_end_to_end_memory_workflow(self, mock_score_text, mock_search_memories, mock_store_memory):
        """Test complete end-to-end memory workflow."""
        # Mock the score_text method
        mock_score_text.return_value = {
            'importance_score': 0.9,
            'personalness_score': 0.8,
            'actionability_score': 0.1,
            'reasoning': 'This is personal information about the user'
        }
        
        # Mock the store_memory method
        mock_store_memory.return_value = "memory_id_123"
        
        # Mock the search_memories method
        mock_search_memories.return_value = [
            {
                "id": "memory_id_123",
                "text": "User is a software developer who loves hiking and photography",
                "memory_type": "semantic_profile",
                "user_id": "test_user_e2e",
                "importance_score": 0.9,
                "personalness_score": 0.8,
                "actionability_score": 0.1,
                "created_at": "2025-07-21T10:00:00Z",
                "updated_at": "2025-07-21T10:00:00Z"
            }
        ]
        
        # Step 1: Store some user information
        memory_id = self.memory_manager.store_memory(
            text="User is a software developer who loves hiking and photography",
            memory_type="semantic_profile",
            user_id="test_user_e2e",
            importance_score=0.9,
            personalness_score=0.8,
            actionability_score=0.1
        )
        
        self.assertIsNotNone(memory_id)
        
        # Step 2: Simulate a conversation where we retrieve memories
        from langchain_core.messages import HumanMessage
        from agents.services.agent_state import AgentState
        
        # Initialize conversation state
        state = AgentState(
            messages=[HumanMessage(content="What do you know about my hobbies?")],
            user_id="test_user_e2e",
            original_input="What do you know about my hobbies?",
            current_question="What do you know about my hobbies?"
        )
        
        # Run through memory retrieval
        result = self.orchestrator._retrieve_memories_node(state)
        
        # Should retrieve the stored memory
        memories = result.get("retrieved_long_term_memories", [])
        self.assertGreater(len(memories), 0)
        
        # Should contain information about hobbies
        memory_texts = [mem.get("text", "") for mem in memories]
        combined_text = " ".join(memory_texts).lower()
        self.assertTrue(
            any(hobby in combined_text for hobby in ["hiking", "photography"])
        )
    
    def test_memory_stats_and_management(self):
        """Test memory statistics and management functions."""
        # Get initial stats
        stats = self.memory_manager.get_memory_stats()
        
        self.assertIn("total_count", stats)
        self.assertIn("by_type", stats)
        self.assertIn("by_user", stats)
        
        initial_count = stats["total_count"]
        
        # Add a memory
        self.memory_manager.store_memory(
            text="Test memory for stats",
            memory_type="explicit_memory",
            user_id="stats_test_user",
            importance_score=0.5,
            personalness_score=0.3,
            actionability_score=0.1
        )
        
        # Check stats updated
        new_stats = self.memory_manager.get_memory_stats()
        self.assertGreaterEqual(new_stats["total_count"], initial_count)


@patch('agents.services.langgraph_orchestrator.LangGraphOrchestrator.process_query')
class FullIntegrationTests(TestCase):
    """Full integration tests that require API access."""
    
    def test_complete_conversation_with_memory(self, mock_process_query):
        """Test a complete conversation flow with memory integration."""
        # Mock the process_query method to return a simple response
        mock_process_query.return_value = [
            {'type': 'response', 'content': "That's great! I'll remember that you're a software engineer who enjoys playing guitar."}
        ]
        
        orchestrator = LangGraphOrchestrator()
        
        # Simulate a conversation where user shares information
        responses = []
        for response in orchestrator.process_query(
            user_input="I'm a software engineer and I love playing guitar",
            user_id="integration_test_user",
            conversation_history=[],
            streaming=True
        ):
            responses.append(response)
        
        # Should have processed successfully
        self.assertGreater(len(responses), 0)
        
        # Update mock for second call
        mock_process_query.return_value = [
            {'type': 'response', 'content': "I remember that you're a software engineer who enjoys playing guitar!"}
        ]
        
        # Now ask about the stored information
        responses2 = []
        for response in orchestrator.process_query(
            user_input="What do you remember about my hobbies?",
            user_id="integration_test_user",
            conversation_history=[
                {"role": "user", "content": "I'm a software engineer and I love playing guitar"},
                {"role": "assistant", "content": "That's great! I'll remember that you're a software engineer who enjoys playing guitar."}
            ],
            streaming=True
        ):
            responses2.append(response)
        
        # Should retrieve and use the stored information
        self.assertGreater(len(responses2), 0)
        
        # Check if we got any response at all
        final_response = responses2[-1].get("content", "") if responses2 else ""
        self.assertTrue(len(final_response) > 5)