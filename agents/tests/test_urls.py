from django.test import SimpleTestCase
from django.urls import reverse, resolve
from agents import views


class UnityIntegrationURLsTestCase(SimpleTestCase):
    """Test case for the Unity Integration API URLs."""
    
    def test_avatar_settings_url(self):
        """Test the URL for retrieving avatar settings."""
        url = reverse('agents:avatar_settings')
        self.assertEqual(url, '/api/agents/avatar/settings/')
        resolver = resolve(url)
        self.assertEqual(resolver.func.view_class, views.AvatarSettingsView)
    
    def test_avatar_outfit_url(self):
        """Test the URL for changing avatar outfit."""
        url = reverse('agents:avatar_outfit')
        self.assertEqual(url, '/api/agents/avatar/outfit/')
        resolver = resolve(url)
        self.assertEqual(resolver.func.view_class, views.AvatarOutfitView)
    
    def test_environment_change_url(self):
        """Test the URL for changing avatar environment."""
        url = reverse('agents:avatar_environment')
        self.assertEqual(url, '/api/agents/avatar/environment/')
        resolver = resolve(url)
        self.assertEqual(resolver.func.view_class, views.EnvironmentChangeView)
    
    def test_animation_play_url(self):
        """Test the URL for playing avatar animations."""
        url = reverse('agents:avatar_animation')
        self.assertEqual(url, '/api/agents/avatar/animation/')
        resolver = resolve(url)
        self.assertEqual(resolver.func.view_class, views.AnimationPlayView)
    
    def test_voice_command_url(self):
        """Test the URL for processing voice commands."""
        url = reverse('agents:avatar_voice_command')
        self.assertEqual(url, '/api/agents/avatar/voice-command/')
        resolver = resolve(url)
        self.assertEqual(resolver.func.view_class, views.VoiceCommandView)