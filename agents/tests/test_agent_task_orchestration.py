from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from unittest.mock import patch, MagicMock

from agents.models import TaskModel
from agents.services.orchestrator import AgentOrchestrator, Domain
from agents.services.domain_router import classify_domain

import uuid
import asyncio
import logging
from asgiref.sync import sync_to_async

User = get_user_model()
logger = logging.getLogger(__name__)

class AgentTaskOrchestrationTestCase(TestCase):
    """
    Comprehensive test suite for agent task orchestration.
    """
    
    def setUp(self):
        """
        Set up test environment with a user and mock orchestrator.
        """
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser', 
            email='<EMAIL>', 
            password='testpassword'
        )
        
        # Initialize the orchestrator
        self.orchestrator = AgentOrchestrator()
    
    def test_create_task_from_query(self):
        """
        Test creating a task from a user query.
        """
        # Mock the LLM response for task extraction
        with patch.object(self.orchestrator.client.chat.completions, 'create') as mock_create:
            # Simulate LLM response
            mock_response = MagicMock()
            mock_response.choices = [
                MagicMock(message=MagicMock(content="Develop a comprehensive project plan"))
            ]
            mock_create.return_value = mock_response
            
            # Create task
            task = self.orchestrator.create_task_from_query(
                user_id=str(self.user.id), 
                query="I need to develop a comprehensive project plan for our next quarter"
            )
        
        # Verify task creation
        self.assertIsNotNone(task)
        self.assertEqual(task['status'], 'pending')
        self.assertEqual(task['progress'], 0.0)
        self.assertTrue(len(task['phases']) > 0)
        
        # Verify database record
        db_task = TaskModel.objects.get(id=uuid.UUID(task['id']))
        self.assertEqual(db_task.user, self.user)
        self.assertEqual(db_task.title, task['title'])
    
    def test_get_user_tasks(self):
        """
        Test retrieving tasks for a user.
        """
        # Create multiple tasks
        tasks_data = [
            {
                'title': 'Project Planning',
                'description': 'Develop quarterly project plan',
                'status': 'pending',
                'progress': 0.0,
                'user': self.user
            },
            {
                'title': 'Market Research',
                'description': 'Conduct market analysis',
                'status': 'running',
                'progress': 25.0,
                'user': self.user
            }
        ]
        
        created_tasks = [TaskModel.objects.create(**task_data) for task_data in tasks_data]
        
        # Retrieve tasks
        user_tasks = self.orchestrator.get_user_tasks(str(self.user.id))
        
        # Verify tasks
        self.assertEqual(len(user_tasks), 2)
        task_titles = [task['title'] for task in user_tasks]
        self.assertIn('Project Planning', task_titles)
        self.assertIn('Market Research', task_titles)
    
    def test_update_task_progress(self):
        """
        Test updating task progress.
        """
        # Create a task
        task = TaskModel.objects.create(
            user=self.user,
            title='Development Task',
            description='Implement new feature',
            status='pending',
            progress=0.0,
            phases=[
                {
                    'phase': 'initialization',
                    'title': 'Project Start',
                    'description': 'Initial project setup',
                    'is_completed': False,
                    'is_current': True
                },
                {
                    'phase': 'development',
                    'title': 'Feature Implementation',
                    'description': 'Develop the new feature',
                    'is_completed': False,
                    'is_current': False
                }
            ]
        )
        
        # Update progress
        updated_task = self.orchestrator.update_task_progress(
            task_id=str(task.id), 
            progress=50.0, 
            current_phase='development'
        )
        
        # Verify update
        self.assertEqual(updated_task['progress'], 50.0)
        
        # Reload from database to confirm
        db_task = TaskModel.objects.get(id=task.id)
        self.assertEqual(db_task.progress, 50.0)
        
        # Check phase updates
        development_phase = [p for p in db_task.phases if p['phase'] == 'development'][0]
        initialization_phase = [p for p in db_task.phases if p['phase'] == 'initialization'][0]
        
        self.assertTrue(development_phase['is_current'])
        self.assertTrue(initialization_phase['is_completed'])
    
    def test_handle_task_intervention(self):
        """
        Test handling task intervention.
        """
        # Create a task
        task = TaskModel.objects.create(
            user=self.user,
            title='Complex Project',
            description='Requires detailed planning',
            status='running',
            progress=30.0
        )
        
        # Trigger intervention
        updated_task = self.orchestrator.handle_task_intervention(
            task_id=str(task.id), 
            intervention_message='Need more detailed requirements'
        )
        
        # Verify intervention
        self.assertEqual(updated_task['status'], 'needs_intervention')
        self.assertEqual(
            updated_task['intervention_message'], 
            'Need more detailed requirements'
        )
        
        # Reload from database to confirm
        db_task = TaskModel.objects.get(id=task.id)
        self.assertEqual(db_task.status, 'needs_intervention')
        self.assertEqual(
            db_task.intervention_message, 
            'Need more detailed requirements'
        )
    
    @patch('agents.services.orchestrator.classify_domain')
    def test_process_query_with_task_creation(self, mock_classify_domain):
        """
        Test process_query method with automatic task creation.
        """
        # Mock domain classification
        mock_classify_domain.return_value = Domain.GENERAL
        
        # Mock the LLM response for task extraction
        with patch.object(self.orchestrator.client.chat.completions, 'create') as mock_create:
            # Simulate LLM response
            mock_response = MagicMock()
            mock_response.choices = [
                MagicMock(message=MagicMock(content="Develop a comprehensive project plan"))
            ]
            mock_create.return_value = mock_response
            
            # Prepare test query
            user_input = "I need to develop a comprehensive project plan for our next quarter"
            
            # Manually create task
            task = self.orchestrator.create_task_from_query(
                user_id=str(self.user.id), 
                query=user_input
            )
            
            # Verify task details
            self.assertIsNotNone(task)
            self.assertEqual(task['status'], 'pending')
            self.assertEqual(task['progress'], 0.0)
            
            # Verify database record
            db_task = TaskModel.objects.get(id=uuid.UUID(task['id']))
            self.assertEqual(db_task.user, self.user)
            self.assertEqual(db_task.title, task['title'])
            
            # Optional: Test process_query method
            async def run_test():
                responses = []
                async for response in self.orchestrator.process_query(
                    user_input=user_input, 
                    user_id=str(self.user.id), 
                    auto_create_task=False  # Disable auto task creation
                ):
                    responses.append(response)
                return responses
            
            # Run the async test
            responses = asyncio.run(run_test())
            
            # Verify domain classification response
            domain_responses = [
                r for r in responses 
                if r['type'] == 'domain_classification'
            ]
            
            self.assertTrue(
                len(domain_responses) > 0, 
                "No domain classification response found"
            )
            
            domain_response = domain_responses[0]
            self.assertEqual(domain_response['domain'], Domain.GENERAL.value)
    
    def test_error_handling(self):
        """
        Test error handling in task management methods.
        """
        # Test creating task for non-existent user
        with self.assertRaises(ValueError):
            self.orchestrator.create_task_from_query(
                user_id=str(uuid.uuid4()),  # Random UUID 
                query="Test task"
            )
        
        # Test updating non-existent task
        with self.assertRaises(ValueError):
            self.orchestrator.update_task_progress(
                task_id=str(uuid.uuid4()),  # Random UUID
                progress=50.0
            )
        
        # Test task intervention for non-existent task
        with self.assertRaises(ValueError):
            self.orchestrator.handle_task_intervention(
                task_id=str(uuid.uuid4()),  # Random UUID
                intervention_message="Test intervention"
            ) 