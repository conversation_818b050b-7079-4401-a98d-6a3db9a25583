import uuid
from unittest import mock
from django.test import TestCase
from django.contrib.auth import get_user_model
from gamification.models import ShopItem, UserInventory, Pet
from agents.serializers import AvatarSettingsSerializer

User = get_user_model()


class AvatarSettingsSerializerTestCase(TestCase):
    """Test case for the AvatarSettingsSerializer."""
    
    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword123'
        )
        
        # Set some avatar preferences
        self.user.selected_personality = 'caringFriend'
        self.user.selected_environment = 'cozy_room'
        self.user.owned_environments = ['cozy_room', 'beach', 'forest']
        self.user.owned_outfits = ['casual', 'formal', 'sporty']
        self.user.owned_pets = ['cat', 'dog']
        self.user.preferences = {'voice_enabled': True, 'animation_speed': 1.0}
        self.user.save()
        
        # Generate UUIDs for test items
        self.outfit_id = uuid.uuid4()
        self.environment_id = uuid.uuid4()
        self.pet_id = uuid.uuid4()
        
        # Create test shop items
        self.outfit_item = ShopItem.objects.create(
            id=self.outfit_id,
            name='casual',
            description='Casual outfit',
            price=100,
            item_type='outfit',
            image_url='https://example.com/casual.png',
            preview_data={'color': 'blue'}
        )
        
        self.environment_item = ShopItem.objects.create(
            id=self.environment_id,
            name='cozy_room',
            description='Cozy room environment',
            price=200,
            item_type='environment',
            image_url='https://example.com/cozy_room.png',
            preview_data={'lighting': 'warm'}
        )
        
        self.pet_item = ShopItem.objects.create(
            id=self.pet_id,
            name='cat',
            description='Cat pet',
            price=300,
            item_type='pet',
            image_url='https://example.com/cat.png',
            preview_data={'behavior': 'playful'}
        )
        
        # Add items to user inventory
        self.outfit_inventory = UserInventory.objects.create(
            user=self.user,
            item=self.outfit_item,
            is_equipped=True
        )
        
        self.environment_inventory = UserInventory.objects.create(
            user=self.user,
            item=self.environment_item,
            is_equipped=True
        )
        
        self.pet_inventory = UserInventory.objects.create(
            user=self.user,
            item=self.pet_item,
            is_equipped=True
        )
        
        # Create a pet in the Pet model
        self.pet = Pet.objects.create(
            name='cat',
            pet_type='feline',
            behavior='playful',
            sound='meow',
            animation_data={'idle': 'cat_idle', 'play': 'cat_play'}
        )
    
    def test_serializer_contains_expected_fields(self):
        """Test that the serializer contains the expected fields."""
        serializer = AvatarSettingsSerializer(instance=self.user)
        data = serializer.data
        
        expected_fields = [
            'selected_personality',
            'selected_environment',
            'owned_environments',
            'owned_outfits',
            'owned_pets',
            'current_outfit',
            'current_environment',
            'current_pet',
            'preferences'
        ]
        
        self.assertEqual(set(data.keys()), set(expected_fields))
    
    def test_serializer_field_content(self):
        """Test that the serializer fields contain the expected content."""
        serializer = AvatarSettingsSerializer(instance=self.user)
        data = serializer.data
        
        # Check basic fields
        self.assertEqual(data['selected_personality'], 'caringFriend')
        self.assertEqual(data['selected_environment'], 'cozy_room')
        self.assertEqual(data['owned_environments'], ['cozy_room', 'beach', 'forest'])
        self.assertEqual(data['owned_outfits'], ['casual', 'formal', 'sporty'])
        self.assertEqual(data['owned_pets'], ['cat', 'dog'])
        self.assertEqual(data['preferences'], {'voice_enabled': True, 'animation_speed': 1.0})
        
        # Check current outfit
        self.assertIsNotNone(data['current_outfit'])
        self.assertEqual(data['current_outfit']['name'], 'casual')
        self.assertEqual(data['current_outfit']['image_url'], 'https://example.com/casual.png')
        self.assertEqual(data['current_outfit']['preview_data'], {'color': 'blue'})
        
        # Check current environment
        self.assertIsNotNone(data['current_environment'])
        self.assertEqual(data['current_environment']['name'], 'cozy_room')
        self.assertEqual(data['current_environment']['image_url'], 'https://example.com/cozy_room.png')
        self.assertEqual(data['current_environment']['preview_data'], {'lighting': 'warm'})
        
        # Check current pet
        self.assertIsNotNone(data['current_pet'])
        self.assertEqual(data['current_pet']['name'], 'cat')
        self.assertEqual(data['current_pet']['image_url'], 'https://example.com/cat.png')
        self.assertEqual(data['current_pet']['preview_data'], {'behavior': 'playful'})
        self.assertEqual(data['current_pet']['pet_type'], 'feline')
        self.assertEqual(data['current_pet']['behavior'], 'playful')
        self.assertEqual(data['current_pet']['sound'], 'meow')
        self.assertEqual(data['current_pet']['animation_data'], {'idle': 'cat_idle', 'play': 'cat_play'})
    
    def test_serializer_with_no_equipped_items(self):
        """Test serializer behavior when no items are equipped."""
        # Unequip all items
        UserInventory.objects.filter(user=self.user).update(is_equipped=False)
        
        serializer = AvatarSettingsSerializer(instance=self.user)
        data = serializer.data
        
        # Current outfit should be None
        self.assertIsNone(data['current_outfit'])
        
        # Current environment should fall back to selected_environment
        self.assertIsNotNone(data['current_environment'])
        self.assertEqual(data['current_environment']['name'], 'cozy_room')
        
        # Current pet should be None
        self.assertIsNone(data['current_pet'])
    
    def test_serializer_with_missing_pet_details(self):
        """Test serializer behavior when pet details are missing."""
        # Delete the pet details
        self.pet.delete()
        
        serializer = AvatarSettingsSerializer(instance=self.user)
        data = serializer.data
        
        # Current pet should still have basic info but not the additional details
        self.assertIsNotNone(data['current_pet'])
        self.assertEqual(data['current_pet']['name'], 'cat')
        self.assertNotIn('pet_type', data['current_pet'])
        self.assertNotIn('behavior', data['current_pet'])
        self.assertNotIn('sound', data['current_pet'])
        self.assertNotIn('animation_data', data['current_pet'])