from rest_framework import serializers
from django.contrib.auth import get_user_model
from gamification.models import ShopI<PERSON>, UserInventory, Pet
from .models import TaskModel
from memory.models import Memory, MemoryType, MemoryConfiguration, MemoryCluster

User = get_user_model()

class AvatarSettingsSerializer(serializers.Serializer):
    """
    Serializer for avatar settings and customization.
    This serializer handles avatar-related settings from the User model
    and provides a comprehensive view of all avatar customization options.
    """
    # Basic avatar settings
    selected_personality = serializers.Char<PERSON>ield(read_only=True)
    selected_environment = serializers.Char<PERSON>ield(read_only=True)
    
    # Owned items
    owned_environments = serializers.<PERSON><PERSON><PERSON><PERSON>(read_only=True)
    owned_outfits = serializers.J<PERSON><PERSON><PERSON>(read_only=True)
    owned_pets = serializers.JSONField(read_only=True)
    
    # Current outfit details
    current_outfit = serializers.SerializerMethodField()
    current_environment = serializers.SerializerMethodField()
    current_pet = serializers.SerializerMethodField()
    
    # Additional avatar preferences
    preferences = serializers.J<PERSON><PERSON>ield(read_only=True)
    
    def get_current_outfit(self, obj):
        """Get details of the currently equipped outfit"""
        try:
            current_outfit = UserInventory.objects.filter(
                user=obj,
                item__item_type='outfit',
                is_equipped=True
            ).select_related('item').first()
            
            if current_outfit:
                return {
                    'id': str(current_outfit.item.id),
                    'name': current_outfit.item.name,
                    'image_url': current_outfit.item.image_url,
                    'preview_data': current_outfit.item.preview_data
                }
            return None
        except Exception:
            return None
    
    def get_current_environment(self, obj):
        """Get details of the currently selected environment"""
        try:
            current_environment = UserInventory.objects.filter(
                user=obj,
                item__item_type='environment',
                is_equipped=True
            ).select_related('item').first()
            
            if current_environment:
                return {
                    'id': str(current_environment.item.id),
                    'name': current_environment.item.name,
                    'image_url': current_environment.item.image_url,
                    'preview_data': current_environment.item.preview_data
                }
            
            # Fallback to default environment if none is equipped
            if obj.selected_environment:
                try:
                    default_env = ShopItem.objects.filter(
                        name=obj.selected_environment,
                        item_type='environment'
                    ).first()
                    if default_env:
                        return {
                            'id': str(default_env.id),
                            'name': default_env.name,
                            'image_url': default_env.image_url,
                            'preview_data': default_env.preview_data
                        }
                except Exception:
                    pass
            return None
        except Exception:
            return None
    
    def get_current_pet(self, obj):
        """Get details of the currently equipped pet"""
        try:
            current_pet = UserInventory.objects.filter(
                user=obj,
                item__item_type='pet',
                is_equipped=True
            ).select_related('item').first()
            
            if current_pet:
                # Try to get additional pet details from Pet model
                pet_details = Pet.objects.filter(name=current_pet.item.name).first()
                
                pet_data = {
                    'id': str(current_pet.item.id),
                    'name': current_pet.item.name,
                    'image_url': current_pet.item.image_url,
                    'preview_data': current_pet.item.preview_data
                }
                
                # Add additional pet details if available
                if pet_details:
                    pet_data.update({
                        'pet_type': pet_details.pet_type,
                        'behavior': pet_details.behavior,
                        'sound': pet_details.sound,
                        'animation_data': pet_details.animation_data
                    })
                
                return pet_data
            return None
        except Exception:
            return None

class TaskModelSerializer(serializers.ModelSerializer):
    """
    Serializer for TaskModel to handle JSON representation.
    """
    phases = serializers.JSONField()
    
    class Meta:
        model = TaskModel
        fields = [
            'id', 
            'user', 
            'title', 
            'description', 
            'status', 
            'progress', 
            'created_at', 
            'updated_at', 
            'phases', 
            'error_message', 
            'intervention_message'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'user']
    
    def create(self, validated_data):
        """
        Create a new task with initial phases.
        """
        phases = validated_data.pop('phases', [])
        
        # Ensure user is set from request context
        request = self.context.get('request')
        if not request or not request.user:
            raise serializers.ValidationError("User is required to create a task")
        
        task = TaskModel.objects.create(user=request.user, **validated_data)
        
        # Add initial phases if provided
        for phase in phases:
            task.add_phase(
                phase=phase.get('phase'),
                title=phase.get('title'),
                description=phase.get('description'),
                sub_tasks=phase.get('sub_tasks', [])
            )
        
        return task
    
    def update(self, instance, validated_data):
        """
        Update task with new phases or status.
        """
        phases = validated_data.pop('phases', None)
        
        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        # Update phases if provided
        if phases is not None:
            instance.phases = phases
        
        instance.save()
        return instance

class MemorySerializer(serializers.ModelSerializer):
    """
    Serializer for Memory model with comprehensive memory management.
    """
    memory_type_display = serializers.CharField(source='get_memory_type_display', read_only=True)
    salience_score = serializers.SerializerMethodField()
    
    class Meta:
        model = Memory
        fields = [
            'id', 'user', 'content', 'memory_type', 'memory_type_display',
            'importance_score', 'personalness_score', 'actionability_score',
            'salience_score', 'vector_id', 'source_conversation', 'source_message',
            'metadata', 'created_at', 'updated_at', 'last_accessed',
            'is_active', 'is_verified'
        ]
        read_only_fields = ['id', 'user', 'vector_id', 'created_at', 'updated_at', 'last_accessed']
    
    def get_salience_score(self, obj):
        """Calculate and return the weighted salience score."""
        return obj.get_salience_score()
    
    def create(self, validated_data):
        """Create a new memory with user from request context."""
        request = self.context.get('request')
        if not request or not request.user:
            raise serializers.ValidationError("User is required to create a memory")
        
        validated_data['user'] = request.user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """Update memory and handle access time tracking."""
        # Update access time when memory is modified
        instance.update_access_time()
        return super().update(instance, validated_data)


class MemoryCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new memories with automatic salience scoring.
    """
    class Meta:
        model = Memory
        fields = [
            'content', 'memory_type', 'importance_score', 'personalness_score',
            'actionability_score', 'metadata', 'source_conversation', 'source_message'
        ]
    
    def create(self, validated_data):
        """Create memory with user and generate vector_id."""
        request = self.context.get('request')
        if not request or not request.user:
            raise serializers.ValidationError("User is required to create a memory")
        
        validated_data['user'] = request.user
        
        # Generate a simple vector_id (in production, this would be from vector store)
        import uuid
        validated_data['vector_id'] = f"mem_{uuid.uuid4().hex[:16]}"
        
        return super().create(validated_data)


class MemoryUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating existing memories.
    """
    class Meta:
        model = Memory
        fields = [
            'content', 'memory_type', 'importance_score', 'personalness_score',
            'actionability_score', 'metadata', 'is_active', 'is_verified'
        ]


class MemoryClusterSerializer(serializers.ModelSerializer):
    """
    Serializer for MemoryCluster model.
    """
    memory_count = serializers.SerializerMethodField()
    
    class Meta:
        model = MemoryCluster
        fields = [
            'id', 'user', 'name', 'description', 'cluster_type',
            'memory_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']
    
    def get_memory_count(self, obj):
        """Get the number of memories in this cluster."""
        return obj.get_memory_count()
    
    def create(self, validated_data):
        """Create cluster with user from request context."""
        request = self.context.get('request')
        if not request or not request.user:
            raise serializers.ValidationError("User is required to create a memory cluster")
        
        validated_data['user'] = request.user
        return super().create(validated_data)


class MemoryConfigurationSerializer(serializers.ModelSerializer):
    """
    Serializer for MemoryConfiguration model.
    """
    class Meta:
        model = MemoryConfiguration
        fields = [
            'min_importance_threshold', 'min_personalness_threshold', 'min_actionability_threshold',
            'max_memories_per_retrieval', 'similarity_threshold',
            'allow_memory_storage', 'allow_cross_conversation_memory',
            'auto_cleanup_enabled', 'memory_retention_days',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def create(self, validated_data):
        """Create configuration with user from request context."""
        request = self.context.get('request')
        if not request or not request.user:
            raise serializers.ValidationError("User is required to create memory configuration")
        
        validated_data['user'] = request.user
        return super().create(validated_data)


class MemoryTypeSerializer(serializers.Serializer):
    """
    Serializer for memory type choices.
    """
    value = serializers.CharField()
    display = serializers.CharField()


class MemoryStatsSerializer(serializers.Serializer):
    """
    Serializer for memory statistics.
    """
    total_memories = serializers.IntegerField()
    active_memories = serializers.IntegerField()
    verified_memories = serializers.IntegerField()
    memory_types = serializers.DictField()
    average_salience_score = serializers.FloatField()
    recent_memories_count = serializers.IntegerField()