"""
Tests for agent services.
"""
import pytest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from django.test import TestCase

from agents.services.domain_router import Domain, classify_domain, domain_router_node
from agents.services.langgraph_orchestrator import LangGraphOrchestrator
from agents.services.memory_manager import MemoryManager
from agents.services.salience_scorer import MemorySalienceScorer
from agents.services.agent_state import AgentState
from agents.services.specialized_agents import (
    BaseAgent, BusinessAgent, LearningAgent, MusicAgent,
    DevAgent, TriviAgent, AgentFactory
)
from agents.services.agent_coordinator import AgentCoordinator, get_agent_coordinator
from agents.services.orchestrator import AgentOrchestrator
from chat.services.fast_response_service import FastResponseService


class DomainRouterTests(TestCase):
    """Test cases for Domain Router functions."""
    
    def test_domain_enum_values(self):
        """Test that Domain enum has expected values."""
        expected_domains = [
            "general", "dev", "web", "business", 
            "learning", "music", "trivia"
        ]
        
        for domain_value in expected_domains:
            self.assertTrue(hasattr(Domain, domain_value.upper()))
            self.assertEqual(getattr(Domain, domain_value.upper()).value, domain_value)
    
    def test_domain_router_node_basic(self):
        """Test domain router node with basic state."""
        from langchain_core.messages import HumanMessage
        
        state = AgentState(
            messages=[HumanMessage(content="How do I fix this Python bug?")],
            current_question="How do I fix this Python bug?"
        )
        
        # This would normally classify the domain, but we'll just test structure
        result = domain_router_node(state)
        
        self.assertIn("domain", result)
        self.assertIsInstance(result["domain"], Domain)
    
    def test_domain_router_node_missing_data(self):
        """Test domain router node with missing data."""
        state = AgentState(messages=[], current_question="")
        
        result = domain_router_node(state)
        
        # Should default to GENERAL when data is missing
        self.assertEqual(result["domain"], Domain.GENERAL)


class LangGraphOrchestratorTests(TestCase):
    """Test cases for LangGraph Orchestrator service."""
    
    def setUp(self):
        self.orchestrator = LangGraphOrchestrator()
    
    def test_orchestrator_initialization(self):
        """Test that LangGraphOrchestrator initializes correctly."""
        self.assertIsNotNone(self.orchestrator.llm)
        self.assertIsNotNone(self.orchestrator.memory_manager)
        self.assertIsNotNone(self.orchestrator.graph)
    
    def test_memory_manager_initialization(self):
        """Test that memory manager initializes correctly."""
        self.assertIsInstance(self.orchestrator.memory_manager, MemoryManager)
        self.assertIsNotNone(self.orchestrator.memory_manager.vectorstore)
    
    def test_build_emotion_guidance_sadness(self):
        """Test emotion guidance building for sadness."""
        emotion_context = {
            'emotions': [
                {'name': 'sadness', 'score': 0.8}
            ]
        }
        
        guidance = self.orchestrator._build_emotion_guidance(emotion_context)
        
        self.assertIn('sadness', guidance.lower())
        self.assertIn('empathy', guidance.lower())
        self.assertIn('support', guidance.lower())
    
    def test_build_emotion_guidance_excitement(self):
        """Test emotion guidance building for excitement."""
        emotion_context = {
            'emotions': [
                {'name': 'excitement', 'score': 0.9}
            ]
        }
        
        guidance = self.orchestrator._build_emotion_guidance(emotion_context)
        
        self.assertIn('excitement', guidance.lower())
        self.assertIn('energy', guidance.lower())
        self.assertIn('enthusiasm', guidance.lower())
    
    def test_build_memory_guidance(self):
        """Test memory guidance building."""
        memory_context = {
            'memories': [
                {'text': 'User likes jazz music', 'type': 'preference'},
                {'text': 'User is a software developer', 'type': 'profession'},
                {'text': 'User has a dog named Max', 'type': 'personal'}
            ]
        }
        
        guidance = self.orchestrator._build_memory_guidance(memory_context)
        
        self.assertIn('jazz music', guidance)
        self.assertIn('software developer', guidance)
        self.assertIn('dog named Max', guidance)
        self.assertIn('personalize', guidance.lower())
    
    def test_build_conversation_messages(self):
        """Test conversation message building."""
        system_prompt = "You are a helpful assistant."
        user_input = "Hello, how are you?"
        conversation_history = [
            {'role': 'user', 'content': 'Hi there'},
            {'role': 'assistant', 'content': 'Hello! How can I help you?'}
        ]
        
        messages = self.orchestrator._build_conversation_messages(
            system_prompt=system_prompt,
            user_input=user_input,
            conversation_history=conversation_history
        )
        
        self.assertEqual(len(messages), 4)  # system + 2 history + current
        self.assertEqual(messages[0]['role'], 'system')
        self.assertEqual(messages[0]['content'], system_prompt)
        self.assertEqual(messages[-1]['role'], 'user')
        self.assertEqual(messages[-1]['content'], user_input)
    
    def test_build_system_prompt_with_emotion_and_memory(self):
        """Test system prompt building with emotion and memory context."""
        emotion_context = {
            'emotions': [{'name': 'joy', 'score': 0.8}]
        }
        memory_context = {
            'memories': [{'text': 'User likes coffee', 'type': 'preference'}]
        }
        
        prompt = self.orchestrator._build_system_prompt(
            domain=Domain.GENERAL,
            emotion_context=emotion_context,
            memory_context=memory_context,
            user_id="test_user"
        )
        
        self.assertIn('KD Assistant', prompt)
        self.assertIn('joy', prompt.lower())
        self.assertIn('coffee', prompt)
        self.assertIn('test_user', prompt)
    
    def test_get_timestamp_format(self):
        """Test timestamp format."""
        timestamp = self.orchestrator._get_timestamp()
        
        # Should be in ISO format
        self.assertRegex(timestamp, r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}')


# Integration tests would require actual API keys and should be run separately
class IntegrationTests(TestCase):
    """Integration tests for agent services (requires API keys)."""
    
    @pytest.mark.skip(reason="Requires OpenAI API key")
    async def test_full_orchestration_flow(self):
        """Test complete orchestration flow with real API calls."""
        orchestrator = AgentOrchestrator()
        
        responses = []
        async for response in orchestrator.process_query(
            user_input="Can you recommend some Python learning resources?",
            user_id="test_user",
            conversation_history=[],
            emotion_context={'emotions': [{'name': 'curiosity', 'score': 0.8}]},
            streaming=True
        ):
            responses.append(response)
        
        # Should have domain classification and response chunks
        domain_responses = [r for r in responses if r['type'] == 'domain_classification']
        content_responses = [r for r in responses if r['type'] == 'response_chunk']
        
        self.assertGreater(len(domain_responses), 0)
        self.assertGreater(len(content_responses), 0)

class SpecializedAgentTests(TestCase):
    """Test cases for specialized agent services."""
    
    def test_agent_factory_create_business_agent(self):
        """Test creating business agent through factory."""
        agent = AgentFactory.create_agent('business')
        self.assertIsInstance(agent, BusinessAgent)
        self.assertEqual(agent.agent_name, 'BusinessAgent')
    
    def test_agent_factory_create_learning_agent(self):
        """Test creating learning agent through factory."""
        agent = AgentFactory.create_agent('learning')
        self.assertIsInstance(agent, LearningAgent)
        self.assertEqual(agent.agent_name, 'LearningAgent')
    
    def test_agent_factory_create_music_agent(self):
        """Test creating music agent through factory."""
        agent = AgentFactory.create_agent('music')
        self.assertIsInstance(agent, MusicAgent)
        self.assertEqual(agent.agent_name, 'MusicAgent')
    
    def test_agent_factory_create_dev_agent(self):
        """Test creating dev agent through factory."""
        agent = AgentFactory.create_agent('dev')
        self.assertIsInstance(agent, DevAgent)
        self.assertEqual(agent.agent_name, 'DevAgent')
    
    def test_agent_factory_create_trivia_agent(self):
        """Test creating trivia agent through factory."""
        agent = AgentFactory.create_agent('trivia')
        self.assertIsInstance(agent, TriviAgent)
        self.assertEqual(agent.agent_name, 'TriviAgent')
    
    def test_agent_factory_invalid_type(self):
        """Test that factory raises error for invalid agent type."""
        with self.assertRaises(ValueError):
            AgentFactory.create_agent('invalid_agent')
    
    def test_agent_factory_get_available_agents(self):
        """Test getting list of available agents."""
        available = AgentFactory.get_available_agents()
        expected = ['business', 'learning', 'music', 'dev', 'trivia']
        self.assertEqual(set(available), set(expected))
    
    def test_business_agent_persona(self):
        """Test business agent persona content."""
        agent = BusinessAgent()
        persona = agent.get_persona()
        
        self.assertIn('Business Agent', persona)
        self.assertIn('business strategy', persona.lower())
        self.assertIn('finance', persona.lower())
        self.assertIn('professional', persona.lower())
        self.assertGreater(len(persona), 200)
    
    def test_learning_agent_persona(self):
        """Test learning agent persona content."""
        agent = LearningAgent()
        persona = agent.get_persona()
        
        self.assertIn('Learning Agent', persona)
        self.assertIn('education', persona.lower())
        self.assertIn('explain', persona.lower())
        self.assertIn('knowledge', persona.lower())
        self.assertGreater(len(persona), 200)
    
    def test_music_agent_persona(self):
        """Test music agent persona content."""
        agent = MusicAgent()
        persona = agent.get_persona()
        
        self.assertIn('Music Agent', persona)
        self.assertIn('music', persona.lower())
        self.assertIn('recommendations', persona.lower())
        self.assertIn('genres', persona.lower())
        self.assertGreater(len(persona), 200)
    
    def test_dev_agent_persona(self):
        """Test dev agent persona content."""
        agent = DevAgent()
        persona = agent.get_persona()
        
        self.assertIn('Dev Agent', persona)
        self.assertIn('programming', persona.lower())
        self.assertIn('software', persona.lower())
        self.assertIn('code', persona.lower())
        self.assertGreater(len(persona), 200)
    
    def test_trivia_agent_persona(self):
        """Test trivia agent persona content."""
        agent = TriviAgent()
        persona = agent.get_persona()
        
        self.assertIn('Trivia Agent', persona)
        self.assertIn('trivia', persona.lower())
        self.assertIn('fun', persona.lower())
        self.assertIn('games', persona.lower())
        self.assertGreater(len(persona), 200)
    
    def test_base_agent_emotion_guidance_sadness(self):
        """Test emotion guidance for sadness."""
        agent = BusinessAgent()
        guidance = agent._get_emotion_specific_guidance('sadness')
        
        self.assertIn('empathy', guidance.lower())
        self.assertIn('support', guidance.lower())
        self.assertIn('comfort', guidance.lower())
    
    def test_base_agent_emotion_guidance_excitement(self):
        """Test emotion guidance for excitement."""
        agent = BusinessAgent()
        guidance = agent._get_emotion_specific_guidance('excitement')
        
        self.assertIn('energy', guidance.lower())
        self.assertIn('enthusiasm', guidance.lower())
        self.assertIn('upbeat', guidance.lower())
    
    def test_base_agent_build_emotion_guidance(self):
        """Test building emotion guidance from context."""
        agent = BusinessAgent()
        emotion_context = {
            'emotions': [
                {'name': 'joy', 'score': 0.8},
                {'name': 'excitement', 'score': 0.6}
            ]
        }
        
        guidance = agent._build_emotion_guidance(emotion_context)
        
        self.assertIn('joy', guidance.lower())
        self.assertIn('0.80', guidance)
        self.assertIn('energy', guidance.lower())
    
    def test_base_agent_build_memory_guidance(self):
        """Test building memory guidance from context."""
        agent = BusinessAgent()
        memory_context = {
            'memories': [
                {'text': 'User works in tech startup', 'type': 'profession'},
                {'text': 'User interested in marketing', 'type': 'interest'}
            ]
        }
        
        guidance = agent._build_memory_guidance(memory_context)
        
        self.assertIn('tech startup', guidance)
        self.assertIn('marketing', guidance)
        self.assertIn('personalize', guidance.lower())
    
    def test_base_agent_build_system_prompt(self):
        """Test building complete system prompt with all contexts."""
        agent = BusinessAgent()
        emotion_context = {'emotions': [{'name': 'curiosity', 'score': 0.7}]}
        memory_context = {'memories': [{'text': 'User is entrepreneur', 'type': 'profession'}]}
        
        prompt = agent._build_system_prompt(
            emotion_context=emotion_context,
            memory_context=memory_context,
            user_id="test_user"
        )
        
        self.assertIn('Business Agent', prompt)
        self.assertIn('curiosity', prompt.lower())
        self.assertIn('entrepreneur', prompt)
        self.assertIn('test_user', prompt)
    
    def test_base_agent_build_messages(self):
        """Test building conversation messages."""
        agent = BusinessAgent()
        system_prompt = "You are a business agent."
        user_input = "How do I start a business?"
        conversation_history = [
            {'role': 'user', 'content': 'Hello'},
            {'role': 'assistant', 'content': 'Hi there!'}
        ]
        
        messages = agent._build_messages(
            system_prompt=system_prompt,
            user_input=user_input,
            conversation_history=conversation_history
        )
        
        self.assertEqual(len(messages), 4)  # system + 2 history + current
        self.assertEqual(messages[0]['role'], 'system')
        self.assertEqual(messages[-1]['content'], user_input)
    
    @patch('agents.services.specialized_agents.openai.OpenAI')
    def test_base_agent_initialization(self, mock_openai):
        """Test base agent initialization."""
        agent = BusinessAgent()
        
        self.assertIsNotNone(agent.client)
        self.assertEqual(agent.model, "gpt-3.5-turbo-0125")
        self.assertEqual(agent.agent_name, "BusinessAgent")
    
    def test_base_agent_timestamp_format(self):
        """Test timestamp format."""
        agent = BusinessAgent()
        timestamp = agent._get_timestamp()
        
        # Should be in ISO format
        self.assertRegex(timestamp, r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}')

class AgentCoordinatorTests(TestCase):
    """Test cases for AgentCoordinator service."""
    
    def setUp(self):
        self.coordinator = AgentCoordinator()
    
    @patch('agents.services.agent_coordinator.AgentFactory')
    def test_coordinator_initialization(self, mock_factory):
        """Test that AgentCoordinator initializes correctly."""
        mock_factory.get_available_agents.return_value = ['business', 'music']
        mock_factory.create_agent.side_effect = [Mock(), Mock()]
        
        coordinator = AgentCoordinator()
        
        # Domain router is now a function, not an attribute
        self.assertIsNotNone(coordinator.orchestrator)
        self.assertIsInstance(coordinator.specialized_agents, dict)
    
    def test_domain_to_agent_type_mapping(self):
        """Test domain to agent type mapping."""
        mappings = [
            (Domain.BUSINESS, 'business'),
            (Domain.LEARNING, 'learning'),
            (Domain.MUSIC, 'music'),
            (Domain.DEV, 'dev'),
            (Domain.TRIVIA, 'trivia'),
            (Domain.WEB, 'learning'),  # Web maps to learning
        ]
        
        for domain, expected_agent_type in mappings:
            agent_type = self.coordinator._domain_to_agent_type(domain)
            self.assertEqual(agent_type, expected_agent_type)
    
    def test_domain_to_agent_type_fallback(self):
        """Test domain to agent type fallback for unknown domains."""
        # Test with a domain that doesn't have explicit mapping
        agent_type = self.coordinator._domain_to_agent_type(Domain.GENERAL)
        self.assertEqual(agent_type, 'business')  # Default fallback
    
    async def test_get_agent_capabilities(self):
        """Test getting agent capabilities."""
        capabilities = await self.coordinator.get_agent_capabilities()
        
        self.assertIn('available_domains', capabilities)
        self.assertIn('specialized_agents', capabilities)
        self.assertIn('features', capabilities)
        
        # Check that all domains are listed
        expected_domains = [domain.value for domain in Domain]
        self.assertEqual(set(capabilities['available_domains']), set(expected_domains))
        
        # Check features
        features = capabilities['features']
        self.assertTrue(features['emotion_awareness'])
        self.assertTrue(features['memory_integration'])
        self.assertTrue(features['streaming_responses'])
        self.assertTrue(features['domain_classification'])
        self.assertTrue(features['confidence_scoring'])
    
    def test_get_timestamp_format(self):
        """Test timestamp format."""
        timestamp = self.coordinator._get_timestamp()
        
        # Should be in ISO format
        self.assertRegex(timestamp, r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}')
    
    def test_singleton_agent_coordinator(self):
        """Test that get_agent_coordinator returns singleton instance."""
        coordinator1 = get_agent_coordinator()
        coordinator2 = get_agent_coordinator()
        
        self.assertIs(coordinator1, coordinator2)
        self.assertIsInstance(coordinator1, AgentCoordinator)
    
    @patch('agents.services.agent_coordinator.AgentFactory')
    async def test_health_check_all_healthy(self, mock_factory):
        """Test health check when all agents are healthy."""
        # Mock agents
        mock_agent = Mock()
        mock_agent.get_persona.return_value = "Test persona"
        
        mock_factory.get_available_agents.return_value = ['business']
        mock_factory.create_agent.return_value = mock_agent
        
        # Mock domain classification function
        with patch('agents.services.agent_coordinator.classify_domain') as mock_classify:
            mock_classify.return_value = Domain.GENERAL
            
            coordinator = AgentCoordinator()
            health = await coordinator.health_check()
            
            self.assertEqual(health['overall_status'], 'healthy')
            self.assertIn('domain_router', health['agents'])
            self.assertEqual(health['agents']['domain_router']['status'], 'healthy')
    
    @patch('agents.services.agent_coordinator.AgentFactory')
    async def test_health_check_with_failures(self, mock_factory):
        """Test health check when some agents fail."""
        # Mock failing agent
        mock_agent = Mock()
        mock_agent.get_persona.side_effect = Exception("Agent error")
        
        mock_factory.get_available_agents.return_value = ['business']
        mock_factory.create_agent.return_value = mock_agent
        
        # Mock domain classification failure
        with patch('agents.services.agent_coordinator.classify_domain') as mock_classify:
            mock_classify.side_effect = Exception("Router error")
            
            coordinator = AgentCoordinator()
            health = await coordinator.health_check()
            
            self.assertEqual(health['overall_status'], 'degraded')
            # Check that domain router and business agent are in health results
            self.assertIn('domain_router', health['agents'])
            self.assertIn('business', health['agents'])


class MockUser:
    """Mock user for testing."""
    def __init__(self, personality='caringFriend', companion_name='Ella', first_name='TestUser'):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = first_name
        self.id = f"test_{personality}"


class FastResponseServiceTests(TestCase):
    """Comprehensive tests for the Fast Response Service."""

    def setUp(self):
        """Set up test fixtures."""
        self.user = MockUser()
        self.service = FastResponseService(user=self.user)

    def test_fast_response_service_initialization(self):
        """Test that FastResponseService initializes correctly."""
        self.assertIsNotNone(self.service.groq_llm)
        self.assertEqual(self.service.user, self.user)
        self.assertEqual(self.service.response_count, 0)
        self.assertEqual(self.service.total_first_response_time, 0.0)

    def test_needs_agent_processing_simple_queries(self):
        """Test agent processing detection for simple conversational queries."""
        simple_queries = [
            "Hello, how are you?",
            "Tell me a joke",
            "What's your favorite color?",
            "Good morning!",
            "How was your day?",
            "I love you",
            "You're amazing",
            "Thanks for helping me"
        ]

        for query in simple_queries:
            with self.subTest(query=query):
                needs_agent = self.service._needs_agent_processing(query)
                self.assertFalse(needs_agent, f"Query '{query}' should not need agent processing")

    def test_needs_agent_processing_complex_queries(self):
        """Test agent processing detection for complex queries requiring specialized agents."""
        complex_queries = [
            # Analysis and research
            "Can you analyze the pros and cons of remote work?",
            "Research the latest trends in artificial intelligence",
            "Compare different investment strategies for beginners",
            "Evaluate the effectiveness of various marketing approaches",

            # Problem solving
            "I'm having trouble with this math problem",
            "Help me solve this coding issue",
            "I need help figuring out my career path",
            "Can you help me debug this Python script?",

            # Planning and strategy
            "Create a business plan for my startup",
            "Plan a comprehensive study schedule for my exams",
            "Develop a marketing strategy for my product",
            "What's the best approach to learning machine learning?",

            # Deep learning and education
            "Explain quantum computing in detail",
            "Teach me about advanced calculus concepts",
            "I want to understand how neural networks work",
            "Can you create a tutorial on web development?",

            # Technical and development
            "Review this code for potential improvements",
            "Help me optimize this algorithm",
            "Design a database schema for my application",
            "What are the best practices for software architecture?"
        ]

        for query in complex_queries:
            with self.subTest(query=query):
                needs_agent = self.service._needs_agent_processing(query)
                self.assertTrue(needs_agent, f"Query '{query}' should need agent processing")

    def test_needs_agent_processing_edge_cases(self):
        """Test edge cases for agent processing detection."""
        edge_cases = [
            # Long conversational queries (should trigger due to length)
            ("This is a very long conversational query that goes on and on about many different topics and should trigger agent processing due to its length even though it's not particularly complex in nature", True),

            # Short but complex
            ("Analyze this", True),
            ("Compare options", True),
            ("Solve problem", True),

            # Borderline cases
            ("How do I learn Python?", True),  # Learning-related
            ("What's the weather like?", False),  # Simple question
            ("Can you recommend a good book?", True),  # Recommendation (might need web search)
        ]

        for query, expected in edge_cases:
            with self.subTest(query=query):
                needs_agent = self.service._needs_agent_processing(query)
                self.assertEqual(needs_agent, expected, f"Query '{query}' agent processing detection failed")

    def test_build_fast_system_prompt_personalities(self):
        """Test system prompt building for different personalities."""
        personalities = [
            ('caringFriend', 'Ella', 'warm, supportive, and caring'),
            ('playfulCompanion', 'Luna', 'energetic, playful, and fun'),
            ('wiseMentor', 'Sage', 'thoughtful, wise, and insightful'),
            ('romanticPartner', 'Alex', 'affectionate, loving, and intimate'),
            ('supportiveTherapist', 'Dr. Hope', 'calm, understanding, and therapeutic')
        ]

        for personality, companion_name, expected_trait in personalities:
            with self.subTest(personality=personality):
                user = MockUser(personality, companion_name)
                service = FastResponseService(user=user)

                prompt = service._build_fast_system_prompt(None, False)

                self.assertIn(companion_name, prompt)
                self.assertIn(expected_trait, prompt)
                self.assertIn(user.first_name, prompt)

    def test_build_fast_system_prompt_with_emotion_context(self):
        """Test system prompt building with emotion context."""
        emotion_contexts = [
            {'primary_emotion': 'happy', 'intensity': 0.8},
            {'primary_emotion': 'sad', 'intensity': 0.6},
            {'primary_emotion': 'stressed', 'intensity': 0.9},
            {'primary_emotion': 'excited', 'intensity': 0.7},
            {'primary_emotion': 'angry', 'intensity': 0.5}
        ]

        for emotion_context in emotion_contexts:
            with self.subTest(emotion=emotion_context['primary_emotion']):
                prompt = self.service._build_fast_system_prompt(emotion_context, False)

                self.assertIn(emotion_context['primary_emotion'], prompt)
                self.assertIn(str(emotion_context['intensity']), prompt)
                self.assertIn('adapt your response accordingly', prompt)

    def test_build_fast_system_prompt_agent_processing_needed(self):
        """Test system prompt when agent processing is needed."""
        prompt = self.service._build_fast_system_prompt(None, True)

        self.assertIn('deeper analysis', prompt)
        self.assertIn('acknowledgment', prompt)
        self.assertIn('continue the conversation', prompt)
        self.assertIn('Let me think about that', prompt)

    def test_performance_metrics_tracking(self):
        """Test performance metrics tracking."""
        # Simulate some response times
        self.service._update_performance_metrics(300.0, 250.0)
        self.service._update_performance_metrics(400.0, 350.0)
        self.service._update_performance_metrics(500.0, 450.0)

        stats = self.service.get_performance_stats()

        self.assertEqual(stats['requests'], 3)
        self.assertEqual(stats['avg_first_chunk_time_ms'], 350.0)  # (250+350+450)/3
        self.assertEqual(stats['target_first_chunk_ms'], 450)
        self.assertAlmostEqual(stats['performance_ratio'], 450/350, places=2)
        self.assertTrue(stats['target_met'])  # 350ms < 450ms target


class FastResponseScenarioTests(TestCase):
    """Comprehensive scenario tests for different use cases."""

    def setUp(self):
        """Set up test fixtures."""
        self.users = {
            'caring': MockUser('caringFriend', 'Ella'),
            'playful': MockUser('playfulCompanion', 'Luna'),
            'wise': MockUser('wiseMentor', 'Sage'),
            'romantic': MockUser('romanticPartner', 'Alex'),
            'therapist': MockUser('supportiveTherapist', 'Dr. Hope')
        }

    @patch('chat.services.fast_response_service.ChatGroq')
    async def test_scenario_simple_greeting(self, mock_groq):
        """Test simple greeting scenario - should be fast, no agent processing."""
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "Hello there! I'm doing great, thanks for asking. How are you doing today?"

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        service = FastResponseService(user=self.users['caring'])

        responses = []
        async for chunk in service.process_query_fast(
            user_input="Hello, how are you?",
            user_id="test_user",
            streaming=True
        ):
            responses.append(chunk)

        # Should have response chunks and completion
        response_chunks = [r for r in responses if r['type'] == 'response_chunk']
        complete_responses = [r for r in responses if r['type'] == 'response_complete']
        agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']

        self.assertGreater(len(response_chunks), 0)
        self.assertEqual(len(complete_responses), 1)
        self.assertEqual(len(agent_processing), 0)  # No agent processing for simple greeting

        # Check response metadata
        complete_response = complete_responses[0]
        self.assertEqual(complete_response['source'], 'fast_groq')
        self.assertFalse(complete_response['needs_agent_processing'])
        self.assertTrue(complete_response['is_final'])

    @patch('chat.services.fast_response_service.ChatGroq')
    async def test_scenario_joke_request(self, mock_groq):
        """Test joke request - should be fast, playful personality, no agent processing."""
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "Why don't scientists trust atoms? Because they make up everything! 😄"

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        service = FastResponseService(user=self.users['playful'])

        responses = []
        async for chunk in service.process_query_fast(
            user_input="Tell me a funny joke!",
            user_id="test_user",
            streaming=True
        ):
            responses.append(chunk)

        # Verify no agent processing needed
        agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']
        self.assertEqual(len(agent_processing), 0)

        # Check that playful personality was used in system prompt
        mock_llm.astream.assert_called_once()
        call_args = mock_llm.astream.call_args[0][0]
        system_message = call_args[0].content
        self.assertIn('Luna', system_message)
        self.assertIn('energetic, playful, and fun', system_message)

    @patch('chat.services.fast_response_service.ChatGroq')
    async def test_scenario_flirting(self, mock_groq):
        """Test flirting scenario - should use romantic personality, fast response."""
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "You always know just what to say to make me smile... 💕"

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        service = FastResponseService(user=self.users['romantic'])

        responses = []
        async for chunk in service.process_query_fast(
            user_input="You look beautiful today",
            user_id="test_user",
            emotion_context={'primary_emotion': 'love', 'intensity': 0.8},
            streaming=True
        ):
            responses.append(chunk)

        # Check that romantic personality and emotion context were used
        mock_llm.astream.assert_called_once()
        call_args = mock_llm.astream.call_args[0][0]
        system_message = call_args[0].content
        self.assertIn('Alex', system_message)
        self.assertIn('affectionate, loving, and intimate', system_message)
        self.assertIn('love', system_message)
        self.assertIn('0.8', system_message)

    @patch('chat.services.fast_response_service.ChatGroq')
    async def test_scenario_email_request(self, mock_groq):
        """Test email sending request - should trigger agent processing."""
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "I'll help you send that email! Let me work on that for you. What else is on your mind today?"

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        service = FastResponseService(user=self.users['caring'])

        responses = []
        async for chunk in service.process_query_fast(
            user_input="Can you help me send an email to my boss about the project update?",
            user_id="test_user",
            streaming=True
        ):
            responses.append(chunk)

        # Should trigger agent processing
        complete_responses = [r for r in responses if r['type'] == 'response_complete']
        agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']

        self.assertEqual(len(complete_responses), 1)
        self.assertEqual(len(agent_processing), 1)

        complete_response = complete_responses[0]
        self.assertTrue(complete_response['needs_agent_processing'])
        self.assertFalse(complete_response['is_final'])  # Not final because agent processing needed

    @patch('chat.services.fast_response_service.ChatGroq')
    async def test_scenario_recommendation_request(self, mock_groq):
        """Test recommendation request - should trigger agent processing for web search."""
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "Great question! Let me research the latest restaurant recommendations for you. By the way, what type of cuisine are you in the mood for?"

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        service = FastResponseService(user=self.users['wise'])

        responses = []
        async for chunk in service.process_query_fast(
            user_input="Can you recommend some good restaurants in San Francisco?",
            user_id="test_user",
            streaming=True
        ):
            responses.append(chunk)

        # Should trigger agent processing for web search
        complete_responses = [r for r in responses if r['type'] == 'response_complete']
        agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']

        self.assertEqual(len(complete_responses), 1)
        self.assertEqual(len(agent_processing), 1)

        complete_response = complete_responses[0]
        self.assertTrue(complete_response['needs_agent_processing'])

    @patch('chat.services.fast_response_service.ChatGroq')
    async def test_scenario_research_paper_review(self, mock_groq):
        """Test research paper review request - should trigger agent processing."""
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "I'd be happy to review that research paper for you! This sounds like an interesting study. Let me take a detailed look at it. How did you come across this paper?"

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        service = FastResponseService(user=self.users['wise'])

        responses = []
        async for chunk in service.process_query_fast(
            user_input="Can you review this research paper on machine learning and give me your thoughts on the methodology?",
            user_id="test_user",
            streaming=True
        ):
            responses.append(chunk)

        # Should definitely trigger agent processing for complex analysis
        complete_responses = [r for r in responses if r['type'] == 'response_complete']
        agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']

        self.assertEqual(len(complete_responses), 1)
        self.assertEqual(len(agent_processing), 1)

        complete_response = complete_responses[0]
        self.assertTrue(complete_response['needs_agent_processing'])

        # Check that wise mentor personality was used
        mock_llm.astream.assert_called_once()
        call_args = mock_llm.astream.call_args[0][0]
        system_message = call_args[0].content
        self.assertIn('Sage', system_message)
        self.assertIn('thoughtful, wise, and insightful', system_message)

    @patch('chat.services.fast_response_service.ChatGroq')
    async def test_scenario_deep_research_request(self, mock_groq):
        """Test deep research request - should trigger agent processing."""
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "That's a fascinating topic! I'll dive deep into the research on climate change impacts. This will take some analysis, but I'm excited to explore this with you. What sparked your interest in this area?"

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        service = FastResponseService(user=self.users['wise'])

        responses = []
        async for chunk in service.process_query_fast(
            user_input="I need comprehensive research on the long-term economic impacts of climate change, including analysis of various studies and policy recommendations",
            user_id="test_user",
            streaming=True
        ):
            responses.append(chunk)

        # Should trigger agent processing for comprehensive research
        complete_responses = [r for r in responses if r['type'] == 'response_complete']
        agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']

        self.assertEqual(len(complete_responses), 1)
        self.assertEqual(len(agent_processing), 1)

        complete_response = complete_responses[0]
        self.assertTrue(complete_response['needs_agent_processing'])
        self.assertFalse(complete_response['is_final'])

    @patch('chat.services.fast_response_service.ChatGroq')
    async def test_scenario_therapeutic_support(self, mock_groq):
        """Test therapeutic support scenario - should be fast, therapeutic personality."""
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "I hear that you're feeling overwhelmed, and that's completely understandable. It takes courage to reach out. Let's take this one step at a time."

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        service = FastResponseService(user=self.users['therapist'])

        responses = []
        async for chunk in service.process_query_fast(
            user_input="I'm feeling really overwhelmed with work and life lately",
            user_id="test_user",
            emotion_context={'primary_emotion': 'stressed', 'intensity': 0.9},
            streaming=True
        ):
            responses.append(chunk)

        # Should be immediate support, no agent processing needed
        agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']
        self.assertEqual(len(agent_processing), 0)

        # Check therapeutic personality and emotion adaptation
        mock_llm.astream.assert_called_once()
        call_args = mock_llm.astream.call_args[0][0]
        system_message = call_args[0].content
        self.assertIn('Dr. Hope', system_message)
        self.assertIn('calm, understanding, and therapeutic', system_message)
        self.assertIn('stressed', system_message)
        self.assertIn('0.9', system_message)

    @patch('chat.services.fast_response_service.ChatGroq')
    async def test_scenario_math_problem_help(self, mock_groq):
        """Test math problem help - should trigger agent processing."""
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "I'd love to help you with that math problem! Let me take a closer look at it and work through the solution step by step. Are you working on this for a class or personal interest?"

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        service = FastResponseService(user=self.users['caring'])

        responses = []
        async for chunk in service.process_query_fast(
            user_input="I'm stuck on this calculus problem about finding the derivative of a complex function",
            user_id="test_user",
            streaming=True
        ):
            responses.append(chunk)

        # Should trigger agent processing for detailed math help
        complete_responses = [r for r in responses if r['type'] == 'response_complete']
        agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']

        self.assertEqual(len(complete_responses), 1)
        self.assertEqual(len(agent_processing), 1)

        complete_response = complete_responses[0]
        self.assertTrue(complete_response['needs_agent_processing'])


class FastResponsePerformanceTests(TestCase):
    """Performance-focused tests for the fast response system."""

    def setUp(self):
        """Set up test fixtures."""
        self.user = MockUser()
        self.service = FastResponseService(user=self.user)

    def test_performance_metrics_initialization(self):
        """Test that performance metrics start at zero."""
        stats = self.service.get_performance_stats()

        self.assertEqual(stats['requests'], 0)

    def test_performance_metrics_target_met(self):
        """Test performance metrics when target is met."""
        # Simulate responses within target
        self.service._update_performance_metrics(400.0, 300.0)
        self.service._update_performance_metrics(350.0, 250.0)
        self.service._update_performance_metrics(450.0, 400.0)

        stats = self.service.get_performance_stats()

        self.assertEqual(stats['requests'], 3)
        self.assertEqual(stats['avg_first_chunk_time_ms'], 316.67)  # (300+250+400)/3
        self.assertTrue(stats['target_met'])
        self.assertGreater(stats['performance_ratio'], 1.0)  # 450/316.67 > 1

    def test_performance_metrics_target_missed(self):
        """Test performance metrics when target is missed."""
        # Simulate responses exceeding target
        self.service._update_performance_metrics(800.0, 600.0)
        self.service._update_performance_metrics(900.0, 700.0)

        stats = self.service.get_performance_stats()

        self.assertEqual(stats['requests'], 2)
        self.assertEqual(stats['avg_first_chunk_time_ms'], 650.0)  # (600+700)/2
        self.assertFalse(stats['target_met'])  # 650ms > 450ms target
        self.assertLess(stats['performance_ratio'], 1.0)  # 450/650 < 1

    def test_conversation_history_limiting(self):
        """Test that conversation history is limited for performance."""
        # Create long conversation history
        long_history = []
        for i in range(20):
            long_history.extend([
                {'role': 'user', 'content': f'User message {i}'},
                {'role': 'assistant', 'content': f'Assistant response {i}'}
            ])

        # Build system prompt with long history
        prompt = self.service._build_fast_system_prompt(None, False)

        # Should be reasonable length for fast processing
        self.assertLess(len(prompt), 2000, "System prompt should be optimized for speed")

    def test_max_tokens_optimization(self):
        """Test that max_tokens is optimized for speed."""
        # Check that Groq LLM is configured for speed
        self.assertEqual(self.service.groq_llm.max_tokens, 150)
        self.assertEqual(self.service.groq_llm.temperature, 0.7)
        self.assertEqual(self.service.groq_llm.model_name, "llama-3.3-70b-versatile")

    @patch('chat.services.fast_response_service.logger')
    def test_performance_logging_target_met(self, mock_logger):
        """Test that performance logging works when target is met."""
        self.service._update_performance_metrics(400.0, 300.0)

        mock_logger.info.assert_called_with("First chunk time 300.0ms - within 450ms target ✅")

    @patch('chat.services.fast_response_service.logger')
    def test_performance_logging_target_missed(self, mock_logger):
        """Test that performance logging works when target is missed."""
        self.service._update_performance_metrics(600.0, 500.0)

        mock_logger.warning.assert_called_with("First chunk time 500.0ms exceeded 450ms target")


class FastResponseIntegrationTests(TestCase):
    """Integration tests for the fast response system."""

    def setUp(self):
        """Set up test fixtures."""
        self.users = {
            'caring': MockUser('caringFriend', 'Ella'),
            'playful': MockUser('playfulCompanion', 'Luna'),
            'wise': MockUser('wiseMentor', 'Sage'),
            'romantic': MockUser('romanticPartner', 'Alex'),
            'therapist': MockUser('supportiveTherapist', 'Dr. Hope')
        }

    def test_all_personalities_system_prompts(self):
        """Test that all personalities generate valid system prompts."""
        for personality, user in self.users.items():
            with self.subTest(personality=personality):
                service = FastResponseService(user=user)

                # Test without emotion context
                prompt1 = service._build_fast_system_prompt(None, False)
                self.assertIn(user.ai_companion_name, prompt1)
                self.assertIn(user.first_name, prompt1)

                # Test with emotion context
                emotion_context = {'primary_emotion': 'happy', 'intensity': 0.7}
                prompt2 = service._build_fast_system_prompt(emotion_context, False)
                self.assertIn('happy', prompt2)
                self.assertIn('0.7', prompt2)

                # Test with agent processing needed
                prompt3 = service._build_fast_system_prompt(None, True)
                self.assertIn('deeper analysis', prompt3)

    def test_agent_processing_detection_consistency(self):
        """Test that agent processing detection is consistent across personalities."""
        test_queries = [
            ("Hello!", False),
            ("Tell me a joke", False),
            ("Analyze this business plan", True),
            ("Help me solve this problem", True),
            ("I love you", False),
            ("Research the latest AI trends", True)
        ]

        for personality, user in self.users.items():
            service = FastResponseService(user=user)

            for query, expected in test_queries:
                with self.subTest(personality=personality, query=query):
                    result = service._needs_agent_processing(query)
                    self.assertEqual(result, expected,
                        f"Personality {personality} failed on query '{query}'")

    def test_timestamp_format_consistency(self):
        """Test that timestamps are consistently formatted."""
        service = FastResponseService(user=self.users['caring'])

        timestamp = service._get_timestamp()

        # Should be ISO format
        self.assertRegex(timestamp, r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}')

    def test_error_handling_graceful_degradation(self):
        """Test that errors are handled gracefully."""
        service = FastResponseService(user=None)  # No user provided

        # Should not crash when building prompts
        try:
            prompt = service._build_fast_system_prompt(None, False)
            self.assertIn('Ella', prompt)  # Should use defaults
        except Exception as e:
            self.fail(f"Should handle missing user gracefully, but got: {e}")

    def test_conversation_flow_metadata(self):
        """Test that response metadata is consistent and complete."""
        service = FastResponseService(user=self.users['caring'])

        # Mock a simple response flow
        with patch.object(service.groq_llm, 'astream') as mock_stream:
            mock_chunk = Mock()
            mock_chunk.content = "Test response"

            async def mock_async_iter():
                yield mock_chunk

            mock_stream.return_value = mock_async_iter()

            async def test_flow():
                responses = []
                async for chunk in service.process_query_fast(
                    user_input="Hello",
                    user_id="test_user",
                    streaming=True
                ):
                    responses.append(chunk)

                # Check response structure
                response_chunks = [r for r in responses if r['type'] == 'response_chunk']
                complete_responses = [r for r in responses if r['type'] == 'response_complete']

                self.assertGreater(len(response_chunks), 0)
                self.assertEqual(len(complete_responses), 1)

                # Check metadata completeness
                complete_response = complete_responses[0]
                required_fields = [
                    'type', 'full_content', 'processing_time_ms', 'first_chunk_time_ms',
                    'source', 'needs_agent_processing', 'timestamp', 'request_id', 'is_final'
                ]

                for field in required_fields:
                    self.assertIn(field, complete_response, f"Missing field: {field}")

            # Run the async test
            import asyncio
            asyncio.run(test_flow())


class MemoryBasedFillerResponseTests(TestCase):
    """Tests for memory-based filler responses in the fast response system."""

    def setUp(self):
        """Set up test fixtures with memory data."""
        self.user = MockUser('caringFriend', 'Ella')
        self.service = FastResponseService(user=self.user)

        # Set up test memories in the memory manager
        if self.service.memory_manager:
            # Add some test memories
            self.service.memory_manager.store_memory(
                text="User works as a software engineer at a tech startup",
                memory_type="semantic_profile",
                user_id="test_user",
                importance_score=0.8,
                personalness_score=0.7,
                actionability_score=0.3
            )

            self.service.memory_manager.store_memory(
                text="User mentioned feeling excited about their new project last week",
                memory_type="episodic_summary",
                user_id="test_user",
                importance_score=0.6,
                personalness_score=0.8,
                actionability_score=0.5
            )

            self.service.memory_manager.store_memory(
                text="User enjoys hiking and photography as hobbies",
                memory_type="semantic_profile",
                user_id="test_user",
                importance_score=0.7,
                personalness_score=0.9,
                actionability_score=0.2
            )

            self.service.memory_manager.store_memory(
                text="User had a job interview last month and was nervous about it",
                memory_type="episodic_summary",
                user_id="test_user",
                importance_score=0.8,
                personalness_score=0.6,
                actionability_score=0.7
            )

    def test_contextual_memory_retrieval(self):
        """Test that contextual memories are retrieved correctly."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")

        memories = self.service._get_contextual_memories("test_user", limit=3)

        # Should retrieve some memories
        self.assertGreater(len(memories), 0)
        self.assertLessEqual(len(memories), 3)

        # Should contain mix of memory types
        memory_types = {mem.get('memory_type') for mem in memories}
        self.assertTrue(len(memory_types) > 0)

        # Check that memories have required fields
        for memory in memories:
            self.assertIn('text', memory)
            self.assertIn('memory_type', memory)
            self.assertIn('importance_score', memory)

    def test_contextual_memory_filtering(self):
        """Test that contextual memories are properly filtered by type and importance."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")

        memories = self.service._get_contextual_memories("test_user", limit=5)

        # Should prioritize higher importance memories
        for memory in memories:
            importance = memory.get('importance_score', 0)
            self.assertGreaterEqual(importance, 0.3)  # Min importance threshold

        # Should include both episodic and semantic profile memories
        memory_types = {mem.get('memory_type') for mem in memories}
        expected_types = {'episodic_summary', 'semantic_profile'}
        self.assertTrue(memory_types.intersection(expected_types))

    def test_memory_based_system_prompt_enhancement(self):
        """Test that system prompts are enhanced with memory context when agent processing is needed."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")

        # Test with agent processing needed
        prompt_with_agent = self.service._build_fast_system_prompt(
            emotion_context=None,
            needs_agent=True,
            user_id="test_user"
        )

        # Should contain memory context
        self.assertIn('CONTEXTUAL MEMORIES', prompt_with_agent)
        self.assertIn('personal context', prompt_with_agent)

        # Should contain some of our test memory content
        prompt_lower = prompt_with_agent.lower()
        memory_indicators = ['work', 'software', 'hiking', 'photography', 'project']
        found_indicators = [indicator for indicator in memory_indicators if indicator in prompt_lower]
        self.assertGreater(len(found_indicators), 0, "Should contain some memory-based context")

        # Test without agent processing
        prompt_without_agent = self.service._build_fast_system_prompt(
            emotion_context=None,
            needs_agent=False,
            user_id="test_user"
        )

        # Should not contain memory context for simple responses
        self.assertNotIn('CONTEXTUAL MEMORIES', prompt_without_agent)

    def test_memory_based_filler_examples(self):
        """Test that memory-based filler response examples are generated."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")

        prompt = self.service._build_fast_system_prompt(
            emotion_context=None,
            needs_agent=True,
            user_id="test_user"
        )

        # Should contain examples of how to use memories
        self.assertIn('EXAMPLES of good filler responses', prompt)
        self.assertIn('personal and engaging', prompt)

        # Should contain contextual memory information
        self.assertIn('CONTEXTUAL MEMORIES', prompt)

        # Should have memory categories
        prompt_lower = prompt.lower()
        categories = ['work', 'interest', 'personal', 'recent conversation']
        found_categories = [cat for cat in categories if cat in prompt_lower]
        self.assertGreater(len(found_categories), 0)

    def test_memory_retrieval_with_no_memories(self):
        """Test behavior when no memories are available."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")

        # Test with non-existent user
        memories = self.service._get_contextual_memories("nonexistent_user", limit=3)
        self.assertEqual(len(memories), 0)

        # System prompt should still work without memories
        prompt = self.service._build_fast_system_prompt(
            emotion_context=None,
            needs_agent=True,
            user_id="nonexistent_user"
        )

        # Should contain basic filler examples even without memories
        self.assertIn('EXAMPLES of good filler responses', prompt)
        self.assertIn('deeper analysis', prompt)

    def test_memory_retrieval_error_handling(self):
        """Test error handling in memory retrieval."""
        # Test with None user_id
        memories = self.service._get_contextual_memories(None, limit=3)
        self.assertEqual(len(memories), 0)

        # Test with empty user_id
        memories = self.service._get_contextual_memories("", limit=3)
        self.assertEqual(len(memories), 0)

        # Test with service that has no memory manager
        service_no_memory = FastResponseService(user=self.user)
        service_no_memory.memory_manager = None

        memories = service_no_memory._get_contextual_memories("test_user", limit=3)
        self.assertEqual(len(memories), 0)

    def test_memory_types_prioritization(self):
        """Test that different memory types are prioritized correctly for filler responses."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")

        # Add more diverse memories
        self.service.memory_manager.store_memory(
            text="User loves Italian food and cooking",
            memory_type="semantic_profile",
            user_id="test_user_2",
            importance_score=0.6,
            personalness_score=0.8,
            actionability_score=0.3
        )

        self.service.memory_manager.store_memory(
            text="Had a great conversation about travel plans yesterday",
            memory_type="episodic_summary",
            user_id="test_user_2",
            importance_score=0.7,
            personalness_score=0.6,
            actionability_score=0.4
        )

        self.service.memory_manager.store_memory(
            text="Learned about machine learning algorithms",
            memory_type="general_knowledge",
            user_id="test_user_2",
            importance_score=0.5,
            personalness_score=0.2,
            actionability_score=0.3
        )

        memories = self.service._get_contextual_memories("test_user_2", limit=3)

        # Should prioritize episodic and semantic profile over general knowledge
        memory_types = [mem.get('memory_type') for mem in memories]
        priority_types = ['episodic_summary', 'semantic_profile']

        priority_count = sum(1 for mem_type in memory_types if mem_type in priority_types)
        self.assertGreater(priority_count, 0, "Should include priority memory types")

    @patch('chat.services.fast_response_service.ChatGroq')
    def test_memory_enhanced_agent_processing_flow(self, mock_groq):
        """Test the complete flow with memory-enhanced filler responses."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")

        # Mock Groq response that should include memory-based context
        mock_chunk = Mock()
        mock_chunk.content = "I'll analyze that business plan for you. By the way, how's your software engineering project going?"

        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]

        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm

        async def test_memory_flow():
            responses = []
            async for chunk in self.service.process_query_fast(
                user_input="Can you analyze this business plan for me?",
                user_id="test_user",
                streaming=True
            ):
                responses.append(chunk)

            # Should trigger agent processing
            complete_responses = [r for r in responses if r['type'] == 'response_complete']
            agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']

            self.assertEqual(len(complete_responses), 1)
            self.assertEqual(len(agent_processing), 1)

            # Check that memory context was used in system prompt
            mock_llm.astream.assert_called_once()
            call_args = mock_llm.astream.call_args[0][0]
            system_message = call_args[0].content

            # Should contain memory context
            self.assertIn('CONTEXTUAL MEMORIES', system_message)

            # Should contain some of our test memories
            system_lower = system_message.lower()
            memory_content = ['software', 'engineer', 'hiking', 'photography']
            found_content = [content for content in memory_content if content in system_lower]
            self.assertGreater(len(found_content), 0, "Should reference user's memories")

        # Run the async test
        import asyncio
        asyncio.run(test_memory_flow())


class DetailedMemoryFillerResponseTests(TestCase):
    """Comprehensive detailed tests for memory-based filler responses with extensive scenarios and personas."""

    def setUp(self):
        """Set up test fixtures with detailed logging."""
        self.test_users = {
            'tech_professional': MockUser('wiseMentor', 'Sage', 'Jordan'),
            'creative_artist': MockUser('playfulCompanion', 'Luna', 'Riley'),
            'busy_parent': MockUser('caringFriend', 'Ella', 'Sam'),
            'college_student': MockUser('supportiveTherapist', 'Dr. Hope', 'Casey'),
            'entrepreneur': MockUser('romanticPartner', 'Alex', 'Morgan'),
            'retiree': MockUser('caringFriend', 'Grace', 'Pat'),
            'healthcare_worker': MockUser('supportiveTherapist', 'Dr. Care', 'Taylor'),
            'teacher': MockUser('wiseMentor', 'Professor', 'Jamie')
        }

        # Set up memories for each persona
        self.persona_memories = self._setup_persona_memories()

    def _setup_persona_memories(self):
        """Set up comprehensive memories for different user personas."""
        return {
            'tech_professional': [
                {
                    "text": "Jordan works as a senior software architect at Microsoft, leading cloud infrastructure projects",
                    "type": "semantic_profile", "importance": 0.9, "personalness": 0.8, "actionability": 0.3
                },
                {
                    "text": "Jordan mentioned being stressed about the upcoming system migration deadline next week",
                    "type": "episodic_summary", "importance": 0.8, "personalness": 0.7, "actionability": 0.8
                },
                {
                    "text": "Jordan loves building mechanical keyboards and has a collection of vintage switches",
                    "type": "semantic_profile", "importance": 0.6, "personalness": 0.9, "actionability": 0.2
                },
                {
                    "text": "Had a conversation about Jordan's side project - an open source monitoring tool",
                    "type": "episodic_summary", "importance": 0.7, "personalness": 0.8, "actionability": 0.5
                }
            ],
            'creative_artist': [
                {
                    "text": "Riley is a freelance graphic designer specializing in brand identity and illustration",
                    "type": "semantic_profile", "importance": 0.9, "personalness": 0.8, "actionability": 0.3
                },
                {
                    "text": "Riley just finished a major rebranding project for a tech startup and felt proud of the outcome",
                    "type": "episodic_summary", "importance": 0.7, "personalness": 0.8, "actionability": 0.4
                },
                {
                    "text": "Riley practices watercolor painting on weekends and dreams of having an art exhibition",
                    "type": "semantic_profile", "importance": 0.6, "personalness": 0.9, "actionability": 0.6
                },
                {
                    "text": "Riley's cat Pixel knocked over paint water last week, ruining a commission piece",
                    "type": "episodic_summary", "importance": 0.6, "personalness": 0.8, "actionability": 0.3
                }
            ],
            'busy_parent': [
                {
                    "text": "Sam is a working parent with two kids (Emma, 8 and Liam, 5) and works in marketing",
                    "type": "semantic_profile", "importance": 0.9, "personalness": 0.9, "actionability": 0.4
                },
                {
                    "text": "Sam mentioned feeling overwhelmed juggling work deadlines and Emma's soccer tournament this weekend",
                    "type": "episodic_summary", "importance": 0.8, "personalness": 0.8, "actionability": 0.7
                },
                {
                    "text": "Sam enjoys cooking family meals and recently started a small herb garden with the kids",
                    "type": "semantic_profile", "importance": 0.6, "personalness": 0.8, "actionability": 0.3
                },
                {
                    "text": "Liam had a fever last Tuesday and Sam had to miss an important client meeting",
                    "type": "episodic_summary", "importance": 0.7, "personalness": 0.7, "actionability": 0.5
                }
            ],
            'college_student': [
                {
                    "text": "Casey is a junior studying psychology at UC Berkeley, interested in cognitive behavioral therapy",
                    "type": "semantic_profile", "importance": 0.9, "personalness": 0.8, "actionability": 0.4
                },
                {
                    "text": "Casey is stressed about upcoming finals and worried about maintaining their GPA for grad school",
                    "type": "episodic_summary", "importance": 0.8, "personalness": 0.7, "actionability": 0.8
                },
                {
                    "text": "Casey volunteers at a local mental health clinic and wants to become a therapist",
                    "type": "semantic_profile", "importance": 0.7, "personalness": 0.8, "actionability": 0.6
                },
                {
                    "text": "Casey's roommate moved out last month, making rent more expensive and living situation stressful",
                    "type": "episodic_summary", "importance": 0.7, "personalness": 0.8, "actionability": 0.6
                }
            ],
            'entrepreneur': [
                {
                    "text": "Morgan founded a sustainable fashion startup called EcoThreads, focusing on recycled materials",
                    "type": "semantic_profile", "importance": 0.9, "personalness": 0.8, "actionability": 0.5
                },
                {
                    "text": "Morgan is preparing for Series A funding round and meeting with VCs next month",
                    "type": "episodic_summary", "importance": 0.9, "personalness": 0.7, "actionability": 0.9
                },
                {
                    "text": "Morgan practices yoga daily and believes in work-life balance despite startup demands",
                    "type": "semantic_profile", "importance": 0.6, "personalness": 0.8, "actionability": 0.3
                },
                {
                    "text": "Morgan's co-founder disagreed about company direction last week, causing tension",
                    "type": "episodic_summary", "importance": 0.8, "personalness": 0.6, "actionability": 0.8
                }
            ],
            'retiree': [
                {
                    "text": "Pat is a retired teacher who taught high school English for 35 years",
                    "type": "semantic_profile", "importance": 0.8, "personalness": 0.8, "actionability": 0.2
                },
                {
                    "text": "Pat recently started learning digital photography and joined a local camera club",
                    "type": "episodic_summary", "importance": 0.6, "personalness": 0.8, "actionability": 0.4
                },
                {
                    "text": "Pat volunteers at the library reading to children and organizing book clubs",
                    "type": "semantic_profile", "importance": 0.7, "personalness": 0.8, "actionability": 0.5
                },
                {
                    "text": "Pat's grandchildren visited last weekend and they baked cookies together",
                    "type": "episodic_summary", "importance": 0.6, "personalness": 0.9, "actionability": 0.2
                }
            ],
            'healthcare_worker': [
                {
                    "text": "Taylor is an ICU nurse at City General Hospital, working 12-hour shifts",
                    "type": "semantic_profile", "importance": 0.9, "personalness": 0.8, "actionability": 0.4
                },
                {
                    "text": "Taylor mentioned feeling emotionally drained after losing a patient last week",
                    "type": "episodic_summary", "importance": 0.8, "personalness": 0.9, "actionability": 0.7
                },
                {
                    "text": "Taylor runs marathons to cope with work stress and is training for Boston Marathon",
                    "type": "semantic_profile", "importance": 0.7, "personalness": 0.8, "actionability": 0.4
                },
                {
                    "text": "Taylor's hospital is understaffed and they've been working extra shifts this month",
                    "type": "episodic_summary", "importance": 0.7, "personalness": 0.6, "actionability": 0.6
                }
            ],
            'teacher': [
                {
                    "text": "Jamie teaches 4th grade at Riverside Elementary and loves inspiring young minds",
                    "type": "semantic_profile", "importance": 0.9, "personalness": 0.8, "actionability": 0.3
                },
                {
                    "text": "Jamie is excited about implementing a new science curriculum focused on hands-on experiments",
                    "type": "episodic_summary", "importance": 0.7, "personalness": 0.7, "actionability": 0.6
                },
                {
                    "text": "Jamie plays guitar and writes songs, sometimes incorporating music into lessons",
                    "type": "semantic_profile", "importance": 0.6, "personalness": 0.8, "actionability": 0.4
                },
                {
                    "text": "Jamie's student Marcus has been struggling with reading and Jamie is working on extra support",
                    "type": "episodic_summary", "importance": 0.8, "personalness": 0.7, "actionability": 0.8
                }
            ]
        }

    def _store_memories_for_persona(self, persona_name, user_id):
        """Store memories for a specific persona."""
        if persona_name not in self.persona_memories:
            return

        service = FastResponseService(user=self.test_users[persona_name])
        if not service.memory_manager:
            return

        for memory in self.persona_memories[persona_name]:
            service.memory_manager.store_memory(
                text=memory["text"],
                memory_type=memory["type"],
                user_id=user_id,
                importance_score=memory["importance"],
                personalness_score=memory["personalness"],
                actionability_score=memory["actionability"]
            )

    def test_memory_retrieval_across_personas(self):
        """Test memory retrieval works correctly across different user personas."""
        for persona_name, user in self.test_users.items():
            with self.subTest(persona=persona_name):
                service = FastResponseService(user=user)
                if not service.memory_manager:
                    self.skipTest("Memory manager not available")

                user_id = f"test_{persona_name}"
                self._store_memories_for_persona(persona_name, user_id)

                memories = service._get_contextual_memories(user_id, limit=3)

                # Should retrieve memories
                self.assertGreater(len(memories), 0, f"Should retrieve memories for {persona_name}")
                self.assertLessEqual(len(memories), 3, f"Should not exceed limit for {persona_name}")

                # Check memory types
                memory_types = {mem.get('memory_type') for mem in memories}
                expected_types = {'episodic_summary', 'semantic_profile'}
                self.assertTrue(memory_types.intersection(expected_types),
                               f"Should have expected memory types for {persona_name}")

    def test_persona_specific_system_prompts(self):
        """Test that system prompts are correctly generated for different personas."""
        test_cases = [
            ('tech_professional', 'Sage', 'thoughtful, wise, and insightful'),
            ('creative_artist', 'Luna', 'energetic, playful, and fun'),
            ('busy_parent', 'Ella', 'warm, supportive, and caring'),
            ('college_student', 'Dr. Hope', 'calm, understanding, and therapeutic'),
            ('entrepreneur', 'Alex', 'affectionate, loving, and intimate')
        ]

        for persona_name, companion_name, expected_trait in test_cases:
            with self.subTest(persona=persona_name):
                user = self.test_users[persona_name]
                service = FastResponseService(user=user)

                if not service.memory_manager:
                    self.skipTest("Memory manager not available")

                user_id = f"test_{persona_name}"
                self._store_memories_for_persona(persona_name, user_id)

                prompt = service._build_fast_system_prompt(
                    emotion_context=None,
                    needs_agent=True,
                    user_id=user_id
                )

                # Check persona-specific elements
                self.assertIn(companion_name, prompt, f"Should contain companion name for {persona_name}")
                self.assertIn(expected_trait, prompt, f"Should contain personality trait for {persona_name}")
                self.assertIn(user.first_name, prompt, f"Should contain user name for {persona_name}")

                # Check memory context
                self.assertIn('CONTEXTUAL MEMORIES', prompt, f"Should have memory context for {persona_name}")

    def test_comprehensive_scenario_coverage(self):
        """Test comprehensive scenarios across different domains and complexity levels."""
        scenarios = [
            # Work and Professional
            {
                "query": "Help me prepare a presentation for the board meeting next week",
                "persona": "tech_professional",
                "expected_agent": True,
                "expected_memory_refs": ["Microsoft", "cloud infrastructure", "system migration"],
                "category": "Work Planning"
            },
            {
                "query": "Can you review my resume and suggest improvements?",
                "persona": "college_student",
                "expected_agent": True,
                "expected_memory_refs": ["psychology", "UC Berkeley", "grad school"],
                "category": "Career Development"
            },

            # Creative and Artistic
            {
                "query": "I need ideas for a new logo design for a tech company",
                "persona": "creative_artist",
                "expected_agent": True,
                "expected_memory_refs": ["graphic designer", "brand identity", "tech startup"],
                "category": "Creative Work"
            },
            {
                "query": "What colors work best for watercolor landscapes?",
                "persona": "creative_artist",
                "expected_agent": True,
                "expected_memory_refs": ["watercolor painting", "art exhibition"],
                "category": "Artistic Advice"
            },

            # Family and Personal Life
            {
                "query": "Help me plan a fun weekend activity for my kids",
                "persona": "busy_parent",
                "expected_agent": True,
                "expected_memory_refs": ["Emma", "Liam", "soccer tournament"],
                "category": "Family Planning"
            },
            {
                "query": "My child is having trouble with homework, any suggestions?",
                "persona": "busy_parent",
                "expected_agent": True,
                "expected_memory_refs": ["kids", "Emma", "8"],
                "category": "Parenting Support"
            },

            # Health and Wellness
            {
                "query": "I'm feeling burned out at work, what should I do?",
                "persona": "healthcare_worker",
                "expected_agent": False,  # Therapeutic support, immediate response
                "expected_memory_refs": ["ICU nurse", "emotionally drained", "12-hour shifts"],
                "category": "Mental Health"
            },
            {
                "query": "Can you create a marathon training plan for me?",
                "persona": "healthcare_worker",
                "expected_agent": True,
                "expected_memory_refs": ["marathons", "Boston Marathon", "running"],
                "category": "Fitness Planning"
            },

            # Education and Learning
            {
                "query": "Help me design engaging science experiments for my students",
                "persona": "teacher",
                "expected_agent": True,
                "expected_memory_refs": ["4th grade", "science curriculum", "hands-on experiments"],
                "category": "Educational Planning"
            },
            {
                "query": "I'm worried about one of my struggling students",
                "persona": "teacher",
                "expected_agent": False,  # Supportive response
                "expected_memory_refs": ["Marcus", "struggling with reading", "extra support"],
                "category": "Student Support"
            },

            # Business and Entrepreneurship
            {
                "query": "Analyze the competitive landscape for sustainable fashion",
                "persona": "entrepreneur",
                "expected_agent": True,
                "expected_memory_refs": ["EcoThreads", "sustainable fashion", "recycled materials"],
                "category": "Business Analysis"
            },
            {
                "query": "Help me prepare for my investor pitch next month",
                "persona": "entrepreneur",
                "expected_agent": True,
                "expected_memory_refs": ["Series A funding", "VCs", "startup"],
                "category": "Investment Preparation"
            },

            # Retirement and Leisure
            {
                "query": "Recommend some good books for my book club",
                "persona": "retiree",
                "expected_agent": True,
                "expected_memory_refs": ["library", "book clubs", "English teacher"],
                "category": "Literature Recommendations"
            },
            {
                "query": "I want to learn more about digital photography techniques",
                "persona": "retiree",
                "expected_agent": True,
                "expected_memory_refs": ["digital photography", "camera club"],
                "category": "Hobby Learning"
            },

            # Casual and Social
            {
                "query": "Tell me a joke to cheer me up",
                "persona": "college_student",
                "expected_agent": False,
                "expected_memory_refs": [],
                "category": "Humor"
            },
            {
                "query": "You're such a good listener, thank you",
                "persona": "healthcare_worker",
                "expected_agent": False,
                "expected_memory_refs": [],
                "category": "Gratitude"
            },

            # Technical and Problem Solving
            {
                "query": "Debug this Python code that's not working properly",
                "persona": "tech_professional",
                "expected_agent": True,
                "expected_memory_refs": ["software architect", "Microsoft", "technical"],
                "category": "Technical Support"
            },
            {
                "query": "Explain machine learning concepts for my research paper",
                "persona": "college_student",
                "expected_agent": True,
                "expected_memory_refs": ["psychology", "cognitive behavioral therapy"],
                "category": "Academic Research"
            }
        ]

        for scenario in scenarios:
            with self.subTest(scenario=scenario["category"], persona=scenario["persona"]):
                persona_name = scenario["persona"]
                user = self.test_users[persona_name]
                service = FastResponseService(user=user)

                if not service.memory_manager:
                    self.skipTest("Memory manager not available")

                user_id = f"test_{persona_name}_scenario"
                self._store_memories_for_persona(persona_name, user_id)

                # Test agent processing detection
                needs_agent = service._needs_agent_processing(scenario["query"])
                self.assertEqual(needs_agent, scenario["expected_agent"],
                               f"Agent detection failed for {scenario['category']}")

                # Test memory integration if agent processing is needed
                if needs_agent:
                    prompt = service._build_fast_system_prompt(
                        emotion_context=None,
                        needs_agent=True,
                        user_id=user_id
                    )

                    # Check for memory context
                    self.assertIn('CONTEXTUAL MEMORIES', prompt,
                                 f"Should have memory context for {scenario['category']}")

                    # Check for relevant memory references (if specified)
                    if scenario["expected_memory_refs"]:
                        prompt_lower = prompt.lower()
                        found_refs = [ref for ref in scenario["expected_memory_refs"]
                                     if ref.lower() in prompt_lower]
                        self.assertGreater(len(found_refs), 0,
                                         f"Should reference relevant memories for {scenario['category']}")

    def test_emotion_context_integration(self):
        """Test that emotion context is properly integrated with memory-based responses."""
        emotion_scenarios = [
            {
                "persona": "healthcare_worker",
                "emotion": {"primary_emotion": "stressed", "intensity": 0.9},
                "query": "I had a really tough day at work",
                "expected_adaptation": "stressed"
            },
            {
                "persona": "college_student",
                "emotion": {"primary_emotion": "anxious", "intensity": 0.8},
                "query": "I'm worried about my finals",
                "expected_adaptation": "anxious"
            },
            {
                "persona": "entrepreneur",
                "emotion": {"primary_emotion": "excited", "intensity": 0.7},
                "query": "I just got great news about funding!",
                "expected_adaptation": "excited"
            },
            {
                "persona": "busy_parent",
                "emotion": {"primary_emotion": "overwhelmed", "intensity": 0.8},
                "query": "I can't keep up with everything",
                "expected_adaptation": "overwhelmed"
            }
        ]

        for scenario in emotion_scenarios:
            with self.subTest(persona=scenario["persona"], emotion=scenario["emotion"]["primary_emotion"]):
                user = self.test_users[scenario["persona"]]
                service = FastResponseService(user=user)

                if not service.memory_manager:
                    self.skipTest("Memory manager not available")

                user_id = f"test_{scenario['persona']}_emotion"
                self._store_memories_for_persona(scenario["persona"], user_id)

                prompt = service._build_fast_system_prompt(
                    emotion_context=scenario["emotion"],
                    needs_agent=False,  # Test emotion integration in simple responses
                    user_id=user_id
                )

                # Check emotion integration
                emotion_text = scenario["emotion"]["primary_emotion"]
                intensity_text = str(scenario["emotion"]["intensity"])

                self.assertIn(emotion_text, prompt, f"Should contain emotion {emotion_text}")
                self.assertIn(intensity_text, prompt, f"Should contain intensity {intensity_text}")
                self.assertIn("adapt your response accordingly", prompt,
                             "Should mention emotion adaptation")

    @patch('chat.services.fast_response_service.ChatGroq')
    def test_realistic_conversation_flows(self, mock_groq):
        """Test realistic conversation flows with memory-enhanced responses."""
        conversation_flows = [
            {
                "persona": "tech_professional",
                "query": "Can you help me architect a scalable microservices system?",
                "expected_response_elements": ["Microsoft", "cloud infrastructure", "architecture"],
                "flow_type": "Technical Consultation"
            },
            {
                "persona": "creative_artist",
                "query": "I need inspiration for my next art project",
                "expected_response_elements": ["graphic designer", "watercolor", "exhibition"],
                "flow_type": "Creative Inspiration"
            },
            {
                "persona": "busy_parent",
                "query": "Help me organize our family schedule better",
                "expected_response_elements": ["Emma", "Liam", "soccer tournament"],
                "flow_type": "Family Organization"
            },
            {
                "persona": "entrepreneur",
                "query": "Analyze market trends for sustainable fashion",
                "expected_response_elements": ["EcoThreads", "sustainable fashion", "funding"],
                "flow_type": "Business Strategy"
            }
        ]

        for flow in conversation_flows:
            with self.subTest(flow_type=flow["flow_type"]):
                # Setup mock response
                mock_chunk = Mock()
                mock_chunk.content = f"I'll help you with that! Let me analyze this thoroughly. By the way, how's your work going?"

                mock_stream = AsyncMock()
                mock_stream.__aiter__.return_value = [mock_chunk]

                mock_llm = Mock()
                mock_llm.astream.return_value = mock_stream
                mock_groq.return_value = mock_llm

                user = self.test_users[flow["persona"]]
                service = FastResponseService(user=user)

                if not service.memory_manager:
                    self.skipTest("Memory manager not available")

                user_id = f"test_{flow['persona']}_flow"
                self._store_memories_for_persona(flow["persona"], user_id)

                async def test_flow():
                    responses = []
                    async for chunk in service.process_query_fast(
                        user_input=flow["query"],
                        user_id=user_id,
                        streaming=True
                    ):
                        responses.append(chunk)

                    # Verify response structure
                    response_chunks = [r for r in responses if r['type'] == 'response_chunk']
                    complete_responses = [r for r in responses if r['type'] == 'response_complete']

                    self.assertGreater(len(response_chunks), 0, f"Should have response chunks for {flow['flow_type']}")
                    self.assertEqual(len(complete_responses), 1, f"Should have complete response for {flow['flow_type']}")

                    # Check that memory context was used in LLM call
                    if mock_llm.astream.called:
                        call_args = mock_llm.astream.call_args[0][0]
                        system_message = call_args[0].content

                        self.assertIn('CONTEXTUAL MEMORIES', system_message,
                                     f"Should have memory context for {flow['flow_type']}")

                # Run async test
                import asyncio
                asyncio.run(test_flow())

    def test_edge_case_scenarios(self):
        """Test edge cases and boundary conditions for memory-based filler responses."""
        edge_cases = [
            # Empty or minimal queries
            {
                "query": "Hi",
                "persona": "tech_professional",
                "expected_agent": False,
                "category": "Minimal Input"
            },
            {
                "query": "Help",
                "persona": "busy_parent",
                "expected_agent": True,  # "Help" is a trigger word
                "category": "Ambiguous Request"
            },

            # Very long queries
            {
                "query": "I need comprehensive help with analyzing the market trends for sustainable fashion startups in the current economic climate considering inflation rates supply chain disruptions consumer behavior changes post-pandemic shopping patterns environmental consciousness among millennials and gen-z demographics competitive landscape analysis including direct and indirect competitors pricing strategies marketing channels distribution networks customer acquisition costs lifetime value calculations and recommendations for strategic positioning",
                "persona": "entrepreneur",
                "expected_agent": True,
                "category": "Very Long Query"
            },

            # Mixed complexity queries
            {
                "query": "Hi! Can you help me with this complex data analysis project?",
                "persona": "tech_professional",
                "expected_agent": True,
                "category": "Mixed Complexity"
            },

            # Emotional queries
            {
                "query": "I'm so frustrated with this coding problem!",
                "persona": "tech_professional",
                "expected_agent": True,
                "category": "Emotional Technical"
            },

            # Personal sharing
            {
                "query": "I just wanted to tell you about my day",
                "persona": "healthcare_worker",
                "expected_agent": False,
                "category": "Personal Sharing"
            }
        ]

        for case in edge_cases:
            with self.subTest(category=case["category"]):
                user = self.test_users[case["persona"]]
                service = FastResponseService(user=user)

                if not service.memory_manager:
                    self.skipTest("Memory manager not available")

                user_id = f"test_{case['persona']}_edge"
                self._store_memories_for_persona(case["persona"], user_id)

                # Test agent processing detection
                needs_agent = service._needs_agent_processing(case["query"])
                self.assertEqual(needs_agent, case["expected_agent"],
                               f"Edge case detection failed for {case['category']}")

                # Test system prompt generation doesn't crash
                prompt = service._build_fast_system_prompt(
                    emotion_context=None,
                    needs_agent=needs_agent,
                    user_id=user_id
                )

                # Should always generate a valid prompt
                self.assertGreater(len(prompt), 100, f"Should generate substantial prompt for {case['category']}")
                self.assertIn(user.ai_companion_name, prompt, f"Should contain companion name for {case['category']}")

    def test_performance_across_personas(self):
        """Test performance characteristics across different personas."""
        import time

        performance_results = {}

        for persona_name, user in self.test_users.items():
            service = FastResponseService(user=user)
            if not service.memory_manager:
                self.skipTest("Memory manager not available")

            user_id = f"test_{persona_name}_perf"
            self._store_memories_for_persona(persona_name, user_id)

            # Test memory retrieval performance
            start_time = time.time()
            memories = service._get_contextual_memories(user_id, limit=3)
            memory_time = (time.time() - start_time) * 1000

            # Test prompt generation performance
            start_time = time.time()
            prompt = service._build_fast_system_prompt(
                emotion_context=None,
                needs_agent=True,
                user_id=user_id
            )
            prompt_time = (time.time() - start_time) * 1000

            performance_results[persona_name] = {
                'memory_retrieval_ms': memory_time,
                'prompt_generation_ms': prompt_time,
                'total_ms': memory_time + prompt_time,
                'memories_found': len(memories),
                'prompt_length': len(prompt)
            }

            # Performance assertions
            self.assertLess(memory_time, 100, f"Memory retrieval should be fast for {persona_name}")
            self.assertLess(prompt_time, 50, f"Prompt generation should be fast for {persona_name}")
            self.assertGreater(len(memories), 0, f"Should find memories for {persona_name}")

        # Overall performance check
        avg_total_time = sum(r['total_ms'] for r in performance_results.values()) / len(performance_results)
        self.assertLess(avg_total_time, 100, "Average processing time should be under 100ms")

    def test_memory_context_quality(self):
        """Test the quality and relevance of memory context in prompts."""
        quality_tests = [
            {
                "persona": "tech_professional",
                "query": "Help me with a software architecture problem",
                "expected_memory_categories": ["work", "technical", "Microsoft"],
                "category": "Technical Context"
            },
            {
                "persona": "busy_parent",
                "query": "I need advice on managing work-life balance",
                "expected_memory_categories": ["kids", "work", "family"],
                "category": "Family Context"
            },
            {
                "persona": "healthcare_worker",
                "query": "I'm feeling stressed about work",
                "expected_memory_categories": ["ICU", "hospital", "emotional"],
                "category": "Healthcare Context"
            },
            {
                "persona": "creative_artist",
                "query": "I want to improve my artistic skills",
                "expected_memory_categories": ["design", "art", "creative"],
                "category": "Creative Context"
            }
        ]

        for test in quality_tests:
            with self.subTest(category=test["category"]):
                user = self.test_users[test["persona"]]
                service = FastResponseService(user=user)

                if not service.memory_manager:
                    self.skipTest("Memory manager not available")

                user_id = f"test_{test['persona']}_quality"
                self._store_memories_for_persona(test["persona"], user_id)

                # Generate prompt with memory context
                prompt = service._build_fast_system_prompt(
                    emotion_context=None,
                    needs_agent=True,
                    user_id=user_id
                )

                # Check for relevant memory categories
                prompt_lower = prompt.lower()
                found_categories = []

                for category in test["expected_memory_categories"]:
                    if category.lower() in prompt_lower:
                        found_categories.append(category)

                # Should find at least some relevant context
                self.assertGreater(len(found_categories), 0,
                                 f"Should find relevant memory context for {test['category']}")

                # Check memory context structure
                self.assertIn('CONTEXTUAL MEMORIES', prompt,
                             f"Should have structured memory context for {test['category']}")
                self.assertIn('personal and engaging', prompt,
                             f"Should mention engagement goal for {test['category']}")
