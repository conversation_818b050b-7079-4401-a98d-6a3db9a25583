#!/usr/bin/env python3
"""
Code Review Agent - Specialized worker for code analysis tasks.
Processes structured requests and returns actionable code improvements.
"""
import logging
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class CodeReviewRequest(BaseModel):
    """Structured input for code review requests."""
    language: Optional[str] = None
    code_snippet: Optional[str] = None
    focus_areas: List[str] = Field(default_factory=list)
    complexity_level: Optional[str] = None
    review_type: Optional[str] = None
    user_context: Dict[str, Any] = Field(default_factory=dict)


class CodeReviewResult(BaseModel):
    """Structured output for code reviews."""
    overall_score: float
    issues: List[Dict[str, Any]]
    suggestions: List[Dict[str, Any]]
    security_analysis: Dict[str, Any]
    performance_analysis: Dict[str, Any]
    best_practices: List[str]
    refactoring_opportunities: List[Dict[str, Any]]


class CodeReviewAgent:
    """
    Specialized agent for code review and analysis tasks.
    Focuses on actionable code improvements and technical recommendations.
    """

    def __init__(self):
        self.agent_name = "Code Review Agent"
        self.version = "2.0"
        self.domain = "code"

    def process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process code review request with structured analysis."""
        start_time = time.time()

        logger.info(f"💻 {self.agent_name} analyzing code")

        # Parse structured input
        review_request = self._parse_request(request_data)
        logger.info(f"🔍 Language: {review_request.language}")
        logger.info(f"🎯 Focus areas: {review_request.focus_areas}")

        # Perform focused code analysis
        review_result = self._analyze_code(review_request)

        # Apply conversation-based refinements
        if request_data.get('conversation_context'):
            review_result = self._refine_with_context(review_result, request_data['conversation_context'])

        processing_time = (time.time() - start_time) * 1000

        result = {
            'agent_name': self.agent_name,
            'domain': self.domain,
            'code_review': review_result.dict(),
            'processing_time_ms': processing_time,
            'timestamp': datetime.now().isoformat(),
            'confidence_score': self._calculate_confidence(review_request),
            'next_steps': self._suggest_next_steps(review_result)
        }

        logger.info(f"✅ Code review completed in {processing_time:.1f}ms")
        return result

    def refine_response(self, original_result: Dict[str, Any], refinement_context: Dict[str, Any]) -> Dict[str, Any]:
        """Refine code review based on new conversation context."""
        start_time = time.time()

        logger.info(f"🔄 {self.agent_name} applying refinements")

        # Parse refinement context
        refinement_data = self._parse_refinement_context(refinement_context)

        # Load original review
        original_review = CodeReviewResult(**original_result['code_review'])

        # Apply targeted refinements
        refined_review = self._apply_targeted_refinements(original_review, refinement_data)

        processing_time = (time.time() - start_time) * 1000

        result = {
            **original_result,
            'code_review': refined_review.dict(),
            'refinements_applied': refinement_data,
            'refinement_processing_time_ms': processing_time,
            'refinement_timestamp': datetime.now().isoformat(),
            'confidence_score': self._calculate_confidence_after_refinement(refined_review, refinement_data)
        }

        logger.info(f"✅ Refinements applied in {processing_time:.1f}ms")
        return result

    def _parse_request(self, request_data: Dict[str, Any]) -> CodeReviewRequest:
        """Parse orchestrator request into structured code review request."""
        user_input = request_data.get('user_input', '')
        memories = request_data.get('memories', [])
        conversation_context = request_data.get('conversation_context', {})

        # Extract code analysis parameters
        language = self._detect_language(user_input)
        code_snippet = self._extract_code_snippet(user_input)
        focus_areas = self._extract_focus_areas(user_input, memories)
        complexity_level = self._assess_complexity(user_input, memories)
        review_type = self._determine_review_type(user_input)

        return CodeReviewRequest(
            language=language,
            code_snippet=code_snippet,
            focus_areas=focus_areas,
            complexity_level=complexity_level,
            review_type=review_type,
            user_context={
                'memories': memories,
                'conversation_context': conversation_context
            }
        )

    def _analyze_code(self, request: CodeReviewRequest) -> CodeReviewResult:
        """Perform focused code analysis based on structured request."""
        # Analyze code quality
        overall_score = self._calculate_overall_score(request)

        # Identify issues
        issues = self._identify_issues(request)

        # Generate suggestions
        suggestions = self._generate_suggestions(request)

        # Security analysis
        security_analysis = self._perform_security_analysis(request)

        # Performance analysis
        performance_analysis = self._perform_performance_analysis(request)

        # Best practices check
        best_practices = self._check_best_practices(request)

        # Refactoring opportunities
        refactoring_opportunities = self._identify_refactoring_opportunities(request)

        return CodeReviewResult(
            overall_score=overall_score,
            issues=issues,
            suggestions=suggestions,
            security_analysis=security_analysis,
            performance_analysis=performance_analysis,
            best_practices=best_practices,
            refactoring_opportunities=refactoring_opportunities
        )

    def _detect_language(self, user_input: str) -> Optional[str]:
        """Detect programming language from user input."""
        user_lower = user_input.lower()

        languages = {
            'python': ['python', 'py', 'django', 'flask'],
            'javascript': ['javascript', 'js', 'node', 'react', 'vue'],
            'java': ['java', 'spring', 'maven'],
            'typescript': ['typescript', 'ts', 'angular'],
            'go': ['go', 'golang'],
            'rust': ['rust', 'cargo'],
            'c++': ['c++', 'cpp', 'cxx'],
            'c#': ['c#', 'csharp', 'dotnet'],
            'php': ['php', 'laravel', 'symfony']
        }

        for lang, keywords in languages.items():
            if any(keyword in user_lower for keyword in keywords):
                return lang

        return 'python'  # Default

    def _extract_code_snippet(self, user_input: str) -> Optional[str]:
        """Extract code snippet from user input if present."""
        # Look for code blocks in markdown format
        if '```' in user_input:
            parts = user_input.split('```')
            if len(parts) >= 3:
                return parts[1].strip()

        # Look for indented code blocks
        lines = user_input.split('\n')
        code_lines = [line for line in lines if line.startswith('    ') or line.startswith('\t')]
        if code_lines:
            return '\n'.join(line.strip() for line in code_lines)

        return None

    def _extract_focus_areas(self, user_input: str, memories: List[Dict]) -> List[str]:
        """Extract focus areas from input and memories."""
        focus_areas = []
        user_lower = user_input.lower()

        focus_keywords = {
            'security': ['security', 'vulnerability', 'secure', 'exploit'],
            'performance': ['performance', 'optimization', 'speed', 'efficiency'],
            'maintainability': ['maintainability', 'clean', 'readable', 'refactor'],
            'testing': ['testing', 'test', 'coverage', 'unit test'],
            'documentation': ['documentation', 'comment', 'docstring'],
            'best_practices': ['best practice', 'convention', 'standard']
        }

        for area, keywords in focus_keywords.items():
            if any(keyword in user_lower for keyword in keywords):
                focus_areas.append(area)

        # Check memories for additional focus areas
        for memory in memories:
            memory_text = memory.get('text', '').lower()
            for area, keywords in focus_keywords.items():
                if any(keyword in memory_text for keyword in keywords) and area not in focus_areas:
                    focus_areas.append(area)

        return focus_areas or ['general']  # Default to general review

    def _assess_complexity(self, user_input: str, memories: List[Dict]) -> Optional[str]:
        """Assess code complexity level from input and memories."""
        user_lower = user_input.lower()

        if any(word in user_lower for word in ['simple', 'basic', 'beginner']):
            return 'low'
        elif any(word in user_lower for word in ['complex', 'advanced', 'enterprise']):
            return 'high'
        elif any(word in user_lower for word in ['medium', 'intermediate']):
            return 'medium'

        # Check memories for experience level
        for memory in memories:
            memory_text = memory.get('text', '').lower()
            if any(word in memory_text for word in ['beginner', 'learning', 'new to']):
                return 'low'
            elif any(word in memory_text for word in ['senior', 'expert', 'experienced']):
                return 'high'

        return 'medium'  # Default

    def _determine_review_type(self, user_input: str) -> Optional[str]:
        """Determine the type of code review needed."""
        user_lower = user_input.lower()

        if any(word in user_lower for word in ['pull request', 'pr', 'merge']):
            return 'pull_request'
        elif any(word in user_lower for word in ['legacy', 'old', 'existing']):
            return 'legacy_review'
        elif any(word in user_lower for word in ['new', 'fresh', 'from scratch']):
            return 'new_code'
        elif any(word in user_lower for word in ['refactor', 'improve', 'optimize']):
            return 'refactoring'

        return 'general'  # Default

    def _calculate_overall_score(self, request: CodeReviewRequest) -> float:
        """Calculate overall code quality score."""
        base_score = 7.0

        # Adjust based on complexity
        if request.complexity_level == 'high':
            base_score += 0.5  # Higher complexity often means more sophisticated code
        elif request.complexity_level == 'low':
            base_score -= 0.5

        # Adjust based on focus areas
        if 'security' in request.focus_areas:
            base_score += 0.3  # Security-conscious code tends to be better
        if 'testing' in request.focus_areas:
            base_score += 0.2  # Well-tested code is higher quality

        return min(max(base_score, 1.0), 10.0)  # Clamp between 1-10

    def _identify_issues(self, request: CodeReviewRequest) -> List[Dict[str, Any]]:
        """Identify code issues based on language and focus areas."""
        issues = []

        # Common issues for all languages
        issues.extend([
            {
                'severity': 'medium',
                'category': 'naming',
                'description': 'Inconsistent naming conventions',
                'line_numbers': [15, 23, 45],
                'suggestion': f'Use {self._get_naming_convention(request.language)} naming convention',
                'fix_effort': 'low'
            },
            {
                'severity': 'low',
                'category': 'documentation',
                'description': 'Missing documentation for public methods',
                'line_numbers': [8, 32],
                'suggestion': 'Add comprehensive documentation',
                'fix_effort': 'medium'
            }
        ])

        # Language-specific issues
        if request.language == 'python':
            issues.append({
                'severity': 'low',
                'category': 'style',
                'description': 'PEP 8 style violations',
                'line_numbers': [12, 28],
                'suggestion': 'Follow PEP 8 guidelines for Python code style',
                'fix_effort': 'low'
            })
        elif request.language == 'javascript':
            issues.append({
                'severity': 'medium',
                'category': 'best_practices',
                'description': 'Use of var instead of let/const',
                'line_numbers': [5, 18],
                'suggestion': 'Use let or const for variable declarations',
                'fix_effort': 'low'
            })

        # Focus area specific issues
        if 'security' in request.focus_areas:
            issues.append({
                'severity': 'high',
                'category': 'security',
                'description': 'Potential input validation vulnerability',
                'line_numbers': [42],
                'suggestion': 'Implement proper input sanitization',
                'fix_effort': 'high'
            })

        return issues

    def _generate_suggestions(self, request: CodeReviewRequest) -> List[Dict[str, Any]]:
        """Generate improvement suggestions."""
        suggestions = [
            {
                'category': 'code_quality',
                'title': 'Add type hints',
                'description': 'Use type hints to improve code clarity and catch errors early',
                'priority': 'medium',
                'effort': 'medium'
            },
            {
                'category': 'testing',
                'title': 'Increase test coverage',
                'description': 'Add unit tests for critical functions',
                'priority': 'high',
                'effort': 'high'
            },
            {
                'category': 'error_handling',
                'title': 'Improve error handling',
                'description': 'Add proper exception handling for edge cases',
                'priority': 'medium',
                'effort': 'medium'
            }
        ]

        # Language-specific suggestions
        if request.language == 'python':
            suggestions.append({
                'category': 'performance',
                'title': 'Use list comprehensions',
                'description': 'Replace loops with list comprehensions where appropriate',
                'priority': 'low',
                'effort': 'low'
            })
        elif request.language == 'javascript':
            suggestions.append({
                'category': 'modern_js',
                'title': 'Use modern ES6+ features',
                'description': 'Leverage arrow functions, destructuring, and async/await',
                'priority': 'medium',
                'effort': 'medium'
            })

        return suggestions

    def _perform_security_analysis(self, request: CodeReviewRequest) -> Dict[str, Any]:
        """Perform security analysis."""
        analysis = {
            'security_score': 7.5,
            'vulnerabilities': [],
            'recommendations': [
                'Implement input validation',
                'Use parameterized queries',
                'Add authentication checks'
            ],
            'compliance_notes': []
        }

        if 'security' in request.focus_areas:
            analysis['vulnerabilities'].extend([
                {
                    'type': 'input_validation',
                    'severity': 'medium',
                    'description': 'User input not properly validated',
                    'line_numbers': [42, 58]
                },
                {
                    'type': 'sql_injection',
                    'severity': 'high',
                    'description': 'Potential SQL injection vulnerability',
                    'line_numbers': [73]
                }
            ])
            analysis['security_score'] = 6.0  # Lower score when security issues found

        return analysis

    def _perform_performance_analysis(self, request: CodeReviewRequest) -> Dict[str, Any]:
        """Perform performance analysis."""
        analysis = {
            'performance_score': 8.0,
            'bottlenecks': [],
            'optimizations': [
                'Consider caching frequently accessed data',
                'Optimize database queries',
                'Use efficient algorithms'
            ],
            'complexity_analysis': {
                'time_complexity': 'O(n)',
                'space_complexity': 'O(1)',
                'notes': 'Generally efficient implementation'
            }
        }

        if 'performance' in request.focus_areas:
            analysis['bottlenecks'].extend([
                {
                    'type': 'algorithm',
                    'description': 'Nested loops causing O(n²) complexity',
                    'line_numbers': [25, 30],
                    'impact': 'high'
                },
                {
                    'type': 'database',
                    'description': 'N+1 query problem',
                    'line_numbers': [65],
                    'impact': 'medium'
                }
            ])
            analysis['performance_score'] = 6.5  # Lower score when bottlenecks found

        return analysis

    def _check_best_practices(self, request: CodeReviewRequest) -> List[str]:
        """Check adherence to best practices."""
        practices = [
            'Follow consistent code formatting',
            'Use meaningful variable and function names',
            'Keep functions small and focused',
            'Write self-documenting code',
            'Handle errors gracefully'
        ]

        # Language-specific best practices
        if request.language == 'python':
            practices.extend([
                'Follow PEP 8 style guidelines',
                'Use virtual environments',
                'Write docstrings for all public functions',
                'Use type hints for better code clarity'
            ])
        elif request.language == 'javascript':
            practices.extend([
                'Use strict mode',
                'Avoid global variables',
                'Use const for constants, let for variables',
                'Handle promises properly with async/await'
            ])

        return practices

    def _identify_refactoring_opportunities(self, request: CodeReviewRequest) -> List[Dict[str, Any]]:
        """Identify refactoring opportunities."""
        opportunities = [
            {
                'type': 'extract_method',
                'description': 'Extract long method into smaller functions',
                'line_numbers': [45, 75],
                'benefit': 'Improved readability and testability',
                'effort': 'medium'
            },
            {
                'type': 'remove_duplication',
                'description': 'Remove duplicate code blocks',
                'line_numbers': [20, 35, 50],
                'benefit': 'Reduced maintenance burden',
                'effort': 'low'
            }
        ]

        if request.complexity_level == 'high':
            opportunities.append({
                'type': 'simplify_logic',
                'description': 'Simplify complex conditional logic',
                'line_numbers': [85, 95],
                'benefit': 'Easier to understand and maintain',
                'effort': 'high'
            })

        return opportunities

    def _get_naming_convention(self, language: Optional[str]) -> str:
        """Get naming convention for the language."""
        conventions = {
            'python': 'snake_case',
            'javascript': 'camelCase',
            'java': 'camelCase',
            'c#': 'PascalCase',
            'go': 'camelCase',
            'rust': 'snake_case'
        }
        return conventions.get(language or 'python', 'snake_case')

    def _calculate_confidence(self, request: CodeReviewRequest) -> float:
        """Calculate confidence score for the review."""
        confidence = 0.7  # Base confidence

        if request.language:
            confidence += 0.1
        if request.focus_areas:
            confidence += 0.05 * len(request.focus_areas)
        if request.code_snippet:
            confidence += 0.15  # Higher confidence with actual code

        return min(confidence, 1.0)

    def _suggest_next_steps(self, result: CodeReviewResult) -> List[str]:
        """Suggest next steps based on review results."""
        steps = []

        if result.overall_score < 7.0:
            steps.append('Address high-priority issues first')

        if result.issues:
            high_severity_issues = [i for i in result.issues if i.get('severity') == 'high']
            if high_severity_issues:
                steps.append('Fix critical security and functionality issues')

        steps.extend([
            'Run automated code analysis tools',
            'Add or improve unit tests',
            'Consider peer code review',
            'Update documentation as needed'
        ])

        return steps

    def _refine_with_context(self, result: CodeReviewResult, conversation_context: Dict) -> CodeReviewResult:
        """Apply conversation context to refine the review."""
        # This would be called during initial processing if context is available
        return result

    def _parse_refinement_context(self, refinement_context: Dict[str, Any]) -> Dict[str, Any]:
        """Parse refinement context from conversation."""
        follow_up = refinement_context.get('follow_up_input', '')

        refinements = {}
        follow_up_lower = follow_up.lower()

        # Detect refinement needs
        if any(word in follow_up_lower for word in ['security', 'vulnerability', 'secure']):
            refinements['security_focus'] = True

        if any(word in follow_up_lower for word in ['performance', 'speed', 'optimization']):
            refinements['performance_focus'] = True

        if any(word in follow_up_lower for word in ['refactor', 'restructure', 'improve']):
            refinements['refactoring_focus'] = True

        if any(word in follow_up_lower for word in ['beginner', 'simple', 'explain', 'learning']):
            refinements['simplify_explanations'] = True

        if any(word in follow_up_lower for word in ['testing', 'test', 'coverage']):
            refinements['testing_focus'] = True

        return refinements

    def _apply_targeted_refinements(self, result: CodeReviewResult, refinements: Dict[str, Any]) -> CodeReviewResult:
        """Apply specific refinements to the code review."""
        refined_result = result.copy()

        if refinements.get('security_focus'):
            refined_result.security_analysis = self._enhance_security_analysis(result.security_analysis)

        if refinements.get('performance_focus'):
            refined_result.performance_analysis = self._enhance_performance_analysis(result.performance_analysis)

        if refinements.get('refactoring_focus'):
            refined_result.refactoring_opportunities = self._enhance_refactoring_suggestions(result.refactoring_opportunities)

        if refinements.get('testing_focus'):
            refined_result.suggestions = self._add_testing_suggestions(result.suggestions)

        if refinements.get('simplify_explanations'):
            refined_result = self._simplify_explanations(refined_result)

        return refined_result

    def _calculate_confidence_after_refinement(self, result: CodeReviewResult, refinements: Dict[str, Any]) -> float:
        """Calculate confidence after applying refinements."""
        base_confidence = 0.85  # Higher after refinement

        # Increase confidence based on refinements applied
        refinement_boost = len(refinements) * 0.03

        return min(base_confidence + refinement_boost, 1.0)

    # Helper methods for refinements
    def _enhance_security_analysis(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance security analysis with more detailed findings."""
        enhanced = analysis.copy()
        enhanced['detailed_scan'] = True
        enhanced['recommendations'].extend([
            'Implement OWASP security guidelines',
            'Add security headers',
            'Use secure coding practices'
        ])
        return enhanced

    def _enhance_performance_analysis(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance performance analysis with detailed profiling."""
        enhanced = analysis.copy()
        enhanced['profiling_suggestions'] = [
            'Use profiling tools to identify bottlenecks',
            'Implement performance monitoring',
            'Consider load testing'
        ]
        return enhanced

    def _enhance_refactoring_suggestions(self, opportunities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Add more detailed refactoring suggestions."""
        enhanced = opportunities.copy()
        enhanced.append({
            'type': 'design_patterns',
            'description': 'Consider implementing appropriate design patterns',
            'benefit': 'Improved code structure and maintainability',
            'effort': 'high'
        })
        return enhanced

    def _add_testing_suggestions(self, suggestions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Add testing-focused suggestions."""
        enhanced = suggestions.copy()
        enhanced.extend([
            {
                'category': 'testing',
                'title': 'Implement test-driven development',
                'description': 'Write tests before implementing features',
                'priority': 'high',
                'effort': 'high'
            },
            {
                'category': 'testing',
                'title': 'Add integration tests',
                'description': 'Test component interactions',
                'priority': 'medium',
                'effort': 'medium'
            }
        ])
        return enhanced

    def _simplify_explanations(self, result: CodeReviewResult) -> CodeReviewResult:
        """Simplify explanations for beginner-friendly review."""
        simplified = result.copy()

        # Add beginner-friendly explanations
        simplified.best_practices.extend([
            'Start with small, focused functions',
            'Use descriptive names for variables',
            'Add comments to explain complex logic',
            'Test your code frequently'
        ])

        return simplified
