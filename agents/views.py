from django.shortcuts import render
from rest_framework import status, views, permissions, generics, viewsets
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from gamification.models import ShopItem, UserInventory, Pet
from .serializers import AvatarSettingsSerializer
from .models import TaskModel
from .serializers import TaskModelSerializer
from rest_framework.decorators import action
from memory.models import Memory, MemoryType, MemoryConfiguration, MemoryCluster
from .serializers import (
    MemorySerializer, MemoryCreateSerializer, MemoryUpdateSerializer,
    MemoryClusterSerializer, MemoryConfigurationSerializer,
    MemoryTypeSerializer, MemoryStatsSerializer
)
from django.db.models import Count, Avg, Q
from django.utils import timezone
from datetime import timedelta

User = get_user_model()

class AvatarOutfitView(views.APIView):
    """
    API view for changing avatar outfit.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        Change the avatar's outfit.
        
        Request body:
        {
            "outfit_id": "uuid-of-outfit-item"
        }
        """
        outfit_id = request.data.get('outfit_id')
        
        if not outfit_id:
            return Response(
                {"error": "outfit_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Check if the outfit exists and is owned by the user
            outfit = UserInventory.objects.filter(
                user=request.user,
                item__id=outfit_id,
                item__item_type='outfit'
            ).first()
            
            if not outfit:
                return Response(
                    {"error": "Outfit not found or not owned by user"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Unequip all other outfits
            UserInventory.objects.filter(
                user=request.user,
                item__item_type='outfit',
                is_equipped=True
            ).update(is_equipped=False)
            
            # Equip the selected outfit
            outfit.is_equipped = True
            outfit.save()
            
            # Return the updated avatar settings
            serializer = AvatarSettingsSerializer(request.user)
            return Response(serializer.data)
            
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class EnvironmentChangeView(views.APIView):
    """
    API view for changing the avatar's environment.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        Change the avatar's environment.
        
        Request body:
        {
            "environment_id": "uuid-of-environment-item"
        }
        """
        environment_id = request.data.get('environment_id')
        
        if not environment_id:
            return Response(
                {"error": "environment_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Check if the environment exists and is owned by the user
            environment = UserInventory.objects.filter(
                user=request.user,
                item__id=environment_id,
                item__item_type='environment'
            ).first()
            
            if not environment:
                return Response(
                    {"error": "Environment not found or not owned by user"},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Unequip all other environments
            UserInventory.objects.filter(
                user=request.user,
                item__item_type='environment',
                is_equipped=True
            ).update(is_equipped=False)
            
            # Equip the selected environment
            environment.is_equipped = True
            environment.save()
            
            # Update the user's selected environment
            request.user.selected_environment = environment.item.name
            request.user.save(update_fields=['selected_environment'])
            
            # Return the updated avatar settings
            serializer = AvatarSettingsSerializer(request.user)
            return Response(serializer.data)
            
        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AnimationPlayView(views.APIView):
    """
    API view for playing avatar animations.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        Play an animation on the avatar.
        
        Request body:
        {
            "animation_name": "animation_name",
            "animation_parameters": {
                "param1": "value1",
                "param2": "value2"
            }
        }
        """
        animation_name = request.data.get('animation_name')
        animation_parameters = request.data.get('animation_parameters', {})
        
        if not animation_name:
            return Response(
                {"error": "animation_name is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate animation name against allowed animations
        allowed_animations = [
            'wave', 'smile', 'laugh', 'dance', 'sit', 'stand',
            'walk', 'run', 'jump', 'idle', 'think', 'nod',
            'shake_head', 'point', 'clap', 'bow', 'excited',
            'sad', 'angry', 'surprised', 'confused', 'wink'
        ]
        
        if animation_name not in allowed_animations:
            return Response(
                {"error": f"Animation '{animation_name}' is not supported. Allowed animations: {', '.join(allowed_animations)}"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # In a real implementation, this would trigger the animation in the Unity client
        # For now, we'll just return a success response
        
        return Response({
            "success": True,
            "message": f"Animation '{animation_name}' triggered successfully",
            "animation_name": animation_name,
            "animation_parameters": animation_parameters
        })


class AvatarSettingsView(generics.RetrieveAPIView):
    """
    API view for retrieving avatar settings.
    """
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = AvatarSettingsSerializer
    
    def get_object(self):
        """Return the authenticated user for serialization"""
        return self.request.user


class VoiceCommandView(views.APIView):
    """
    API view for processing voice commands for the avatar.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        Process a voice command for the avatar.
        
        Request body:
        {
            "command": "voice command text",
            "audio_url": "optional url to audio file",
            "context": {
                "location": "optional context information",
                "previous_action": "optional previous action"
            }
        }
        """
        command = request.data.get('command')
        audio_url = request.data.get('audio_url')
        context = request.data.get('context', {})
        
        if not command and not audio_url:
            return Response(
                {"error": "Either command text or audio_url is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Process the voice command
        # In a real implementation, this would:
        # 1. If audio_url is provided, transcribe the audio to text
        # 2. Parse the command using NLP
        # 3. Execute the appropriate action
        # 4. Return the result
        
        # For now, we'll implement a simple command parser
        response_data = self._process_command(command, context)
        
        return Response(response_data)
    
    def _process_command(self, command, context):
        """
        Process a voice command and return appropriate response.
        
        This is a simple implementation that recognizes a few basic commands.
        In a real implementation, this would use more sophisticated NLP.
        """
        command = command.lower() if command else ""
        
        # Define a list of known command keywords
        known_commands = [
            'wave', 'hello', 'hi', 'greet',
            'smile', 'happy', 'grin',
            'dance', 'dancing',
            'sit', 'sit down',
            'stand', 'stand up', 'get up',
            'change outfit', 'wear', 'dress',
            'change environment', 'new place', 'different room'
        ]
        
        # Check if any known command keywords are in the command
        for known_command in known_commands:
            if known_command in command:
                if 'wave' in known_command:
                    return {
                        "success": True,
                        "action": "animation",
                        "animation_name": "wave",
                        "message": "Avatar is waving"
                    }
                
                elif 'smile' in known_command:
                    return {
                        "success": True,
                        "action": "animation",
                        "animation_name": "smile",
                        "message": "Avatar is smiling"
                    }
                
                elif 'dance' in known_command:
                    return {
                        "success": True,
                        "action": "animation",
                        "animation_name": "dance",
                        "message": "Avatar is dancing"
                    }
                
                elif 'sit' in known_command:
                    return {
                        "success": True,
                        "action": "animation",
                        "animation_name": "sit",
                        "message": "Avatar is sitting down"
                    }
                
                elif 'stand' in known_command:
                    return {
                        "success": True,
                        "action": "animation",
                        "animation_name": "stand",
                        "message": "Avatar is standing up"
                    }
                
                elif 'change outfit' in known_command:
                    return {
                        "success": True,
                        "action": "suggest_outfit_change",
                        "message": "Would you like to change the avatar's outfit?"
                    }
                
                elif 'change environment' in known_command:
                    return {
                        "success": True,
                        "action": "suggest_environment_change",
                        "message": "Would you like to change the environment?"
                    }
        
        # If no known command is found
        return {
            "success": True,
            "action": "unknown",
            "message": "I'm not sure how to respond to that command"
        }


class TaskModelViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing tasks with advanced tracking capabilities.
    """
    queryset = TaskModel.objects.all()
    serializer_class = TaskModelSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """
        Return tasks for the current authenticated user.
        """
        return TaskModel.objects.filter(user=self.request.user)
    
    def get_serializer_context(self):
        """
        Extra context provided to the serializer class.
        """
        context = super().get_serializer_context()
        context['request'] = self.request
        return context
    
    def _convert_task_to_background_task(self, task):
        """
        Convert a TaskModel to a background task dictionary
        compatible with agent orchestrator's task tracking.
        """
        return {
            'task_id': str(task.id),
            'title': task.title,
            'description': task.description,
            'status': task.status,
            'progress': task.progress,
            'phases': task.phases,
            'error_message': task.error_message,
            'intervention_message': task.intervention_message,
            'created_at': task.created_at.isoformat(),
            'updated_at': task.updated_at.isoformat()
        }
    
    @action(detail=False, methods=['GET'])
    def sync_with_orchestrator(self, request):
        """
        Synchronize tasks with the agent orchestrator's task tracking.
        """
        # Get all tasks for the current user
        tasks = TaskModel.objects.filter(user=request.user)
        
        # Convert tasks to background task format
        background_tasks = [
            self._convert_task_to_background_task(task) for task in tasks
        ]
        
        return Response({
            'active_tasks': background_tasks,
            'total_tasks': len(background_tasks)
        })
    
    @action(detail=True, methods=['PATCH'])
    def update_status(self, request, pk=None):
        """
        Update task status with optional error or intervention messages.
        """
        task = self.get_object()
        
        new_status = request.data.get('status')
        error_message = request.data.get('error_message')
        intervention_message = request.data.get('intervention_message')
        
        if not new_status:
            return Response(
                {"error": "Status is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.update_status(
            new_status, 
            error_message=error_message, 
            intervention_message=intervention_message
        )
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['PATCH'])
    def update_progress(self, request, pk=None):
        """
        Update task progress and optionally mark current phase.
        """
        task = self.get_object()
        
        progress = request.data.get('progress')
        current_phase = request.data.get('current_phase')
        
        if progress is None:
            return Response(
                {"error": "Progress is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.update_progress(
            float(progress), 
            current_phase=current_phase
        )
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)
    
    @action(detail=True, methods=['POST'])
    def add_phase(self, request, pk=None):
        """
        Add a new phase to the task.
        """
        task = self.get_object()
        
        phase = request.data.get('phase')
        title = request.data.get('title')
        description = request.data.get('description')
        sub_tasks = request.data.get('sub_tasks', [])
        
        if not all([phase, title, description]):
            return Response(
                {"error": "Phase, title, and description are required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        task.add_phase(
            phase=phase, 
            title=title, 
            description=description, 
            sub_tasks=sub_tasks
        )
        
        serializer = self.get_serializer(task)
        return Response(serializer.data)


class MemoryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user memories with comprehensive CRUD operations.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return memories for the current authenticated user."""
        return Memory.objects.filter(user=self.request.user)
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return MemoryCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return MemoryUpdateSerializer
        return MemorySerializer
    
    def get_serializer_context(self):
        """Extra context provided to the serializer class."""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context
    
    @action(detail=False, methods=['GET'])
    def types(self, request):
        """Get available memory types."""
        memory_types = [
            {'value': choice[0], 'display': choice[1]}
            for choice in MemoryType.choices
        ]
        serializer = MemoryTypeSerializer(memory_types, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['GET'])
    def stats(self, request):
        """Get memory statistics for the user."""
        user_memories = Memory.objects.filter(user=request.user)
        
        # Calculate statistics
        total_memories = user_memories.count()
        active_memories = user_memories.filter(is_active=True).count()
        verified_memories = user_memories.filter(is_verified=True).count()
        
        # Memory types distribution
        memory_types = user_memories.values('memory_type').annotate(
            count=Count('id')
        )
        memory_types_dict = {
            item['memory_type']: item['count'] 
            for item in memory_types
        }
        
        # Average salience score
        avg_salience = user_memories.aggregate(
            avg_score=Avg('importance_score')
        )['avg_score'] or 0.0
        
        # Recent memories (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_memories = user_memories.filter(
            created_at__gte=thirty_days_ago
        ).count()
        
        stats_data = {
            'total_memories': total_memories,
            'active_memories': active_memories,
            'verified_memories': verified_memories,
            'memory_types': memory_types_dict,
            'average_salience_score': round(avg_salience, 2),
            'recent_memories_count': recent_memories
        }
        
        serializer = MemoryStatsSerializer(stats_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['GET'])
    def search(self, request):
        """Search memories by content and type."""
        query = request.query_params.get('q', '')
        memory_type = request.query_params.get('type', '')
        is_active = request.query_params.get('active', None)
        
        queryset = self.get_queryset()
        
        # Apply filters
        if query:
            queryset = queryset.filter(content__icontains=query)
        
        if memory_type:
            queryset = queryset.filter(memory_type=memory_type)
        
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)
        
        # Order by salience score
        queryset = queryset.order_by('-importance_score', '-created_at')
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['POST'])
    def verify(self, request, pk=None):
        """Mark a memory as verified."""
        memory = self.get_object()
        memory.is_verified = True
        memory.save()
        
        serializer = self.get_serializer(memory)
        return Response(serializer.data)
    
    @action(detail=True, methods=['POST'])
    def deactivate(self, request, pk=None):
        """Deactivate a memory."""
        memory = self.get_object()
        memory.is_active = False
        memory.save()
        
        serializer = self.get_serializer(memory)
        return Response(serializer.data)
    
    @action(detail=True, methods=['POST'])
    def reactivate(self, request, pk=None):
        """Reactivate a memory."""
        memory = self.get_object()
        memory.is_active = True
        memory.save()
        
        serializer = self.get_serializer(memory)
        return Response(serializer.data)


class MemoryClusterViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing memory clusters.
    """
    serializer_class = MemoryClusterSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return memory clusters for the current authenticated user."""
        return MemoryCluster.objects.filter(user=self.request.user)
    
    def get_serializer_context(self):
        """Extra context provided to the serializer class."""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context
    
    @action(detail=True, methods=['POST'])
    def add_memory(self, request, pk=None):
        """Add a memory to a cluster."""
        cluster = self.get_object()
        memory_id = request.data.get('memory_id')
        
        if not memory_id:
            return Response(
                {"error": "memory_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            memory = Memory.objects.get(id=memory_id, user=request.user)
            cluster.memories.add(memory)
            
            serializer = self.get_serializer(cluster)
            return Response(serializer.data)
            
        except Memory.DoesNotExist:
            return Response(
                {"error": "Memory not found"},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['POST'])
    def remove_memory(self, request, pk=None):
        """Remove a memory from a cluster."""
        cluster = self.get_object()
        memory_id = request.data.get('memory_id')
        
        if not memory_id:
            return Response(
                {"error": "memory_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            memory = Memory.objects.get(id=memory_id, user=request.user)
            cluster.memories.remove(memory)
            
            serializer = self.get_serializer(cluster)
            return Response(serializer.data)
            
        except Memory.DoesNotExist:
            return Response(
                {"error": "Memory not found"},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['GET'])
    def memories(self, request, pk=None):
        """Get all memories in a cluster."""
        cluster = self.get_object()
        memories = cluster.memories.all()
        
        serializer = MemorySerializer(memories, many=True)
        return Response(serializer.data)


class MemoryConfigurationView(generics.RetrieveUpdateAPIView):
    """
    API view for managing user memory configuration.
    """
    serializer_class = MemoryConfigurationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        """Get or create memory configuration for the user."""
        config, created = MemoryConfiguration.objects.get_or_create(
            user=self.request.user
        )
        return config
    
    def get_serializer_context(self):
        """Extra context provided to the serializer class."""
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class MemoryBulkView(views.APIView):
    """
    API view for bulk memory operations.
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        Bulk create memories.
        
        Request body:
        {
            "memories": [
                {
                    "content": "User prefers dark theme",
                    "memory_type": "preference",
                    "importance_score": 0.8,
                    "personalness_score": 0.9,
                    "actionability_score": 0.7
                }
            ]
        }
        """
        memories_data = request.data.get('memories', [])
        
        if not memories_data:
            return Response(
                {"error": "memories array is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        created_memories = []
        errors = []
        
        for i, memory_data in enumerate(memories_data):
            try:
                memory_data['user'] = request.user
                
                # Generate vector_id
                import uuid
                memory_data['vector_id'] = f"mem_{uuid.uuid4().hex[:16]}"
                
                memory = Memory.objects.create(**memory_data)
                created_memories.append(memory)
                
            except Exception as e:
                errors.append({
                    'index': i,
                    'error': str(e),
                    'data': memory_data
                })
        
        # Serialize created memories
        serializer = MemorySerializer(created_memories, many=True)
        
        response_data = {
            'created_count': len(created_memories),
            'created_memories': serializer.data,
            'errors': errors
        }
        
        return Response(response_data, status=status.HTTP_201_CREATED)
    
    def delete(self, request):
        """
        Bulk delete memories.
        
        Request body:
        {
            "memory_ids": ["uuid1", "uuid2", "uuid3"]
        }
        """
        memory_ids = request.data.get('memory_ids', [])
        
        if not memory_ids:
            return Response(
                {"error": "memory_ids array is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Delete memories that belong to the user
        deleted_count = Memory.objects.filter(
            id__in=memory_ids,
            user=request.user
        ).delete()[0]
        
        return Response({
            'deleted_count': deleted_count,
            'requested_count': len(memory_ids)
        })