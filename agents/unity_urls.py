"""
Unity Integration URL aliases for frontend compatibility.

These URLs provide the exact endpoints documented in BACKEND_INTEGRATION_READINESS.md
while redirecting to the actual implementation in the agents app.
"""

from django.urls import path
from .views import (
    AvatarSettingsView, 
    AvatarOutfitView, 
    EnvironmentChangeView, 
    AnimationPlayView, 
    VoiceCommandView
)

app_name = 'unity'

urlpatterns = [
    # Unity Integration Endpoints (as documented in BACKEND_INTEGRATION_READINESS.md)
    path('avatar/change-outfit/', AvatarOutfitView.as_view(), name='change_outfit'),
    path('avatar/change-environment/', EnvironmentChangeView.as_view(), name='change_environment'),
    path('avatar/play-animation/', AnimationPlayView.as_view(), name='play_animation'),
    path('avatar/settings/', AvatarSettingsView.as_view(), name='avatar_settings'),
    path('voice-command/', VoiceCommandView.as_view(), name='voice_command'),
]
