#!/usr/bin/env python3
"""
Fitness Coach Agent - Specialized worker for fitness planning tasks.
Processes structured requests and returns actionable fitness plans.
"""
import logging
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class FitnessRequest(BaseModel):
    """Structured input for fitness coaching requests."""
    goal: Optional[str] = None
    experience_level: Optional[str] = None
    time_available: Optional[str] = None
    equipment: Optional[str] = None
    focus_areas: List[str] = Field(default_factory=list)
    constraints: List[str] = Field(default_factory=list)
    user_context: Dict[str, Any] = Field(default_factory=dict)


class FitnessPlan(BaseModel):
    """Structured output for fitness plans."""
    program_overview: Dict[str, Any]
    workout_schedule: Dict[str, Any]
    exercise_library: List[Dict[str, Any]]
    nutrition_guidelines: Dict[str, Any]
    progress_tracking: Dict[str, Any]
    safety_notes: List[str]


class FitnessCoachAgent:
    """
    Specialized agent for fitness coaching and workout planning.
    Focuses on creating actionable, safe, and effective fitness plans.
    """

    def __init__(self):
        self.agent_name = "Fitness Coach Agent"
        self.version = "2.0"
        self.domain = "fitness"

    def process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process fitness coaching request with structured analysis."""
        start_time = time.time()

        logger.info(f"💪 {self.agent_name} creating fitness plan")

        # Parse structured input
        fitness_request = self._parse_request(request_data)
        logger.info(f"🎯 Goal: {fitness_request.goal}")
        logger.info(f"⏱️ Time available: {fitness_request.time_available}")
        logger.info(f"🏋️ Equipment: {fitness_request.equipment}")

        # Create focused fitness plan
        fitness_plan = self._create_fitness_plan(fitness_request)

        # Apply conversation-based refinements
        if request_data.get('conversation_context'):
            fitness_plan = self._refine_with_context(fitness_plan, request_data['conversation_context'])

        processing_time = (time.time() - start_time) * 1000

        result = {
            'agent_name': self.agent_name,
            'domain': self.domain,
            'fitness_plan': fitness_plan.dict(),
            'processing_time_ms': processing_time,
            'timestamp': datetime.now().isoformat(),
            'confidence_score': self._calculate_confidence(fitness_request),
            'next_steps': self._suggest_next_steps(fitness_plan)
        }

        logger.info(f"✅ Fitness plan completed in {processing_time:.1f}ms")
        return result
    
    def refine_response(self, original_result: Dict[str, Any], refinement_context: Dict[str, Any]) -> Dict[str, Any]:
        """Refine fitness plan based on user feedback."""
        start_time = time.time()
        
        logger.info(f"🔄 {self.agent_name} refining response")
        
        follow_up_input = refinement_context.get('follow_up_input', '')
        semantic_gaps = refinement_context.get('semantic_gaps', [])
        
        # Analyze refinement needs
        refinement_needs = self._analyze_fitness_refinement_needs(
            original_result, follow_up_input, semantic_gaps
        )
        
        # Apply refinements
        refined_plan = self._apply_fitness_refinements(
            original_result['fitness_plan'], refinement_needs, follow_up_input
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        refined_result = {
            **original_result,
            'fitness_plan': refined_plan,
            'refinement_applied': refinement_needs,
            'refinement_processing_time_ms': processing_time,
            'refinement_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ Fitness plan refined in {processing_time:.1f}ms")
        return refined_result
    
    def _analyze_fitness_request(self, user_input: str) -> Dict[str, Any]:
        """Analyze the fitness request."""
        details = {
            'goal': 'general_fitness',
            'experience_level': 'beginner',
            'time_available': '30-45 minutes',
            'equipment': 'bodyweight',
            'focus_areas': [],
            'constraints': []
        }
        
        user_lower = user_input.lower()
        
        # Detect goals
        if 'weight loss' in user_lower or 'lose weight' in user_lower:
            details['goal'] = 'weight_loss'
        elif 'muscle' in user_lower or 'strength' in user_lower:
            details['goal'] = 'muscle_building'
        elif 'endurance' in user_lower or 'cardio' in user_lower:
            details['goal'] = 'endurance'
        elif 'tone' in user_lower or 'toning' in user_lower:
            details['goal'] = 'toning'
        
        # Detect experience level
        if 'beginner' in user_lower or 'new to' in user_lower:
            details['experience_level'] = 'beginner'
        elif 'intermediate' in user_lower:
            details['experience_level'] = 'intermediate'
        elif 'advanced' in user_lower or 'experienced' in user_lower:
            details['experience_level'] = 'advanced'
        
        # Detect equipment
        if 'gym' in user_lower:
            details['equipment'] = 'full_gym'
        elif 'home' in user_lower and 'equipment' in user_lower:
            details['equipment'] = 'home_equipment'
        elif 'no equipment' in user_lower or 'bodyweight' in user_lower:
            details['equipment'] = 'bodyweight'
        
        # Detect constraints
        if 'injury' in user_lower or 'hurt' in user_lower:
            details['constraints'].append('injury')
        if 'time' in user_lower and ('limited' in user_lower or 'busy' in user_lower):
            details['constraints'].append('time_limited')
        
        return details
    
    def _apply_memory_context(self, memories: List[Dict], fitness_details: Dict) -> Dict[str, Any]:
        """Apply memory context for personalized fitness planning."""
        personalization = {
            'fitness_history': [],
            'preferences': [],
            'limitations': [],
            'past_goals': []
        }
        
        for memory in memories:
            memory_text = memory.get('text', '').lower()
            
            if 'workout' in memory_text or 'exercise' in memory_text:
                personalization['fitness_history'].append(memory_text)
            
            if 'loves' in memory_text or 'enjoys' in memory_text:
                personalization['preferences'].append(memory_text)
            
            if 'injury' in memory_text or 'problem' in memory_text:
                personalization['limitations'].append(memory_text)
            
            if 'goal' in memory_text or 'wants to' in memory_text:
                personalization['past_goals'].append(memory_text)
        
        return personalization
    
    def _generate_fitness_plan(self, details: Dict, personalization: Dict) -> Dict[str, Any]:
        """Generate comprehensive fitness plan."""
        plan = {
            'program_overview': {
                'duration': '8 weeks',
                'frequency': '3-4 times per week',
                'goal': details['goal'],
                'difficulty': details['experience_level']
            },
            'weekly_schedule': {
                'week_1_2': {
                    'focus': 'Foundation building',
                    'workouts': [
                        {
                            'day': 'Monday',
                            'type': 'Full body strength',
                            'duration': '30 minutes',
                            'exercises': ['Push-ups', 'Squats', 'Planks', 'Lunges']
                        },
                        {
                            'day': 'Wednesday', 
                            'type': 'Cardio',
                            'duration': '25 minutes',
                            'exercises': ['Walking/Jogging', 'Jumping jacks', 'High knees']
                        },
                        {
                            'day': 'Friday',
                            'type': 'Strength + Core',
                            'duration': '35 minutes',
                            'exercises': ['Modified push-ups', 'Wall sits', 'Crunches']
                        }
                    ]
                }
            },
            'nutrition_guidelines': {
                'calories': 'Moderate deficit for weight loss' if details['goal'] == 'weight_loss' else 'Maintenance',
                'protein': '1.2-1.6g per kg body weight',
                'hydration': '8-10 glasses of water daily',
                'meal_timing': 'Eat within 2 hours post-workout'
            },
            'progress_tracking': {
                'measurements': ['Weight', 'Body fat %', 'Muscle measurements'],
                'performance': ['Reps completed', 'Duration', 'Intensity level'],
                'frequency': 'Weekly check-ins'
            }
        }
        
        # Adjust based on equipment
        if details['equipment'] == 'full_gym':
            plan['equipment_workouts'] = {
                'strength_training': ['Dumbbells', 'Barbells', 'Machines'],
                'cardio_options': ['Treadmill', 'Elliptical', 'Rowing machine']
            }
        
        # Adjust for constraints
        if 'time_limited' in details['constraints']:
            plan['quick_workouts'] = {
                'hiit_sessions': '15-20 minute high intensity workouts',
                'micro_workouts': '5-10 minute movement breaks'
            }
        
        return plan
    
    def _generate_refinement_suggestions(self) -> List[str]:
        """Generate suggestions for potential refinements."""
        return [
            "Ask about specific fitness goals and timeline",
            "Inquire about available equipment and space",
            "Check for injuries or physical limitations",
            "Verify time availability and schedule preferences",
            "Confirm nutrition and dietary considerations"
        ]
    
    def _analyze_fitness_refinement_needs(
        self, 
        original_result: Dict, 
        follow_up: str, 
        semantic_gaps: List
    ) -> Dict[str, Any]:
        """Analyze what aspects of the fitness plan need refinement."""
        needs = {}
        follow_up_lower = follow_up.lower()
        
        # Check for injury mentions
        if any(word in follow_up_lower for word in ['injury', 'hurt', 'pain', 'problem']):
            needs['injury_adaptation'] = 'User mentioned injury or physical limitation'
        
        # Check for time constraints
        if any(word in follow_up_lower for word in ['busy', 'time', 'quick', 'short']):
            needs['time_adjustment'] = 'User has time constraints'
        
        # Check for intensity changes
        if any(word in follow_up_lower for word in ['easier', 'harder', 'intense', 'gentle']):
            needs['intensity_adjustment'] = 'User requested intensity modification'
        
        # Check for equipment changes
        if any(word in follow_up_lower for word in ['equipment', 'gym', 'home', 'weights']):
            needs['equipment_adjustment'] = 'User specified equipment preferences'
        
        # Check for goal changes
        if any(word in follow_up_lower for word in ['actually', 'instead', 'change goal']):
            needs['goal_adjustment'] = 'User changed fitness goals'
        
        return needs
    
    def _apply_fitness_refinements(
        self, 
        original_plan: Dict, 
        refinement_needs: Dict, 
        follow_up: str
    ) -> Dict[str, Any]:
        """Apply specific refinements to the fitness plan."""
        refined_plan = original_plan.copy()
        
        # Apply injury adaptations
        if 'injury_adaptation' in refinement_needs:
            refined_plan['injury_modifications'] = {
                'low_impact_alternatives': [
                    'Swimming instead of running',
                    'Chair exercises for mobility issues',
                    'Resistance bands instead of weights'
                ],
                'recovery_focus': 'Emphasis on rehabilitation and gentle movement',
                'professional_recommendation': 'Consult with physical therapist'
            }
        
        # Apply time adjustments
        if 'time_adjustment' in refinement_needs:
            refined_plan['time_efficient_workouts'] = {
                'express_sessions': '15-minute HIIT workouts',
                'micro_workouts': '5-minute movement breaks throughout day',
                'compound_exercises': 'Multi-muscle group exercises for efficiency'
            }
        
        # Apply intensity adjustments
        if 'intensity_adjustment' in refinement_needs:
            if 'easier' in follow_up.lower():
                refined_plan['beginner_modifications'] = {
                    'reduced_intensity': 'Lower reps and longer rest periods',
                    'progression_plan': 'Gradual increase over 4-6 weeks',
                    'alternative_exercises': 'Modified versions of challenging moves'
                }
            elif 'harder' in follow_up.lower():
                refined_plan['advanced_progressions'] = {
                    'increased_intensity': 'Higher reps, shorter rest, added resistance',
                    'advanced_exercises': 'Plyometric and complex movements',
                    'challenge_workouts': 'Weekly high-intensity challenge sessions'
                }
        
        return refined_plan
