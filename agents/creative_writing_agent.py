#!/usr/bin/env python3
"""
Creative Writing Agent with semantic refinement capabilities.
Helps with stories and adapts to genre/style changes.
"""
import logging
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class CreativeWritingAgent:
    """
    Agent that assists with creative writing and adapts to style/genre changes.
    """
    
    def __init__(self):
        self.agent_name = "Creative Writing Agent"
        self.version = "1.0"
        self.capabilities = [
            "story_development",
            "character_creation",
            "plot_structuring",
            "style_adaptation",
            "genre_expertise"
        ]
        
    def process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process initial creative writing request."""
        start_time = time.time()
        
        logger.info(f"✍️ {self.agent_name} processing request")
        
        user_input = request_data.get('user_input', '')
        user_id = request_data.get('user_id', 'unknown')
        memories = request_data.get('memories', [])
        
        # Analyze writing request
        writing_details = self._analyze_writing_request(user_input)
        logger.info(f"📖 Writing analysis: {writing_details}")
        
        # Apply memory context
        personalization = self._apply_memory_context(memories, writing_details)
        
        # Generate writing assistance
        writing_plan = self._generate_writing_plan(writing_details, personalization)
        
        processing_time = (time.time() - start_time) * 1000
        
        result = {
            'agent_name': self.agent_name,
            'writing_plan': writing_plan,
            'personalization_used': personalization,
            'extracted_details': writing_details,
            'processing_time_ms': processing_time,
            'timestamp': datetime.now().isoformat(),
            'refinement_suggestions': self._generate_refinement_suggestions(),
            'user_id': user_id
        }
        
        logger.info(f"✅ Writing plan completed in {processing_time:.1f}ms")
        return result
    
    def refine_response(self, original_result: Dict[str, Any], refinement_context: Dict[str, Any]) -> Dict[str, Any]:
        """Refine writing assistance based on user feedback."""
        start_time = time.time()
        
        logger.info(f"🔄 {self.agent_name} refining response")
        
        follow_up_input = refinement_context.get('follow_up_input', '')
        semantic_gaps = refinement_context.get('semantic_gaps', [])
        
        # Analyze refinement needs
        refinement_needs = self._analyze_writing_refinement_needs(
            original_result, follow_up_input, semantic_gaps
        )
        
        # Apply refinements
        refined_plan = self._apply_writing_refinements(
            original_result['writing_plan'], refinement_needs, follow_up_input
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        refined_result = {
            **original_result,
            'writing_plan': refined_plan,
            'refinement_applied': refinement_needs,
            'refinement_processing_time_ms': processing_time,
            'refinement_timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ Writing plan refined in {processing_time:.1f}ms")
        return refined_result
    
    def _analyze_writing_request(self, user_input: str) -> Dict[str, Any]:
        """Analyze the creative writing request."""
        details = {
            'project_type': 'short_story',
            'genre': 'general_fiction',
            'length': 'short',
            'style': 'contemporary',
            'themes': [],
            'target_audience': 'general'
        }
        
        user_lower = user_input.lower()
        
        # Detect project type
        if 'novel' in user_lower:
            details['project_type'] = 'novel'
        elif 'screenplay' in user_lower or 'script' in user_lower:
            details['project_type'] = 'screenplay'
        elif 'poem' in user_lower or 'poetry' in user_lower:
            details['project_type'] = 'poetry'
        elif 'essay' in user_lower:
            details['project_type'] = 'essay'
        
        # Detect genre
        genres = ['fantasy', 'sci-fi', 'romance', 'mystery', 'thriller', 'horror', 'comedy', 'drama']
        for genre in genres:
            if genre in user_lower or genre.replace('-', ' ') in user_lower:
                details['genre'] = genre
                break
        
        # Detect length
        if 'short' in user_lower or 'brief' in user_lower:
            details['length'] = 'short'
        elif 'long' in user_lower or 'novel' in user_lower:
            details['length'] = 'long'
        elif 'medium' in user_lower:
            details['length'] = 'medium'
        
        # Detect themes
        themes = ['love', 'adventure', 'coming of age', 'redemption', 'betrayal', 'family', 'friendship']
        for theme in themes:
            if theme in user_lower:
                details['themes'].append(theme)
        
        return details
    
    def _apply_memory_context(self, memories: List[Dict], writing_details: Dict) -> Dict[str, Any]:
        """Apply memory context for personalized writing assistance."""
        personalization = {
            'writing_experience': 'beginner',
            'favorite_genres': [],
            'writing_goals': [],
            'past_projects': []
        }
        
        for memory in memories:
            memory_text = memory.get('text', '').lower()
            
            if 'published' in memory_text or 'author' in memory_text:
                personalization['writing_experience'] = 'experienced'
            elif 'writing for' in memory_text:
                personalization['writing_experience'] = 'intermediate'
            
            if 'loves' in memory_text and any(genre in memory_text for genre in ['fantasy', 'sci-fi', 'romance']):
                personalization['favorite_genres'].append(memory_text)
            
            if 'working on' in memory_text or 'project' in memory_text:
                personalization['past_projects'].append(memory_text)
        
        return personalization
    
    def _generate_writing_plan(self, details: Dict, personalization: Dict) -> Dict[str, Any]:
        """Generate comprehensive writing assistance plan."""
        plan = {
            'project_overview': {
                'type': details['project_type'],
                'genre': details['genre'],
                'estimated_length': self._get_length_estimate(details['length'], details['project_type']),
                'target_completion': '4-6 weeks'
            },
            'story_structure': {
                'act_1': {
                    'purpose': 'Setup and character introduction',
                    'key_elements': ['Hook', 'Inciting incident', 'Character goals'],
                    'length_percentage': '25%'
                },
                'act_2': {
                    'purpose': 'Conflict and development',
                    'key_elements': ['Rising action', 'Obstacles', 'Character growth'],
                    'length_percentage': '50%'
                },
                'act_3': {
                    'purpose': 'Resolution',
                    'key_elements': ['Climax', 'Falling action', 'Resolution'],
                    'length_percentage': '25%'
                }
            },
            'character_development': {
                'protagonist': {
                    'traits': 'Relatable, flawed, motivated',
                    'arc': 'Growth through overcoming obstacles',
                    'backstory': 'Relevant to current conflict'
                },
                'supporting_characters': {
                    'purpose': 'Advance plot and challenge protagonist',
                    'diversity': 'Varied backgrounds and motivations'
                }
            },
            'writing_techniques': {
                'pov': 'Third person limited (recommended for beginners)',
                'tense': 'Past tense',
                'style_notes': 'Show don\'t tell, active voice, varied sentence structure'
            },
            'daily_goals': {
                'word_count': '500-1000 words per day',
                'writing_time': '1-2 hours daily',
                'consistency': 'Write at the same time each day'
            }
        }
        
        # Adjust based on genre
        if details['genre'] == 'fantasy':
            plan['world_building'] = {
                'magic_system': 'Define rules and limitations',
                'setting': 'Create detailed maps and cultures',
                'mythology': 'Develop consistent lore and history'
            }
        elif details['genre'] == 'mystery':
            plan['plot_structure'] = {
                'clues': 'Plant evidence throughout story',
                'red_herrings': 'Mislead readers without cheating',
                'revelation': 'Satisfying and logical conclusion'
            }
        
        # Adjust for experience level
        if personalization['writing_experience'] == 'beginner':
            plan['beginner_tips'] = {
                'start_simple': 'Focus on one POV character',
                'outline_first': 'Plan major plot points before writing',
                'edit_later': 'Complete first draft before major revisions'
            }
        
        return plan
    
    def _get_length_estimate(self, length: str, project_type: str) -> str:
        """Get estimated word count based on length and type."""
        estimates = {
            'short_story': {'short': '1,000-3,000 words', 'medium': '3,000-7,500 words', 'long': '7,500-15,000 words'},
            'novel': {'short': '50,000-70,000 words', 'medium': '70,000-90,000 words', 'long': '90,000+ words'},
            'screenplay': {'short': '90-110 pages', 'medium': '110-130 pages', 'long': '130+ pages'}
        }
        return estimates.get(project_type, {}).get(length, '1,000-5,000 words')
    
    def _generate_refinement_suggestions(self) -> List[str]:
        """Generate suggestions for potential refinements."""
        return [
            "Ask about specific genre preferences and themes",
            "Inquire about target audience and publication goals",
            "Check for writing experience and skill level",
            "Verify project timeline and daily writing capacity",
            "Confirm story elements and character preferences"
        ]
    
    def _analyze_writing_refinement_needs(
        self, 
        original_result: Dict, 
        follow_up: str, 
        semantic_gaps: List
    ) -> Dict[str, Any]:
        """Analyze what aspects of the writing plan need refinement."""
        needs = {}
        follow_up_lower = follow_up.lower()
        
        # Check for genre changes
        genres = ['fantasy', 'sci-fi', 'romance', 'mystery', 'thriller', 'horror']
        for genre in genres:
            if genre in follow_up_lower:
                needs['genre_change'] = f'User specified {genre} genre'
                break
        
        # Check for style changes
        if any(word in follow_up_lower for word in ['style', 'tone', 'voice', 'perspective']):
            needs['style_adjustment'] = 'User requested style modifications'
        
        # Check for length changes
        if any(word in follow_up_lower for word in ['shorter', 'longer', 'brief', 'extended']):
            needs['length_adjustment'] = 'User changed length requirements'
        
        # Check for character focus
        if any(word in follow_up_lower for word in ['character', 'protagonist', 'villain']):
            needs['character_focus'] = 'User wants character development focus'
        
        # Check for plot changes
        if any(word in follow_up_lower for word in ['plot', 'story', 'twist', 'ending']):
            needs['plot_adjustment'] = 'User requested plot modifications'
        
        return needs
    
    def _apply_writing_refinements(
        self, 
        original_plan: Dict, 
        refinement_needs: Dict, 
        follow_up: str
    ) -> Dict[str, Any]:
        """Apply specific refinements to the writing plan."""
        refined_plan = original_plan.copy()
        
        # Apply genre changes
        if 'genre_change' in refinement_needs:
            if 'fantasy' in follow_up.lower():
                refined_plan['fantasy_elements'] = {
                    'magic_system': 'Develop consistent magical rules',
                    'world_building': 'Create detailed fantasy setting',
                    'creatures': 'Design unique fantasy beings'
                }
            elif 'mystery' in follow_up.lower():
                refined_plan['mystery_structure'] = {
                    'clue_placement': 'Strategic evidence distribution',
                    'suspect_development': 'Multiple viable suspects',
                    'resolution': 'Fair and satisfying reveal'
                }
        
        # Apply style adjustments
        if 'style_adjustment' in refinement_needs:
            refined_plan['style_guide'] = {
                'narrative_voice': 'Adjust POV and tense as requested',
                'tone_consistency': 'Maintain appropriate mood throughout',
                'pacing': 'Balance action, dialogue, and description'
            }
        
        # Apply character focus
        if 'character_focus' in refinement_needs:
            refined_plan['enhanced_character_work'] = {
                'character_sheets': 'Detailed profiles for main characters',
                'dialogue_voice': 'Unique speech patterns for each character',
                'motivation_mapping': 'Clear goals and conflicts for each character'
            }
        
        return refined_plan
