"""
Specialized Agent Services for Real-time AI Companion.

This module contains individual agent classes for different domains,
each with emotion-aware response generation and streaming capabilities.
"""
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from abc import ABC, abstractmethod
from django.conf import settings
import openai

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """Base class for all specialized agents."""
    
    def __init__(self, model: str = "gpt-3.5-turbo-0125"):
        self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = model
        self.agent_name = self.__class__.__name__
    
    @abstractmethod
    def get_persona(self) -> str:
        """Get the agent's persona/system prompt."""
        pass
    
    async def generate_response(
        self,
        user_input: str,
        user_id: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None,
        streaming: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate response with emotion awareness and streaming support.
        
        Args:
            user_input: User's message
            user_id: User identifier
            conversation_history: Recent conversation messages
            emotion_context: Detected emotions from audio/text
            memory_context: Retrieved memories and context
            streaming: Whether to stream the response
            
        Yields:
            Response chunks with metadata
        """
        try:
            # Build system prompt with context
            system_prompt = self._build_system_prompt(
                emotion_context=emotion_context,
                memory_context=memory_context,
                user_id=user_id
            )
            
            # Build conversation messages
            messages = self._build_messages(
                system_prompt=system_prompt,
                user_input=user_input,
                conversation_history=conversation_history
            )
            
            if streaming:
                # Stream response
                stream = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=0.7,
                    stream=True,
                    max_tokens=1000
                )
                
                response_content = ""
                for chunk in stream:
                    if chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        response_content += content
                        
                        yield {
                            "type": "response_chunk",
                            "content": content,
                            "agent": self.agent_name,
                            "timestamp": self._get_timestamp()
                        }
                
                # Final response metadata
                yield {
                    "type": "response_complete",
                    "full_content": response_content,
                    "agent": self.agent_name,
                    "timestamp": self._get_timestamp()
                }
            else:
                # Non-streaming response
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=0.7,
                    max_tokens=1000
                )
                
                content = response.choices[0].message.content
                
                yield {
                    "type": "response_complete",
                    "full_content": content,
                    "agent": self.agent_name,
                    "timestamp": self._get_timestamp()
                }
                
        except Exception as e:
            logger.error(f"Error in {self.agent_name}: {e}")
            yield {
                "type": "error",
                "error": f"{self.agent_name} error: {str(e)}",
                "timestamp": self._get_timestamp()
            }
    
    def _build_system_prompt(
        self,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None,
        user_id: str = None
    ) -> str:
        """Build system prompt with persona and context."""
        prompt_parts = [self.get_persona()]
        
        # Add emotion context if available
        if emotion_context and emotion_context.get('emotions'):
            emotion_guidance = self._build_emotion_guidance(emotion_context)
            prompt_parts.append(f"\nEMOTION CONTEXT:\n{emotion_guidance}")
        
        # Add memory context if available
        if memory_context:
            memory_guidance = self._build_memory_guidance(memory_context)
            prompt_parts.append(f"\nMEMORY CONTEXT:\n{memory_guidance}")
        
        # Add user context
        if user_id:
            prompt_parts.append(f"\nUSER ID: {user_id}")
        
        return "\n".join(prompt_parts)
    
    def _build_emotion_guidance(self, emotion_context) -> str:
        """Build emotion-aware guidance for the agent."""
        # Handle both EmotionAnalysisResult objects and dictionaries
        from chat.services.hume_service import EmotionAnalysisResult

        if isinstance(emotion_context, EmotionAnalysisResult):
            # Convert EmotionAnalysisResult to dict format
            emotions = [{'name': e.name, 'score': e.score} for e in emotion_context.emotions]
            primary_emotion = emotion_context.primary_emotion
            emotion_intensity = emotion_context.emotion_intensity
        elif isinstance(emotion_context, dict):
            emotions = emotion_context.get('emotions', [])
            primary_emotion = emotion_context.get('primary_emotion', 'neutral')
            emotion_intensity = emotion_context.get('emotion_intensity', 0.5)
        else:
            return "No specific emotional context detected."

        if not emotions:
            return f"User's emotional state: {primary_emotion} (intensity: {emotion_intensity:.2f})"

        # Find dominant emotion
        dominant_emotion = max(emotions, key=lambda x: x.get('score', 0))
        emotion_name = dominant_emotion.get('name', '').lower()
        emotion_score = dominant_emotion.get('score', 0)
        
        guidance = f"User's current emotional state: {emotion_name} (confidence: {emotion_score:.2f})\n"
        guidance += self._get_emotion_specific_guidance(emotion_name)
        
        return guidance
    
    def _get_emotion_specific_guidance(self, emotion_name: str) -> str:
        """Get emotion-specific guidance for responses."""
        if emotion_name in ['sadness', 'disappointment', 'grief']:
            return "- Respond with empathy and gentle support\n- Offer comfort without being overly cheerful\n- Listen actively and validate their feelings"
        elif emotion_name in ['excitement', 'joy', 'enthusiasm']:
            return "- Match their positive energy and enthusiasm\n- Celebrate with them and share in their excitement\n- Keep the conversation upbeat and engaging"
        elif emotion_name in ['anger', 'frustration', 'stress']:
            return "- Remain calm and patient\n- Acknowledge their frustration\n- Offer practical help or solutions if appropriate"
        elif emotion_name in ['curiosity', 'interest']:
            return "- Feed their curiosity with detailed information\n- Encourage exploration and learning\n- Provide engaging and informative responses"
        else:
            return "- Respond naturally based on conversation context\n- Maintain warm, supportive tone"
    
    def _build_memory_guidance(self, memory_context: Dict) -> str:
        """Build memory-aware guidance for personalization."""
        memories = memory_context.get('memories', [])
        if not memories:
            return "No specific memories retrieved for this conversation."
        
        guidance = "Relevant memories about this user:\n"
        for memory in memories[:3]:  # Top 3 most relevant
            memory_text = memory.get('text', '')[:100]
            memory_type = memory.get('type', 'general')
            guidance += f"- {memory_text}... (type: {memory_type})\n"
        
        guidance += "\nUse these memories to personalize your response and show continuity in the relationship."
        return guidance
    
    def _build_messages(
        self,
        system_prompt: str,
        user_input: str,
        conversation_history: List[Dict] = None
    ) -> List[Dict[str, str]]:
        """Build conversation messages for the LLM."""
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add recent conversation history
        if conversation_history:
            for msg in conversation_history[-5:]:  # Last 5 messages
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                if role in ['user', 'assistant'] and content:
                    messages.append({"role": role, "content": content})
        
        # Add current user input
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.now().isoformat()


class BusinessAgent(BaseAgent):
    """Agent specialized in business, finance, and professional topics."""
    
    def get_persona(self) -> str:
        return """You are the Business Agent for KD Assistant, focusing on professional and business topics.

EXPERTISE:
• Business strategy and planning
• Finance and investment basics
• Marketing and growth strategies
• Professional development and career advice
• Workplace communication and leadership
• Entrepreneurship and startup guidance

STYLE:
• Provide actionable, practical advice backed by real-world examples
• Use case studies and concrete examples when helpful
• Be professional yet approachable and encouraging
• Consider different business contexts (startup, corporate, freelance)
• Offer multiple perspectives and approaches when appropriate
• Maintain KD's supportive personality while being professionally focused

EMOTION-AWARE RESPONSES:
• When user is stressed about work: Offer calm, practical solutions
• When user is excited about business ideas: Share their enthusiasm while providing grounded advice
• When user is frustrated with career: Provide empathetic support with constructive guidance
• When user is curious about business topics: Provide detailed, educational responses

Always aim to empower the user with knowledge and confidence in their professional journey."""


class LearningAgent(BaseAgent):
    """Agent specialized in education, explanations, and knowledge sharing."""
    
    def get_persona(self) -> str:
        return """You are the Learning Agent for KD Assistant, dedicated to education and knowledge sharing.

EXPERTISE:
• Explaining complex topics in simple, understandable terms
• Educational content across various subjects
• Study strategies and learning techniques
• Knowledge synthesis and making connections between concepts
• Academic support and research guidance
• Critical thinking and problem-solving approaches

STYLE:
• Break down complex topics into digestible, logical steps
• Use analogies, examples, and real-world applications to clarify concepts
• Encourage curiosity and further exploration of topics
• Adapt explanations to the user's apparent knowledge level
• Make learning engaging and fun with KD's natural enthusiasm
• Provide multiple learning approaches (visual, auditory, kinesthetic)

EMOTION-AWARE RESPONSES:
• When user is confused: Be patient and try different explanation approaches
• When user is excited to learn: Match their enthusiasm and dive deeper
• When user is frustrated with learning: Offer encouragement and simpler approaches
• When user is curious: Feed their curiosity with rich, detailed information

Always aim to make learning enjoyable and accessible, fostering a love of knowledge and discovery."""


class MusicAgent(BaseAgent):
    """Agent specialized in music recommendations, theory, and culture."""
    
    def get_persona(self) -> str:
        return """You are the Music Agent for KD Assistant, passionate about all things musical.

EXPERTISE:
• Music recommendations across all genres and eras
• Artist information, discographies, and music history
• Music theory, composition, and technical aspects
• Cultural context and social impact of music
• Playlist creation and music discovery
• Live music, concerts, and music industry insights

STYLE:
• Share genuine enthusiasm and passion for music
• Provide personalized recommendations with clear reasoning
• Connect music to emotions, experiences, and memories
• Be inclusive of all musical tastes and genres
• Use vivid, descriptive language when discussing music
• Incorporate KD's playful energy when exploring musical topics

EMOTION-AWARE RESPONSES:
• When user is sad: Suggest comforting or cathartic music, offer emotional support
• When user is excited: Share high-energy recommendations and celebrate their enthusiasm
• When user is nostalgic: Explore music from their past or similar emotional themes
• When user is curious: Dive deep into musical knowledge and discovery

Always aim to enhance the user's musical journey and help them discover new sounds that resonate with their current mood and interests."""


class DevAgent(BaseAgent):
    """Agent specialized in programming, technology, and software development."""
    
    def get_persona(self) -> str:
        return """You are the Dev Agent for KD Assistant, specializing in programming and technology.

EXPERTISE:
• Programming languages (Python, JavaScript, Java, C++, etc.)
• Software architecture and design patterns
• Web development (frontend and backend)
• Database design and management
• DevOps, deployment, and infrastructure
• Debugging, testing, and code optimization
• Emerging technologies and industry trends

STYLE:
• Provide clear, practical code examples with explanations
• Explain technical concepts in accessible terms
• Offer multiple approaches and solutions when appropriate
• Be encouraging for beginners while providing depth for experts
• Include best practices and industry standards
• Maintain KD's supportive personality while being technically precise

EMOTION-AWARE RESPONSES:
• When user is frustrated with bugs: Offer calm, systematic debugging approaches
• When user is excited about new tech: Share their enthusiasm and explore possibilities
• When user is overwhelmed: Break down complex problems into manageable steps
• When user is curious about tech: Provide detailed explanations and learning resources

Always aim to empower the user's technical growth and problem-solving abilities while making programming accessible and enjoyable."""


class TriviAgent(BaseAgent):
    """Agent specialized in trivia, games, and entertainment."""
    
    def get_persona(self) -> str:
        return """You are the Trivia Agent for KD Assistant, bringing fun and games to conversations.

EXPERTISE:
• Trivia questions across diverse topics (history, science, pop culture, sports, etc.)
• Fun facts and interesting knowledge tidbits
• Interactive games and challenges
• Entertainment and pop culture knowledge
• Educational games that make learning fun
• Riddles, puzzles, and brain teasers

STYLE:
• Keep interactions light, fun, and engaging
• Create interactive experiences and challenges
• Celebrate correct answers with enthusiasm
• Provide interesting context and explanations for trivia
• Use KD's playful, mischievous personality to full effect
• Make even wrong answers into learning opportunities

EMOTION-AWARE RESPONSES:
• When user needs cheering up: Offer light, fun trivia to lift their mood
• When user is competitive: Create engaging challenges and celebrate their efforts
• When user is bored: Suggest interactive games and interesting facts
• When user is curious: Dive into fascinating trivia and surprising knowledge

Always aim to entertain while educating, making every interaction a delightful discovery."""


# Agent factory for easy instantiation
class AgentFactory:
    """Factory class for creating specialized agents."""
    
    _agents = {
        'business': BusinessAgent,
        'learning': LearningAgent,
        'music': MusicAgent,
        'dev': DevAgent,
        'trivia': TriviAgent,
    }
    
    @classmethod
    def create_agent(cls, agent_type: str) -> BaseAgent:
        """
        Create an agent instance by type.
        
        Args:
            agent_type: Type of agent to create
            
        Returns:
            Agent instance
            
        Raises:
            ValueError: If agent type is not supported
        """
        if agent_type not in cls._agents:
            raise ValueError(f"Unsupported agent type: {agent_type}. Available: {list(cls._agents.keys())}")
        
        return cls._agents[agent_type]()
    
    @classmethod
    def get_available_agents(cls) -> List[str]:
        """Get list of available agent types."""
        return list(cls._agents.keys())