"""
Memory tools for LangGraph integration in Django.
Adapted from kd_assistant_langgraph implementation.

This module contains LangChain tools for memory management that can be used
within the LangGraph agent workflow.
"""
import logging
from typing import Type, Optional, List, Dict, Any

# Pydantic and Langchain Core components
from langchain_core.tools import BaseTool

try:
    from pydantic.v1 import BaseModel, Field
except ImportError:
    from pydantic import BaseModel, Field  # Fallback for Pydantic v2

# Project-specific memory components
from .memory_manager import MemoryManager
from .salience_scorer import MemorySalienceScorer

logger = logging.getLogger(__name__)

# --- Configuration for SaveMemoryTool ---
DEFAULT_SALIENCE_THRESHOLD = 0.55
DEFAULT_IMPORTANCE_WEIGHT = 0.5
DEFAULT_PERSONALNESS_WEIGHT = 0.4
DEFAULT_ACTIONABILITY_WEIGHT = 0.1


# --- Input Schemas for Tools ---
class SaveMemoryToolSchema(BaseModel):
    text_to_save: str = Field(
        description="The actual piece of text, fact, or note to be saved."
    )
    memory_type: str = Field(
        description="The type of memory to save (e.g., 'semantic_profile', 'episodic_summary', 'explicit_memory', 'general_knowledge', 'task_related')."
    )
    user_id: Optional[str] = Field(
        None, description="The unique identifier for the user, if applicable."
    )
    user_context_for_scoring: Optional[str] = Field(
        None,
        description="Optional broader context about the user or conversation to help the salience scorer make a better judgment.",
    )
    override_importance_score: Optional[float] = Field(
        None,
        description="(Optional) If LLM pre-assessed, provide importance score (0.0-1.0).",
    )
    override_personalness_score: Optional[float] = Field(
        None,
        description="(Optional) If LLM pre-assessed, provide personalness score (0.0-1.0).",
    )
    override_actionability_score: Optional[float] = Field(
        None,
        description="(Optional) If LLM pre-assessed, provide actionability score (0.0-1.0).",
    )
    additional_metadata: Optional[Dict[str, Any]] = Field(
        None, description="Any other relevant metadata to store with the memory."
    )


class QueryMemoryToolSchema(BaseModel):
    query: str = Field(
        description="The natural language query to search for relevant memories."
    )
    memory_types: Optional[List[str]] = Field(
        None,
        description="(Optional) A list of memory types to specifically search within (e.g., ['semantic_profile', 'episodic_summary']). Searches all if None.",
    )
    user_id: Optional[str] = Field(
        None,
        description="The unique identifier for the user, to retrieve their specific memories.",
    )
    min_importance: Optional[float] = Field(
        None,
        description="(Optional) Minimum importance score (0.0-1.0) for retrieved memories.",
    )
    min_personalness: Optional[float] = Field(
        None,
        description="(Optional) Minimum personalness score (0.0-1.0) for retrieved memories.",
    )
    min_actionability: Optional[float] = Field(
        None,
        description="(Optional) Minimum actionability score (0.0-1.0) for retrieved memories.",
    )
    top_k: int = Field(
        3, description="The maximum number of relevant memories to retrieve."
    )


# --- Tool Implementations ---


class SaveMemoryTool(BaseTool):
    name: str = "save_memory"
    description: str = (
        "Use this tool to save a piece of information, a fact, a note, or a summary to long-term memory. "
        "The information will be scored for salience (importance, personalness, actionability) unless overridden, "
        "and will only be saved if it meets a relevance threshold. "
        "Provide the text to save, its type, and optionally user ID and context for better scoring."
    )
    args_schema: Type[BaseModel] = SaveMemoryToolSchema
    memory_manager: MemoryManager
    salience_scorer: MemorySalienceScorer
    salience_threshold: float = DEFAULT_SALIENCE_THRESHOLD
    importance_weight: float = DEFAULT_IMPORTANCE_WEIGHT
    personalness_weight: float = DEFAULT_PERSONALNESS_WEIGHT
    actionability_weight: float = DEFAULT_ACTIONABILITY_WEIGHT

    def _run(
        self,
        text_to_save: str,
        memory_type: str,
        user_id: Optional[str] = None,
        user_context_for_scoring: Optional[str] = None,
        override_importance_score: Optional[float] = None,
        override_personalness_score: Optional[float] = None,
        override_actionability_score: Optional[float] = None,
        additional_metadata: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> str:
        logger.info(
            f"SaveMemoryTool invoked for text: '{text_to_save[:50]}...' of type '{memory_type}'"
        )

        if not text_to_save.strip():
            return "Error: Cannot save empty text to memory."

        if memory_type not in self.memory_manager.memory_types:
            valid_types = list(self.memory_manager.memory_types.keys())
            return f"Error: Invalid memory_type '{memory_type}'. Valid types are: {valid_types}"

        imp_score, per_score, act_score = None, None, None
        reasoning = "Scores provided by LLM or defaults used."

        if (
            override_importance_score is not None
            and override_personalness_score is not None
            and override_actionability_score is not None
        ):
            imp_score = override_importance_score
            per_score = override_personalness_score
            act_score = override_actionability_score
            logger.info(
                f"Using overridden salience scores: Imp={imp_score}, Per={per_score}, Act={act_score}"
            )
        else:
            logger.info("No override scores provided, invoking MemorySalienceScorer.")
            salience_result = self.salience_scorer.score_text(
                text=text_to_save, user_context=user_context_for_scoring
            )
            if salience_result:
                imp_score = salience_result.get("importance_score")
                per_score = salience_result.get("personalness_score")
                act_score = salience_result.get("actionability_score")
                reasoning = salience_result.get("reasoning", "Scoring successful.")
                logger.info(
                    f"Scores from SalienceScorer: Imp={imp_score}, Per={per_score}, Act={act_score}. Reasoning: {reasoning}"
                )
            else:
                logger.warning(
                    "MemorySalienceScorer failed to return scores. Using default low scores for safety."
                )
                imp_score, per_score, act_score = 0.1, 0.1, 0.1
                reasoning = "Salience scoring failed; using default low scores."

        if None in [imp_score, per_score, act_score]:
            logger.error(
                "Salience scores are incomplete even after scoring/override. Cannot proceed."
            )
            return "Error: Salience scores could not be determined. Memory not saved."

        weighted_score = (
            self.importance_weight * imp_score
            + self.personalness_weight * per_score
            + self.actionability_weight * act_score
        )
        logger.info(f"Calculated weighted salience score: {weighted_score:.2f}")

        if weighted_score >= self.salience_threshold:
            memory_id = self.memory_manager.store_memory(
                text=text_to_save,
                memory_type=memory_type,
                importance_score=imp_score,
                personalness_score=per_score,
                actionability_score=act_score,
                user_id=user_id,
            )
            if memory_id:
                return f"Successfully saved memory of type '{memory_type}' with ID {memory_id[:8]}... Weighted score: {weighted_score:.2f}. (Reasoning: {reasoning})"
            else:
                return f"Error: Failed to save memory to store despite meeting threshold. Weighted score: {weighted_score:.2f}."
        else:
            return f"Memory not saved. Weighted salience score {weighted_score:.2f} is below threshold {self.salience_threshold}. (Reasoning: {reasoning})"


class QueryMemoryTool(BaseTool):
    name: str = "query_memory"
    description: str = (
        "Use this tool to recall/retrieve relevant information, facts, notes, or past conversation summaries from long-term memory. "
        "Provide a natural language query. You can optionally filter by memory types, user ID, and minimum salience scores."
    )
    args_schema: Type[BaseModel] = QueryMemoryToolSchema
    memory_manager: MemoryManager

    def _run(
        self,
        query: str,
        memory_types: Optional[List[str]] = None,
        user_id: Optional[str] = None,
        min_importance: Optional[float] = None,
        min_personalness: Optional[float] = None,
        min_actionability: Optional[float] = None,
        top_k: int = 3,
        **kwargs,
    ) -> str:
        logger.info(
            f"QueryMemoryTool invoked with query: '{query[:50]}...', types: {memory_types}, user: {user_id}"
        )

        if not query.strip():
            return "Error: Cannot query memory with empty text."

        # Use the memory manager's search functionality
        try:
            results = self.memory_manager.search_memories(
                query=query,
                user_id=user_id,
                k=top_k,
                min_importance=min_importance or 0.1,
            )

            if not results:
                # Fallback to get all user memories if no search results
                if user_id:
                    # Use search_memories with empty query to get all memories for user
                    results = self.memory_manager.search_memories(
                        query="", user_id=user_id, k=top_k, min_importance=0.0
                    )

            if not results:
                return "No relevant memories found for the query."

            # Format the results for display
            formatted_memories = []
            for i, mem in enumerate(results):
                text = mem.get("text", "N/A")
                meta = mem.get("metadata", {})
                mem_type = meta.get("memory_type", "unknown")
                
                formatted_memories.append(f"{i+1}. {mem_type}: {text}")

            return "\n".join(formatted_memories)

        except Exception as e:
            logger.error(f"Error in QueryMemoryTool: {e}")
            return f"Error retrieving memories: {str(e)}"