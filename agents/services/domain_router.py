"""
Domain Router for KD Assistant Django implementation.
Adapted from kd_assistant_langgraph for Django integration.

This module provides intent classification and routing to specialized agents based on the user query.
"""
import logging
from typing import Dict, Any, List, Literal, Optional
from enum import Enum
from langchain_openai import ChatOpenAI
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.messages import AIMessage, HumanMessage, BaseMessage
from django.conf import settings

from .agent_state import AgentState

logger = logging.getLogger(__name__)


class Domain(str, Enum):
    """Domains that can be handled by specialized agents."""

    GENERAL = "general"  # Default orchestrator domain
    DEV = "dev"  # Development, coding, technical questions
    WEB = "web"  # Web searches, current events, factual information
    BUSINESS = "business"  # Business, finance, strategy questions
    LEARNING = "learning"  # Educational content, explanations, knowledge
    MUSIC = "music"  # Music-related questions, recommendations
    TRIVIA = "trivia"  # Trivia, games, entertainment
    TRAVEL = "travel"  # Travel planning, destinations, itineraries
    CODE = "code"  # Code review, analysis, optimization
    FITNESS = "fitness"  # Fitness planning, workouts, nutrition
    CREATIVE = "creative"  # Creative writing, art, design


# The main domain router prompt for intent classification
DOMAIN_ROUTER_PROMPT = """You are the Domain Router for KD Assistant, responsible for analyzing user queries and determining which specialized agent should handle them.

Based on the user's message and conversation history, classify this query into ONE of the following domains:

1. GENERAL: Default for conversation, chit-chat, personal questions, or anything that doesn't clearly fit other domains.
2. TRAVEL: Travel planning, destinations, itineraries, accommodations, budget planning, safety advice.
3. CODE: Code review, analysis, optimization, debugging, best practices, security analysis.
4. FITNESS: Fitness planning, workouts, nutrition, exercise routines, health goals.
5. CREATIVE: Creative writing, art, design, brainstorming, artistic projects.
6. DEV: Programming, coding, technical questions, software development, debugging.
7. WEB: Current events, web searches, factual information requiring internet access.
8. BUSINESS: Business strategy, finance, marketing, entrepreneurship.
9. LEARNING: Educational content, explanations, knowledge questions.
10. MUSIC: Music recommendations, artist information, music theory.
11. TRIVIA: Game-related questions, entertainment trivia, quiz questions.

Conversation History:
{history}

User's current query: {query}

Instructions:
- Respond with a single domain name ONLY (e.g., "GENERAL", "DEV", etc.)
- Pick the MOST relevant domain, even if multiple could apply
- For ambiguous cases, pick the domain that would provide the most helpful response
- For general conversation, personal questions, or small talk, use GENERAL
"""


def create_domain_router_chain():
    """Creates a classification chain to route queries to specialized domains."""
    llm = ChatOpenAI(
        model="gpt-3.5-turbo-0125", 
        temperature=0,
        openai_api_key=settings.OPENAI_API_KEY
    )
    prompt = ChatPromptTemplate.from_template(DOMAIN_ROUTER_PROMPT)
    chain = prompt | llm
    return chain


async def classify_domain(query: str, history: List[BaseMessage]) -> Domain:
    """
    Classifies the user query into a domain using the router chain.

    Args:
        query: The user's current query
        history: Conversation history

    Returns:
        Domain enum value
    """
    logger.info(f"Classifying domain for query: '{query[:50]}...'")

    # Format the conversation history for the prompt
    formatted_history = "\n".join(
        [
            f"{msg.type}: {msg.content[:100]}..."
            if len(msg.content) > 100
            else f"{msg.type}: {msg.content}"
            for msg in history[-5:]
            if hasattr(msg, "type")  # Only include most recent messages with a type attribute
        ]
    )

    router_chain = create_domain_router_chain()

    try:
        # Get classification from LLM
        response = await router_chain.ainvoke(
            {"query": query, "history": formatted_history}
        )

        # Parse the response content to get the domain
        domain_str = response.content.strip().upper()

        # Map the domain string to Domain enum
        domain_map = {
            "GENERAL": Domain.GENERAL,
            "TRAVEL": Domain.TRAVEL,
            "CODE": Domain.CODE,
            "FITNESS": Domain.FITNESS,
            "CREATIVE": Domain.CREATIVE,
            "DEV": Domain.DEV,
            "WEB": Domain.WEB,
            "BUSINESS": Domain.BUSINESS,
            "LEARNING": Domain.LEARNING,
            "MUSIC": Domain.MUSIC,
            "TRIVIA": Domain.TRIVIA,
        }

        classified_domain = domain_map.get(domain_str, Domain.GENERAL)
        logger.info(f"Query classified as domain: {classified_domain}")
        return classified_domain

    except Exception as e:
        logger.error(f"Error classifying domain: {e}")
        return Domain.GENERAL  # Default to general domain on error


def domain_router_node(state: AgentState) -> AgentState:
    """
    LangGraph node that routes to specialized agents based on the query domain.
    Adds the classified domain to the state.

    Args:
        state: Current agent state

    Returns:
        Updated state with domain classification
    """
    logger.info("---DOMAIN ROUTER NODE---")

    if not state.get("messages") or not state.get("current_question"):
        logger.warning("Domain router: Missing messages or current_question in state")
        state["domain"] = Domain.GENERAL
        return state

    # Get the query and user info from state
    query = state.get("current_question", "")
    user_id = state.get("user_id", "unknown")

    logger.info(f"🔀 DETAILED ROUTING ANALYSIS:")
    logger.info(f"   📝 Query: '{query[:100]}{'...' if len(query) > 100 else ''}'")
    logger.info(f"   👤 User ID: {user_id}")
    logger.info(f"   🔍 Classification Method: Keyword matching")

    # For now, use a simple synchronous classification to avoid async issues
    # TODO: Implement proper async handling or use sync classification
    try:
        # Simple keyword-based classification for now
        query_lower = query.lower()
        matched_keywords = []

        if any(word in query_lower for word in ['code', 'programming', 'python', 'javascript', 'bug', 'debug', 'software', 'development', 'coding']):
            domain = Domain.DEV
            matched_keywords = [word for word in ['code', 'programming', 'python', 'javascript', 'bug', 'debug', 'software', 'development', 'coding'] if word in query_lower]
        elif any(word in query_lower for word in ['music', 'song', 'album', 'artist', 'jazz', 'rock', 'classical', 'playlist', 'spotify']):
            domain = Domain.MUSIC
            matched_keywords = [word for word in ['music', 'song', 'album', 'artist', 'jazz', 'rock', 'classical', 'playlist', 'spotify'] if word in query_lower]
        elif any(word in query_lower for word in ['business', 'finance', 'marketing', 'strategy', 'startup', 'entrepreneur', 'investment', 'revenue']):
            domain = Domain.BUSINESS
            matched_keywords = [word for word in ['business', 'finance', 'marketing', 'strategy', 'startup', 'entrepreneur', 'investment', 'revenue'] if word in query_lower]
        elif any(word in query_lower for word in ['learn', 'education', 'explain', 'teach', 'study', 'tutorial', 'lesson', 'course']):
            domain = Domain.LEARNING
            matched_keywords = [word for word in ['learn', 'education', 'explain', 'teach', 'study', 'tutorial', 'lesson', 'course'] if word in query_lower]
        elif any(word in query_lower for word in ['trivia', 'quiz', 'game', 'fun', 'question', 'riddle', 'puzzle']):
            domain = Domain.TRIVIA
            matched_keywords = [word for word in ['trivia', 'quiz', 'game', 'fun', 'question', 'riddle', 'puzzle'] if word in query_lower]
        else:
            domain = Domain.GENERAL
            matched_keywords = ["general conversation"]

        logger.info(f"   🎯 Classified Domain: {domain}")
        logger.info(f"   🔑 Matched Keywords: {matched_keywords}")

        # Log domain-specific routing details
        if domain == Domain.MUSIC:
            logger.info(f"   🎵 Music domain detected - routing to music specialists")
        elif domain == Domain.DEV:
            logger.info(f"   💻 Development domain detected - routing to coding specialists")
        elif domain == Domain.BUSINESS:
            logger.info(f"   💼 Business domain detected - routing to business specialists")
        elif domain == Domain.LEARNING:
            logger.info(f"   📚 Learning domain detected - routing to educational specialists")
        elif domain == Domain.TRIVIA:
            logger.info(f"   🎮 Trivia domain detected - routing to entertainment specialists")
        elif domain == Domain.GENERAL:
            logger.info(f"   💬 General domain detected - routing to main orchestrator")
        else:
            logger.info(f"   ❓ Unknown domain detected - defaulting to general routing")

        logger.info(f"Query classified as domain: {domain} (using keyword matching)")
    except Exception as e:
        logger.error(f"❌ Error in domain classification: {e}")
        domain = Domain.GENERAL
        logger.info(f"   🔄 Fallback: Using GENERAL domain due to classification error")

    # Add the classified domain to state
    state["domain"] = domain

    logger.info(f"✅ Domain router completed: {domain}")
    return state