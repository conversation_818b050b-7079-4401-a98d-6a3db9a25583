#!/usr/bin/env python3
"""
Semantic Refinement Analyzer - Critic Component for Actor-Critic Architecture
Uses semantic understanding and conversation analysis to detect refinement needs.
"""
import logging
import json
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import re
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ConversationTurn:
    """Represents a single turn in the conversation."""
    speaker: str  # 'user' or 'assistant'
    content: str
    timestamp: datetime
    metadata: Dict[str, Any] = None


@dataclass
class SemanticGap:
    """Represents a semantic gap between conversation and agent result."""
    gap_type: str  # 'missing_requirement', 'contradictory_preference', 'scope_expansion', etc.
    description: str
    confidence: float  # 0.0 to 1.0
    conversation_evidence: List[str]  # Quotes from conversation supporting this gap
    suggested_refinement: str


class SemanticRefinementAnalyzer:
    """
    Critic component that analyzes conversation semantics to detect refinement needs.
    Uses contextual understanding rather than keyword matching.
    """
    
    def __init__(self):
        self.analyzer_name = "Semantic Refinement Analyzer"
        self.version = "1.0"
        
        # Semantic categories for different types of requirements
        self.requirement_categories = {
            'budget': {
                'indicators': ['cost', 'price', 'expensive', 'cheap', 'afford', 'budget', 'money', 'spend', 'financial'],
                'context_words': ['tight', 'limited', 'flexible', 'unlimited', 'constraint', 'range']
            },
            'preferences': {
                'indicators': ['prefer', 'like', 'love', 'hate', 'dislike', 'want', 'need', 'avoid'],
                'context_words': ['instead', 'rather', 'better', 'worse', 'alternative']
            },
            'scope': {
                'indicators': ['include', 'add', 'also', 'additionally', 'plus', 'extend', 'expand'],
                'context_words': ['more', 'extra', 'further', 'beyond', 'additional']
            },
            'constraints': {
                'indicators': ['must', 'required', 'necessary', 'essential', 'important', 'critical'],
                'context_words': ['cannot', 'unable', 'restriction', 'limitation', 'requirement']
            },
            'style': {
                'indicators': ['style', 'aesthetic', 'look', 'feel', 'vibe', 'appearance', 'design'],
                'context_words': ['modern', 'classic', 'minimalist', 'bold', 'subtle', 'elegant']
            },
            'timeline': {
                'indicators': ['time', 'deadline', 'schedule', 'when', 'date', 'duration'],
                'context_words': ['urgent', 'flexible', 'soon', 'later', 'quick', 'slow']
            }
        }
        
        # Intent change indicators
        self.intent_change_patterns = [
            r'\b(actually|instead|rather|change|modify|different|new)\b',
            r'\b(let me (clarify|correct|update|refine))\b',
            r'\b(on second thought|thinking about it)\b',
            r'\b(what I really (want|need|mean))\b'
        ]
        
        # Contradiction indicators
        self.contradiction_patterns = [
            r'\b(not|no|never|don\'t|won\'t|can\'t)\b',
            r'\b(opposite|contrary|different from)\b',
            r'\b(except|but|however|although)\b'
        ]
    
    def analyze_conversation_for_refinement(
        self, 
        conversation_history: List[ConversationTurn],
        agent_result: Dict[str, Any],
        original_request: str
    ) -> Tuple[bool, List[SemanticGap], float]:
        """
        Analyze conversation to detect if agent result needs refinement.
        
        Returns:
            (should_refine, semantic_gaps, overall_confidence)
        """
        start_time = time.time()
        
        logger.info(f"🔍 Analyzing conversation for semantic refinement needs")
        logger.info(f"📝 Conversation turns: {len(conversation_history)}")
        logger.info(f"🎯 Original request: {original_request[:100]}...")
        
        # Extract user turns since original request
        user_turns = [turn for turn in conversation_history if turn.speaker == 'user']
        
        if len(user_turns) <= 1:
            logger.info("❌ Insufficient conversation history for refinement analysis")
            return False, [], 0.0
        
        # Analyze different types of semantic gaps
        semantic_gaps = []
        
        # 1. Analyze requirement evolution
        requirement_gaps = self._analyze_requirement_evolution(user_turns, agent_result, original_request)
        semantic_gaps.extend(requirement_gaps)
        
        # 2. Analyze intent changes
        intent_gaps = self._analyze_intent_changes(user_turns, agent_result)
        semantic_gaps.extend(intent_gaps)
        
        # 3. Analyze scope expansion
        scope_gaps = self._analyze_scope_expansion(user_turns, agent_result)
        semantic_gaps.extend(scope_gaps)
        
        # 4. Analyze preference contradictions
        contradiction_gaps = self._analyze_preference_contradictions(user_turns, agent_result)
        semantic_gaps.extend(contradiction_gaps)
        
        # 5. Analyze missing context
        context_gaps = self._analyze_missing_context(user_turns, agent_result)
        semantic_gaps.extend(context_gaps)
        
        # Calculate overall refinement confidence
        overall_confidence = self._calculate_refinement_confidence(semantic_gaps)
        should_refine = overall_confidence > 0.4  # Threshold for refinement
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"✅ Semantic analysis completed in {processing_time:.1f}ms")
        logger.info(f"🎯 Semantic gaps found: {len(semantic_gaps)}")
        logger.info(f"📊 Overall confidence: {overall_confidence:.2f}")
        logger.info(f"🔄 Should refine: {should_refine}")
        
        return should_refine, semantic_gaps, overall_confidence
    
    def _analyze_requirement_evolution(
        self, 
        user_turns: List[ConversationTurn], 
        agent_result: Dict[str, Any],
        original_request: str
    ) -> List[SemanticGap]:
        """Analyze how user requirements have evolved since the original request."""
        gaps = []
        
        # Extract requirements from original request
        original_requirements = self._extract_requirements(original_request)
        
        # Extract requirements from subsequent conversation
        conversation_requirements = {}
        for turn in user_turns[1:]:  # Skip the original request
            turn_requirements = self._extract_requirements(turn.content)
            for category, details in turn_requirements.items():
                if category not in conversation_requirements:
                    conversation_requirements[category] = []
                conversation_requirements[category].extend(details)
        
        # Compare requirements
        for category, new_requirements in conversation_requirements.items():
            if category not in original_requirements or not original_requirements[category]:
                # New requirement category introduced
                gap = SemanticGap(
                    gap_type='new_requirement',
                    description=f"User introduced new {category} requirements not in original request",
                    confidence=0.8,
                    conversation_evidence=[req['evidence'] for req in new_requirements],
                    suggested_refinement=f"Update agent result to include {category} considerations"
                )
                gaps.append(gap)
                logger.info(f"🆕 New requirement detected: {category}")
        
        return gaps
    
    def _analyze_intent_changes(
        self, 
        user_turns: List[ConversationTurn], 
        agent_result: Dict[str, Any]
    ) -> List[SemanticGap]:
        """Analyze explicit intent changes in conversation."""
        gaps = []
        
        for turn in user_turns[1:]:
            content_lower = turn.content.lower()
            
            # Check for intent change patterns
            for pattern in self.intent_change_patterns:
                if re.search(pattern, content_lower):
                    gap = SemanticGap(
                        gap_type='intent_change',
                        description="User explicitly changed or clarified their intent",
                        confidence=0.9,
                        conversation_evidence=[turn.content],
                        suggested_refinement="Revise agent result based on updated intent"
                    )
                    gaps.append(gap)
                    logger.info(f"🔄 Intent change detected: {turn.content[:50]}...")
                    break
        
        return gaps
    
    def _analyze_scope_expansion(
        self, 
        user_turns: List[ConversationTurn], 
        agent_result: Dict[str, Any]
    ) -> List[SemanticGap]:
        """Analyze if user has expanded the scope of their request."""
        gaps = []
        
        scope_indicators = self.requirement_categories['scope']['indicators']
        
        for turn in user_turns[1:]:
            content_lower = turn.content.lower()
            
            # Check for scope expansion
            scope_mentions = [word for word in scope_indicators if word in content_lower]
            if scope_mentions:
                gap = SemanticGap(
                    gap_type='scope_expansion',
                    description="User expanded the scope of their original request",
                    confidence=0.7,
                    conversation_evidence=[turn.content],
                    suggested_refinement="Expand agent result to cover additional scope"
                )
                gaps.append(gap)
                logger.info(f"📈 Scope expansion detected: {scope_mentions}")
        
        return gaps
    
    def _analyze_preference_contradictions(
        self, 
        user_turns: List[ConversationTurn], 
        agent_result: Dict[str, Any]
    ) -> List[SemanticGap]:
        """Analyze if user preferences contradict the agent result."""
        gaps = []
        
        preference_indicators = self.requirement_categories['preferences']['indicators']
        
        for turn in user_turns[1:]:
            content_lower = turn.content.lower()
            
            # Check for preference statements
            preference_mentions = [word for word in preference_indicators if word in content_lower]
            
            # Check for contradiction patterns
            contradiction_mentions = []
            for pattern in self.contradiction_patterns:
                if re.search(pattern, content_lower):
                    contradiction_mentions.append(pattern)
            
            if preference_mentions and contradiction_mentions:
                gap = SemanticGap(
                    gap_type='preference_contradiction',
                    description="User expressed preferences that may contradict agent result",
                    confidence=0.6,
                    conversation_evidence=[turn.content],
                    suggested_refinement="Adjust agent result to align with user preferences"
                )
                gaps.append(gap)
                logger.info(f"⚠️ Preference contradiction detected")
        
        return gaps
    
    def _analyze_missing_context(
        self, 
        user_turns: List[ConversationTurn], 
        agent_result: Dict[str, Any]
    ) -> List[SemanticGap]:
        """Analyze if conversation reveals context missing from agent result."""
        gaps = []
        
        # Look for detailed context provided after original request
        context_rich_turns = []
        for turn in user_turns[1:]:
            if len(turn.content.split()) > 10:  # Substantial content
                context_rich_turns.append(turn)
        
        if context_rich_turns:
            gap = SemanticGap(
                gap_type='missing_context',
                description="User provided additional context not reflected in agent result",
                confidence=0.5,
                conversation_evidence=[turn.content for turn in context_rich_turns],
                suggested_refinement="Incorporate additional context into agent result"
            )
            gaps.append(gap)
            logger.info(f"📋 Missing context detected: {len(context_rich_turns)} rich turns")
        
        return gaps
    
    def _extract_requirements(self, text: str) -> Dict[str, List[Dict[str, Any]]]:
        """Extract requirements from text using semantic analysis."""
        requirements = {}
        text_lower = text.lower()
        
        for category, config in self.requirement_categories.items():
            category_requirements = []
            
            # Look for category indicators
            for indicator in config['indicators']:
                if indicator in text_lower:
                    # Find context around the indicator
                    context = self._extract_context_around_word(text, indicator)
                    if context:
                        category_requirements.append({
                            'indicator': indicator,
                            'context': context,
                            'evidence': text
                        })
            
            if category_requirements:
                requirements[category] = category_requirements
        
        return requirements
    
    def _extract_context_around_word(self, text: str, word: str, window: int = 5) -> str:
        """Extract context around a specific word."""
        words = text.split()
        word_lower = word.lower()
        
        for i, w in enumerate(words):
            if word_lower in w.lower():
                start = max(0, i - window)
                end = min(len(words), i + window + 1)
                return ' '.join(words[start:end])
        
        return ""
    
    def _calculate_refinement_confidence(self, semantic_gaps: List[SemanticGap]) -> float:
        """Calculate overall confidence that refinement is needed."""
        if not semantic_gaps:
            return 0.0
        
        # Weight different gap types
        gap_weights = {
            'intent_change': 1.0,
            'new_requirement': 0.8,
            'scope_expansion': 0.7,
            'preference_contradiction': 0.6,
            'missing_context': 0.4
        }
        
        total_weighted_confidence = 0.0
        total_weight = 0.0
        
        for gap in semantic_gaps:
            weight = gap_weights.get(gap.gap_type, 0.5)
            total_weighted_confidence += gap.confidence * weight
            total_weight += weight
        
        if total_weight == 0:
            return 0.0
        
        # Normalize and apply diminishing returns for multiple gaps
        base_confidence = total_weighted_confidence / total_weight
        
        # Apply diminishing returns - more gaps increase confidence but with diminishing effect
        gap_count_factor = min(1.0, len(semantic_gaps) / 3.0)
        
        final_confidence = base_confidence * (0.7 + 0.3 * gap_count_factor)
        
        return min(1.0, final_confidence)
    
    def generate_refinement_instructions(self, semantic_gaps: List[SemanticGap]) -> Dict[str, Any]:
        """Generate specific refinement instructions based on semantic gaps."""
        instructions = {
            'refinement_type': 'semantic_analysis',
            'gaps_identified': len(semantic_gaps),
            'specific_refinements': [],
            'priority_order': []
        }
        
        # Sort gaps by confidence
        sorted_gaps = sorted(semantic_gaps, key=lambda g: g.confidence, reverse=True)
        
        for gap in sorted_gaps:
            refinement = {
                'type': gap.gap_type,
                'description': gap.description,
                'confidence': gap.confidence,
                'action': gap.suggested_refinement,
                'evidence': gap.conversation_evidence
            }
            instructions['specific_refinements'].append(refinement)
            instructions['priority_order'].append(gap.gap_type)
        
        return instructions
