"""
Memory manager for the agents app.
"""
import logging
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)


class MemoryManager:
    """Memory manager for the agents app."""
    
    def __init__(self, embedding_model=None):
        """Initialize memory manager."""
        self.embedding_model = embedding_model
        # Mock vectorstore for testing
        self.vectorstore = {}
        # Define memory types
        self.memory_types = [
            "semantic_profile",
            "episodic_summary",
            "episodic",  # Add episodic type
            "general_knowledge",
            "explicit_memory",
            "task_related"
        ]
        # In-memory storage for testing
        self._memories = {}
    
    async def async_search_memories(self, query, user_id, k=5, min_importance=0.3):
        """Search memories asynchronously."""
        # This is a mock implementation
        return self.search_memories(query, user_id, k, min_importance)

    async def async_store_memory(self, user_id, text, memory_type, importance_score=0.5, personalness_score=0.5, metadata=None):
        """Store a memory asynchronously."""
        return self.store_memory(text, memory_type, user_id, importance_score, personalness_score, metadata)
    
    def search_memories(self, query, user_id, k=5, min_importance=0.3):
        """Search memories synchronously."""
        # This is a mock implementation for testing
        results = []
        
        # Filter memories by user_id and importance
        for memory_id, memory in self._memories.items():
            if memory.get("user_id") == user_id and memory.get("importance_score", 0) >= min_importance:
                results.append(memory)
        
        # Sort by relevance (mock implementation just sorts by importance)
        results.sort(key=lambda x: x.get("importance_score", 0), reverse=True)
        
        # Return top k results
        return results[:k]
    
    def store_memory(self, text, memory_type, user_id, importance_score=0.5, personalness_score=0.5, actionability_score=0.5, metadata=None):
        """Store a memory."""
        if not text:
            return ""
            
        # Validate memory type
        if memory_type not in self.memory_types:
            logger.warning(f"Invalid memory type: {memory_type}. Defaulting to explicit_memory.")
            memory_type = "explicit_memory"
        
        # Create memory object
        memory_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        
        memory = {
            "id": memory_id,
            "text": text,
            "memory_type": memory_type,
            "user_id": user_id,
            "importance_score": importance_score,
            "personalness_score": personalness_score,
            "actionability_score": actionability_score,
            "created_at": timestamp,
            "updated_at": timestamp,
            "metadata": metadata or {"memory_type": memory_type}
        }
        
        # Store memory
        self._memories[memory_id] = memory
        
        return memory_id
    
    def get_memory_stats(self):
        """Get memory statistics."""
        stats = {
            "total_count": len(self._memories),
            "by_type": {},
            "by_user": {}
        }
        
        # Count by type and user
        for memory in self._memories.values():
            memory_type = memory.get("memory_type", "unknown")
            user_id = memory.get("user_id", "unknown")
            
            # Count by type
            if memory_type not in stats["by_type"]:
                stats["by_type"][memory_type] = 0
            stats["by_type"][memory_type] += 1
            
            # Count by user
            if user_id not in stats["by_user"]:
                stats["by_user"][user_id] = 0
            stats["by_user"][user_id] += 1
        
        return stats


# Singleton instance
_memory_manager = None

def get_memory_manager():
    """Get memory manager singleton."""
    global _memory_manager
    if _memory_manager is None:
        _memory_manager = MemoryManager()
    return _memory_manager