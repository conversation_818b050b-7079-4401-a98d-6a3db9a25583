#!/usr/bin/env python3
"""
Agent Refinement System - Handles refinement of agent responses based on follow-up conversation.
"""
import logging
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from chat.services.conversation_logger import conversation_logger

logger = logging.getLogger(__name__)

# Import the semantic analyzer
try:
    from agents.services.semantic_refinement_analyzer import (
        SemanticRefinementAnalyzer,
        ConversationTurn,
        SemanticGap
    )
    SEMANTIC_ANALYZER_AVAILABLE = True
except ImportError:
    logger.warning("Semantic refinement analyzer not available")
    SEMANTIC_ANALYZER_AVAILABLE = False


class AgentRefinementSystem:
    """
    Manages the refinement of agent responses based on follow-up user input and conversation context.
    """
    
    def __init__(self):
        self.pending_refinements = {}  # Store pending agent results for refinement
        self.refinement_timeout = 300  # 5 minutes timeout for refinements

        # Initialize semantic analyzer
        self.semantic_analyzer = None
        if SEMANTIC_ANALYZER_AVAILABLE:
            try:
                self.semantic_analyzer = SemanticRefinementAnalyzer()
                logger.info("Semantic refinement analyzer initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize semantic analyzer: {e}")
        else:
            logger.warning("Semantic analyzer not available, falling back to keyword-based detection")
        
    def store_agent_result(self, request_id: str, agent_result: Dict[str, Any], conversation_context: Dict[str, Any]):
        """
        Store an agent result that might need refinement based on follow-up conversation.

        Args:
            request_id: Unique identifier for the request
            agent_result: The original agent result
            conversation_context: Context from the filler response conversation
        """
        logger.info(f"📋 Storing agent result for potential refinement: {request_id}")

        # Validate agent result matches expected domain
        expected_domain = conversation_context.get('domain_context', 'unknown')
        if self._validate_agent_result_domain(agent_result, expected_domain):
            self.pending_refinements[request_id] = {
                'agent_result': agent_result,
                'conversation_context': conversation_context,
                'timestamp': datetime.now(),
                'refinement_attempts': 0,
                'user_id': conversation_context.get('user_id', 'unknown'),
                'original_request': conversation_context.get('original_request', ''),
                'conversation_history': conversation_context.get('conversation_history', []),
                'domain': expected_domain
            }

            logger.info(f"💾 {expected_domain} agent result stored. Pending refinements: {len(self.pending_refinements)}")
        else:
            logger.warning(f"⚠️ Agent result validation failed for {expected_domain} domain in request {request_id}")

        # Clean up old pending refinements
        self._cleanup_expired_refinements()

    def _validate_agent_result_domain(self, agent_result: dict, expected_domain: str) -> bool:
        """Validate that agent result matches the expected domain."""
        if not agent_result or expected_domain == 'unknown':
            return True  # Allow unknown domains

        # Check if result contains domain-specific keywords
        result_text = str(agent_result).lower()

        domain_keywords = {
            'travel': ['travel', 'trip', 'destination', 'itinerary', 'vacation', 'journey'],
            'business': ['strategy', 'business', 'marketing', 'project', 'work', 'professional'],
            'fitness': ['workout', 'exercise', 'fitness', 'training', 'nutrition', 'health'],
            'code': ['code', 'programming', 'algorithm', 'performance', 'optimization', 'technical'],
            'writing': ['writing', 'story', 'creative', 'narrative', 'content', 'copy']
        }

        expected_keywords = domain_keywords.get(expected_domain, [])

        # Check if at least one domain keyword is present
        has_domain_keywords = any(keyword in result_text for keyword in expected_keywords)

        # Check for wrong domain contamination
        wrong_domains = {k: v for k, v in domain_keywords.items() if k != expected_domain}
        has_wrong_domain = False

        for wrong_domain, wrong_keywords in wrong_domains.items():
            wrong_keyword_count = sum(1 for keyword in wrong_keywords if keyword in result_text)
            if wrong_keyword_count > 2:  # Too many keywords from wrong domain
                has_wrong_domain = True
                logger.warning(f"Agent result contains {wrong_keyword_count} keywords from {wrong_domain} domain")
                break

        return has_domain_keywords and not has_wrong_domain

    def check_for_refinement_opportunity(self, user_input: str, user_id: str, conversation_history: List[Dict] = None) -> Optional[Dict[str, Any]]:
        """
        Check if the user's input could be used to refine a pending agent result using semantic analysis.

        Args:
            user_input: The user's follow-up input
            user_id: User identifier
            conversation_history: Full conversation history for semantic analysis

        Returns:
            Refinement opportunity data if found, None otherwise
        """
        logger.info(f"🔍 Checking for refinement opportunities for user: {user_id}")
        logger.info(f"📝 User input: {user_input[:100]}...")

        # Find pending refinements for this user
        user_pending = {
            req_id: data for req_id, data in self.pending_refinements.items()
            if data['user_id'] == user_id
        }

        logger.info(f"📊 Found {len(user_pending)} pending refinements for user")

        if not user_pending:
            logger.info("❌ No pending refinements found for this user")
            return None

        # Get the most recent pending refinement
        most_recent = max(user_pending.items(), key=lambda x: x[1]['timestamp'])
        request_id, refinement_data = most_recent

        # Update conversation history with new user input
        updated_conversation = self._build_conversation_history(
            refinement_data, user_input, conversation_history
        )

        # Use semantic analysis if available, otherwise fall back to keyword analysis
        if self.semantic_analyzer:
            refinement_analysis = self._semantic_refinement_analysis(
                updated_conversation, refinement_data, user_input
            )
        else:
            refinement_analysis = self._keyword_refinement_analysis(user_input, refinement_data)

        if refinement_analysis['should_refine']:
            logger.info(f"✅ Refinement opportunity found for request: {request_id}")
            logger.info(f"🎯 Refinement analysis: {refinement_analysis}")

            return {
                'request_id': request_id,
                'refinement_data': refinement_data,
                'refinement_analysis': refinement_analysis,
                'user_input': user_input,
                'conversation_history': updated_conversation
            }
        else:
            logger.info("❌ No strong refinement indicators found")
            return None
    
    def perform_refinement(self, refinement_opportunity: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform the actual refinement of an agent result.
        
        Args:
            refinement_opportunity: Data about the refinement opportunity
            
        Returns:
            Refined agent result
        """
        start_time = time.time()
        
        request_id = refinement_opportunity['request_id']
        refinement_data = refinement_opportunity['refinement_data']
        user_input = refinement_opportunity['user_input']
        refinement_analysis = refinement_opportunity['refinement_analysis']

        logger.info(f"🔄 Performing refinement for request: {request_id}")
        logger.info(f"🎯 Analysis type: {refinement_analysis.get('analysis_type', 'unknown')}")

        # Extract original agent result and context
        original_result = refinement_data['agent_result']
        conversation_context = refinement_data['conversation_context']

        # Build enhanced refinement context
        refinement_context = {
            'follow_up_input': user_input,
            'filler_response': conversation_context.get('filler_response', ''),
            'questions_asked': self._extract_questions_from_filler(conversation_context.get('filler_response', '')),
            'user_answers': [user_input],  # Current input as answer
            'refinement_analysis': refinement_analysis,
            'conversation_history': refinement_opportunity.get('conversation_history', []),
            'original_timestamp': refinement_data['timestamp'].isoformat(),
            'refinement_timestamp': datetime.now().isoformat(),
            'semantic_gaps': refinement_analysis.get('semantic_gaps', []),
            'refinement_instructions': refinement_analysis.get('refinement_instructions', {})
        }
        
        logger.info(f"📋 Refinement context prepared: {refinement_context}")
        
        # Determine which agent to use for refinement based on context
        agent_name = original_result.get('agent_name', 'unknown')
        conversation_context_text = ' '.join([
            conversation_context.get('original_request', ''),
            user_input
        ]).lower()

        # Route to appropriate agent based on context
        if ('travel' in agent_name.lower() or
            any(keyword in conversation_context_text for keyword in ['travel', 'trip', 'vacation', 'destination', 'journey'])):
            refined_result = self._refine_with_travel_agent(original_result, refinement_context)
        elif ('code' in agent_name.lower() or
              any(keyword in conversation_context_text for keyword in ['code', 'programming', 'python', 'security', 'review'])):
            refined_result = self._refine_with_code_agent(original_result, refinement_context)
        elif ('fitness' in agent_name.lower() or
              any(keyword in conversation_context_text for keyword in ['fitness', 'workout', 'exercise', 'gym', 'training'])):
            refined_result = self._refine_with_fitness_agent(original_result, refinement_context)
        elif ('writing' in agent_name.lower() or
              any(keyword in conversation_context_text for keyword in ['writing', 'story', 'novel', 'character', 'plot'])):
            refined_result = self._refine_with_writing_agent(original_result, refinement_context)
        elif ('business' in agent_name.lower() or
              any(keyword in conversation_context_text for keyword in ['business', 'strategy', 'market', 'startup', 'company'])):
            refined_result = self._refine_with_business_agent(original_result, refinement_context)
        else:
            # Generic refinement for unknown agents
            refined_result = self._generic_refinement(original_result, refinement_context)
        
        # Update refinement tracking
        self.pending_refinements[request_id]['refinement_attempts'] += 1
        self.pending_refinements[request_id]['last_refinement'] = datetime.now()
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"✅ Refinement completed in {processing_time:.1f}ms")
        logger.info(f"🔄 Refinement attempt #{self.pending_refinements[request_id]['refinement_attempts']}")

        # Log the full refined result
        agent_name = original_result.get('agent_name', 'Unknown Agent')
        conversation_logger.print_full_agent_response(
            agent_name=agent_name,
            full_response=str(refined_result.get('refined_content', refined_result)),
            metadata={
                'processing_time_ms': processing_time,
                'refinement_attempt': self.pending_refinements[request_id]['refinement_attempts'],
                'request_id': request_id
            }
        )

        # Add refinement metadata
        refined_result['refinement_metadata'] = {
            'request_id': request_id,
            'refinement_processing_time_ms': processing_time,
            'refinement_attempt': self.pending_refinements[request_id]['refinement_attempts'],
            'refinement_context_used': refinement_context
        }

        return refined_result

    def _build_conversation_history(
        self,
        refinement_data: Dict,
        new_user_input: str,
        conversation_history: List[Dict] = None
    ) -> List[ConversationTurn]:
        """Build conversation history for semantic analysis."""
        turns = []

        # Add original request
        original_request = refinement_data.get('original_request', '')
        if original_request:
            turns.append(ConversationTurn(
                speaker='user',
                content=original_request,
                timestamp=refinement_data['timestamp']
            ))

        # Add conversation history if available
        if conversation_history:
            for turn in conversation_history:
                turns.append(ConversationTurn(
                    speaker=turn.get('role', 'user'),
                    content=turn.get('content', ''),
                    timestamp=datetime.now()  # Approximate timestamp
                ))

        # Add new user input
        turns.append(ConversationTurn(
            speaker='user',
            content=new_user_input,
            timestamp=datetime.now()
        ))

        return turns

    def _semantic_refinement_analysis(
        self,
        conversation_history: List[ConversationTurn],
        refinement_data: Dict,
        user_input: str
    ) -> Dict[str, Any]:
        """Perform semantic analysis to determine refinement needs."""
        logger.info("🧠 Performing semantic refinement analysis")

        agent_result = refinement_data['agent_result']
        original_request = refinement_data.get('original_request', '')

        try:
            should_refine, semantic_gaps, confidence = self.semantic_analyzer.analyze_conversation_for_refinement(
                conversation_history, agent_result, original_request
            )

            # Generate refinement instructions
            refinement_instructions = self.semantic_analyzer.generate_refinement_instructions(semantic_gaps)

            analysis = {
                'should_refine': should_refine,
                'confidence': confidence,
                'analysis_type': 'semantic',
                'semantic_gaps': [
                    {
                        'type': gap.gap_type,
                        'description': gap.description,
                        'confidence': gap.confidence,
                        'evidence': gap.conversation_evidence
                    } for gap in semantic_gaps
                ],
                'refinement_instructions': refinement_instructions
            }

            logger.info(f"🎯 Semantic analysis result: should_refine={should_refine}, confidence={confidence:.2f}")
            logger.info(f"📊 Semantic gaps found: {len(semantic_gaps)}")

            return analysis

        except Exception as e:
            logger.error(f"Error in semantic analysis: {e}")
            # Fall back to keyword analysis
            return self._keyword_refinement_analysis(user_input, refinement_data)

    def _keyword_refinement_analysis(self, user_input: str, refinement_data: Dict) -> Dict[str, Any]:
        """Fallback keyword-based analysis when semantic analysis is not available."""
        logger.info("🔤 Performing keyword-based refinement analysis (fallback)")

        user_lower = user_input.lower()

        analysis = {
            'should_refine': False,
            'confidence': 0.0,
            'analysis_type': 'keyword',
            'refinement_type': [],
            'specific_requests': []
        }

        # Check for direct refinement requests
        refinement_keywords = [
            'actually', 'instead', 'change', 'modify', 'adjust', 'different',
            'prefer', 'rather', 'budget', 'cheaper', 'expensive', 'luxury',
            'more', 'less', 'add', 'remove', 'include', 'exclude'
        ]

        found_keywords = [kw for kw in refinement_keywords if kw in user_lower]

        if found_keywords:
            analysis['should_refine'] = True
            analysis['confidence'] = min(len(found_keywords) * 0.3, 1.0)
            analysis['specific_requests'] = found_keywords

        # Check for specific refinement types
        if any(word in user_lower for word in ['budget', 'cost', 'price', 'money']):
            analysis['refinement_type'].append('budget')
        if any(word in user_lower for word in ['activity', 'adventure', 'relax', 'active']):
            analysis['refinement_type'].append('activities')
        if any(word in user_lower for word in ['hotel', 'accommodation', 'stay', 'room']):
            analysis['refinement_type'].append('accommodation')
        if any(word in user_lower for word in ['food', 'diet', 'vegetarian', 'vegan']):
            analysis['refinement_type'].append('dietary')

        # Check for questions that were asked in filler response
        filler_response = refinement_data['conversation_context'].get('filler_response', '')
        if '?' in filler_response and len(user_input.split()) > 3:
            analysis['should_refine'] = True
            analysis['confidence'] = max(analysis['confidence'], 0.7)
            analysis['refinement_type'].append('question_response')

        logger.info(f"🔍 Keyword analysis result:")
        logger.info(f"   Should refine: {analysis['should_refine']}")
        logger.info(f"   Confidence: {analysis['confidence']:.2f}")
        logger.info(f"   Types: {analysis['refinement_type']}")
        logger.info(f"   Keywords found: {found_keywords}")

        return analysis
    
    def _extract_questions_from_filler(self, filler_response: str) -> List[str]:
        """Extract questions that were asked in the filler response."""
        if not filler_response:
            return []
        
        # Simple question extraction (split by sentence and find those ending with ?)
        sentences = filler_response.split('.')
        questions = [s.strip() + '?' for s in sentences if '?' in s]
        
        return questions
    
    def _refine_with_travel_agent(self, original_result: Dict, refinement_context: Dict) -> Dict[str, Any]:
        """Refine using the travel planning agent."""
        logger.info("🧳 Using Travel Planning Agent for refinement")

        try:
            from agents.example_travel_agent import TravelPlanningAgent
            travel_agent = TravelPlanningAgent()

            # Log agent processing start
            conversation_logger.print_agent_processing(
                agent_type='travel',
                task_description='Refining travel plan based on user feedback'
            )

            refined_result = travel_agent.refine_response(original_result, refinement_context)

            # Log the agent result
            conversation_logger.print_agent_result(
                agent_type='travel',
                agent_result=refined_result,
                processing_time=1.0  # Approximate processing time
            )

            return refined_result
        except Exception as e:
            logger.error(f"Error in travel agent refinement: {e}")
            conversation_logger.print_error('travel_agent_refinement', str(e))
            return self._generic_refinement(original_result, refinement_context)

    def _refine_with_code_agent(self, original_result: Dict, refinement_context: Dict) -> Dict[str, Any]:
        """Refine using the code review agent."""
        logger.info("💻 Using Code Review Agent for refinement")

        try:
            from agents.code_review_agent import CodeReviewAgent
            code_agent = CodeReviewAgent()

            # Log agent processing start
            conversation_logger.print_agent_processing(
                agent_type='code',
                task_description='Refining code analysis based on user feedback'
            )

            refined_result = code_agent.refine_response(original_result, refinement_context)

            # Log the agent result
            conversation_logger.print_agent_result(
                agent_type='code',
                agent_result=refined_result,
                processing_time=1.2  # Approximate processing time
            )

            return refined_result
        except Exception as e:
            logger.error(f"Error in code agent refinement: {e}")
            conversation_logger.print_error('code_agent_refinement', str(e))
            return self._generic_refinement(original_result, refinement_context)

    def _refine_with_fitness_agent(self, original_result: Dict, refinement_context: Dict) -> Dict[str, Any]:
        """Refine using the fitness coach agent."""
        logger.info("💪 Using Fitness Coach Agent for refinement")

        try:
            from agents.fitness_coach_agent import FitnessCoachAgent
            fitness_agent = FitnessCoachAgent()
            refined_result = fitness_agent.refine_response(original_result, refinement_context)
            return refined_result
        except Exception as e:
            logger.error(f"Error in fitness agent refinement: {e}")
            return self._generic_refinement(original_result, refinement_context)

    def _refine_with_writing_agent(self, original_result: Dict, refinement_context: Dict) -> Dict[str, Any]:
        """Refine using the creative writing agent."""
        logger.info("✍️ Using Creative Writing Agent for refinement")

        try:
            from agents.creative_writing_agent import CreativeWritingAgent
            writing_agent = CreativeWritingAgent()
            refined_result = writing_agent.refine_response(original_result, refinement_context)
            return refined_result
        except Exception as e:
            logger.error(f"Error in writing agent refinement: {e}")
            return self._generic_refinement(original_result, refinement_context)

    def _refine_with_business_agent(self, original_result: Dict, refinement_context: Dict) -> Dict[str, Any]:
        """Refine using the business strategy agent."""
        logger.info("📊 Using Business Strategy Agent for refinement")

        try:
            from agents.business_strategy_agent import BusinessStrategyAgent
            business_agent = BusinessStrategyAgent()
            refined_result = business_agent.refine_response(original_result, refinement_context)
            return refined_result
        except Exception as e:
            logger.error(f"Error in business agent refinement: {e}")
            return self._generic_refinement(original_result, refinement_context)
    
    def _generic_refinement(self, original_result: Dict, refinement_context: Dict) -> Dict[str, Any]:
        """Generic refinement for agents that don't have specific refinement logic."""
        logger.info("🔧 Using generic refinement")

        follow_up_input = refinement_context.get('follow_up_input', '')
        semantic_gaps = refinement_context.get('semantic_gaps', [])

        # Analyze what type of refinement is needed
        refinement_needs = {}
        follow_up_lower = follow_up_input.lower()

        # Check for common refinement patterns
        if any(word in follow_up_lower for word in ['actually', 'instead', 'change', 'different']):
            refinement_needs['intent_change'] = 'User changed their intent or requirements'

        if any(word in follow_up_lower for word in ['budget', 'cost', 'money', 'expensive', 'cheap']):
            refinement_needs['budget_consideration'] = 'User mentioned budget constraints'

        if any(word in follow_up_lower for word in ['time', 'schedule', 'busy', 'quick']):
            refinement_needs['time_constraint'] = 'User has time limitations'

        if any(word in follow_up_lower for word in ['simple', 'easy', 'beginner', 'basic']):
            refinement_needs['simplification'] = 'User needs simpler approach'

        # Include semantic gap information
        for gap in semantic_gaps:
            gap_type = gap.get('type', 'unknown')
            refinement_needs[f'semantic_{gap_type}'] = gap.get('description', 'Semantic gap identified')

        refined_result = original_result.copy()
        refined_result['refinement_applied'] = refinement_needs
        refined_result['refinement_context_used'] = refinement_context
        refined_result['refinement_timestamp'] = datetime.now().isoformat()

        # Add a generic refined content field
        refined_result['refined_content'] = {
            'acknowledgment': f"I understand you mentioned: {follow_up_input}",
            'adjustments_made': list(refinement_needs.keys()),
            'updated_approach': 'Adjusted based on your feedback'
        }

        return refined_result
    
    def _cleanup_expired_refinements(self):
        """Remove expired pending refinements."""
        current_time = datetime.now()
        expired_keys = []
        
        for request_id, data in self.pending_refinements.items():
            if (current_time - data['timestamp']).seconds > self.refinement_timeout:
                expired_keys.append(request_id)
        
        for key in expired_keys:
            del self.pending_refinements[key]
            logger.info(f"🗑️ Removed expired refinement: {key}")
    
    def get_refinement_stats(self) -> Dict[str, Any]:
        """Get statistics about the refinement system."""
        return {
            'pending_refinements': len(self.pending_refinements),
            'total_refinement_attempts': sum(
                data['refinement_attempts'] for data in self.pending_refinements.values()
            ),
            'oldest_pending': min(
                (data['timestamp'] for data in self.pending_refinements.values()),
                default=None
            ),
            'users_with_pending': len(set(
                data['user_id'] for data in self.pending_refinements.values()
            ))
        }
