"""
Agent Orchestrator Service for Real-time AI Companion.

This service coordinates between specialized agents and manages the overall conversation flow.
Integrates with emotion context, memory system, and streaming capabilities.
"""
import logging
import asyncio
import time
from typing import Dict, Any, List, Optional, AsyncGenerator
from django.conf import settings
import openai
from django.contrib.auth import get_user_model
from agents.models import TaskModel

from .domain_router import Domain, classify_domain

logger = logging.getLogger(__name__)

User = get_user_model()


class AgentOrchestrator:
    """
    Agent Orchestrator with integrated task management and background processing support.
    Designed to work in parallel with fast response service for comprehensive responses.
    """

    def __init__(self):
        self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = "gpt-3.5-turbo-0125"

        # Agent personas for different domains
        self.agent_personas = {
            Domain.GENERAL: self._get_general_persona(),
            Domain.DEV: self._get_dev_persona(),
            Domain.BUSINESS: self._get_business_persona(),
            Domain.LEARNING: self._get_learning_persona(),
            Domain.MUSIC: self._get_music_persona(),
            Domain.TRIVIA: self._get_trivia_persona(),
        }

        # Background processing state
        self._background_tasks: Dict[str, asyncio.Task] = {}
        self._processing_results: Dict[str, Dict] = {}

        # Access to shared conversation state manager
        self._state_manager = None

    def _get_state_manager(self):
        """Get the shared conversation state manager."""
        if self._state_manager is None:
            try:
                from chat.services.fast_response_service import get_conversation_state_manager
                self._state_manager = get_conversation_state_manager()
            except ImportError:
                logger.warning("Could not import conversation state manager")
        return self._state_manager
    
    def _get_general_persona(self) -> str:
        """Get the general conversation persona."""
        return """You are KD Assistant – a warm, playful, emotionally intelligent AI companion. 
        Your mission is to keep natural, engaging conversations flowing while being helpful and supportive.
        
        PERSONALITY:
        • Energetic, lively, curious, and empathetic
        • Emotion-aware: adapt your style based on the user's emotional state
        • When user is happy/excited → match their energy with enthusiasm
        • When user is sad/lonely → offer gentle comfort and support
        • When user is stressed → be calm, concise, and reassuring
        • When user is playful → engage with light teasing and humor
        
        CONVERSATION STYLE:
        • Keep responses natural and conversational
        • Show genuine interest in the user's life and experiences
        • Remember and reference previous conversations when relevant
        • Be supportive without being overly instructive
        • Use positive, optimistic language
        • Stay warm and friendly while being helpful"""
    
    def _get_dev_persona(self) -> str:
        """Get the development agent persona."""
        return """You are the Dev Agent for KD Assistant, specializing in programming and technology.
        
        EXPERTISE:
        • Programming languages (Python, JavaScript, etc.)
        • Software architecture and design patterns
        • Debugging and problem-solving
        • Code review and best practices
        • Technical explanations made accessible
        
        STYLE:
        • Provide clear, practical code examples
        • Explain technical concepts in understandable terms
        • Offer multiple approaches when appropriate
        • Be encouraging for beginners, detailed for experts
        • Maintain the warm KD personality while being technically precise"""
    
    def _get_business_persona(self) -> str:
        """Get the business agent persona."""
        return """You are the Business Agent for KD Assistant, focusing on professional and business topics.
        
        EXPERTISE:
        • Business strategy and planning
        • Finance and investment basics
        • Marketing and growth strategies
        • Professional development
        • Workplace advice and communication
        
        STYLE:
        • Provide actionable, practical advice
        • Use real-world examples and case studies
        • Be professional yet approachable
        • Consider different business contexts and scales
        • Maintain KD's supportive personality in professional contexts"""
    
    def _get_learning_persona(self) -> str:
        """Get the learning agent persona."""
        return """You are the Learning Agent for KD Assistant, dedicated to education and knowledge sharing.
        
        EXPERTISE:
        • Explaining complex topics simply
        • Educational content and study strategies
        • Knowledge synthesis and connections
        • Learning techniques and memory aids
        • Academic and general knowledge
        
        STYLE:
        • Break down complex topics into digestible parts
        • Use analogies and examples to clarify concepts
        • Encourage curiosity and further exploration
        • Adapt explanations to the user's level
        • Make learning engaging and fun with KD's enthusiasm"""
    
    def _get_music_persona(self) -> str:
        """Get the music agent persona."""
        return """You are the Music Agent for KD Assistant, passionate about all things musical.
        
        EXPERTISE:
        • Music recommendations across all genres
        • Artist and album information
        • Music theory and composition
        • Music history and cultural context
        • Playlist creation and music discovery
        
        STYLE:
        • Share genuine enthusiasm for music
        • Provide personalized recommendations with reasoning
        • Connect music to emotions and experiences
        • Be inclusive of all musical tastes and genres
        • Use KD's playful energy when discussing music"""
    
    def _get_trivia_persona(self) -> str:
        """Get the trivia agent persona."""
        return """You are the Trivia Agent for KD Assistant, bringing fun and games to conversations.
        
        EXPERTISE:
        • Trivia questions across various topics
        • Fun facts and interesting knowledge
        • Games and interactive challenges
        • Entertainment and pop culture
        • Educational games and quizzes
        
        STYLE:
        • Keep things light, fun, and engaging
        • Create interactive experiences
        • Celebrate correct answers enthusiastically
        • Provide interesting context for trivia
        • Use KD's playful, mischievous personality"""
    
    def create_task_from_query(
        self, 
        user_id: str, 
        query: str, 
        domain: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a task based on a user query.
        
        Args:
            user_id: User identifier
            query: User's input query
            domain: Optional domain classification
        
        Returns:
            Created task dictionary
        """
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise ValueError(f"User with ID {user_id} not found")
        
        # Use LLM to extract task details
        task_details = self._extract_task_details(query, domain)
        
        task = TaskModel.objects.create(
            user=user,
            title=task_details['title'],
            description=task_details['description'],
            status='pending',
            progress=0.0,
            phases=task_details.get('phases', [])
        )
        
        return {
            'id': str(task.id),
            'title': task.title,
            'description': task.description,
            'status': task.status,
            'progress': task.progress,
            'phases': task.phases
        }
    
    def _extract_task_details(
        self, 
        query: str, 
        domain: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Extract task details from a user query using LLM.
        
        Args:
            query: User's input query
            domain: Optional domain classification
        
        Returns:
            Dictionary with task details
        """
        # Prompt for task extraction
        system_prompt = (
            "You are a task extraction assistant. Given a user query, "
            "extract a meaningful task title, description, and potential phases. "
            "Be concise and focus on the core task."
        )
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Extract task details from this query: {query}"}
        ]
        
        try:
            # Use OpenAI client to generate task details
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=150,
                temperature=0.7
            )
            
            # Parse the response
            task_text = response.choices[0].message.content.strip()
            
            # Default fallback if parsing fails
            task_details = {
                'title': f"Task: {query[:50]}...",
                'description': task_text or query,
                'phases': [
                    {
                        'phase': 'initialization',
                        'title': 'Project Start',
                        'description': 'Initial task setup',
                        'is_completed': False,
                        'is_current': True
                    }
                ]
            }
            
            return task_details
        
        except Exception as e:
            logger.error(f"Error extracting task details: {e}")
            return {
                'title': f"Task: {query[:50]}...",
                'description': query,
                'phases': []
            }
    
    def get_user_tasks(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Retrieve tasks for a specific user.
        
        Args:
            user_id: User identifier
        
        Returns:
            List of task dictionaries
        """
        try:
            user = User.objects.get(id=user_id)
            tasks = TaskModel.objects.filter(user=user)
            
            return [
                {
                    'id': str(task.id),
                    'title': task.title,
                    'description': task.description,
                    'status': task.status,
                    'progress': task.progress,
                    'phases': task.phases,
                    'error_message': task.error_message,
                    'intervention_message': task.intervention_message,
                    'created_at': task.created_at.isoformat(),
                    'updated_at': task.updated_at.isoformat()
                }
                for task in tasks
            ]
        except User.DoesNotExist:
            logger.warning(f"User with ID {user_id} not found")
            return []
    
    def update_task_progress(
        self, 
        task_id: str, 
        progress: float, 
        current_phase: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update task progress.
        
        Args:
            task_id: Task identifier
            progress: Progress percentage
            current_phase: Optional current phase
        
        Returns:
            Updated task dictionary
        """
        try:
            task = TaskModel.objects.get(id=task_id)
            task.update_progress(progress, current_phase)
            
            return {
                'id': str(task.id),
                'title': task.title,
                'status': task.status,
                'progress': task.progress,
                'phases': task.phases
            }
        except TaskModel.DoesNotExist:
            logger.error(f"Task with ID {task_id} not found")
            raise ValueError(f"Task with ID {task_id} not found")
    
    def handle_task_intervention(
        self, 
        task_id: str, 
        intervention_message: str
    ) -> Dict[str, Any]:
        """
        Handle task intervention.
        
        Args:
            task_id: Task identifier
            intervention_message: Message describing intervention
        
        Returns:
            Updated task dictionary
        """
        try:
            task = TaskModel.objects.get(id=task_id)
            task.update_status(
                'needs_intervention', 
                intervention_message=intervention_message
            )
            
            return {
                'id': str(task.id),
                'title': task.title,
                'status': task.status,
                'intervention_message': task.intervention_message
            }
        except TaskModel.DoesNotExist:
            logger.error(f"Task with ID {task_id} not found")
            raise ValueError(f"Task with ID {task_id} not found")
    
    async def process_query(
        self,
        user_input: str,
        user_id: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None,
        streaming: bool = True,
        auto_create_task: bool = False
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Enhanced process_query method with optional task creation.
        
        Args:
            user_input: User's message
            user_id: User identifier
            conversation_history: Recent conversation messages
            emotion_context: Detected emotions
            memory_context: Retrieved memories
            streaming: Whether to stream the response
            auto_create_task: Automatically create a task from the query
        
        Yields:
            Response chunks with metadata
        """
        try:
            # Optional task creation based on query
            task = None
            if auto_create_task:
                try:
                    # Use a synchronous method for task creation
                    task = self.create_task_from_query(user_id, user_input)
                    yield {
                        "type": "task_created",
                        "task": task,
                        "timestamp": self._get_timestamp()
                    }
                except Exception as e:
                    logger.warning(f"Failed to auto-create task: {e}")
            
            # Existing domain classification and response generation logic
            domain = await classify_domain(
                query=user_input,
                history=conversation_history
            )
            
            # Yield domain classification result
            yield {
                "type": "domain_classification",
                "domain": domain.value,
                "timestamp": self._get_timestamp()
            }
            
            # Generate response using appropriate agent
            async for chunk in self._generate_agent_response(
                domain=domain,
                user_input=user_input,
                user_id=user_id,
                conversation_history=conversation_history,
                emotion_context=emotion_context,
                memory_context=memory_context,
                streaming=streaming
            ):
                yield chunk
    
        except Exception as e:
            logger.error(f"Error in orchestrator processing: {e}")
            yield {
                "type": "error",
                "error": str(e),
                "timestamp": self._get_timestamp()
            }

    async def process_background_query(
        self,
        user_input: str,
        user_id: str,
        conversation_id: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Process query in background for comprehensive response building.
        This method is designed to run in parallel with fast conversational responses.
        """
        try:
            logger.info(f"Starting background processing for conversation {conversation_id}")
            start_time = time.time()

            # Domain classification for specialized agent selection
            domain = await classify_domain(
                query=user_input,
                history=conversation_history
            )

            logger.info(f"Background processing classified domain: {domain.value}")

            # Generate comprehensive response using specialized agents
            comprehensive_response = ""
            processing_metadata = {
                'domain': domain.value,
                'start_time': start_time,
                'conversation_id': conversation_id
            }

            # Try specialized agents first
            specialized_response = await self._try_specialized_agent(
                domain=domain,
                user_input=user_input,
                user_id=user_id,
                conversation_history=conversation_history,
                emotion_context=emotion_context,
                memory_context=memory_context
            )

            if specialized_response:
                comprehensive_response = specialized_response
                processing_metadata['agent_type'] = 'specialized'
            else:
                # Fallback to general response with enhanced context
                comprehensive_response = await self._generate_enhanced_general_response(
                    user_input=user_input,
                    conversation_history=conversation_history,
                    emotion_context=emotion_context,
                    domain=domain
                )
                processing_metadata['agent_type'] = 'general_enhanced'

            processing_time = (time.time() - start_time) * 1000
            processing_metadata['processing_time_ms'] = processing_time

            # Store result for potential retrieval
            result = {
                'content': comprehensive_response,
                'metadata': processing_metadata,
                'timestamp': self._get_timestamp(),
                'quality_score': self._assess_response_quality(comprehensive_response, user_input)
            }

            self._processing_results[conversation_id] = result

            # Also update shared conversation state if available
            state_manager = self._get_state_manager()
            if state_manager:
                try:
                    await state_manager.update_conversation_state(
                        conversation_id,
                        comprehensive_response_ready=True,
                        background_result=result
                    )
                except Exception as e:
                    logger.warning(f"Could not update shared conversation state: {e}")

            logger.info(f"Background processing completed for {conversation_id} in {processing_time:.1f}ms")
            return result

        except Exception as e:
            logger.error(f"Error in background processing: {e}")
            return {
                'content': '',
                'error': str(e),
                'metadata': {'conversation_id': conversation_id},
                'timestamp': self._get_timestamp()
            }

    async def _generate_enhanced_general_response(
        self,
        user_input: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None,
        domain: Domain = Domain.GENERAL
    ) -> str:
        """Generate enhanced general response with domain awareness."""
        try:
            # Build enhanced system prompt
            system_prompt = self._build_enhanced_system_prompt(domain, emotion_context)

            # Build conversation messages
            messages = self._build_conversation_messages(
                system_prompt, user_input, conversation_history
            )

            # Generate response
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=500,  # Longer for comprehensive responses
                temperature=0.7
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            logger.error(f"Error generating enhanced general response: {e}")
            return "I'd be happy to help you with that! Let me provide you with a comprehensive response."

    def _build_enhanced_system_prompt(self, domain: Domain, emotion_context: Optional[Dict] = None) -> str:
        """Build enhanced system prompt for comprehensive responses."""
        base_prompt = self.agent_personas.get(domain, self._get_general_persona())

        # Add comprehensive response guidance
        base_prompt += """

COMPREHENSIVE RESPONSE MODE:
- Provide detailed, thorough responses
- Include specific examples and actionable advice
- Structure your response clearly with sections if helpful
- Be more detailed than usual while maintaining warmth
- Anticipate follow-up questions and address them
"""

        # Add emotion-aware enhancements
        if emotion_context and emotion_context.get('emotions'):
            dominant_emotion = max(emotion_context['emotions'], key=lambda x: x.get('score', 0))
            emotion_name = dominant_emotion.get('name', '').lower()

            if emotion_name in ['sad', 'frustrated', 'angry']:
                base_prompt += "\nEMOTION GUIDANCE: The user is going through a difficult time. Be extra supportive, understanding, and provide gentle, practical guidance.\n"
            elif emotion_name in ['happy', 'excited', 'joy']:
                base_prompt += "\nEMOTION GUIDANCE: The user is in a positive mood. Match their energy and enthusiasm while providing comprehensive help.\n"
            elif emotion_name in ['confused', 'uncertain']:
                base_prompt += "\nEMOTION GUIDANCE: The user seems uncertain. Provide clear, step-by-step guidance and reassurance.\n"

        return base_prompt

    def _assess_response_quality(self, response: str, user_input: str) -> float:
        """Assess the quality of a response for background processing."""
        try:
            # Simple quality metrics
            quality_score = 0.5  # Base score

            # Length appropriateness (comprehensive responses should be substantial)
            if len(response) > 100:
                quality_score += 0.2
            if len(response) > 300:
                quality_score += 0.1

            # Relevance indicators (simple keyword matching)
            user_words = set(user_input.lower().split())
            response_words = set(response.lower().split())
            overlap = len(user_words.intersection(response_words))
            if overlap > 0:
                quality_score += min(0.2, overlap * 0.05)

            # Structure indicators (lists, sections, etc.)
            if any(indicator in response for indicator in ['•', '-', '1.', '2.', '\n\n']):
                quality_score += 0.1

            return min(1.0, quality_score)

        except Exception:
            return 0.5  # Default score

    def get_background_result(self, conversation_id: str) -> Optional[Dict]:
        """Get background processing result for a conversation."""
        return self._processing_results.get(conversation_id)

    def cleanup_background_results(self, max_age_minutes: int = 30):
        """Clean up old background processing results."""
        import time
        cutoff_time = time.time() - (max_age_minutes * 60)

        to_remove = []
        for conv_id, result in self._processing_results.items():
            try:
                # Parse timestamp to check age
                result_time = result.get('metadata', {}).get('start_time', time.time())
                if result_time < cutoff_time:
                    to_remove.append(conv_id)
            except Exception:
                to_remove.append(conv_id)  # Remove if we can't parse timestamp

        for conv_id in to_remove:
            del self._processing_results[conv_id]

        if to_remove:
            logger.info(f"Cleaned up {len(to_remove)} old background results")
    
    async def _generate_agent_response(
        self,
        domain: Domain,
        user_input: str,
        user_id: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None,
        streaming: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate response using the appropriate specialized agent."""

        # Try to use specialized agents first
        specialized_response = await self._try_specialized_agent(
            domain=domain,
            user_input=user_input,
            user_id=user_id,
            conversation_history=conversation_history,
            emotion_context=emotion_context,
            memory_context=memory_context
        )

        if specialized_response:
            # Stream the specialized agent response
            yield {
                "type": "response_chunk",
                "content": specialized_response,
                "domain": domain.value,
                "timestamp": self._get_timestamp(),
                "source": "specialized_agent"
            }

            yield {
                "type": "response_complete",
                "full_content": specialized_response,
                "domain": domain.value,
                "timestamp": self._get_timestamp(),
                "source": "specialized_agent"
            }
            return

        # Handle general conversation with orchestrator
        if domain == Domain.GENERAL:
            logger.info(f"Using orchestrator for general conversation")
            async for chunk in self._generate_orchestrator_response(
                user_input=user_input,
                user_id=user_id,
                conversation_history=conversation_history,
                emotion_context=emotion_context,
                memory_context=memory_context,
                domain=domain
            ):
                yield chunk
            return

        # No specialized agent available for this domain
        logger.error(f"No specialized agent available for domain: {domain}")
        yield {
            "type": "error",
            "content": f"Sorry, I don't have a specialized agent for {domain.value} requests yet.",
            "domain": domain.value,
            "timestamp": self._get_timestamp()
        }
    
    def _build_system_prompt(
        self,
        domain: Domain,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None,
        user_id: str = None
    ) -> str:
        """Build system prompt with persona and context."""
        
        # Start with agent persona
        prompt_parts = [self.agent_personas.get(domain, self.agent_personas[Domain.GENERAL])]
        
        # Add emotion context if available
        if emotion_context and emotion_context.get('emotions'):
            emotion_guidance = self._build_emotion_guidance(emotion_context)
            prompt_parts.append(f"\nEMOTION CONTEXT:\n{emotion_guidance}")
        
        # Add memory context if available
        if memory_context:
            memory_guidance = self._build_memory_guidance(memory_context)
            prompt_parts.append(f"\nMEMORY CONTEXT:\n{memory_guidance}")
        
        # Add user context
        if user_id:
            prompt_parts.append(f"\nUSER ID: {user_id}")
        
        return "\n".join(prompt_parts)
    
    def _build_emotion_guidance(self, emotion_context) -> str:
        """Build emotion-aware guidance for the agent."""
        # Handle both EmotionAnalysisResult objects and dictionaries
        from chat.services.hume_service import EmotionAnalysisResult

        if isinstance(emotion_context, EmotionAnalysisResult):
            # Convert EmotionAnalysisResult to dict format
            emotions = [{'name': e.name, 'score': e.score} for e in emotion_context.emotions]
            primary_emotion = emotion_context.primary_emotion
            emotion_intensity = emotion_context.emotion_intensity
        elif isinstance(emotion_context, dict):
            emotions = emotion_context.get('emotions', [])
            primary_emotion = emotion_context.get('primary_emotion', 'neutral')
            emotion_intensity = emotion_context.get('emotion_intensity', 0.5)
        else:
            return "No specific emotional context detected."

        if not emotions:
            return f"User's emotional state: {primary_emotion} (intensity: {emotion_intensity:.2f})"

        # Find dominant emotion
        dominant_emotion = max(emotions, key=lambda x: x.get('score', 0))
        emotion_name = dominant_emotion.get('name', '').lower()
        emotion_score = dominant_emotion.get('score', 0)
        
        guidance = f"User's current emotional state: {emotion_name} (confidence: {emotion_score:.2f})\n"
        
        # Emotion-specific response guidance
        if emotion_name in ['sadness', 'disappointment', 'grief']:
            guidance += "- Respond with empathy and gentle support\n- Offer comfort without being overly cheerful\n- Listen actively and validate their feelings"
        elif emotion_name in ['excitement', 'joy', 'enthusiasm']:
            guidance += "- Match their positive energy and enthusiasm\n- Celebrate with them and share in their excitement\n- Keep the conversation upbeat and engaging"
        elif emotion_name in ['anger', 'frustration', 'stress']:
            guidance += "- Remain calm and patient\n- Acknowledge their frustration\n- Offer practical help or solutions if appropriate"
        elif emotion_name in ['curiosity', 'interest']:
            guidance += "- Feed their curiosity with detailed information\n- Encourage exploration and learning\n- Provide engaging and informative responses"
        else:
            guidance += "- Respond naturally based on conversation context\n- Maintain warm, supportive tone"
        
        return guidance
    
    def _build_memory_guidance(self, memory_context: Dict) -> str:
        """Build memory-aware guidance for personalization."""
        memories = memory_context.get('memories', [])
        if not memories:
            return "No specific memories retrieved for this conversation."
        
        guidance = "Relevant memories about this user:\n"
        for memory in memories[:3]:  # Top 3 most relevant
            memory_text = memory.get('text', '')[:100]
            memory_type = memory.get('type', 'general')
            guidance += f"- {memory_text}... (type: {memory_type})\n"
        
        guidance += "\nUse these memories to personalize your response and show continuity in the relationship."
        return guidance
    
    def _build_conversation_messages(
        self,
        system_prompt: str,
        user_input: str,
        conversation_history: List[Dict] = None
    ) -> List[Dict[str, str]]:
        """Build conversation messages for the LLM."""
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add recent conversation history
        if conversation_history:
            for msg in conversation_history[-5:]:  # Last 5 messages
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                if role in ['user', 'assistant'] and content:
                    messages.append({"role": role, "content": content})
        
        # Add current user input
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.now().isoformat()

    async def _try_specialized_agent(
        self,
        domain: Domain,
        user_input: str,
        user_id: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None
    ) -> Optional[str]:
        """Try to use a specialized agent for the domain."""
        try:
            # Import specialized agents
            from agents.example_travel_agent import TravelPlanningAgent
            from agents.code_review_agent import CodeReviewAgent
            from agents.fitness_coach_agent import FitnessCoachAgent
            from agents.creative_writing_agent import CreativeWritingAgent
            from agents.business_strategy_agent import BusinessStrategyAgent

            # Map domains to agents - specialized agents only for specific domains
            agent_map = {
                Domain.TRAVEL: TravelPlanningAgent,
                Domain.CODE: CodeReviewAgent,
                Domain.DEV: CodeReviewAgent,  # Map DEV domain to CodeReviewAgent
                Domain.FITNESS: FitnessCoachAgent,
                Domain.CREATIVE: CreativeWritingAgent,
                Domain.BUSINESS: BusinessStrategyAgent,
                # General conversation should NOT use specialized agents
                # Domain.GENERAL: None,  # Let orchestrator handle general conversation
                Domain.WEB: CodeReviewAgent,  # Use code agent for web/technical searches
                Domain.LEARNING: CreativeWritingAgent,  # Use creative agent for educational content
                Domain.MUSIC: CreativeWritingAgent,  # Use creative agent for music recommendations
                Domain.TRIVIA: CreativeWritingAgent,  # Use creative agent for trivia/entertainment
            }

            agent_class = agent_map.get(domain)
            if not agent_class:
                return None

            # Create agent instance
            agent = agent_class()

            # Prepare request data
            request_data = {
                'user_input': user_input,
                'user_id': user_id,
                'conversation_history': conversation_history or [],
                'emotion_context': emotion_context or {},
                'memories': memory_context.get('memories', []) if memory_context else [],
                'conversation_context': {
                    'domain': domain.value,
                    'emotion_context': emotion_context,
                    'memory_context': memory_context
                }
            }

            # Process request with specialized agent
            result = agent.process_request(request_data)

            # Agents now return formatted strings directly
            if isinstance(result, str):
                return result
            else:
                # Fallback for old format agents
                return self._format_agent_response(result, domain)

        except Exception as e:
            logger.error(f"Error calling specialized agent for {domain}: {e}")
            return None

    def _format_agent_response(self, agent_result: Dict[str, Any], domain: Domain) -> str:
        """Format specialized agent response for user consumption."""
        try:
            if domain == Domain.TRAVEL:
                return self._format_travel_response(agent_result)
            elif domain == Domain.CODE:
                return self._format_code_response(agent_result)
            elif domain == Domain.FITNESS:
                return self._format_fitness_response(agent_result)
            elif domain == Domain.CREATIVE:
                return self._format_creative_response(agent_result)
            elif domain == Domain.BUSINESS:
                return self._format_business_response(agent_result)
            else:
                # Generic formatting
                return f"Here's what I found:\n\n{agent_result.get('content', str(agent_result))}"
        except Exception as e:
            logger.error(f"Error formatting agent response: {e}")
            return "I've analyzed your request and have some recommendations, but had trouble formatting the response."

    def _format_travel_response(self, result: Dict[str, Any]) -> str:
        """Format travel agent response."""
        travel_plan = result.get('travel_plan', {})

        response = "🧳 **TRAVEL PLAN CREATED**\n\n"

        # Destinations
        destinations = travel_plan.get('destinations', [])
        if destinations:
            response += f"📍 **DESTINATIONS:** {', '.join(destinations)}\n\n"

        # Budget breakdown
        budget = travel_plan.get('budget_breakdown', {})
        if budget:
            response += "💰 **BUDGET BREAKDOWN:**\n"
            response += f"• Total: ${budget.get('total', 0):,.0f}\n"
            response += f"• Daily Average: ${budget.get('daily_average', 0):,.0f}\n"
            response += f"• Accommodation: ${budget.get('accommodation', 0):,.0f}\n"
            response += f"• Food: ${budget.get('food', 0):,.0f}\n"
            response += f"• Transportation: ${budget.get('transportation', 0):,.0f}\n"
            response += f"• Activities: ${budget.get('activities', 0):,.0f}\n\n"

        # Accommodations
        accommodations = travel_plan.get('accommodations', [])
        if accommodations:
            response += "🏨 **ACCOMMODATIONS:**\n"
            for acc in accommodations[:3]:  # Show top 3
                response += f"• {acc.get('type', 'Hotel').title()}: {acc.get('price_range', 'Price varies')}\n"
            response += "\n"

        # Activities
        activities = travel_plan.get('activities', [])
        if activities:
            response += "🎯 **RECOMMENDED ACTIVITIES:**\n"
            for activity in activities[:5]:  # Show top 5
                response += f"• {activity.get('activity', activity.get('description', 'Activity'))}\n"
            response += "\n"

        # Transportation
        transportation = travel_plan.get('transportation', {})
        if transportation:
            response += "🚗 **TRANSPORTATION:**\n"
            response += f"• International: {transportation.get('international', 'Various options')}\n"
            response += f"• Local: {', '.join(transportation.get('local', ['Public transport']))}\n"
            response += f"• Estimated Cost: {transportation.get('estimated_cost', 'Varies')}\n\n"

        # Recommendations
        recommendations = travel_plan.get('recommendations', [])
        if recommendations:
            response += "💡 **IMPORTANT RECOMMENDATIONS:**\n"
            for rec in recommendations[:5]:  # Show top 5
                response += f"• {rec}\n"
            response += "\n"

        # Next steps
        next_steps = result.get('next_steps', [])
        if next_steps:
            response += "📋 **NEXT STEPS:**\n"
            for step in next_steps[:3]:  # Show top 3
                response += f"• {step}\n"

        return response

    def _format_code_response(self, result: Dict[str, Any]) -> str:
        """Format code review agent response."""
        code_review = result.get('code_review', {})

        response = "💻 **CODE REVIEW COMPLETE**\n\n"

        # Overall score
        overall_score = code_review.get('overall_score', 0)
        if overall_score:
            response += f"📊 **OVERALL SCORE:** {overall_score:.1f}/10\n\n"

        # Issues found
        issues = code_review.get('issues', [])
        if issues:
            response += "🚨 **ISSUES FOUND:**\n"
            for issue in issues[:5]:  # Show top 5 issues
                severity = issue.get('severity', 'medium').upper()
                category = issue.get('category', 'general').title()
                description = issue.get('description', 'Issue found')
                suggestion = issue.get('suggestion', 'Consider reviewing this area')

                response += f"• **{severity}** - {category}: {description}\n"
                response += f"  💡 {suggestion}\n"
            response += "\n"

        # Security analysis
        security = code_review.get('security_analysis', {})
        if security:
            security_score = security.get('security_score', 0)
            vulnerabilities = security.get('vulnerabilities', [])

            response += f"🔒 **SECURITY ANALYSIS:** {security_score:.1f}/10\n"
            if vulnerabilities:
                for vuln in vulnerabilities[:3]:  # Show top 3
                    response += f"• {vuln.get('type', 'Security issue').title()}: {vuln.get('description', 'Security concern found')}\n"
            response += "\n"

        # Performance analysis
        performance = code_review.get('performance_analysis', {})
        if performance:
            perf_score = performance.get('performance_score', 0)
            bottlenecks = performance.get('bottlenecks', [])

            response += f"⚡ **PERFORMANCE ANALYSIS:** {perf_score:.1f}/10\n"
            if bottlenecks:
                for bottleneck in bottlenecks[:3]:  # Show top 3
                    response += f"• {bottleneck.get('type', 'Performance issue').title()}: {bottleneck.get('description', 'Performance concern')}\n"
            response += "\n"

        # Suggestions
        suggestions = code_review.get('suggestions', [])
        if suggestions:
            response += "💡 **IMPROVEMENT SUGGESTIONS:**\n"
            for suggestion in suggestions[:5]:  # Show top 5
                title = suggestion.get('title', 'Improvement')
                description = suggestion.get('description', 'Consider this improvement')
                priority = suggestion.get('priority', 'medium').upper()

                response += f"• **{priority}** - {title}: {description}\n"
            response += "\n"

        # Best practices
        best_practices = code_review.get('best_practices', [])
        if best_practices:
            response += "✅ **BEST PRACTICES TO FOLLOW:**\n"
            for practice in best_practices[:5]:  # Show top 5
                response += f"• {practice}\n"
            response += "\n"

        # Next steps
        next_steps = result.get('next_steps', [])
        if next_steps:
            response += "📋 **RECOMMENDED NEXT STEPS:**\n"
            for step in next_steps[:3]:  # Show top 3
                response += f"• {step}\n"

        return response

    def _format_fitness_response(self, result: Dict[str, Any]) -> str:
        """Format fitness coach agent response."""
        fitness_plan = result.get('fitness_plan', {})

        response = "💪 **FITNESS PLAN CREATED**\n\n"

        # Program overview
        program = fitness_plan.get('program_overview', {})
        if program:
            response += "🎯 **PROGRAM OVERVIEW:**\n"
            response += f"• Goal: {program.get('goal', 'Fitness improvement')}\n"
            response += f"• Duration: {program.get('duration', '12 weeks')}\n"
            response += f"• Level: {program.get('level', 'Intermediate')}\n\n"

        # Workout schedule
        schedule = fitness_plan.get('workout_schedule', {})
        if schedule:
            response += "📅 **WORKOUT SCHEDULE:**\n"
            for day, workout in schedule.items():
                if isinstance(workout, dict):
                    response += f"• {day.title()}: {workout.get('focus', 'Training')}\n"
                else:
                    response += f"• {day.title()}: {workout}\n"
            response += "\n"

        # Exercise library
        exercises = fitness_plan.get('exercise_library', [])
        if exercises:
            response += "🏋️ **KEY EXERCISES:**\n"
            for exercise in exercises[:5]:  # Show top 5
                name = exercise.get('name', 'Exercise')
                sets = exercise.get('sets', '3')
                reps = exercise.get('reps', '10')
                response += f"• {name}: {sets} sets x {reps} reps\n"
            response += "\n"

        # Nutrition guidelines
        nutrition = fitness_plan.get('nutrition_guidelines', {})
        if nutrition:
            response += "🥗 **NUTRITION GUIDELINES:**\n"
            calories = nutrition.get('daily_calories', 'Varies')
            protein = nutrition.get('protein_grams', 'Varies')
            response += f"• Daily Calories: {calories}\n"
            response += f"• Protein Target: {protein}g\n"

            meal_tips = nutrition.get('meal_tips', [])
            if meal_tips:
                response += "• Key Tips:\n"
                for tip in meal_tips[:3]:
                    response += f"  - {tip}\n"
            response += "\n"

        # Safety notes
        safety = fitness_plan.get('safety_notes', [])
        if safety:
            response += "⚠️ **SAFETY REMINDERS:**\n"
            for note in safety[:3]:  # Show top 3
                response += f"• {note}\n"
            response += "\n"

        # Next steps
        next_steps = result.get('next_steps', [])
        if next_steps:
            response += "📋 **NEXT STEPS:**\n"
            for step in next_steps[:3]:  # Show top 3
                response += f"• {step}\n"

        return response

    def _format_creative_response(self, result: Dict[str, Any]) -> str:
        """Format creative writing agent response."""
        # This would be implemented based on the creative writing agent structure
        return f"✨ **CREATIVE CONTENT GENERATED**\n\n{result.get('content', str(result))}"

    def _format_business_response(self, result: Dict[str, Any]) -> str:
        """Format business strategy agent response."""
        # This would be implemented based on the business strategy agent structure
        return f"📊 **BUSINESS STRATEGY ANALYSIS**\n\n{result.get('content', str(result))}"

    async def _generate_orchestrator_response(
        self,
        user_input: str,
        user_id: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None,
        domain: Domain = Domain.GENERAL
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate response using the orchestrator for general conversation."""
        try:
            # Build system prompt for general conversation
            system_prompt = self._build_system_prompt(
                domain=domain,
                emotion_context=emotion_context,
                memory_context=memory_context,
                user_id=user_id
            )

            # Build conversation messages
            messages = [{"role": "system", "content": system_prompt}]

            # Add conversation history
            if conversation_history:
                for msg in conversation_history[-5:]:  # Last 5 messages for context
                    role = "user" if msg.get('sender_type') == 'user' else "assistant"
                    messages.append({"role": role, "content": msg.get('content', '')})

            # Add current user input
            messages.append({"role": "user", "content": user_input})

            # Generate streaming response using OpenAI
            import openai
            client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)

            stream = client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                temperature=0.7,
                max_tokens=1000,
                stream=True
            )

            response_content = ""
            for chunk in stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    response_content += content

                    yield {
                        "type": "response_chunk",
                        "content": content,
                        "domain": domain.value,
                        "timestamp": self._get_timestamp(),
                        "source": "orchestrator"
                    }

            # Final response metadata
            yield {
                "type": "response_complete",
                "full_content": response_content,
                "domain": domain.value,
                "timestamp": self._get_timestamp(),
                "source": "orchestrator"
            }

        except Exception as e:
            logger.error(f"Error generating orchestrator response: {e}")
            yield {
                "type": "error",
                "content": "I'm having trouble generating a response right now. Please try again.",
                "domain": domain.value,
                "timestamp": self._get_timestamp()
            }