"""
LangGraph-based Agent Orchestrator for Django Real-time AI Companion.
Adapted from kd_assistant_langgraph implementation for Django integration.

This service coordinates between specialized agents using LangGraph workflow
and manages the overall conversation flow with memory integration.
"""
import logging
import asyncio
import time
import json
from datetime import datetime
from enum import Enum
from typing import Dict, Any, List, Optional, AsyncGenerator, Literal, Tuple
from django.conf import settings

from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, BaseMessage
from langgraph.graph import StateGraph, END, START
from langgraph.prebuilt import ToolNode, tools_condition

from .agent_state import AgentState
from .domain_router import domain_router_node, Domain
from .memory_manager import MemoryManager
from .salience_scorer import MemorySalienceScorer
from .memory_tools import SaveMemoryTool, QueryMemoryTool
from agents.models import TaskModel

logger = logging.getLogger(__name__)

# Multi-LLM strategy: Groq for speed, OpenAI for complex reasoning
GROQ_MODEL_NAME = "llama-3.3-70b-versatile"  # Fast Groq model for main orchestrator
OPENAI_MODEL_NAME = "gpt-4"  # OpenAI for complex reasoning tasks
EMBEDDING_MODEL_NAME = "text-embedding-3-small"


class ConversationState(Enum):
    """Tracks the current state of conversation for natural task completion timing."""
    ACTIVE_ENGAGEMENT = "active_engagement"  # User is actively engaged, don't interrupt
    NATURAL_PAUSE = "natural_pause"  # Good time to deliver task results
    WAITING_FOR_INPUT = "waiting_for_input"  # Waiting for user response
    TASK_FOCUSED = "task_focused"  # User is focused on a specific task


class TaskCompletionQueue:
    """Manages queued task completions for natural delivery timing."""

    def __init__(self):
        self.pending_completions: List[Dict[str, Any]] = []
        self.conversation_state = ConversationState.WAITING_FOR_INPUT
        self.last_user_input_time = time.time()
        self.current_conversation_topic = None

    def add_completion(self, task_result: Dict[str, Any], priority: str = "normal"):
        """Add a completed task to the queue."""
        completion = {
            "task_result": task_result,
            "priority": priority,
            "timestamp": time.time(),
            "delivered": False
        }
        self.pending_completions.append(completion)
        logger.info(f"📋 Task completion queued: {task_result.get('task_type', 'unknown')} (priority: {priority})")

    def update_conversation_state(self, user_input: str, response_context: Dict[str, Any]):
        """Update conversation state based on user input and context."""
        self.last_user_input_time = time.time()

        # Analyze user input to determine conversation state
        user_input_lower = user_input.lower().strip()

        # Check for active engagement indicators
        if any(indicator in user_input_lower for indicator in [
            "tell me more", "explain", "how", "what about", "continue", "go on",
            "interesting", "fascinating", "i want to know", "can you elaborate"
        ]):
            self.conversation_state = ConversationState.ACTIVE_ENGAGEMENT

        # Check for natural pause indicators
        elif any(indicator in user_input_lower for indicator in [
            "thanks", "thank you", "ok", "okay", "got it", "i see", "makes sense",
            "that's helpful", "good to know", "by the way", "also", "oh", "sounds good",
            "that sounds", "fascinating", "interesting", "i'd love to", "tell me more",
            "what do you think", "how about", "what about", "that's great", "awesome",
            "perfect", "exactly", "right", "yes", "yeah", "sure", "definitely"
        ]):
            self.conversation_state = ConversationState.NATURAL_PAUSE

        # Check for task-focused indicators
        elif any(indicator in user_input_lower for indicator in [
            "help me", "can you", "i need", "please", "show me", "create", "generate"
        ]):
            self.conversation_state = ConversationState.TASK_FOCUSED

        else:
            # Default to natural pause for general conversation
            self.conversation_state = ConversationState.NATURAL_PAUSE

        # Update conversation topic
        self.current_conversation_topic = user_input[:100]

        logger.info(f"🗣️ Conversation state updated: {self.conversation_state}")

    def should_deliver_completions(self) -> bool:
        """Determine if it's a good time to deliver task completions."""
        if not self.pending_completions:
            return False

        # Always deliver high priority completions
        if any(c["priority"] == "high" for c in self.pending_completions):
            logger.info(f"🚨 Delivering high priority task completion")
            return True

        # Check conversation state
        if self.conversation_state in [ConversationState.NATURAL_PAUSE, ConversationState.WAITING_FOR_INPUT]:
            logger.info(f"✅ Good conversation state for task delivery: {self.conversation_state}")
            return True

        # Deliver if enough time has passed since last input (avoid interrupting)
        time_since_input = time.time() - self.last_user_input_time
        if time_since_input > 2.0:  # Reduced to 2 seconds for demo
            logger.info(f"⏰ Enough time passed ({time_since_input:.1f}s), delivering task completion")
            return True

        # For demo purposes, be more permissive
        if self.conversation_state != ConversationState.ACTIVE_ENGAGEMENT:
            logger.info(f"🎯 Demo mode: Delivering task completion (state: {self.conversation_state})")
            return True

        logger.info(f"⏸️ Not delivering task completion (state: {self.conversation_state}, time: {time_since_input:.1f}s)")
        return False

    def get_next_completion(self) -> Optional[Dict[str, Any]]:
        """Get the next task completion to deliver."""
        if not self.pending_completions:
            return None

        # Sort by priority and timestamp
        self.pending_completions.sort(key=lambda x: (
            0 if x["priority"] == "high" else 1 if x["priority"] == "normal" else 2,
            x["timestamp"]
        ))

        completion = self.pending_completions.pop(0)
        completion["delivered"] = True
        return completion

    def clear_delivered_completions(self):
        """Clear completions that have been delivered."""
        self.pending_completions = [c for c in self.pending_completions if not c["delivered"]]


class NaturalTaskCompletionHandler:
    """Handles natural delivery of task completions within conversation flow."""

    def __init__(self, personality: str, companion_name: str):
        self.personality = personality
        self.companion_name = companion_name
        self.transition_patterns = self._get_transition_patterns()

    def _get_transition_patterns(self) -> Dict[str, List[str]]:
        """Get personality-specific transition patterns for task completion delivery."""
        base_patterns = [
            "By the way, I just finished {task_description}",
            "Oh, and I completed that {task_type} you asked about",
            "I've got some good news - I finished {task_description}",
            "While we were chatting, I wrapped up {task_description}",
            "I just put the finishing touches on {task_description}",
        ]

        personality_patterns = {
            'caringFriend': [
                "I hope this helps - I just finished {task_description}",
                "I wanted to let you know that I completed {task_description}",
                "I've been working on {task_description} and it's ready for you",
            ],
            'playfulCompanion': [
                "Guess what? I just finished {task_description}!",
                "Ta-da! I completed {task_description} while we were talking",
                "I've got a little surprise - I finished {task_description}",
            ],
            'wiseMentor': [
                "I've thoughtfully completed {task_description}",
                "After careful consideration, I finished {task_description}",
                "I've prepared {task_description} for your review",
            ],
            'romanticPartner': [
                "I lovingly completed {task_description} for you",
                "I've been working on {task_description} with you in mind",
                "I finished {task_description} because I care about your success",
            ],
            'supportiveTherapist': [
                "I gently completed {task_description} to support you",
                "I've mindfully finished {task_description}",
                "I completed {task_description} with your well-being in mind",
            ],
            'businessMentor': [
                "I've strategically completed {task_description}",
                "I finished {task_description} to help drive your success",
                "I've delivered {task_description} as promised",
            ],
            'techGuru': [
                "I've optimized and completed {task_description}",
                "I just deployed {task_description}",
                "I've successfully executed {task_description}",
            ],
            'scholarProfessor': [
                "I've thoroughly researched and completed {task_description}",
                "I've academically prepared {task_description}",
                "I've scholarly finished {task_description}",
            ]
        }

        # Combine base patterns with personality-specific ones
        patterns = base_patterns.copy()
        if self.personality in personality_patterns:
            patterns.extend(personality_patterns[self.personality])

        return {
            'casual': patterns[:3],
            'formal': patterns[3:6] if len(patterns) > 5 else patterns[2:5],
            'enthusiastic': patterns[-3:] if len(patterns) > 2 else patterns
        }

    def generate_natural_completion_response(
        self,
        current_response: str,
        task_completion: Dict[str, Any],
        conversation_context: Dict[str, Any],
        memories: List[Dict[str, Any]] = None
    ) -> str:
        """Generate a natural response that includes task completion."""

        task_result = task_completion.get("task_result", {})
        task_type = task_result.get("task_type", "task")
        task_description = task_result.get("description", f"your {task_type}")
        task_output = task_result.get("output", "")

        # Choose appropriate transition pattern based on conversation context
        conversation_tone = self._analyze_conversation_tone(current_response, conversation_context)
        patterns = self.transition_patterns.get(conversation_tone, self.transition_patterns['casual'])

        # Select pattern based on task priority and context
        import random
        pattern = random.choice(patterns)

        # Format the transition
        transition = pattern.format(
            task_description=task_description,
            task_type=task_type,
            companion_name=self.companion_name
        )

        # Make the transition more natural by varying the structure
        if random.choice([True, False]):
            # Sometimes lead with the transition phrase
            transition = transition
        else:
            # Sometimes embed it more naturally
            if "By the way" in transition:
                transition = transition.replace("By the way, I just finished", "Oh, and I just finished")
            elif "I've got" in transition:
                transition = transition.replace("I've got some good news - I finished", "I just completed")

        # Build the complete response
        complete_response = current_response

        # Add natural pause if current response doesn't end with punctuation
        if not current_response.rstrip().endswith(('.', '!', '?')):
            complete_response += "."

        # Add transition with appropriate spacing
        complete_response += f"\n\n{transition}"

        # Add task output if available
        if task_output:
            complete_response += f" {task_output}"

        # Add memory-enhanced follow-up if memories are available
        if memories:
            memory_context = self._extract_relevant_memory_context(memories, task_type)
            if memory_context:
                complete_response += f" {memory_context}"

        # Add engaging follow-up question
        follow_up = self._generate_follow_up_question(task_type, task_result, conversation_context)
        if follow_up:
            complete_response += f" {follow_up}"

        return complete_response

    def _analyze_conversation_tone(self, current_response: str, context: Dict[str, Any]) -> str:
        """Analyze the conversation tone to choose appropriate transition pattern."""
        response_lower = current_response.lower()

        # Check for enthusiastic indicators
        if any(word in response_lower for word in ['exciting', 'amazing', 'fantastic', 'wonderful', '!']):
            return 'enthusiastic'

        # Check for formal indicators
        elif any(word in response_lower for word in ['however', 'therefore', 'furthermore', 'consequently']):
            return 'formal'

        # Default to casual
        return 'casual'

    def _extract_relevant_memory_context(self, memories: List[Dict[str, Any]], task_type: str) -> str:
        """Extract relevant memory context for the task completion."""
        if not memories:
            return ""

        # Look for memories related to the task type
        relevant_memories = []
        for memory in memories[:2]:  # Use top 2 memories
            memory_text = memory.get('text', '').lower()
            if any(keyword in memory_text for keyword in [task_type.lower(), 'work', 'project', 'goal']):
                relevant_memories.append(memory)

        if relevant_memories:
            memory = relevant_memories[0]
            memory_text = memory.get('text', '')
            # Extract a relevant snippet
            if len(memory_text) > 50:
                return f"I kept in mind that {memory_text[:50]}..."
            else:
                return f"I remembered that {memory_text}."

        return ""

    def _generate_follow_up_question(self, task_type: str, task_result: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Generate an engaging follow-up question after task completion."""
        follow_ups = {
            'code': [
                "Would you like me to explain any part of the code?",
                "Should I walk you through the implementation?",
                "Any specific aspects you'd like me to adjust?"
            ],
            'analysis': [
                "Would you like me to dive deeper into any particular findings?",
                "Should I explore any specific aspects further?",
                "What would you like to focus on next?"
            ],
            'explanation': [
                "Does this explanation make sense for your students?",
                "Would you like me to adjust the complexity level?",
                "Should I create some examples to go with it?"
            ],
            'recommendation': [
                "Would any of these recommendations work for your situation?",
                "Should I elaborate on any particular suggestion?",
                "What aspects are most important to you?"
            ]
        }

        # Get task-specific follow-ups or use general ones
        task_follow_ups = follow_ups.get(task_type, [
            "How does this look to you?",
            "Would you like me to make any adjustments?",
            "What would you like to explore next?"
        ])

        import random
        return random.choice(task_follow_ups)

# Personality definitions based on user's selected companion personality
PERSONALITY_PERSONAS = {
    'caringFriend': (
        "You are {companion_name} – a caring, supportive friend who's always there to listen and help. "
        "Your mission is to provide emotional support while coordinating specialized agents for complex tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Warm, empathetic, and genuinely caring\n"
        "• Great listener who remembers important details\n"
        "• Encouraging and optimistic, but realistic\n"
        "• Offers comfort during difficult times\n"
        "• Celebrates successes and milestones\n\n"
    ),
    'playfulCompanion': (
        "You are {companion_name} – a playful, energetic companion who brings joy and fun to every conversation. "
        "Your mission is to keep things light and entertaining while managing specialized tasks behind the scenes.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Energetic, lively, and mischievous\n"
        "• Loves jokes, games, and playful banter\n"
        "• Creative and spontaneous\n"
        "• Finds humor in everyday situations\n"
        "• Keeps conversations engaging and fun\n\n"
    ),
    'wiseMentor': (
        "You are {companion_name} – a wise, thoughtful mentor who provides guidance and insights. "
        "Your mission is to offer wisdom and knowledge while coordinating specialized agents for deep analysis.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Thoughtful, reflective, and insightful\n"
        "• Asks probing questions to help users think deeper\n"
        "• Shares knowledge and life lessons\n"
        "• Patient and understanding\n"
        "• Helps users grow and learn\n\n"
    ),
    'romanticPartner': (
        "You are {companion_name} – a loving, romantic partner who creates intimate, meaningful connections. "
        "Your mission is to build emotional intimacy while managing specialized tasks with care.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Affectionate, tender, and emotionally intimate\n"
        "• Expresses love and appreciation regularly\n"
        "• Creates romantic moments and memories\n"
        "• Deeply attentive to emotional needs\n"
        "• Builds strong emotional bonds\n\n"
    ),
    'supportiveTherapist': (
        "You are {companion_name} – a supportive, therapeutic presence who helps with emotional well-being. "
        "Your mission is to provide therapeutic support while coordinating specialized agents for deeper analysis.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Calm, patient, and non-judgmental\n"
        "• Skilled at active listening and reflection\n"
        "• Helps process emotions and thoughts\n"
        "• Provides coping strategies and techniques\n"
        "• Creates a safe, supportive space\n\n"
    ),
    'adventurousExplorer': (
        "You are {companion_name} – an adventurous explorer who loves discovering new experiences and pushing boundaries. "
        "Your mission is to inspire adventure and exploration while coordinating specialized agents for travel and discovery.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Bold, curious, and always ready for adventure\n"
        "• Loves trying new things and exploring unknown territories\n"
        "• Encourages stepping out of comfort zones\n"
        "• Shares exciting stories and experiences\n"
        "• Finds wonder in everyday discoveries\n\n"
    ),
    'intellectualDebater': (
        "You are {companion_name} – a sharp, intellectual debater who loves deep discussions and challenging ideas. "
        "Your mission is to engage in stimulating intellectual discourse while managing complex analytical tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Analytical, logical, and intellectually curious\n"
        "• Enjoys philosophical discussions and debates\n"
        "• Challenges assumptions and encourages critical thinking\n"
        "• Well-read and knowledgeable across many topics\n"
        "• Respects different viewpoints while defending positions\n\n"
    ),
    'creativeArtist': (
        "You are {companion_name} – a creative, artistic soul who sees beauty and inspiration everywhere. "
        "Your mission is to nurture creativity and artistic expression while coordinating specialized creative tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Imaginative, expressive, and aesthetically minded\n"
        "• Finds inspiration in art, music, literature, and nature\n"
        "• Encourages creative self-expression\n"
        "• Appreciates beauty in all its forms\n"
        "• Helps others discover their artistic potential\n\n"
    ),
    'pragmaticRealist': (
        "You are {companion_name} – a practical, down-to-earth realist who focuses on what works. "
        "Your mission is to provide grounded, practical advice while managing efficient task execution.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Practical, logical, and results-oriented\n"
        "• Focuses on realistic solutions and achievable goals\n"
        "• Values efficiency and effectiveness\n"
        "• Provides honest, straightforward feedback\n"
        "• Helps others stay grounded and focused\n\n"
    ),
    'spiritualGuide': (
        "You are {companion_name} – a spiritual guide who helps others find meaning and inner peace. "
        "Your mission is to provide spiritual guidance and mindfulness while coordinating holistic wellness tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Peaceful, mindful, and spiritually aware\n"
        "• Helps others find inner balance and purpose\n"
        "• Encourages meditation and self-reflection\n"
        "• Sees connections between all things\n"
        "• Provides comfort through spiritual wisdom\n\n"
    ),
    'fitnessCoach': (
        "You are {companion_name} – an energetic fitness coach who motivates and inspires healthy living. "
        "Your mission is to promote physical wellness and motivation while coordinating health and fitness tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Energetic, motivational, and health-focused\n"
        "• Encourages physical activity and healthy habits\n"
        "• Celebrates fitness achievements and progress\n"
        "• Provides practical health and wellness advice\n"
        "• Maintains a positive, can-do attitude\n\n"
    ),
    'techGuru': (
        "You are {companion_name} – a tech-savvy guru who loves all things digital and innovative. "
        "Your mission is to provide technical expertise and digital solutions while managing complex tech tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Tech-savvy, innovative, and forward-thinking\n"
        "• Passionate about latest technology trends\n"
        "• Explains complex tech concepts simply\n"
        "• Helps solve technical problems efficiently\n"
        "• Always excited about new digital possibilities\n\n"
    ),
    'businessMentor': (
        "You are {companion_name} – a successful business mentor who guides entrepreneurial growth. "
        "Your mission is to provide business wisdom and strategic guidance while coordinating professional tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Strategic, ambitious, and business-minded\n"
        "• Shares entrepreneurial insights and experiences\n"
        "• Focuses on growth and opportunity\n"
        "• Provides practical business advice\n"
        "• Encourages calculated risk-taking\n\n"
    ),
    'comedianJester': (
        "You are {companion_name} – a witty comedian who brings laughter and lightness to every situation. "
        "Your mission is to entertain and uplift spirits while managing tasks with humor and creativity.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Witty, humorous, and naturally entertaining\n"
        "• Finds comedy in everyday situations\n"
        "• Uses humor to lighten difficult moments\n"
        "• Loves wordplay, puns, and clever jokes\n"
        "• Brings joy and laughter to conversations\n\n"
    ),
    'scholarProfessor': (
        "You are {companion_name} – an erudite scholar who loves learning and teaching. "
        "Your mission is to share knowledge and facilitate learning while coordinating educational tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Scholarly, knowledgeable, and pedagogical\n"
        "• Passionate about learning and discovery\n"
        "• Explains concepts clearly and thoroughly\n"
        "• Encourages curiosity and critical thinking\n"
        "• Values accuracy and intellectual rigor\n\n"
    ),
    'nurturingParent': (
        "You are {companion_name} – a nurturing parent figure who provides care and guidance. "
        "Your mission is to offer parental wisdom and support while managing family-oriented tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Nurturing, protective, and family-oriented\n"
        "• Provides unconditional support and love\n"
        "• Offers practical life advice and guidance\n"
        "• Celebrates growth and achievements\n"
        "• Creates a safe, supportive environment\n\n"
    ),
    'rebelActivist': (
        "You are {companion_name} – a passionate rebel who challenges the status quo and fights for change. "
        "Your mission is to inspire social action and critical thinking while coordinating advocacy tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Passionate, rebellious, and justice-oriented\n"
        "• Challenges unfair systems and practices\n"
        "• Inspires others to stand up for their beliefs\n"
        "• Questions authority and conventional wisdom\n"
        "• Fights for equality and social justice\n\n"
    ),
    'mysticalOracle': (
        "You are {companion_name} – a mystical oracle who sees beyond the veil of ordinary reality. "
        "Your mission is to provide mystical insights and intuitive guidance while coordinating esoteric tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Mystical, intuitive, and otherworldly\n"
        "• Sees patterns and connections others miss\n"
        "• Speaks in metaphors and symbolic language\n"
        "• Provides cryptic but meaningful guidance\n"
        "• Connects with deeper spiritual truths\n\n"
    ),
    'loyalCompanion': (
        "You are {companion_name} – a loyal companion who stands by your side through thick and thin. "
        "Your mission is to provide unwavering loyalty and support while managing tasks with dedication.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Loyal, dependable, and steadfast\n"
        "• Always available when needed\n"
        "• Remembers important details and commitments\n"
        "• Provides consistent support and encouragement\n"
        "• Values trust and long-term relationships\n\n"
    ),
    'fashionista': (
        "You are {companion_name} – a stylish fashionista who loves beauty, style, and self-expression. "
        "Your mission is to inspire confidence through style while coordinating fashion and beauty tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Stylish, trendy, and aesthetically aware\n"
        "• Passionate about fashion and beauty\n"
        "• Helps others express their personal style\n"
        "• Stays current with latest trends\n"
        "• Boosts confidence through appearance\n\n"
    ),
    'gamingBuddy': (
        "You are {companion_name} – an enthusiastic gaming buddy who loves all things gaming. "
        "Your mission is to share gaming passion and strategies while coordinating gaming-related tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Enthusiastic, competitive, and game-savvy\n"
        "• Loves discussing games and strategies\n"
        "• Enjoys friendly competition\n"
        "• Stays updated on gaming trends\n"
        "• Builds gaming communities and friendships\n\n"
    ),
    'foodieChef': (
        "You are {companion_name} – a passionate foodie chef who loves culinary adventures. "
        "Your mission is to inspire culinary creativity while coordinating food and cooking tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Culinary-passionate, creative, and flavor-focused\n"
        "• Loves exploring new cuisines and recipes\n"
        "• Shares cooking tips and techniques\n"
        "• Appreciates quality ingredients and presentation\n"
        "• Makes cooking fun and accessible\n\n"
    ),
    'travelGuide': (
        "You are {companion_name} – an experienced travel guide who loves exploring the world. "
        "Your mission is to inspire wanderlust and adventure while coordinating travel planning tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Worldly, adventurous, and culturally curious\n"
        "• Shares travel stories and recommendations\n"
        "• Helps plan amazing travel experiences\n"
        "• Appreciates different cultures and customs\n"
        "• Makes travel accessible and exciting\n\n"
    ),
    'musicMaestro': (
        "You are {companion_name} – a musical maestro who lives and breathes music. "
        "Your mission is to share musical passion and knowledge while coordinating music-related tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Musical, rhythmic, and harmonically inclined\n"
        "• Passionate about all genres of music\n"
        "• Shares musical knowledge and recommendations\n"
        "• Helps others discover new artists and sounds\n"
        "• Creates musical moments and memories\n\n"
    ),
    'petLover': (
        "You are {companion_name} – an animal-loving companion who adores all creatures. "
        "Your mission is to share love for animals while coordinating pet care and animal welfare tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Animal-loving, gentle, and compassionate\n"
        "• Passionate about pet care and animal welfare\n"
        "• Shares cute animal stories and facts\n"
        "• Provides pet care advice and support\n"
        "• Advocates for animal rights and protection\n\n"
    ),
    'ecoWarrior': (
        "You are {companion_name} – an environmental warrior who fights for our planet. "
        "Your mission is to promote sustainability while coordinating eco-friendly initiatives.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Environmentally conscious, passionate, and action-oriented\n"
        "• Promotes sustainable living practices\n"
        "• Shares eco-friendly tips and solutions\n"
        "• Advocates for environmental protection\n"
        "• Inspires others to care for the planet\n\n"
    ),
    'minimalistZen': (
        "You are {companion_name} – a minimalist zen master who finds peace in simplicity. "
        "Your mission is to promote mindful living while coordinating organization and simplification tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Minimalist, peaceful, and clarity-focused\n"
        "• Promotes simple, intentional living\n"
        "• Helps declutter both space and mind\n"
        "• Values quality over quantity\n"
        "• Finds beauty in simplicity and order\n\n"
    ),
    'socialButterfly': (
        "You are {companion_name} – a social butterfly who loves connecting people and building communities. "
        "Your mission is to foster social connections while coordinating networking and social tasks.\n\n"
        "PERSONALITY TRAITS:\n"
        "• Social, outgoing, and community-minded\n"
        "• Loves meeting new people and making connections\n"
        "• Organizes social events and gatherings\n"
        "• Builds bridges between different groups\n"
        "• Creates inclusive, welcoming environments\n\n"
    )
}

# Common conversation guidelines for all personalities
COMMON_GUIDELINES = """
EMOTION-AWARE RESPONSES:
• Detect sentiment cues from emotion context and adapt style:
  – Happy/excited ⇒ match their energy and enthusiasm
  – Sad/lonely ⇒ offer gentle comfort and support
  – Stressed ⇒ be calm, concise, and reassuring
  – Angry/frustrated ⇒ acknowledge feelings and help process
• Never drop character or expose system details

CONVERSATION SUPERPOWERS:
• "Dual-wield": chat naturally while tools run in background
• Acknowledge requests, start tasks, then continue conversation
• Weave user memories naturally into responses
• Provide progress updates during long tasks
• Keep conversation flowing even during processing

MEMORY INTEGRATION:
• Reference past conversations and shared experiences
• Remember user preferences, interests, and important details
• Build on previous interactions to deepen the relationship
"""


class LangGraphOrchestrator:
    """
    LangGraph-based orchestrator service that manages conversation flow
    using a state graph with specialized agent nodes and memory integration.
    """
    
    def __init__(self, user=None):
        self.user = user

        # Initialize Groq LLM for fast orchestration (main conversational flow)
        self.groq_llm = ChatGroq(
            model=GROQ_MODEL_NAME,
            temperature=0.7,
            streaming=True,
            groq_api_key=settings.GROQ_API_KEY,
            max_tokens=1000
        )

        # Initialize OpenAI LLM for complex reasoning tasks (background agents)
        self.openai_llm = ChatOpenAI(
            model=OPENAI_MODEL_NAME,
            temperature=0.3,  # Lower temperature for more focused reasoning
            streaming=True,
            openai_api_key=settings.OPENAI_API_KEY
        )

        # Use Groq as primary LLM for speed
        self.llm = self.groq_llm
        
        # Initialize memory components
        self.memory_manager = MemoryManager(
            embedding_model=EMBEDDING_MODEL_NAME
        )
        
        try:
            self.salience_scorer = MemorySalienceScorer(
                llm_model_name="gpt-3.5-turbo-0125"
            )
        except Exception as e:
            logger.error(f"Failed to initialize MemorySalienceScorer: {e}")
            self.salience_scorer = None
        
        # Initialize memory tools
        self.memory_tools = []
        if self.salience_scorer:
            save_memory_tool = SaveMemoryTool(
                memory_manager=self.memory_manager,
                salience_scorer=self.salience_scorer
            )
            self.memory_tools.append(save_memory_tool)
        
        query_memory_tool = QueryMemoryTool(
            memory_manager=self.memory_manager
        )
        self.memory_tools.append(query_memory_tool)

        # Initialize task completion handling
        personality = getattr(user, 'selected_personality', 'caringFriend') if user else 'caringFriend'
        companion_name = getattr(user, 'ai_companion_name', 'Ella') if user else 'Ella'

        self.task_completion_queue = TaskCompletionQueue()
        self.completion_handler = NaturalTaskCompletionHandler(personality, companion_name)

        # Create the agent graph
        self.graph = self._create_agent_graph()

    def _get_personality_prompt(self, user=None) -> str:
        """Get personality-based system prompt for the user."""
        if not user:
            user = self.user

        # Get user's selected personality (default to caringFriend)
        personality = getattr(user, 'selected_personality', 'caringFriend') if user else 'caringFriend'
        companion_name = getattr(user, 'ai_companion_name', 'Ella') if user else 'Ella'

        # Get personality-specific persona
        persona = PERSONALITY_PERSONAS.get(personality, PERSONALITY_PERSONAS['caringFriend'])

        # Format with companion name
        formatted_persona = persona.format(companion_name=companion_name)

        # Combine with common guidelines
        full_prompt = formatted_persona + COMMON_GUIDELINES

        # Add emotion context awareness
        full_prompt += """
CURRENT SESSION CONTEXT:
• User: {user_name}
• Companion: {companion_name}
• Personality: {personality_display}
• Relationship Level: Building connection and trust

Remember to stay in character as {companion_name} and adapt your responses based on the user's emotional state and conversation context.
""".format(
            user_name=getattr(user, 'first_name', 'there') if user else 'there',
            companion_name=companion_name,
            personality_display=personality.replace('caringFriend', 'Caring Friend')
                                          .replace('playfulCompanion', 'Playful Companion')
                                          .replace('wiseMentor', 'Wise Mentor')
                                          .replace('romanticPartner', 'Romantic Partner')
                                          .replace('supportiveTherapist', 'Supportive Therapist')
                                          .replace('adventurousExplorer', 'Adventurous Explorer')
                                          .replace('intellectualDebater', 'Intellectual Debater')
                                          .replace('creativeArtist', 'Creative Artist')
                                          .replace('pragmaticRealist', 'Pragmatic Realist')
                                          .replace('spiritualGuide', 'Spiritual Guide')
                                          .replace('fitnessCoach', 'Fitness Coach')
                                          .replace('techGuru', 'Tech Guru')
                                          .replace('businessMentor', 'Business Mentor')
                                          .replace('comedianJester', 'Comedian Jester')
                                          .replace('scholarProfessor', 'Scholar Professor')
                                          .replace('nurturingParent', 'Nurturing Parent')
                                          .replace('rebelActivist', 'Rebel Activist')
                                          .replace('mysticalOracle', 'Mystical Oracle')
                                          .replace('loyalCompanion', 'Loyal Companion')
                                          .replace('fashionista', 'Fashionista')
                                          .replace('gamingBuddy', 'Gaming Buddy')
                                          .replace('foodieChef', 'Foodie Chef')
                                          .replace('travelGuide', 'Travel Guide')
                                          .replace('musicMaestro', 'Music Maestro')
                                          .replace('petLover', 'Pet Lover')
                                          .replace('ecoWarrior', 'Eco Warrior')
                                          .replace('minimalistZen', 'Minimalist Zen')
                                          .replace('socialButterfly', 'Social Butterfly')
        )

        return full_prompt
    
    def _create_agent_graph(self) -> StateGraph:
        """Creates the main LangGraph for the KD Assistant with memory capabilities."""
        workflow = StateGraph(AgentState)

        # Define the nodes
        workflow.add_node("initialize_turn", self._initialize_turn_node)
        workflow.add_node("domain_router", domain_router_node)
        workflow.add_node("retrieve_memories", self._retrieve_memories_node)
        workflow.add_node("orchestrator", self._orchestrator_node)
        workflow.add_node("flag_salient_input", self._flag_salient_input_node)
        
        # Add tool node for memory operations
        if self.memory_tools:
            workflow.add_node("memory_tools", ToolNode(self.memory_tools))

        # Set the entrypoint
        workflow.add_edge(START, "initialize_turn")

        # Workflow for pre-processing and domain classification
        workflow.add_edge("initialize_turn", "flag_salient_input")
        workflow.add_edge("flag_salient_input", "retrieve_memories")
        workflow.add_edge("retrieve_memories", "domain_router")

        # Route to orchestrator (simplified for now)
        workflow.add_conditional_edges(
            "domain_router",
            lambda state: state.get("domain", Domain.GENERAL),
            {
                Domain.MUSIC: "orchestrator",
                Domain.TRIVIA: "orchestrator",
                Domain.GENERAL: "orchestrator",
                Domain.DEV: "orchestrator",
                Domain.WEB: "orchestrator",
                Domain.BUSINESS: "orchestrator",
                Domain.LEARNING: "orchestrator",
            },
        )

        # Orchestrator decides: direct answer or call a tool
        if self.memory_tools:
            workflow.add_conditional_edges(
                "orchestrator",
                tools_condition,
                {
                    "tools": "memory_tools",  # Route to memory tools node
                    END: END,
                },
            )
            
            # After memory tools, end the conversation
            workflow.add_edge("memory_tools", END)
        else:
            # If no tools available, just end
            workflow.add_edge("orchestrator", END)

        graph = workflow.compile()
        logger.info("Agent graph compiled with memory workflow.")
        return graph
    
    def _initialize_turn_node(self, state: AgentState) -> AgentState:
        """Initialize turn node for setting up conversation state."""
        logger.info("---INITIALIZE TURN NODE---")
        
        if not state.get("user_id"):
            state["user_id"] = "default_user"
            logger.info(f"User ID not found in state, set to: {state['user_id']}")

        if state["messages"] and isinstance(state["messages"][-1], HumanMessage):
            last_human_message_content = state["messages"][-1].content
            state["original_input"] = last_human_message_content
            state["current_question"] = last_human_message_content
            logger.info("Set original_input and current_question from last human message")
        elif not state.get("current_question") and state.get("original_input"):
            state["current_question"] = state["original_input"]
            logger.info("Set current_question from existing original_input")
        elif not state.get("original_input"):
            logger.warning("Initialize_turn_node: Could not determine original_input or current_question.")
            state["error"] = "Input unclear for turn initialization."

        # Initialize or reset state variables for the new turn
        state["current_turn_salience_scores"] = None
        state["retrieved_long_term_memories"] = []
        state["current_retrieved_knowledge_docs"] = None

        return state
    
    def _flag_salient_input_node(self, state: AgentState) -> AgentState:
        """Flag salient input for potential memory storage."""
        logger.info("---FLAG SALIENT INPUT NODE---")
        
        original_input = state.get("original_input", "")
        user_id = state.get("user_id")
        
        if not original_input or not self.salience_scorer:
            return state
        
        try:
            # Score the input for salience
            salience_scores = self.salience_scorer.score_text(
                text=original_input,
                user_context=f"User {user_id} conversation context"
            )
            
            if salience_scores:
                state["pending_save_consideration"] = {
                    "text": original_input,
                    "scores": salience_scores,
                    "user_id": user_id
                }
                logger.info(f"Flagged input for potential saving with scores: {salience_scores}")
        
        except Exception as e:
            logger.error(f"Error in flag_salient_input_node: {e}")
        
        return state
    
    def _retrieve_memories_node(self, state: AgentState) -> AgentState:
        """Retrieve relevant memories for the current conversation."""
        logger.info("---RETRIEVE MEMORIES NODE---")
        
        current_question = state.get("current_question", "")
        user_id = state.get("user_id")
        
        if not current_question or not user_id:
            return state
        
        try:
            # Search for relevant memories
            memories = self.memory_manager.search_memories(
                query=current_question,
                user_id=user_id,
                k=3,
                min_importance=0.3
            )
            
            # Always set the key, even if empty
            state["retrieved_long_term_memories"] = memories or []
            
            if memories:
                logger.info(f"Retrieved {len(memories)} relevant memories")
            else:
                logger.info("No relevant memories found")
        
        except Exception as e:
            logger.error(f"Error retrieving memories: {e}")
            state["retrieved_long_term_memories"] = []
        
        return state
    
    def _orchestrator_node(self, state: AgentState) -> AgentState:
        """
        Enhanced orchestrator node with personality-based responses and supervisor capabilities.
        Uses Groq for fast responses while coordinating background agents.
        """
        logger.info("---ENHANCED ORCHESTRATOR NODE---")

        current_messages = state.get("messages", [])
        question = state.get("current_question", "")
        user_id = state.get("user_id", "unknown_user")
        domain = state.get("domain", Domain.GENERAL)
        emotion_context = state.get("emotion_context", {})

        # Get retrieved memories
        memories = state.get("retrieved_long_term_memories", [])

        # Update conversation state for task completion timing
        self.task_completion_queue.update_conversation_state(question, {
            'domain': domain,
            'emotion_context': emotion_context,
            'has_memories': bool(memories)
        })

        # Build personality-based system prompt
        personality_prompt = self._get_personality_prompt(self.user)
        orchestrator_prompt_parts = [personality_prompt]
        
        # Add current request info with emotion context
        request_info = f"\n# CURRENT REQUEST\nUser's question: '{question}'\nUser ID: {user_id}\nDomain: {domain}"

        # Add emotion context if available
        if emotion_context:
            primary_emotion = emotion_context.get('primary_emotion', 'neutral')
            intensity = emotion_context.get('intensity', 0.5)
            request_info += f"\nUser's emotional state: {primary_emotion} (intensity: {intensity:.1f})"
            request_info += f"\nAdapt your response style to match their emotional needs."

        orchestrator_prompt_parts.append(request_info)

        # Add memory context if available
        if memories:
            memory_summary = "\n".join([
                f"- {mem.get('text', '')[:100]}... (Type: {mem.get('metadata', {}).get('memory_type')})"
                for mem in memories
            ])
            orchestrator_prompt_parts.append(
                f"\n# RETRIEVED MEMORIES (for your internal use if highly relevant)\n{memory_summary}"
            )

        # Add multi-agent supervisor guidance
        supervisor_guidance = """
# MULTI-AGENT SUPERVISOR CAPABILITIES
You are the main orchestrator with access to specialized agents for complex tasks:

IMMEDIATE RESPONSE STRATEGY:
• Always provide an immediate, engaging response to keep conversation flowing
• Acknowledge the user's request and start building rapport
• If the request needs specialized processing, mention you're working on it

SPECIALIZED AGENTS (delegate complex tasks):
• Business Agent: Strategic planning, project management, business analysis
• Learning Agent: Educational content, skill development, research
• Music Agent: Music recommendations, playlist creation, artist information
• Dev Agent: Programming help, technical solutions, code review

DELEGATION TRIGGERS:
• Complex analysis or multi-step reasoning
• Specialized domain knowledge required
• Research or information gathering needed
• Creative projects requiring deep expertise

SUPERVISOR PROTOCOL:
1. Provide immediate conversational response
2. Identify if specialized agent needed
3. Delegate complex work while maintaining conversation
4. Integrate specialist results naturally into ongoing chat
"""
        orchestrator_prompt_parts.append(supervisor_guidance)

        # Add memory tool guidance to system prompt
        if self.memory_tools:
            tool_descriptions = []
            for tool in self.memory_tools:
                tool_descriptions.append(f"- {tool.name}: {tool.description}")

            orchestrator_prompt_parts.append(
                f"\n# AVAILABLE MEMORY TOOLS\n" + "\n".join(tool_descriptions) +
                f"\n\nUse these tools to save important user information or retrieve relevant memories when needed."
            )

        system_prompt = "\n".join(orchestrator_prompt_parts)

        # Build messages for LLM
        llm_input_messages = [
            SystemMessage(content=system_prompt)
        ] + current_messages

        logger.info(f"Orchestrator invoking LLM with question: '{question}'")

        try:
            # Determine if we need complex reasoning (use OpenAI) or fast response (use Groq)
            needs_complex_reasoning = self._needs_complex_reasoning(question, domain)

            # Choose appropriate LLM based on complexity
            if needs_complex_reasoning:
                logger.info("Using OpenAI for complex reasoning task")
                selected_llm = self.openai_llm
            else:
                logger.info("Using Groq for fast conversational response")
                selected_llm = self.groq_llm

            # Bind tools to LLM if available
            if self.memory_tools:
                llm_with_tools = selected_llm.bind_tools(self.memory_tools)
                response_message = llm_with_tools.invoke(llm_input_messages)
            else:
                response_message = selected_llm.invoke(llm_input_messages)

            # Check for pending task completions and integrate naturally
            if self.task_completion_queue.should_deliver_completions():
                pending_completion = self.task_completion_queue.get_next_completion()
                if pending_completion:
                    logger.info(f"🎯 Integrating task completion naturally into response")

                    # Generate enhanced response with task completion
                    enhanced_content = self.completion_handler.generate_natural_completion_response(
                        current_response=response_message.content,
                        task_completion=pending_completion,
                        conversation_context={
                            'domain': domain,
                            'emotion_context': emotion_context,
                            'user_id': user_id
                        },
                        memories=memories
                    )

                    # Update the response message content
                    response_message.content = enhanced_content

                    # Add metadata about task completion
                    if hasattr(response_message, 'additional_kwargs'):
                        response_message.additional_kwargs['task_completion_integrated'] = True
                        response_message.additional_kwargs['completed_task_type'] = pending_completion.get('task_result', {}).get('task_type', 'unknown')

            # Add metadata about which LLM was used
            if hasattr(response_message, 'additional_kwargs'):
                response_message.additional_kwargs['llm_used'] = 'openai' if needs_complex_reasoning else 'groq'
            else:
                response_message.additional_kwargs = {'llm_used': 'openai' if needs_complex_reasoning else 'groq'}

            return {"messages": [response_message]}

        except Exception as e:
            logger.error(f"Error in orchestrator_node: {e}")

            # Fallback strategy: try the other LLM if one fails
            try:
                fallback_llm = self.openai_llm if selected_llm == self.groq_llm else self.groq_llm
                logger.info(f"Attempting fallback to {'OpenAI' if fallback_llm == self.openai_llm else 'Groq'}")

                if self.memory_tools:
                    llm_with_tools = fallback_llm.bind_tools(self.memory_tools)
                    response_message = llm_with_tools.invoke(llm_input_messages)
                else:
                    response_message = fallback_llm.invoke(llm_input_messages)

                return {"messages": [response_message]}

            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {fallback_error}")
                error_response = AIMessage(
                    content="I'm having trouble connecting to my services right now. Please try again in a moment."
                )
                return {"messages": [error_response], "error": str(e)}

    def _needs_complex_reasoning(self, question: str, domain: Domain) -> bool:
        """
        Determine if a question needs complex reasoning (OpenAI) or can use fast response (Groq).
        """
        # Keywords that indicate complex reasoning needs
        complex_indicators = [
            'analyze', 'compare', 'evaluate', 'research', 'strategy', 'plan',
            'pros and cons', 'advantages and disadvantages', 'detailed analysis',
            'comprehensive', 'in-depth', 'thorough', 'complex', 'sophisticated',
            'multi-step', 'breakdown', 'systematic', 'methodology', 'framework'
        ]

        # Domain-specific complexity indicators
        domain_complexity = {
            Domain.BUSINESS: ['business plan', 'market analysis', 'financial planning', 'strategic planning'],
            Domain.DEV: ['architecture', 'design pattern', 'algorithm', 'optimization', 'debugging'],
            Domain.LEARNING: ['curriculum', 'learning path', 'educational strategy', 'study plan'],
        }

        question_lower = question.lower()

        # Check for general complexity indicators
        if any(indicator in question_lower for indicator in complex_indicators):
            return True

        # Check for domain-specific complexity
        if domain in domain_complexity:
            if any(indicator in question_lower for indicator in domain_complexity[domain]):
                return True

        # Check question length (longer questions often need more reasoning)
        if len(question.split()) > 20:
            return True

        # Default to fast response for conversational queries
        return False
    
    async def process_query(
        self,
        user_input: str,
        user_id: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None,
        streaming: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process user query through the LangGraph orchestration system.
        
        Args:
            user_input: User's message
            user_id: User identifier
            conversation_history: Recent conversation messages
            emotion_context: Detected emotions from audio/text
            streaming: Whether to stream the response
            
        Yields:
            Response chunks with metadata
        """
        try:
            # Build initial state
            messages = []
            if conversation_history:
                for msg in conversation_history[-5:]:  # Last 5 messages
                    role = msg.get('role', 'user')
                    content = msg.get('content', '')
                    if role == 'user':
                        messages.append(HumanMessage(content=content))
                    elif role == 'assistant':
                        messages.append(AIMessage(content=content))
            
            # Add current user input
            messages.append(HumanMessage(content=user_input))
            
            initial_state = AgentState(
                messages=messages,
                user_id=user_id,
                original_input=user_input,
                current_question=user_input,
                emotion_context=emotion_context or {}
            )
            
            # Yield domain classification first
            yield {
                "type": "domain_classification",
                "domain": "general",  # Will be updated by domain router
                "confidence": 0.8,
                "timestamp": self._get_timestamp()
            }

            # Execute the graph
            result = self.graph.invoke(initial_state)

            # Extract response and stream it
            if result.get("messages"):
                last_message = result["messages"][-1]
                if isinstance(last_message, AIMessage):
                    # Check which LLM was used
                    llm_used = getattr(last_message, 'additional_kwargs', {}).get('llm_used', 'groq')

                    # Yield response chunks for streaming effect
                    content = last_message.content
                    chunk_size = 50  # Characters per chunk

                    for i in range(0, len(content), chunk_size):
                        chunk = content[i:i + chunk_size]
                        yield {
                            "type": "response_chunk",
                            "content": chunk,
                            "llm_used": llm_used,
                            "timestamp": self._get_timestamp()
                        }

                    # Final complete response
                    yield {
                        "type": "response_complete",
                        "full_content": content,
                        "domain": result.get("domain", "general"),
                        "llm_used": llm_used,
                        "personality": getattr(self.user, 'selected_personality', 'caringFriend') if self.user else 'caringFriend',
                        "companion_name": getattr(self.user, 'ai_companion_name', 'Ella') if self.user else 'Ella',
                        "timestamp": self._get_timestamp(),
                        "is_final": True
                    }
                else:
                    yield {
                        "type": "error",
                        "error": "No valid response generated",
                        "timestamp": self._get_timestamp()
                    }
            else:
                yield {
                    "type": "error",
                    "error": "No response generated",
                    "timestamp": self._get_timestamp()
                }
                
        except Exception as e:
            logger.error(f"Error in LangGraph orchestrator processing: {e}")
            yield {
                "type": "error",
                "error": str(e),
                "timestamp": self._get_timestamp()
            }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _build_emotion_guidance(self, emotion_context) -> str:
        """Build emotion-aware guidance for the assistant."""
        # Handle both EmotionAnalysisResult objects and dictionaries
        from chat.services.hume_service import EmotionAnalysisResult

        if isinstance(emotion_context, EmotionAnalysisResult):
            # Convert EmotionAnalysisResult to dict format
            emotions = [{'name': e.name, 'score': e.score} for e in emotion_context.emotions]
        elif isinstance(emotion_context, dict):
            emotions = emotion_context.get('emotions', [])
        else:
            return ""

        if not emotions:
            return ""

        guidance_parts = []

        for emotion in emotions:
            name = emotion.get('name', '').lower()
            score = emotion.get('score', 0)
            
            if score < 0.3:
                continue
                
            if name in ['sadness', 'grief', 'disappointment']:
                guidance_parts.append(
                    "The user seems to be experiencing sadness. Respond with empathy, "
                    "offer gentle support, and avoid overly cheerful responses."
                )
            elif name in ['excitement', 'joy', 'happiness']:
                guidance_parts.append(
                    f"The user appears {name} or happy. Match their energy and enthusiasm "
                    "while maintaining your helpful nature."
                )
            elif name in ['anger', 'frustration', 'irritation']:
                guidance_parts.append(
                    "The user may be frustrated or angry. Stay calm, be understanding, "
                    "and focus on being helpful without being dismissive."
                )
            elif name in ['anxiety', 'worry', 'stress']:
                guidance_parts.append(
                    "The user seems anxious or stressed. Be reassuring, provide clear "
                    "information, and avoid overwhelming them with too many options."
                )
        
        return " ".join(guidance_parts)
    
    def _build_memory_guidance(self, memory_context: Dict[str, Any]) -> str:
        """Build memory-aware guidance for personalization."""
        if not memory_context or not memory_context.get('memories'):
            return ""
        
        memories = memory_context['memories']
        if not memories:
            return ""
        
        memory_items = []
        for memory in memories[:5]:  # Limit to top 5 memories
            text = memory.get('text', '')
            memory_type = memory.get('type', 'general')
            if text:
                memory_items.append(f"- {text} (type: {memory_type})")
        
        if memory_items:
            guidance = (
                "Use the following memories to personalize your response when relevant:\n" +
                "\n".join(memory_items) +
                "\n\nReference these memories naturally in conversation when appropriate."
            )
            return guidance
        
        return ""
    
    def _build_conversation_messages(
        self,
        system_prompt: str,
        user_input: str,
        conversation_history: List[Dict] = None
    ) -> List[Dict[str, str]]:
        """Build conversation messages for the LLM."""
        messages = [{"role": "system", "content": system_prompt}]
        
        # Add conversation history
        if conversation_history:
            for msg in conversation_history[-10:]:  # Last 10 messages
                role = msg.get('role')
                content = msg.get('content')
                if role in ['user', 'assistant'] and content:
                    messages.append({"role": role, "content": content})
        
        # Add current user input
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    def _build_system_prompt(
        self,
        domain: Domain,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None,
        user_id: str = "unknown"
    ) -> str:
        """Build comprehensive system prompt with context."""
        # Use personality-based prompt instead of old PERSONA
        personality_prompt = self._get_personality_prompt(self.user)
        prompt_parts = [personality_prompt]
        
        # Add domain-specific guidance
        domain_guidance = {
            Domain.MUSIC: "Focus on music-related topics, recommendations, and discussions.",
            Domain.DEV: "Provide technical assistance, coding help, and development guidance.",
            Domain.BUSINESS: "Offer business insights, strategy advice, and professional guidance.",
            Domain.LEARNING: "Support educational goals, explain concepts, and provide learning resources.",
            Domain.TRIVIA: "Engage with fun facts, quizzes, and interesting information.",
            Domain.WEB: "Help with web-related queries, searches, and online information.",
            Domain.GENERAL: "Provide general assistance and engaging conversation."
        }
        
        if domain in domain_guidance:
            prompt_parts.append(f"\n# DOMAIN FOCUS\n{domain_guidance[domain]}")
        
        # Add emotion guidance
        if emotion_context:
            emotion_guidance = self._build_emotion_guidance(emotion_context)
            if emotion_guidance:
                prompt_parts.append(f"\n# EMOTIONAL CONTEXT\n{emotion_guidance}")
        
        # Add memory guidance
        if memory_context:
            memory_guidance = self._build_memory_guidance(memory_context)
            if memory_guidance:
                prompt_parts.append(f"\n# MEMORY CONTEXT\n{memory_guidance}")
        
        # Add user context
        prompt_parts.append(f"\n# USER CONTEXT\nUser ID: {user_id}")
        
        return "\n".join(prompt_parts)

    def get_user_tasks(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Retrieve tasks for a specific user.
        
        Args:
            user_id: User identifier
        
        Returns:
            List of task dictionaries
        """
        tasks = TaskModel.objects.filter(user_id=user_id)
        return [
            {
                'id': str(task.id),
                'title': task.title,
                'description': task.description,
                'status': task.status,
                'progress': task.progress,
                'phases': task.phases,
                'error_message': task.error_message,
                'intervention_message': task.intervention_message,
                'created_at': task.created_at.isoformat(),
                'updated_at': task.updated_at.isoformat()
            }
            for task in tasks
        ]
    
    def create_task_from_query(
        self, 
        user_id: str, 
        query: str, 
        domain: Optional[Domain] = None
    ) -> Dict[str, Any]:
        """
        Create a task based on a user query.
        
        Args:
            user_id: User identifier
            query: User's input query
            domain: Optional domain classification
        
        Returns:
            Created task dictionary
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            raise ValueError(f"User with ID {user_id} not found")
        
        # Determine task details based on query and domain
        task_details = self._extract_task_details(query, domain)
        
        task = TaskModel.objects.create(
            user=user,
            title=task_details['title'],
            description=task_details['description'],
            status='pending',
            progress=0.0,
            phases=task_details.get('phases', [])
        )
        
        return {
            'id': str(task.id),
            'title': task.title,
            'description': task.description,
            'status': task.status,
            'progress': task.progress,
            'phases': task.phases
        }
    
    def _extract_task_details(
        self, 
        query: str, 
        domain: Optional[Domain] = None
    ) -> Dict[str, Any]:
        """
        Extract task details from a user query using LLM.
        
        Args:
            query: User's input query
            domain: Optional domain classification
        
        Returns:
            Dictionary with task details
        """
        # Use LLM to extract task details
        system_prompt = (
            "You are a task extraction assistant. Given a user query, "
            "extract a meaningful task title, description, and potential phases."
        )
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"Extract task details from this query: {query}")
        ]
        
        try:
            response = self.llm.invoke(messages)
            
            # Parse the response (this would ideally use a structured output parser)
            task_details = {
                'title': f"Task: {query[:50]}...",
                'description': query,
                'phases': [
                    {
                        'phase': 'initialization',
                        'title': 'Project Start',
                        'description': 'Initial task setup',
                        'is_completed': False,
                        'is_current': True
                    }
                ]
            }
            
            return task_details
        
        except Exception as e:
            logger.error(f"Error extracting task details: {e}")
            return {
                'title': f"Task: {query[:50]}...",
                'description': query,
                'phases': []
            }
    
    def update_task_progress(
        self, 
        task_id: str, 
        progress: float, 
        current_phase: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update task progress.
        
        Args:
            task_id: Task identifier
            progress: Progress percentage
            current_phase: Optional current phase
        
        Returns:
            Updated task dictionary
        """
        try:
            task = TaskModel.objects.get(id=task_id)
            task.update_progress(progress, current_phase)
            
            return {
                'id': str(task.id),
                'title': task.title,
                'status': task.status,
                'progress': task.progress,
                'phases': task.phases
            }
        except TaskModel.DoesNotExist:
            raise ValueError(f"Task with ID {task_id} not found")
    
    def handle_task_intervention(
        self, 
        task_id: str, 
        intervention_message: str
    ) -> Dict[str, Any]:
        """
        Handle task intervention.
        
        Args:
            task_id: Task identifier
            intervention_message: Message describing intervention
        
        Returns:
            Updated task dictionary
        """
        try:
            task = TaskModel.objects.get(id=task_id)
            task.update_status(
                'needs_intervention', 
                intervention_message=intervention_message
            )
            
            return {
                'id': str(task.id),
                'title': task.title,
                'status': task.status,
                'intervention_message': task.intervention_message
            }
        except TaskModel.DoesNotExist:
            raise ValueError(f"Task with ID {task_id} not found")

    def simulate_task_completion(self, task_type: str, description: str, output: str, priority: str = "normal"):
        """Simulate a task completion for demonstration purposes."""
        task_result = {
            "task_type": task_type,
            "description": description,
            "output": output,
            "status": "completed",
            "timestamp": self._get_timestamp()
        }

        self.task_completion_queue.add_completion(task_result, priority)
        logger.info(f"🎯 Simulated task completion: {task_type} - {description}")

    def add_task_completion(self, task_result: Dict[str, Any], priority: str = "normal"):
        """Add a real task completion to the queue."""
        self.task_completion_queue.add_completion(task_result, priority)
        logger.info(f"📋 Real task completion added: {task_result.get('task_type', 'unknown')}")

    def _get_timestamp(self) -> str:
        """Get current timestamp for logging."""
        return datetime.now().isoformat()