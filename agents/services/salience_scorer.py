"""
Salience scorer module for memory management in Django.
Adapted from kd_assistant_langgraph implementation.

This module contains the implementation of the salience scorer, which scores text based on
importance, personalness, and actionability.
"""
import os
import logging
import asyncio
from typing import Dict, Any, Optional, List, TypedDict
from django.conf import settings

from langchain_openai import ChatOpenAI
from langchain.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class SalienceScore(BaseModel):
    """
    Schema for salience scores returned by the salience scorer.
    Each score is on a scale from 0.0 to 1.0 where higher values indicate higher salience.
    """

    importance_score: float = Field(
        description="A 0 to 1 score of how important this information is for providing help or understanding the user in the future (1 = extremely important)"
    )
    personalness_score: float = Field(
        description="A 0 to 1 score of how much this text reveals about the user's stable preferences, biographical details, or core characteristics (1 = highly personal)"
    )
    actionability_score: float = Field(
        description="A 0 to 1 score of how much this text implies a need for action, follow-up, reminder, or continuation at a future date (1 = highly actionable)"
    )
    reasoning: str = Field(
        description="A brief explanation of why these scores were assigned to the text"
    )


class MemorySalienceScorer:
    """
    Scores text on dimensions of importance, personalness, and actionability.
    Uses an LLM to assess these dimensions for memory salience.
    Django-integrated version of the kd_assistant_langgraph MemorySalienceScorer.
    """

    def __init__(self, llm_model_name: str = "gpt-3.5-turbo-0125"):
        """
        Initialize the salience scorer with an LLM.

        Args:
            llm_model_name: Name of the LLM model to use (faster models recommended)
        """
        # Use a faster model for scoring to reduce latency
        self.llm = ChatOpenAI(
            model=llm_model_name,
            temperature=0,
            # Don't use streaming for scoring - we want the full output
            streaming=False,
            openai_api_key=settings.OPENAI_API_KEY
        )

        # Setup for the parser that will handle the LLM's output
        self.parser = PydanticOutputParser(pydantic_object=SalienceScore)

        self._http_clients = set()  # Track active HTTP clients

    def score_text(
        self, text: str, user_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Score text for salience on various dimensions.

        Args:
            text: The text to score
            user_context: Optional context about the user to help with scoring

        Returns:
            A dictionary with salience scores (importance, personalness, actionability)
        """
        # Avoid scoring empty or very short texts
        if not text or len(text.strip()) < 3:
            logger.warning("Text too short to meaningfully score for salience")
            return {
                "importance_score": 0.0,
                "personalness_score": 0.0,
                "actionability_score": 0.0,
                "reasoning": "Text too short to score",
            }

        # Truncate very long texts to avoid token limits
        if len(text) > 2000:
            scored_text = text[:2000] + "..."
            logger.info(
                f"Truncated long text ({len(text)} chars) to 2000 chars for salience scoring"
            )
        else:
            scored_text = text

        logger.info(f"Scoring text for salience: '{scored_text[:50]}...'")

        # Construct the system prompt that guides the LLM's scoring
        system_prompt = f"""You are an AI memory salience scorer.
Your task is to analyze text and score its importance across dimensions that affect whether it should be remembered.

Analyze this text carefully: "{scored_text}"

{self.parser.get_format_instructions()}

Remember:
- Importance: How crucial is this for future assistance (facts, preferences, crucial context)?
- Personalness: How much does it reveal about the user's identity, preferences, or characteristics?
- Actionability: Does this text imply a need for follow-up, reminder, or future reference?

Score each dimension from 0.0 (not at all) to 1.0 (extremely high).
Provide brief reasoning for your scores.
"""

        # If user context is provided, add it to help with personalization of scores
        if user_context:
            system_prompt += f"\nAdditional context about the user: {user_context}"

        try:
            # Call the LLM to get the scores
            response = self.llm.invoke(system_prompt)

            # Parse the LLM response into structured data
            scores = self.parser.parse(response.content)

            logger.info(f"Successfully scored text: {scores}")
            return scores.dict()

        except Exception as e:
            logger.error(f"Error scoring text for salience: {e}")
            # Return default low scores if scoring fails
            return {
                "importance_score": 0.1,
                "personalness_score": 0.1,
                "actionability_score": 0.1,
                "reasoning": f"Error during scoring: {str(e)}",
            }

    # Add an async scoring method that properly handles async resources
    async def score_text_async(
        self, text: str, user_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Asynchronously score text for salience on various dimensions.

        Args:
            text: The text to score
            user_context: Optional context about the user to help with scoring

        Returns:
            A dictionary with salience scores (importance, personalness, actionability)
        """
        # Avoid scoring empty or very short texts
        if not text or len(text.strip()) < 3:
            logger.warning("Text too short to meaningfully score for salience")
            return {
                "importance_score": 0.0,
                "personalness_score": 0.0,
                "actionability_score": 0.0,
                "reasoning": "Text too short to score",
            }

        # Truncate very long texts to avoid token limits
        if len(text) > 2000:
            scored_text = text[:2000] + "..."
            logger.info(
                f"Truncated long text ({len(text)} chars) to 2000 chars for salience scoring"
            )
        else:
            scored_text = text

        logger.info(f"Scoring text for salience: '{scored_text[:50]}...'")

        # Construct the system prompt that guides the LLM's scoring
        system_prompt = f"""You are an AI memory salience scorer.
Your task is to analyze text and score its importance across dimensions that affect whether it should be remembered.

Analyze this text carefully: "{scored_text}"

{self.parser.get_format_instructions()}

Remember:
- Importance: How crucial is this for future assistance (facts, preferences, crucial context)?
- Personalness: How much does it reveal about the user's identity, preferences, or characteristics?
- Actionability: Does this text imply a need for follow-up, reminder, or future reference?

Score each dimension from 0.0 (not at all) to 1.0 (extremely high).
Provide brief reasoning for your scores.
"""

        # If user context is provided, add it to help with personalization of scores
        if user_context:
            system_prompt += f"\nAdditional context about the user: {user_context}"

        try:
            # Use an async LLM client for scoring
            async_llm = ChatOpenAI(
                model=self.llm.model_name, 
                temperature=0, 
                streaming=False,
                openai_api_key=settings.OPENAI_API_KEY
            )

            # Call the LLM asynchronously to get the scores
            response = await async_llm.ainvoke(system_prompt)

            # Parse the LLM response into structured data
            scores = self.parser.parse(response.content)

            logger.info(f"Successfully scored text: {scores}")
            return scores.dict()

        except Exception as e:
            logger.error(f"Error scoring text for salience: {e}")
            # Return default low scores if scoring fails
            return {
                "importance_score": 0.1,
                "personalness_score": 0.1,
                "actionability_score": 0.1,
                "reasoning": f"Error during scoring: {str(e)}",
            }

    def __del__(self):
        """Ensure all HTTP clients are properly closed on garbage collection."""
        # Cleanup resources when the object is destroyed
        try:
            for client in self._http_clients:
                if hasattr(client, "close"):
                    client.close()
        except Exception as e:
            logger.error(f"Error cleaning up HTTP clients: {e}")