"""
Agent state models for the LangGraph implementation in Django.
Adapted from kd_assistant_langgraph for Django integration.
"""
from typing import List, Dict, Any, Optional
from pydantic import Field
from langgraph.graph import MessagesState


class Message:
    """Convenience class for Message objects in state transitions."""

    HUMAN = "human"
    AI = "ai"
    SYSTEM = "system"
    TOOL = "tool"

    @staticmethod
    def get_message_type(message):
        """Get message type as string."""
        return message.type

    @staticmethod
    def is_user_message(message):
        """Check if message is from user."""
        return Message.get_message_type(message) == Message.HUMAN


class AgentState(MessagesState):
    """
    Shared state object for the LangGraph agents in Django.
    This state is passed between nodes in the graph.
    Inherits 'messages' from MessagesState.
    """

    # User identification for memory
    user_id: Optional[str] = Field(
        default=None,
        description="Unique identifier for the current user. Essential for personalized memory.",
    )

    # Original input is useful for nodes like rewrite_question or generate_answer
    original_input: str = Field(
        default="", description="The original user input for the current turn"
    )

    # Current question being processed (could be original or rewritten)
    current_question: str = Field(
        default="",
        description="The current question to be answered, possibly rewritten",
    )

    # Domain routing
    domain: Optional[str] = Field(
        default=None,
        description="The classified domain for the current query (e.g., 'general', 'dev', 'web', etc.)",
    )

    # --- Memory Related Fields ---
    current_turn_salience_scores: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Salience scores (importance, personalness, actionability, reasoning) for the current user input or processed text.",
    )

    long_term_memory_retrieval_query: Optional[str] = Field(
        default=None,
        description="The query used to fetch long-term memories for the current turn.",
    )

    retrieved_long_term_memories: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Memories retrieved from long-term storage for the current turn, formatted for context.",
    )

    # RAG / Knowledge Retrieval - distinct from conversational memory
    current_retrieved_knowledge_docs: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Documents retrieved from general knowledge RAG for the current query (not conversational memory). Each dict should have 'page_content' and 'metadata'.",
    )

    # Context for LLM
    consolidated_context_for_llm: Optional[str] = Field(
        default=None,
        description="The final combined context (short-term history, long-term memories, profile info, retrieved knowledge docs) to be passed to the LLM.",
    )

    # --- End Memory Related Fields ---

    # Routing and control flow
    next_node: Optional[str] = Field(
        default=None, description="The next node to route to"
    )

    # Specific agent responses
    orchestrator_response: Optional[Any] = Field(
        default=None, description="Response from the orchestrator agent"
    )
    dev_agent_response: Optional[Any] = Field(
        default=None, description="Response from the developer agent"
    )
    business_agent_response: Optional[Any] = Field(
        default=None, description="Response from the business agent"
    )
    learning_agent_response: Optional[Any] = Field(
        default=None, description="Response from the learning agent"
    )
    web_agent_response: Optional[Any] = Field(
        default=None, description="Response from the web agent"
    )
    music_agent_response: Optional[Any] = Field(
        default=None, description="Response from the music agent"
    )
    trivia_agent_response: Optional[Any] = Field(
        default=None, description="Response from the trivia agent"
    )

    # Error handling
    error: Optional[str] = Field(
        default=None, description="Error message if any occurred"
    )

    # Task management
    active_tasks: Optional[Dict[str, Any]] = Field(
        default=None, description="Currently active tasks"
    )
    pending_intervention_for_task_id: Optional[str] = Field(
        default=None, description="Task ID needing intervention"
    )

    # For background tasks
    background_tasks: Optional[Dict[str, Dict[str, Any]]] = Field(
        default=None, description="Background tasks that are currently running"
    )

    # Pending save consideration for memory tools
    pending_save_consideration: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Information flagged as potentially worth saving to memory",
    )

    # Django-specific fields
    session_id: Optional[str] = Field(
        default=None, description="Django session identifier"
    )
    
    websocket_connection_id: Optional[str] = Field(
        default=None, description="WebSocket connection identifier for streaming"
    )
    
    emotion_context: Optional[Dict[str, Any]] = Field(
        default=None, description="Current emotion context from Hume AI analysis"
    )
    
    streaming_session_id: Optional[str] = Field(
        default=None, description="Streaming session identifier for real-time features"
    )