#!/usr/bin/env python3
"""
Detailed logging test for memory-based filler responses.
Shows step-by-step what happens during memory retrieval and filler generation.
"""
import asyncio
import os
import sys
import time
import unittest
from unittest.mock import Mock, patch, AsyncMock

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')

import django
django.setup()

from chat.services.fast_response_service import FastResponseService


class MockUser:
    """Mock user for testing."""
    def __init__(self, personality='caringFriend', companion_name='<PERSON>', first_name='<PERSON>'):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = first_name
        self.id = f"detailed_test_{personality}"


class DetailedMemoryFillerTests(unittest.TestCase):
    """Detailed tests with comprehensive logging for memory-based filler responses."""
    
    def setUp(self):
        """Set up test fixtures with detailed logging."""
        print("\n" + "="*80)
        print("🔧 SETTING UP DETAILED MEMORY FILLER TEST")
        print("="*80)
        
        self.user = MockUser('caringFriend', 'Ella', 'Alex')
        print(f"👤 Created user: {self.user.first_name} ({self.user.ai_companion_name}, {self.user.selected_personality})")
        
        self.service = FastResponseService(user=self.user)
        print(f"⚡ Initialized FastResponseService")
        print(f"🧠 Memory manager available: {self.service.memory_manager is not None}")
        
        if self.service.memory_manager:
            print("📝 Setting up test memories...")
            self._setup_test_memories()
        else:
            print("❌ Memory manager not available - tests will be limited")
    
    def _setup_test_memories(self):
        """Set up comprehensive test memories with logging."""
        memories = [
            {
                "text": "Alex works as a data scientist at TechCorp, specializing in machine learning",
                "type": "semantic_profile",
                "importance": 0.9,
                "personalness": 0.8,
                "actionability": 0.3,
                "category": "work"
            },
            {
                "text": "Alex mentioned being excited about the new deep learning project starting next month",
                "type": "episodic_summary", 
                "importance": 0.8,
                "personalness": 0.7,
                "actionability": 0.6,
                "category": "work_event"
            },
            {
                "text": "Alex loves photography and has a Canon EOS R5 camera for landscape shots",
                "type": "semantic_profile",
                "importance": 0.7,
                "personalness": 0.9,
                "actionability": 0.2,
                "category": "hobby"
            },
            {
                "text": "Had a conversation about planning a photography trip to Iceland for the Northern Lights",
                "type": "episodic_summary",
                "importance": 0.7,
                "personalness": 0.8,
                "actionability": 0.5,
                "category": "travel_plan"
            },
            {
                "text": "Alex is learning to play guitar and practices jazz standards",
                "type": "semantic_profile",
                "importance": 0.6,
                "personalness": 0.8,
                "actionability": 0.3,
                "category": "hobby"
            },
            {
                "text": "Alex's dog Max had a vet checkup last week and got a clean bill of health",
                "type": "episodic_summary",
                "importance": 0.6,
                "personalness": 0.9,
                "actionability": 0.4,
                "category": "pet_care"
            }
        ]
        
        for i, memory in enumerate(memories, 1):
            self.service.memory_manager.store_memory(
                text=memory["text"],
                memory_type=memory["type"],
                user_id="detailed_test_user",
                importance_score=memory["importance"],
                personalness_score=memory["personalness"],
                actionability_score=memory["actionability"]
            )
            print(f"   {i}. [{memory['category']}] {memory['text'][:50]}... (imp:{memory['importance']:.1f})")
        
        print(f"✅ Stored {len(memories)} test memories")
    
    def test_memory_retrieval_detailed(self):
        """Test memory retrieval with detailed logging."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")
        
        print("\n" + "="*80)
        print("🔍 TESTING MEMORY RETRIEVAL (DETAILED)")
        print("="*80)
        
        print("📋 Retrieving contextual memories for user...")
        start_time = time.time()
        
        memories = self.service._get_contextual_memories("detailed_test_user", limit=3)
        
        retrieval_time = (time.time() - start_time) * 1000
        print(f"⏱️  Memory retrieval took: {retrieval_time:.1f}ms")
        print(f"📊 Retrieved {len(memories)} memories")
        
        if memories:
            print("\n🧠 RETRIEVED MEMORIES:")
            for i, memory in enumerate(memories, 1):
                mem_type = memory.get('memory_type', 'unknown')
                text = memory.get('text', '')
                importance = memory.get('importance_score', 0)
                personalness = memory.get('personalness_score', 0)
                actionability = memory.get('actionability_score', 0)
                
                print(f"\n   Memory {i}:")
                print(f"   📝 Type: {mem_type}")
                print(f"   💬 Text: {text}")
                print(f"   📊 Scores: Imp={importance:.1f}, Per={personalness:.1f}, Act={actionability:.1f}")
        else:
            print("❌ No memories retrieved")
        
        # Test assertions
        self.assertGreater(len(memories), 0, "Should retrieve at least one memory")
        self.assertLessEqual(len(memories), 3, "Should not exceed limit")
        
        print("✅ Memory retrieval test passed")
    
    def test_system_prompt_enhancement_detailed(self):
        """Test system prompt enhancement with detailed logging."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")
        
        print("\n" + "="*80)
        print("📝 TESTING SYSTEM PROMPT ENHANCEMENT (DETAILED)")
        print("="*80)
        
        # Test without agent processing
        print("🔹 Generating prompt WITHOUT agent processing...")
        prompt_simple = self.service._build_fast_system_prompt(
            emotion_context=None,
            needs_agent=False,
            user_id="detailed_test_user"
        )
        
        print(f"📏 Simple prompt length: {len(prompt_simple)} characters")
        print(f"🧠 Contains memory context: {'CONTEXTUAL MEMORIES' in prompt_simple}")
        
        # Test with agent processing
        print("\n🔹 Generating prompt WITH agent processing...")
        start_time = time.time()
        
        prompt_agent = self.service._build_fast_system_prompt(
            emotion_context=None,
            needs_agent=True,
            user_id="detailed_test_user"
        )
        
        generation_time = (time.time() - start_time) * 1000
        print(f"⏱️  Prompt generation took: {generation_time:.1f}ms")
        print(f"📏 Agent prompt length: {len(prompt_agent)} characters")
        print(f"📈 Enhancement: +{len(prompt_agent) - len(prompt_simple)} characters")
        
        # Analyze memory context
        has_memory_context = 'CONTEXTUAL MEMORIES' in prompt_agent
        has_examples = 'EXAMPLES of good filler responses' in prompt_agent
        
        print(f"🧠 Contains memory context: {has_memory_context}")
        print(f"💡 Contains filler examples: {has_examples}")
        
        if has_memory_context:
            print("\n🎯 MEMORY CONTEXT SECTION:")
            lines = prompt_agent.split('\n')
            in_memory_section = False
            
            for line in lines:
                if 'CONTEXTUAL MEMORIES' in line:
                    in_memory_section = True
                    print(f"   {line}")
                elif in_memory_section and (line.strip().startswith('•') or 'Use the contextual' in line):
                    print(f"   {line}")
                elif in_memory_section and line.strip() and not line.strip().startswith('•'):
                    break
        
        # Check for specific memory content
        prompt_lower = prompt_agent.lower()
        memory_indicators = [
            'data scientist', 'techcorp', 'machine learning',
            'photography', 'canon', 'landscape',
            'guitar', 'jazz', 'standards',
            'iceland', 'northern lights',
            'dog', 'max', 'vet'
        ]
        
        found_indicators = [indicator for indicator in memory_indicators if indicator in prompt_lower]
        print(f"\n🔍 Memory content analysis:")
        print(f"   Total indicators checked: {len(memory_indicators)}")
        print(f"   Found in prompt: {len(found_indicators)}")
        print(f"   Found indicators: {', '.join(found_indicators[:5])}{'...' if len(found_indicators) > 5 else ''}")
        
        # Test assertions
        self.assertTrue(has_memory_context, "Agent prompt should contain memory context")
        self.assertTrue(has_examples, "Agent prompt should contain filler examples")
        self.assertGreater(len(found_indicators), 0, "Should contain some memory content")
        
        print("✅ System prompt enhancement test passed")
    
    def test_agent_processing_detection_detailed(self):
        """Test agent processing detection with detailed logging."""
        print("\n" + "="*80)
        print("🤖 TESTING AGENT PROCESSING DETECTION (DETAILED)")
        print("="*80)
        
        test_cases = [
            # (query, expected_result, category)
            ("Hello, how are you today?", False, "Simple Greeting"),
            ("Tell me a funny joke about cats", False, "Humor Request"),
            ("You're so sweet, I love you", False, "Romantic/Affection"),
            ("Can you help me analyze this business plan?", True, "Business Analysis"),
            ("I need help with this complex math problem", True, "Problem Solving"),
            ("Research the latest trends in AI for my presentation", True, "Research Task"),
            ("Send an email to my colleague about the meeting", True, "Email Task"),
            ("What's a good restaurant for Italian food nearby?", True, "Recommendation"),
            ("Help me plan a comprehensive study schedule", True, "Planning Task"),
            ("Good night, sweet dreams", False, "Casual Goodbye")
        ]
        
        print(f"📋 Testing {len(test_cases)} different query types...")
        
        results = {'correct': 0, 'total': 0, 'details': []}
        
        for query, expected, category in test_cases:
            start_time = time.time()
            result = self.service._needs_agent_processing(query)
            detection_time = (time.time() - start_time) * 1000
            
            is_correct = result == expected
            status = "✅" if is_correct else "❌"
            
            results['total'] += 1
            if is_correct:
                results['correct'] += 1
            
            results['details'].append({
                'query': query,
                'expected': expected,
                'actual': result,
                'category': category,
                'correct': is_correct,
                'time_ms': detection_time
            })
            
            print(f"   {status} {category:20} | Agent: {str(result):5} | Time: {detection_time:4.1f}ms | '{query[:40]}...'")
        
        accuracy = (results['correct'] / results['total']) * 100
        avg_time = sum(d['time_ms'] for d in results['details']) / len(results['details'])
        
        print(f"\n📊 DETECTION RESULTS:")
        print(f"   Accuracy: {results['correct']}/{results['total']} ({accuracy:.1f}%)")
        print(f"   Average detection time: {avg_time:.1f}ms")
        
        if results['correct'] < results['total']:
            print(f"\n❌ INCORRECT DETECTIONS:")
            for detail in results['details']:
                if not detail['correct']:
                    print(f"   {detail['category']}: Expected {detail['expected']}, got {detail['actual']}")
                    print(f"      Query: '{detail['query']}'")
        
        # Test assertion
        self.assertGreater(accuracy, 85, f"Detection accuracy too low: {accuracy:.1f}%")
        
        print("✅ Agent processing detection test passed")
    
    @patch('chat.services.fast_response_service.ChatGroq')
    def test_complete_flow_detailed(self, mock_groq):
        """Test complete memory-enhanced response flow with detailed logging."""
        if not self.service.memory_manager:
            self.skipTest("Memory manager not available")
        
        print("\n" + "="*80)
        print("🔄 TESTING COMPLETE MEMORY-ENHANCED FLOW (DETAILED)")
        print("="*80)
        
        # Setup mock response
        mock_chunk = Mock()
        mock_chunk.content = "I'll analyze that data science project for you! Let me dive into the machine learning aspects. By the way, how's your deep learning project at TechCorp going?"
        
        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]
        
        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm
        
        query = "Can you analyze this data science project and give me detailed feedback?"
        print(f"💬 Test Query: '{query}'")
        
        async def test_flow():
            print("\n🔹 Starting response flow...")
            
            responses = []
            start_time = time.time()
            
            async for chunk in self.service.process_query_fast(
                user_input=query,
                user_id="detailed_test_user",
                streaming=True
            ):
                chunk_time = (time.time() - start_time) * 1000
                chunk_type = chunk.get('type', 'unknown')
                
                print(f"   📦 Chunk received: {chunk_type} at {chunk_time:.1f}ms")
                
                if chunk_type == 'response_chunk':
                    content = chunk.get('content', '')
                    print(f"      Content: '{content[:50]}...'")
                elif chunk_type == 'response_complete':
                    full_content = chunk.get('full_content', '')
                    needs_agent = chunk.get('needs_agent_processing', False)
                    source = chunk.get('source', 'unknown')
                    print(f"      Full response: '{full_content}'")
                    print(f"      Source: {source}")
                    print(f"      Needs agent: {needs_agent}")
                elif chunk_type == 'agent_processing_started':
                    message = chunk.get('message', '')
                    print(f"      Background message: '{message}'")
                
                responses.append(chunk)
            
            total_time = (time.time() - start_time) * 1000
            print(f"\n⏱️  Total flow time: {total_time:.1f}ms")
            
            # Analyze responses
            response_chunks = [r for r in responses if r['type'] == 'response_chunk']
            complete_responses = [r for r in responses if r['type'] == 'response_complete']
            agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']
            
            print(f"\n📊 RESPONSE ANALYSIS:")
            print(f"   Response chunks: {len(response_chunks)}")
            print(f"   Complete responses: {len(complete_responses)}")
            print(f"   Agent processing events: {len(agent_processing)}")
            
            # Check LLM call details
            if mock_llm.astream.called:
                print(f"\n🤖 LLM CALL ANALYSIS:")
                call_args = mock_llm.astream.call_args[0][0]
                system_message = call_args[0].content
                
                print(f"   System message length: {len(system_message)} chars")
                print(f"   Contains memory context: {'CONTEXTUAL MEMORIES' in system_message}")
                
                # Show memory references in system message
                if 'CONTEXTUAL MEMORIES' in system_message:
                    print(f"   🧠 Memory context found in LLM call")
                    
                    # Extract memory lines
                    lines = system_message.split('\n')
                    memory_lines = [line for line in lines if line.strip().startswith('•')]
                    
                    print(f"   Memory references: {len(memory_lines)}")
                    for i, line in enumerate(memory_lines[:3], 1):
                        print(f"      {i}. {line.strip()}")
            
            # Test assertions
            self.assertGreater(len(response_chunks), 0, "Should have response chunks")
            self.assertEqual(len(complete_responses), 1, "Should have one complete response")
            self.assertTrue(mock_llm.astream.called, "Should call LLM")
            
            if complete_responses:
                complete_response = complete_responses[0]
                self.assertTrue(complete_response.get('needs_agent_processing', False), 
                               "Complex query should need agent processing")
            
            print("✅ Complete flow test passed")
        
        # Run the async test
        asyncio.run(test_flow())


def run_detailed_memory_tests():
    """Run detailed memory filler tests with comprehensive logging."""
    print("🚀 RUNNING DETAILED MEMORY FILLER TESTS")
    print("="*80)
    print("This test suite provides detailed logging of the memory-based")
    print("filler response system to show exactly how it works.")
    print()
    
    # Create test suite
    suite = unittest.TestSuite()
    suite.addTest(DetailedMemoryFillerTests('test_memory_retrieval_detailed'))
    suite.addTest(DetailedMemoryFillerTests('test_system_prompt_enhancement_detailed'))
    suite.addTest(DetailedMemoryFillerTests('test_agent_processing_detection_detailed'))
    suite.addTest(DetailedMemoryFillerTests('test_complete_flow_detailed'))
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    print("\n" + "="*80)
    print("📊 DETAILED TEST SUMMARY")
    print("="*80)
    
    if result.wasSuccessful():
        print("✅ ALL DETAILED TESTS PASSED!")
        print("\n🎉 Memory-based filler response system is working perfectly!")
        print("\n🔍 Key observations from detailed testing:")
        print("  • Memory retrieval is fast and accurate")
        print("  • System prompts are properly enhanced with context")
        print("  • Agent processing detection is highly accurate")
        print("  • Complete flow integrates all components seamlessly")
        print("  • Memory content is successfully used in responses")
    else:
        print("❌ SOME DETAILED TESTS FAILED")
        if result.failures:
            print(f"\nFailures: {len(result.failures)}")
        if result.errors:
            print(f"Errors: {len(result.errors)}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_detailed_memory_tests()
    sys.exit(0 if success else 1)
