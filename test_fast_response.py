#!/usr/bin/env python3
"""
Test the fast response system to validate ≤450ms first TTS chunk target.
"""
import asyncio
import os
import sys
import time

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')

import django
django.setup()

from chat.services.fast_response_service import FastResponseService


class MockUser:
    def __init__(self, personality, companion_name):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = "TestUser"
        self.id = f"test_{personality}"


async def test_fast_response_performance():
    """Test fast response system performance."""
    print("⚡ Fast Response System Performance Test")
    print("Target: First TTS chunk in ≤450ms")
    print("=" * 50)
    
    # Test different personalities and query types
    test_cases = [
        # (personality, companion_name, query, expected_agent_processing)
        ('caring<PERSON>riend', '<PERSON>', 'Hello, how are you today?', <PERSON>als<PERSON>),
        ('playfulCompanion', 'Luna', 'Tell me a joke!', <PERSON>als<PERSON>),
        ('wiseMentor', '<PERSON>', 'What do you think about life?', False),
        ('caringFriend', 'Ella', 'I need help with this math problem', True),
        ('wiseMentor', 'Sage', 'Can you analyze the pros and cons of remote work?', True),
        ('supportiveTherapist', 'Dr. Hope', 'I\'m feeling stressed about work', False),
    ]
    
    results = []
    
    for i, (personality, companion_name, query, expects_agent) in enumerate(test_cases, 1):
        print(f"\n{i}. Testing {personality} ({companion_name})")
        print(f"   Query: '{query}'")
        print(f"   Expected agent processing: {expects_agent}")
        
        # Create user and service
        user = MockUser(personality, companion_name)
        service = FastResponseService(user=user)
        
        # Test response timing
        start_time = time.time()
        first_chunk_time = None
        total_chunks = 0
        response_content = ""
        needs_agent = False
        
        try:
            async for chunk in service.process_query_fast(
                user_input=query,
                user_id=f"test_{personality}",
                emotion_context={'primary_emotion': 'neutral', 'intensity': 0.5},
                streaming=True
            ):
                chunk_type = chunk.get('type')
                
                if chunk_type == 'response_chunk':
                    if first_chunk_time is None:
                        first_chunk_time = (time.time() - start_time) * 1000
                    
                    content = chunk.get('content', '')
                    response_content += content
                    total_chunks += 1
                
                elif chunk_type == 'response_complete':
                    full_content = chunk.get('full_content', '')
                    needs_agent = chunk.get('needs_agent_processing', False)
                    source = chunk.get('source', 'unknown')
                    total_time = (time.time() - start_time) * 1000
                    
                    # Performance assessment
                    time_status = "✅" if first_chunk_time and first_chunk_time <= 450 else "❌"
                    agent_status = "✅" if needs_agent == expects_agent else "⚠️"
                    
                    print(f"   {time_status} First chunk: {first_chunk_time:.1f}ms (target: ≤450ms)")
                    print(f"   📊 Total time: {total_time:.1f}ms")
                    print(f"   📝 Response length: {len(full_content)} chars")
                    print(f"   🤖 Agent processing: {needs_agent} {agent_status}")
                    print(f"   💬 Preview: '{full_content[:60]}...'")
                    
                    # Store results
                    results.append({
                        'personality': personality,
                        'companion_name': companion_name,
                        'query': query,
                        'first_chunk_time_ms': first_chunk_time,
                        'total_time_ms': total_time,
                        'response_length': len(full_content),
                        'needs_agent': needs_agent,
                        'expects_agent': expects_agent,
                        'target_met': first_chunk_time <= 450 if first_chunk_time else False
                    })
                    break
                
                elif chunk_type == 'agent_processing_started':
                    message = chunk.get('message', '')
                    print(f"   🔄 Background processing: {message}")
                
                elif chunk_type == 'error':
                    error = chunk.get('error', 'Unknown error')
                    print(f"   ❌ Error: {error}")
                    break
        
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    # Performance summary
    print("\n" + "=" * 50)
    print("📊 PERFORMANCE SUMMARY")
    print("=" * 50)
    
    if results:
        # Calculate statistics
        successful_responses = [r for r in results if r['first_chunk_time_ms'] is not None]
        target_met_count = len([r for r in successful_responses if r['target_met']])
        
        if successful_responses:
            avg_first_chunk = sum(r['first_chunk_time_ms'] for r in successful_responses) / len(successful_responses)
            min_first_chunk = min(r['first_chunk_time_ms'] for r in successful_responses)
            max_first_chunk = max(r['first_chunk_time_ms'] for r in successful_responses)
            
            print(f"\n⚡ First Chunk Performance:")
            print(f"   Average: {avg_first_chunk:.1f}ms")
            print(f"   Best: {min_first_chunk:.1f}ms")
            print(f"   Worst: {max_first_chunk:.1f}ms")
            print(f"   Target met: {target_met_count}/{len(successful_responses)} ({target_met_count/len(successful_responses)*100:.1f}%)")
            
            # Agent routing accuracy
            correct_routing = len([r for r in results if r['needs_agent'] == r['expects_agent']])
            routing_accuracy = correct_routing / len(results) * 100
            
            print(f"\n🤖 Agent Routing Accuracy:")
            print(f"   Correct routing: {correct_routing}/{len(results)} ({routing_accuracy:.1f}%)")
            
            # Overall assessment
            print(f"\n🎯 Overall Assessment:")
            if target_met_count / len(successful_responses) >= 0.8 and routing_accuracy >= 80:
                print("   ✅ EXCELLENT - System ready for real-time TTS!")
            elif target_met_count / len(successful_responses) >= 0.6:
                print("   ⚠️ GOOD - Minor optimizations needed")
            else:
                print("   ❌ NEEDS IMPROVEMENT - Significant optimization required")
            
            print(f"\n💡 Recommendations:")
            if avg_first_chunk > 450:
                print("   • Consider reducing max_tokens for even faster responses")
                print("   • Optimize system prompt length")
                print("   • Consider caching personality prompts")
            if routing_accuracy < 90:
                print("   • Refine agent processing triggers")
                print("   • Add more specific keywords for routing")
    
    else:
        print("❌ No successful responses to analyze")
    
    print("\n" + "=" * 50)


async def test_conversation_flow():
    """Test the conversation flow with agent processing."""
    print("\n🗣️ Conversation Flow Test")
    print("=" * 30)
    
    user = MockUser('caringFriend', 'Ella')
    service = FastResponseService(user=user)
    
    # Test a query that should trigger agent processing
    query = "I'm having trouble with this complex math problem. Can you help me solve it step by step?"
    
    print(f"Query: '{query}'")
    print("\nExpected flow:")
    print("1. Immediate response acknowledging the request")
    print("2. Background agent processing notification")
    print("3. Continued conversation while processing")
    
    print("\nActual flow:")
    
    async for chunk in service.process_query_fast(
        user_input=query,
        user_id="test_conversation",
        streaming=True
    ):
        chunk_type = chunk.get('type')
        
        if chunk_type == 'response_chunk':
            content = chunk.get('content', '')
            print(f"💬 Response chunk: '{content}'")
        
        elif chunk_type == 'response_complete':
            full_content = chunk.get('full_content', '')
            needs_agent = chunk.get('needs_agent_processing', False)
            print(f"✅ Complete response: '{full_content}'")
            print(f"🤖 Needs agent processing: {needs_agent}")
        
        elif chunk_type == 'agent_processing_started':
            message = chunk.get('message', '')
            print(f"🔄 Background processing started: {message}")
        
        elif chunk_type == 'error':
            error = chunk.get('error', '')
            print(f"❌ Error: {error}")


if __name__ == "__main__":
    async def main():
        await test_fast_response_performance()
        await test_conversation_flow()
    
    asyncio.run(main())
