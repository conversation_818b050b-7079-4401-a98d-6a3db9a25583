#!/usr/bin/env python3
"""
Create Memory-Enhanced Test Users
Creates realistic test users with comprehensive memory profiles for emotion detection testing.
"""
import os
import sys
import django
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, List, Any

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from agents.services.memory_manager import MemoryManager
from chat.services.memory_cache_service import MemoryCacheService


class MemoryEnhancedTestUserCreator:
    """Creates test users with realistic memory profiles for emotion detection testing."""
    
    def __init__(self):
        self.memory_manager = MemoryManager()
        self.memory_cache_service = MemoryCacheService()
    
    async def create_test_user_profiles(self):
        """Create comprehensive test user profiles with varied memory contexts."""
        
        print("🧠 CREATING MEMORY-ENHANCED TEST USERS")
        print("=" * 60)
        
        # Define test user profiles
        user_profiles = [
            {
                "email": "<EMAIL>",
                "name": "<PERSON> <PERSON>",
                "personality": "anxious_achiever",
                "emotional_patterns": ["anxiety", "stress", "determination", "relief"],
                "memory_profile": "high_stress_professional"
            },
            {
                "email": "<EMAIL>", 
                "name": "<PERSON>",
                "personality": "optimistic_social",
                "emotional_patterns": ["joy", "excitement", "gratitude", "contentment"],
                "memory_profile": "positive_social_connector"
            },
            {
                "email": "<EMAIL>",
                "name": "Emma Thompson", 
                "personality": "emotionally_sensitive",
                "emotional_patterns": ["sadness", "empathy", "love", "disappointment"],
                "memory_profile": "deep_emotional_processor"
            },
            {
                "email": "<EMAIL>",
                "name": "Alex Kim",
                "personality": "emotionally_balanced",
                "emotional_patterns": ["calmness", "curiosity", "satisfaction", "contemplation"],
                "memory_profile": "stable_reflective_type"
            },
            {
                "email": "<EMAIL>",
                "name": "Maya Patel",
                "personality": "creative_perfectionist",
                "emotional_patterns": ["frustration", "excitement", "joy", "pride", "contentment"],
                "memory_profile": "artistic_creative_type"
            },
            {
                "email": "<EMAIL>",
                "name": "David Johnson",
                "personality": "family_oriented_healer",
                "emotional_patterns": ["anger", "sadness", "empathy", "love", "gratitude", "peace"],
                "memory_profile": "relationship_focused_type"
            }
        ]
        
        created_users = []
        
        for profile in user_profiles:
            print(f"\n👤 Creating user: {profile['name']} ({profile['personality']})")
            
            # Create or get user
            user = await self._create_or_get_user(profile)
            user_id = str(user.id)
            
            # Create memory profile
            await self._create_memory_profile(user_id, profile)
            
            # Create emotional history
            await self._create_emotional_history(user_id, profile)
            
            # Create relationship memories
            await self._create_relationship_memories(user_id, profile)
            
            # Create trigger memories
            await self._create_trigger_memories(user_id, profile)
            
            created_users.append({
                "user": user,
                "profile": profile,
                "user_id": user_id
            })
            
            print(f"   ✅ Created comprehensive memory profile for {profile['name']}")
        
        return created_users
    
    async def _create_or_get_user(self, profile: Dict[str, Any]) -> User:
        """Create or get existing test user."""
        try:
            user = await sync_to_async(User.objects.get)(email=profile["email"])
            print(f"   📝 Using existing user: {profile['email']}")
        except User.DoesNotExist:
            user = await sync_to_async(User.objects.create_user)(
                username=profile["email"],
                email=profile["email"],
                password="memorytest123",
                first_name=profile["name"].split()[0],
                last_name=profile["name"].split()[-1]
            )
            print(f"   ✨ Created new user: {profile['email']}")
        
        return user
    
    async def _create_memory_profile(self, user_id: str, profile: Dict[str, Any]):
        """Create basic memory profile for the user."""
        
        profile_memories = {
            "high_stress_professional": [
                "I work in a high-pressure consulting job with tight deadlines",
                "I often feel anxious about meeting client expectations", 
                "I find relief in completing projects successfully",
                "I tend to overthink and worry about future outcomes"
            ],
            "positive_social_connector": [
                "I love connecting with people and building relationships",
                "I usually see the bright side of situations",
                "I get excited about social gatherings and events",
                "I feel grateful for the supportive people in my life"
            ],
            "deep_emotional_processor": [
                "I feel emotions very deeply and intensely",
                "I'm highly empathetic and pick up on others' emotions",
                "I sometimes get overwhelmed by emotional situations",
                "I value deep, meaningful connections with others"
            ],
            "stable_reflective_type": [
                "I prefer to think things through carefully before reacting",
                "I maintain emotional balance even in stressful situations",
                "I enjoy contemplating life's deeper questions",
                "I find satisfaction in understanding complex problems"
            ],
            "artistic_creative_type": [
                "I'm a visual artist who experiences intense creative highs and lows",
                "I'm a perfectionist who gets frustrated when my work doesn't meet my vision",
                "I find deep joy and fulfillment in the creative process",
                "I often struggle with creative blocks but breakthrough moments are magical"
            ],
            "relationship_focused_type": [
                "Family relationships are the most important thing in my life",
                "I believe in working through conflicts rather than avoiding them",
                "I have a strong capacity for empathy and understanding others",
                "I find peace and healing through honest communication and forgiveness"
            ]
        }
        
        memories = profile_memories.get(profile["memory_profile"], [])
        
        for i, memory_text in enumerate(memories):
            await self.memory_manager.async_store_memory(
                user_id=user_id,
                text=memory_text,
                memory_type="semantic_profile",
                importance_score=0.8,
                personalness_score=0.9,
                metadata={
                    "category": "personality_profile",
                    "emotional_relevance": "high",
                    "created_for": "memory_enhanced_testing"
                }
            )
    
    async def _create_emotional_history(self, user_id: str, profile: Dict[str, Any]):
        """Create emotional history memories for the user."""
        
        emotional_scenarios = {
            "anxiety": [
                "I felt really anxious before my big presentation last week",
                "I get nervous when I have to speak in public",
                "I worry a lot about making mistakes at work"
            ],
            "joy": [
                "I felt so happy when I got promoted last month",
                "I love the feeling of accomplishing something challenging", 
                "I get excited when I learn something new"
            ],
            "sadness": [
                "I felt really sad when my grandmother passed away",
                "I sometimes feel lonely when I'm away from family",
                "I get disappointed when plans don't work out"
            ],
            "stress": [
                "I feel stressed when I have multiple deadlines",
                "I get overwhelmed when too many things happen at once",
                "I find it hard to relax when work is busy"
            ],
            "gratitude": [
                "I'm grateful for my supportive friends and family",
                "I appreciate when people take time to help me",
                "I feel thankful for the opportunities I've had"
            ],
            "empathy": [
                "I feel deeply for others when they're going through hard times",
                "I can usually sense when someone is upset or troubled",
                "I often put myself in other people's shoes"
            ],
            "calmness": [
                "I feel most peaceful when I'm in nature",
                "I maintain my composure even in difficult situations",
                "I find meditation and quiet time very centering"
            ],
            "determination": [
                "I don't give up easily when facing challenges",
                "I push through difficulties to reach my goals",
                "I feel motivated when I have a clear purpose"
            ],
            "frustration": [
                "I get frustrated when my creative work doesn't match my vision",
                "I feel blocked when inspiration doesn't come naturally",
                "I sometimes get impatient with the creative process"
            ],
            "excitement": [
                "I feel electric when a new creative idea strikes me",
                "I get excited about exploring new artistic techniques",
                "I love the anticipation of starting a new project"
            ],
            "pride": [
                "I feel proud when I complete a challenging piece",
                "I take satisfaction in my artistic growth over time",
                "I'm proud of my unique creative voice and style"
            ],
            "contentment": [
                "I feel deeply satisfied after a productive creative session",
                "I find peace in the quiet moments of artistic reflection",
                "I'm content when my art expresses what I truly feel"
            ],
            "anger": [
                "I feel angry when family members dismiss my feelings",
                "I get upset when old patterns of conflict resurface",
                "I feel frustrated by years of misunderstanding"
            ],
            "resentment": [
                "I sometimes hold onto hurt from past family conflicts",
                "I feel resentful when my perspective isn't acknowledged",
                "I struggle with letting go of old grievances"
            ],
            "empathy": [
                "I try to understand what others are going through",
                "I can feel deeply for family members even when we disagree",
                "I believe everyone has their own struggles and pain"
            ],
            "love": [
                "I love my family deeply despite our challenges",
                "I feel overwhelming love when we connect authentically",
                "I cherish the moments of genuine understanding"
            ],
            "peace": [
                "I feel peaceful when family relationships are harmonious",
                "I find serenity in forgiveness and letting go",
                "I'm at peace when we can be ourselves together"
            ],
            "vulnerability": [
                "I feel vulnerable when sharing my deepest feelings",
                "I'm scared of being hurt again but willing to try",
                "I feel exposed when I open my heart to family"
            ]
        }
        
        for emotion in profile["emotional_patterns"]:
            scenarios = emotional_scenarios.get(emotion, [])
            
            for scenario in scenarios:
                # Vary the age of memories (recent to old)
                days_ago = [1, 7, 30, 90][scenarios.index(scenario) % 4]
                created_time = time.time() - (days_ago * 24 * 60 * 60)
                
                await self.memory_manager.async_store_memory(
                    user_id=user_id,
                    text=scenario,
                    memory_type="episodic",
                    importance_score=0.6 + (0.3 * (4 - days_ago/30)),  # Recent memories more important
                    personalness_score=0.8,
                    metadata={
                        "category": "emotional_history",
                        "emotion": emotion,
                        "days_ago": days_ago,
                        "created_for": "memory_enhanced_testing"
                    }
                )
    
    async def _create_relationship_memories(self, user_id: str, profile: Dict[str, Any]):
        """Create relationship context memories."""
        
        relationship_memories = [
            "My partner is very supportive and understanding",
            "I have a close relationship with my sister",
            "My manager can be demanding but fair",
            "I enjoy spending time with my college friends",
            "My parents worry about me sometimes"
        ]
        
        for memory in relationship_memories:
            await self.memory_manager.async_store_memory(
                user_id=user_id,
                text=memory,
                memory_type="semantic_profile",
                importance_score=0.7,
                personalness_score=0.9,
                metadata={
                    "category": "relationships",
                    "emotional_relevance": "medium",
                    "created_for": "memory_enhanced_testing"
                }
            )
    
    async def _create_trigger_memories(self, user_id: str, profile: Dict[str, Any]):
        """Create emotional trigger memories."""
        
        trigger_scenarios = {
            "anxious_achiever": [
                "I get triggered when people criticize my work",
                "Unexpected changes in plans make me very anxious",
                "I feel upset when I don't meet my own high standards"
            ],
            "optimistic_social": [
                "I feel down when social events get cancelled",
                "I get frustrated when people are consistently negative",
                "I feel hurt when friends don't include me"
            ],
            "emotionally_sensitive": [
                "I get overwhelmed in crowded, noisy environments",
                "I feel deeply affected by sad movies or news",
                "I get upset when people dismiss my feelings"
            ],
            "emotionally_balanced": [
                "I rarely get triggered, but injustice bothers me",
                "I feel frustrated when people won't listen to reason",
                "I get annoyed by unnecessary drama"
            ],
            "creative_perfectionist": [
                "I get triggered when people criticize my artistic vision",
                "I feel overwhelmed when I can't achieve my creative goals",
                "I get frustrated when technical limitations block my expression"
            ],
            "family_oriented_healer": [
                "I get triggered by family conflict and tension",
                "I feel upset when family members won't communicate openly",
                "I get frustrated by repeated patterns of misunderstanding"
            ]
        }
        
        triggers = trigger_scenarios.get(profile["personality"], [])
        
        for trigger in triggers:
            await self.memory_manager.async_store_memory(
                user_id=user_id,
                text=trigger,
                memory_type="episodic",
                importance_score=0.9,  # Triggers are very important
                personalness_score=0.95,
                metadata={
                    "category": "emotional_triggers",
                    "trigger_type": "emotional",
                    "created_for": "memory_enhanced_testing"
                }
            )
    
    async def verify_memory_profiles(self, created_users: List[Dict[str, Any]]):
        """Verify that memory profiles were created successfully."""
        
        print(f"\n🔍 VERIFYING MEMORY PROFILES")
        print("=" * 40)
        
        for user_data in created_users:
            user_id = user_data["user_id"]
            profile = user_data["profile"]
            
            # Search for memories
            memories = await self.memory_manager.async_search_memories(
                query="emotion emotional feeling",
                user_id=user_id,
                k=20
            )
            
            print(f"\n👤 {profile['name']}:")
            print(f"   Total memories: {len(memories)}")
            
            # Count by category
            categories = {}
            for memory in memories:
                category = memory.get('metadata', {}).get('category', 'unknown')
                categories[category] = categories.get(category, 0) + 1
            
            for category, count in categories.items():
                print(f"   {category}: {count} memories")
            
            # Show sample memories
            if memories:
                print(f"   Sample memory: '{memories[0].get('text', '')[:60]}...'")


async def main():
    """Create memory-enhanced test users for emotion detection testing."""
    
    print("🧠 MEMORY-ENHANCED TEST USER CREATION")
    print("=" * 70)
    print("Creating realistic users with comprehensive memory profiles")
    
    creator = MemoryEnhancedTestUserCreator()
    
    # Create test users with memory profiles
    created_users = await creator.create_test_user_profiles()
    
    # Verify memory profiles
    await creator.verify_memory_profiles(created_users)
    
    print(f"\n✨ Memory-enhanced test users created successfully!")
    print(f"🎯 Ready for memory-informed emotion detection testing!")
    
    # Return user info for testing
    return created_users


if __name__ == "__main__":
    asyncio.run(main())
