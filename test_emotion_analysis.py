#!/usr/bin/env python3
"""
Test emotion analysis functionality and fix any issues.
"""
import os
import sys
import django
import asyncio
import time

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.services.hume_service import (
    HumeEmotionClient, 
    EmotionAnalysisResult, 
    emotion_result_to_dict,
    safe_get_emotion_context
)
from chat.services.error_recovery import error_recovery_manager

async def test_text_emotion_analysis():
    """Test text-based emotion analysis."""
    print("📝 Testing Text Emotion Analysis")
    print("-" * 40)
    
    try:
        client = HumeEmotionClient()
        test_texts = [
            "I'm so happy and excited about this!",
            "I feel really sad and disappointed.",
            "This is making me angry and frustrated.",
            "I'm feeling calm and peaceful today."
        ]
        
        for text in test_texts:
            print(f"\n🔍 Analyzing: '{text}'")
            start_time = time.time()
            
            result = await client.analyze_text_emotions(text)
            analysis_time = (time.time() - start_time) * 1000
            
            print(f"  ✅ Primary emotion: {result.primary_emotion}")
            print(f"  📊 Intensity: {result.emotion_intensity:.2f}")
            print(f"  ⏱️ Analysis time: {analysis_time:.1f}ms")
            print(f"  🎯 Confidence: {result.confidence_score:.2f}")
            
            # Test conversion to dict
            emotion_dict = emotion_result_to_dict(result)
            print(f"  🔄 Dict conversion: ✅")
            
            # Test safe context conversion
            safe_context = safe_get_emotion_context(result)
            print(f"  🛡️ Safe context: ✅")
            
        return True
        
    except Exception as e:
        print(f"❌ Text emotion analysis failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

async def test_audio_emotion_analysis():
    """Test audio-based emotion analysis with synthetic data."""
    print("\n🎵 Testing Audio Emotion Analysis")
    print("-" * 40)
    
    try:
        client = HumeEmotionClient()
        
        # Create synthetic audio data (16kHz, 1 second of silence)
        sample_rate = 16000
        duration = 1.0  # 1 second
        num_samples = int(sample_rate * duration)
        
        # Generate simple sine wave as test audio
        import math
        frequency = 440  # A4 note
        audio_samples = []
        for i in range(num_samples):
            sample = int(32767 * 0.1 * math.sin(2 * math.pi * frequency * i / sample_rate))
            # Convert to 16-bit PCM bytes
            audio_samples.extend([sample & 0xFF, (sample >> 8) & 0xFF])
        
        audio_data = bytes(audio_samples)
        
        print(f"🔍 Analyzing synthetic audio ({len(audio_data)} bytes)")
        start_time = time.time()
        
        result = await client.analyze_audio_stream(
            audio_data=audio_data,
            sample_rate=sample_rate
        )
        
        analysis_time = (time.time() - start_time) * 1000
        
        print(f"  ✅ Primary emotion: {result.primary_emotion}")
        print(f"  📊 Intensity: {result.emotion_intensity:.2f}")
        print(f"  ⏱️ Analysis time: {analysis_time:.1f}ms")
        print(f"  🎯 Confidence: {result.confidence_score:.2f}")
        print(f"  📈 Emotions detected: {len(result.emotions)}")
        
        # Test conversion functions
        emotion_dict = emotion_result_to_dict(result)
        safe_context = safe_get_emotion_context(result)
        print(f"  🔄 Conversions: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ Audio emotion analysis failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

async def test_circuit_breaker_recovery():
    """Test circuit breaker recovery for emotion analysis."""
    print("\n🔧 Testing Circuit Breaker Recovery")
    print("-" * 40)
    
    try:
        # Check current status
        status = error_recovery_manager.get_circuit_breaker_status()
        hume_status = status.get('hume', {})
        
        print(f"📊 Hume circuit breaker: {hume_status.get('state', 'unknown')}")
        print(f"📊 Failure count: {hume_status.get('failure_count', 0)}")
        
        # Reset if needed
        if hume_status.get('state') != 'closed':
            print("🔄 Resetting circuit breaker...")
            error_recovery_manager.reset_circuit_breaker('hume')
            print("✅ Circuit breaker reset")
        
        return True
        
    except Exception as e:
        print(f"❌ Circuit breaker test failed: {e}")
        return False

async def main():
    """Run all emotion analysis tests."""
    print("🧠 Emotion Analysis Test Suite")
    print("=" * 50)
    
    # Test 1: Circuit breaker health
    cb_ok = await test_circuit_breaker_recovery()
    
    # Test 2: Text emotion analysis
    text_ok = await test_text_emotion_analysis()
    
    # Test 3: Audio emotion analysis
    audio_ok = await test_audio_emotion_analysis()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    print(f"  🔧 Circuit Breaker: {'✅' if cb_ok else '❌'}")
    print(f"  📝 Text Analysis: {'✅' if text_ok else '❌'}")
    print(f"  🎵 Audio Analysis: {'✅' if audio_ok else '❌'}")
    
    all_passed = cb_ok and text_ok and audio_ok
    
    if all_passed:
        print("\n🎉 All emotion analysis tests PASSED!")
        print("✅ Emotion analysis should work properly now")
    else:
        print("\n⚠️ Some emotion analysis tests FAILED")
        print("🔧 Check the errors above and restart your Django server")
    
    print("\n💡 Recommendations:")
    print("1. Restart Django server to pick up fixes")
    print("2. Use longer timeouts for emotion analysis (3+ seconds)")
    print("3. Monitor circuit breaker status if issues persist")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
