#!/usr/bin/env python3
"""
Text-Only Emotion Detection Test
Tests if <PERSON>'s language model is working correctly.
"""
import asyncio
import websockets
import json
import ssl
import time

# Hume Configuration
HUME_API_KEY = "VcaXvkuAbmtrQTCI29D449EuMt2uYHWCFMs8rY4ior6zhFQ5"
WEBSOCKET_URL = "wss://api.hume.ai/v0/stream/models"

# Test texts with clear emotions
TEST_TEXTS = [
    {
        "text": "I am absolutely furious about this situation!",
        "expected": "anger"
    },
    {
        "text": "I'm so happy and excited about this amazing news!",
        "expected": "joy"
    },
    {
        "text": "I'm really worried and anxious about tomorrow.",
        "expected": "anxiety"
    },
    {
        "text": "Thank you so much, I'm incredibly grateful!",
        "expected": "gratitude"
    },
    {
        "text": "This is such a disappointment, nothing worked out.",
        "expected": "disappointment"
    }
]


async def test_text_emotion_detection():
    """Test text-only emotion detection."""
    print("🔤 TEXT-ONLY EMOTION DETECTION TEST")
    print("=" * 60)
    
    try:
        # Connect to Hume WebSocket
        websocket_url_with_key = f"{WEBSOCKET_URL}?apiKey={HUME_API_KEY}"
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        print(f"🔗 Connecting to: {WEBSOCKET_URL}")
        
        async with websockets.connect(
            websocket_url_with_key,
            ssl=ssl_context,
            ping_interval=None
        ) as websocket:
            print("✅ WebSocket connected successfully")
            
            for i, test_case in enumerate(TEST_TEXTS):
                text = test_case['text']
                expected = test_case['expected']
                
                print(f"\n📝 Test {i+1}: {expected}")
                print(f"   Text: '{text}'")
                
                # Send text message
                text_message = {
                    "models": {
                        "language": {}
                    },
                    "raw_text": True,
                    "data": text
                }
                
                print(f"📤 Sending: {json.dumps(text_message)}")
                
                start_time = time.time()
                await websocket.send(json.dumps(text_message))
                
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    response_time = (time.time() - start_time) * 1000
                    
                    print(f"📥 Response received in {response_time:.1f}ms")
                    
                    try:
                        response_data = json.loads(response)
                        print(f"📊 Response keys: {list(response_data.keys())}")
                        
                        if 'language' in response_data:
                            language_data = response_data['language']
                            print(f"📊 Language data keys: {list(language_data.keys())}")
                            
                            if 'predictions' in language_data:
                                predictions = language_data['predictions']
                                print(f"📊 Found {len(predictions)} predictions")
                                
                                if predictions:
                                    # Get emotions from all predictions
                                    all_emotions = []
                                    for pred in predictions:
                                        if 'emotions' in pred:
                                            all_emotions.extend(pred['emotions'])
                                    
                                    if all_emotions:
                                        # Aggregate emotions by name
                                        emotion_totals = {}
                                        for emotion in all_emotions:
                                            name = emotion['name']
                                            score = emotion['score']
                                            if name in emotion_totals:
                                                emotion_totals[name] += score
                                            else:
                                                emotion_totals[name] = score
                                        
                                        # Sort by total score
                                        sorted_emotions = sorted(emotion_totals.items(), key=lambda x: x[1], reverse=True)
                                        
                                        print(f"✅ Top 5 emotions:")
                                        for j, (name, score) in enumerate(sorted_emotions[:5]):
                                            match_indicator = "✓" if name.lower() == expected.lower() else " "
                                            print(f"   {j+1}. {name}: {score:.3f} {match_indicator}")
                                        
                                        # Check if expected emotion is in top 3
                                        top_3_names = [name.lower() for name, _ in sorted_emotions[:3]]
                                        if expected.lower() in top_3_names:
                                            print(f"✅ SUCCESS: {expected} found in top 3!")
                                        else:
                                            print(f"❌ MISS: {expected} not in top 3")
                                    else:
                                        print("⚠️ No emotions found in predictions")
                                else:
                                    print("⚠️ No predictions found")
                            else:
                                print("⚠️ No predictions key in language data")
                        else:
                            print("⚠️ No language key in response")
                            print(f"Raw response: {response[:200]}...")
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON decode error: {e}")
                        print(f"Raw response: {response[:200]}...")
                        
                except asyncio.TimeoutError:
                    print("❌ No response (timeout)")
                
                # Small delay between tests
                await asyncio.sleep(0.5)
                
    except Exception as e:
        print(f"❌ WebSocket error: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Run text-only emotion detection test."""
    print("🎯 HUME TEXT-ONLY EMOTION DETECTION TEST")
    print("=" * 70)
    
    await test_text_emotion_detection()
    
    print(f"\n✨ Text-only emotion detection test completed!")


if __name__ == "__main__":
    asyncio.run(main())
