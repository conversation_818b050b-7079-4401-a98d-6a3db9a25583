# Design Document

## Overview

This design document outlines the architecture and implementation details for integrating the Django backend with the Flutter frontend application. The integration will enable seamless communication between the Flutter app and the Django backend, supporting features such as authentication, user management, chat functionality, gamification, shop system, and Unity integration.

The design focuses on creating a comprehensive REST API layer using Django REST Framework, implementing JWT authentication for both HTTP and WebSocket connections, and ensuring that the backend models align with the Flutter app's requirements.

## Architecture

### High-Level Architecture

The system will follow a layered architecture:

1. **API Layer**: Django REST Framework endpoints for HTTP requests
2. **WebSocket Layer**: Django Channels for real-time communication
3. **Service Layer**: Business logic implementation
4. **Data Layer**: Django models and database interactions

```mermaid
graph TD
    A[Flutter App] -->|HTTP Requests| B[Django REST API]
    A -->|WebSocket| C[Django Channels]
    B --> D[Service Layer]
    C --> D
    D --> E[Data Layer]
    E --> F[Database]
    D --> G[External Services]
    G --> H[AI Services]
    G --> I[File Storage]
    G --> J[Push Notifications]
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant User as Flutter App
    participant Auth as Authentication API
    participant JWT as JWT Service
    participant DB as Database
    
    User->>Auth: Login Request
    Auth->>DB: Validate Credentials
    DB-->>Auth: User Data
    Auth->>JWT: Generate Token
    JWT-->>Auth: JWT Token
    Auth-->>User: Return JWT Token
    
    User->>Auth: Request with JWT
    Auth->>JWT: Validate Token
    JWT-->>Auth: Token Valid
    Auth->>DB: Process Request
    DB-->>Auth: Response Data
    Auth-->>User: Return Response
```

### WebSocket Authentication Flow

```mermaid
sequenceDiagram
    participant User as Flutter App
    participant WS as WebSocket
    participant Auth as JWT Middleware
    participant Consumer as Chat Consumer
    
    User->>WS: Connect with JWT
    WS->>Auth: Validate JWT
    Auth->>WS: User Authenticated
    WS->>Consumer: Connection Established
    User->>Consumer: Send Message
    Consumer->>User: Stream Response
```

## Components and Interfaces

### API Components

1. **Authentication API**
   - Registration
   - Login
   - Token Refresh
   - Social Authentication (Google, Apple)

2. **User Management API**
   - Profile Management
   - Settings Management
   - Progress Tracking

3. **Chat API**
   - Conversation Management
   - Message History
   - File Upload (Audio, Images)

4. **Gamification API**
   - XP and Level Management
   - Achievement System
   - Daily Rewards

5. **Shop API**
   - Item Listing
   - Purchase Processing
   - Inventory Management
   - Relationship Gates

6. **Unity Integration API**
   - Avatar Customization
   - Environment Management
   - Animation Control

### WebSocket Components

1. **Chat Consumer**
   - Real-time Message Processing
   - Typing Indicators
   - Status Updates

2. **Authentication Middleware**
   - JWT Validation for WebSocket

### Service Components

1. **Chat Service**
   - Message Processing
   - AI Response Generation
   - Memory Management

2. **File Service**
   - File Upload Handling
   - File Validation
   - File Serving

3. **Gamification Service**
   - XP Calculation
   - Level Progression
   - Achievement Unlocking

4. **Shop Service**
   - Purchase Validation
   - Inventory Management
   - Relationship Gate Checking

## Data Models

### User Model Extensions

```python
class User(AbstractUser):
    # Existing fields
    
    # New fields to match Flutter model
    selected_personality = models.CharField(
        max_length=50,
        choices=[
            ('caringFriend', 'Caring Friend'),
            ('playfulCompanion', 'Playful Companion'),
            ('wiseMentor', 'Wise Mentor'),
            ('romanticPartner', 'Romantic Partner'),
            ('supportiveTherapist', 'Supportive Therapist'),
        ],
        default='caringFriend'
    )
    selected_environment = models.CharField(max_length=100, default='cozy_room')
    owned_environments = models.JSONField(default=list)
    owned_outfits = models.JSONField(default=list)
    owned_pets = models.JSONField(default=list)
    preferences = models.JSONField(default=dict)
    ai_companion_name = models.CharField(max_length=100, default='Ella')
    bio = models.TextField(blank=True, null=True)
    enable_email_notifications = models.BooleanField(default=True)
    personality_traits = models.JSONField(default=list)
```

### UserProgress Model

```python
class UserProgress(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='progress')
    xp = models.IntegerField(default=0)
    level = models.IntegerField(default=1)
    hearts = models.IntegerField(default=0)
    messages_count = models.IntegerField(default=0)
    voice_messages_count = models.IntegerField(default=0)
    achievements = models.JSONField(default=list)
    total_time_spent = models.IntegerField(default=0)  # in seconds
```

### Wallet Model

```python
class Wallet(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='wallet')
    balance = models.IntegerField(default=0)
    lifetime_earnings = models.IntegerField(default=0)
```

### Shop Models

```python
class ShopItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.IntegerField()
    item_type = models.CharField(
        max_length=50,
        choices=[
            ('environment', 'Environment'),
            ('outfit', 'Outfit'),
            ('accessory', 'Accessory'),
            ('companion', 'Companion'),
            ('pet', 'Pet'),
        ]
    )
    image_url = models.URLField()
    preview_data = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    is_limited = models.BooleanField(default=False)
    stock_quantity = models.IntegerField(null=True, blank=True)
    level_requirement = models.IntegerField(default=1)
    relationship_level_requirement = models.IntegerField(default=1)
    
    def can_purchase(self, user):
        """Check if user can purchase this item"""
        if not self.is_active:
            return False, "Item is not available"
        
        if hasattr(user, 'progress') and user.progress.level < self.level_requirement:
            return False, f"Requires level {self.level_requirement}"
        
        if hasattr(user, 'wallet') and user.wallet.balance < self.price:
            return False, "Insufficient funds"
        
        if self.is_limited and self.stock_quantity is not None and self.stock_quantity <= 0:
            return False, "Out of stock"
        
        # Check if user already owns this item
        if self.user_inventories.filter(user=user).exists():
            return False, "Already owned"
        
        # Check relationship level requirement
        if hasattr(user, 'relationship') and user.relationship.level < self.relationship_level_requirement:
            return False, f"Requires relationship level {self.relationship_level_requirement}"
        
        return True, "Can purchase"
```

### Inventory Model

```python
class UserInventory(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='inventory_items')
    item = models.ForeignKey(ShopItem, on_delete=models.CASCADE, related_name='user_inventories')
    acquired_at = models.DateTimeField(auto_now_add=True)
    is_equipped = models.BooleanField(default=False)
```

### Chat Models

```python
class Conversation(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversations')
    title = models.CharField(max_length=255, default="New Conversation")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

class Message(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    content = models.TextField()
    message_type = models.CharField(
        max_length=20,
        choices=[
            ('text', 'Text'),
            ('voice', 'Voice'),
            ('image', 'Image'),
            ('system', 'System'),
            ('typing', 'Typing'),
        ],
        default='text'
    )
    sender = models.CharField(max_length=50)  # 'user' or 'assistant'
    created_at = models.DateTimeField(auto_now_add=True)
    media_url = models.URLField(null=True, blank=True)
    emotion = models.CharField(max_length=50, null=True, blank=True)
    metadata = models.JSONField(default=dict)
```

### Pet Model

```python
class Pet(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    name = models.CharField(max_length=100)
    pet_type = models.CharField(max_length=50)
    behavior = models.CharField(max_length=50)
    sound = models.CharField(max_length=50)
    image_url = models.URLField()
    animation_data = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
```

### Relationship Model

```python
class Relationship(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='relationship')
    level = models.IntegerField(default=1)
    xp = models.IntegerField(default=0)
    xp_to_next_level = models.IntegerField(default=100)
    last_interaction = models.DateTimeField(auto_now=True)
```

## API Endpoints

### Authentication Endpoints

```
POST /api/auth/register/
POST /api/auth/login/
POST /api/auth/refresh/
POST /api/auth/logout/
POST /api/auth/google/
POST /api/auth/apple/
```

### User Management Endpoints

```
GET /api/user/profile/
PUT /api/user/profile/
GET /api/user/settings/
PUT /api/user/settings/
GET /api/user/progress/
POST /api/user/progress/update/
```

### Gamification Endpoints

```
GET /api/user/level/
GET /api/user/achievements/
POST /api/achievements/unlock/
GET /api/user/wallet/
POST /api/user/wallet/add-currency/
GET /api/daily-rewards/
POST /api/daily-rewards/claim/
```

### Shop System Endpoints

```
GET /api/shop/items/
GET /api/shop/items/{category}/
POST /api/shop/purchase/
GET /api/user/inventory/
POST /api/shop/preview/
GET /api/shop/relationship-gates/
```

### Chat & Memory Endpoints

```
GET /api/conversations/
POST /api/conversations/
GET /api/conversations/{id}/messages/
POST /api/conversations/{id}/messages/
DELETE /api/conversations/{id}/
GET /api/memories/
POST /api/memories/
DELETE /api/memories/{id}/
```

### Unity Integration Endpoints

```
POST /api/unity/avatar/change-outfit/
POST /api/unity/avatar/change-environment/
POST /api/unity/avatar/play-animation/
GET /api/unity/avatar/settings/
POST /api/unity/voice-command/
```

### File Upload Endpoints

```
POST /api/upload/avatar/
POST /api/upload/audio/
GET /api/media/{file_id}/
```

## WebSocket Protocol

### Connection

```
ws://domain/ws/chat/?token=<jwt_token>
```

### Message Format

```json
{
  "type": "message",
  "message": {
    "content": "Hello, how are you?",
    "message_type": "text",
    "conversation_id": "uuid-here"
  }
}
```

### Typing Indicator

```json
{
  "type": "typing",
  "is_typing": true,
  "conversation_id": "uuid-here"
}
```

### Response Format

```json
{
  "type": "message",
  "message": {
    "id": "uuid-here",
    "content": "I'm doing well, thank you!",
    "message_type": "text",
    "sender": "assistant",
    "created_at": "2023-07-21T12:34:56Z",
    "emotion": "happy",
    "metadata": {}
  }
}
```

## Error Handling

### API Error Response Format

```json
{
  "error": {
    "code": "error_code",
    "message": "Human-readable error message",
    "details": {}
  }
}
```

### Common Error Codes

- `authentication_failed`: Authentication issues
- `validation_error`: Input validation failures
- `resource_not_found`: Requested resource doesn't exist
- `permission_denied`: User doesn't have permission
- `rate_limited`: Too many requests
- `server_error`: Internal server error

### WebSocket Error Handling

For WebSocket connections, errors will be sent as messages with the type "error":

```json
{
  "type": "error",
  "error": {
    "code": "error_code",
    "message": "Error message"
  }
}
```

### Fallback Mechanism

The system will implement a fallback mechanism for handling service failures:

```python
async def fallback_response(self, user_input: str, context: Dict[str, Any]) -> str:
    """
    Generate a fallback response when all services fail.
    
    Args:
        user_input: User's input message
        context: Context information
        
    Returns:
        Fallback response text
    """
    # Simple template-based fallback responses
    fallback_templates = [
        "I'm having trouble connecting to my services right now. Could you please try again in a moment?",
        "It seems I'm experiencing some technical difficulties. Please bear with me.",
        "I apologize, but I'm unable to process your request right now due to connectivity issues.",
        "My systems are currently experiencing some issues. Could we try again shortly?",
        "I'm sorry, but I can't access my full capabilities at the moment. Please try again soon."
    ]
    
    # Choose a template based on context
    if context.get('error_type') == 'timeout':
        response = "I apologize for the delay. My response is taking longer than expected. Please try again."
    elif context.get('error_type') == 'api_failure':
        response = random.choice(fallback_templates)
    else:
        response = "I apologize, but I encountered an unexpected issue. Please try again."
    
    return response
```

## Testing Strategy

### Unit Testing

- Test individual components in isolation
- Mock external dependencies
- Focus on business logic and edge cases

### Integration Testing

- Test API endpoints with actual database interactions
- Test WebSocket connections and message handling
- Verify authentication flows

### End-to-End Testing

- Simulate complete user journeys
- Test frontend-backend integration
- Verify real-time communication

### Performance Testing

- Load testing for API endpoints
- Stress testing for WebSocket connections
- Measure response times under various loads

## Security Considerations

### Authentication Security

- JWT tokens with short expiration
- Secure token storage
- HTTPS for all communications
- Rate limiting for authentication endpoints

### Data Protection

- Input validation and sanitization
- Parameterized queries to prevent SQL injection
- XSS protection
- CSRF protection for API endpoints

### File Upload Security

- File type validation
- File size limits
- Virus scanning
- Secure storage with access controls

### WebSocket Security

- Authentication middleware for WebSocket connections
- Message validation
- Rate limiting for message sending

## Performance Optimization

### Caching Strategy

- Redis for caching frequent queries
- Cache invalidation strategy
- Cache user data for quick access

### Database Optimization

- Indexing for frequently queried fields
- Query optimization
- Connection pooling

### WebSocket Optimization

- Connection management
- Message batching for multiple updates
- Efficient serialization/deserialization