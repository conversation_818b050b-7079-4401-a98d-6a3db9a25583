# Design Document

## Overview

The Real-time AI Companion system is designed to provide sub-450ms response times through a highly optimized streaming architecture. The system integrates multiple AI services (Groq for LLM, Hume AI for emotion detection and TTS) with WebSocket-based communication to create a natural, emotionally-aware conversational experience.

The architecture leverages the existing LangGraph multi-agent system while adding real-time streaming capabilities, emotion processing, and adaptive content filtering based on relationship development.

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    Client[Client WebSocket] --> Gateway[WebSocket Gateway]
    Gateway --> AudioProcessor[Audio Processing Pipeline]
    Gateway --> TextProcessor[Text Processing Pipeline]
    
    AudioProcessor --> HumeEmotion[Hume Expression Measurement]
    AudioProcessor --> Transcription[Speech-to-Text]
    
    TextProcessor --> EmotionContext[Emotion Context Builder]
    HumeEmotion --> EmotionContext
    
    EmotionContext --> AgentOrchestrator[Multi-Agent Orchestrator]
    Transcription --> AgentOrchestrator
    
    AgentOrchestrator --> GroqLLM[Groq LLM Streaming]
    AgentOrchestrator --> MemorySystem[Memory & Context]
    AgentOrchestrator --> ContentFilter[Adaptive Content Filter]
    
    GroqLLM --> ResponseProcessor[Response Processing]
    ResponseProcessor --> HumeTTS[Hume TTS Streaming]
    
    HumeTTS --> Gateway
    ResponseProcessor --> Gateway
    
    MemorySystem --> VectorDB[(Vector Database)]
    ContentFilter --> RelationshipDB[(Relationship DB)]
```

### Performance-Optimized Pipeline

The system is designed around a streaming pipeline that minimizes latency at each stage:

1. **Audio Input Processing (Target: <50ms)**
   - WebSocket receives audio chunks in real-time
   - Parallel processing: Emotion detection + Transcription
   - Stream transcription results as they become available

2. **Context Building (Target: <100ms)**
   - Emotion context integration with cached user profile
   - Memory retrieval using pre-computed embeddings
   - Relationship level lookup for content filtering

3. **LLM Processing (Target: <200ms to first token)**
   - Groq API with streaming enabled
   - Optimized prompts with pre-built context
   - Parallel agent routing and tool execution

4. **TTS Generation (Target: <100ms to first audio chunk)**
   - Hume TTS with streaming synthesis
   - Emotion-aware voice modulation
   - Chunked audio delivery

### Component Architecture

#### WebSocket Gateway (Django Implementation)
- **Technology**: Enhanced Django Channels with Redis backend
- **Location**: `chat/consumers.py` (enhanced from existing)
- **Responsibilities**:
  - Maintain persistent connections
  - Route messages to appropriate processors
  - Handle connection management and reconnection
  - Stream responses back to clients
- **Optimizations**:
  - Connection pooling
  - Message queuing for burst handling
  - Automatic scaling with Redis clustering

#### Audio Processing Pipeline (New Django Service)
- **Location**: `chat/services/audio_service.py` (new)
- **Input**: Base64-encoded audio chunks via WebSocket
- **Parallel Processing**:
  - **Hume Expression Measurement**: Emotion detection from audio
  - **Speech-to-Text**: Real-time transcription using Groq Whisper
- **Output**: Transcribed text + emotion scores
- **Optimizations**:
  - Streaming transcription with partial results
  - Cached emotion profiles for faster processing
  - Audio chunk buffering for optimal API calls

#### Multi-Agent Orchestrator (LangGraph in Django)
- **Location**: `agents/` Django app (enhanced from existing structure)
- **Reference**: Copy architecture from `kd_assistant_langgraph/agents/orchestrator_agent.py`
- **Implementation**: LangGraph StateGraph within Django app structure
- **Integration**: Django models and services integrated with LangGraph nodes
- **Enhancements**:
  - Emotion-aware prompt engineering
  - Streaming response generation via LangGraph streaming
  - Parallel tool execution using LangGraph parallel processing
  - Adaptive content filtering integration as LangGraph tools
- **Agent Architecture** (LangGraph Nodes):
  - `agents/nodes/business_agent.py`: Professional/work queries node
  - `agents/nodes/learning_agent.py`: Educational content node
  - `agents/nodes/music_agent.py`: Music recommendations/theory node
  - `agents/nodes/dev_agent.py`: Programming assistance node
  - `agents/nodes/orchestrator.py`: Main orchestration node
  - `agents/graph.py`: LangGraph StateGraph definition and compilation

#### Emotion Processing System
- **Input Sources**:
  - Hume audio emotion detection
  - Text sentiment analysis
  - Historical emotional patterns
- **Processing**:
  - Real-time emotion scoring
  - Emotional state tracking
  - Context-aware emotion interpretation
- **Integration**:
  - LLM prompt enhancement with emotional context
  - TTS voice modulation instructions
  - Memory salience scoring adjustment

#### Memory & Context System
- **Components**:
  - Vector database for semantic memory
  - Relational database for structured data
  - Redis cache for session context
- **Memory Types**:
  - Short-term: Current conversation context
  - Medium-term: Session-based interactions
  - Long-term: User preferences, relationship history
- **Optimizations**:
  - Pre-computed embeddings
  - Hierarchical memory retrieval
  - Context window management

#### Adaptive Content Filtering
- **Relationship Tracking**:
  - Interaction frequency and duration
  - Emotional intimacy indicators
  - User-initiated relationship progression
- **Content Levels**:
  - Level 1: General conversation, basic personality
  - Level 2: Personal topics, mild flirtation
  - Level 3: Intimate conversation, emotional support
  - Level 4: Adult content (with explicit user consent)
- **Implementation**:
  - Rule-based filtering with ML enhancement
  - Real-time relationship level assessment
  - User control and override mechanisms

## Components and Interfaces

### WebSocket Message Protocol

#### Client to Server Messages
```json
{
  "type": "audio_chunk",
  "data": "base64_encoded_audio",
  "chunk_id": "uuid",
  "is_final": false,
  "timestamp": "iso_timestamp"
}

{
  "type": "text_message",
  "content": "user message text",
  "conversation_id": "uuid",
  "timestamp": "iso_timestamp"
}

{
  "type": "emotion_feedback",
  "emotion_scores": {...},
  "user_validation": true/false
}
```

#### Server to Client Messages
```json
{
  "type": "transcription_partial",
  "text": "partial transcription",
  "confidence": 0.95,
  "chunk_id": "uuid"
}

{
  "type": "emotion_detected",
  "emotions": [
    {"name": "joy", "score": 0.8},
    {"name": "excitement", "score": 0.6}
  ],
  "timestamp": "iso_timestamp"
}

{
  "type": "llm_response_chunk",
  "content": "response text chunk",
  "chunk_id": "uuid",
  "is_final": false
}

{
  "type": "audio_chunk",
  "data": "base64_encoded_audio",
  "chunk_id": "uuid",
  "is_final": false,
  "voice_settings": {...}
}
```

### API Interfaces

#### Groq Integration
```python
class GroqStreamingClient:
    async def stream_chat_completion(
        self,
        messages: List[Dict],
        model: str = "llama-3.3-70b-versatile",
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> AsyncGenerator[str, None]:
        # Stream LLM responses with <200ms to first token
```

#### Hume AI Integration
```python
class HumeEmotionClient:
    async def analyze_audio_stream(
        self,
        audio_data: bytes
    ) -> Dict[str, float]:
        # Real-time emotion detection from audio

class HumeTTSClient:
    async def synthesize_streaming(
        self,
        text: str,
        voice_settings: Dict,
        emotion_context: Dict
    ) -> AsyncGenerator[bytes, None]:
        # Stream TTS audio with emotion modulation
```

#### Memory System Interface
```python
class MemoryManager:
    async def retrieve_context(
        self,
        user_id: str,
        query: str,
        emotion_context: Dict
    ) -> Dict[str, Any]:
        # Retrieve relevant memories with emotion weighting
    
    async def store_interaction(
        self,
        user_id: str,
        interaction_data: Dict,
        emotion_scores: Dict,
        salience_score: float
    ) -> None:
        # Store interaction with emotional context
```

### Agent System Integration

#### Enhanced Orchestrator
```python
class EmotionAwareOrchestrator:
    def __init__(self):
        self.emotion_processor = EmotionProcessor()
        self.content_filter = AdaptiveContentFilter()
        self.memory_manager = MemoryManager()
        self.groq_client = GroqStreamingClient()
    
    async def process_streaming_input(
        self,
        user_input: str,
        emotion_context: Dict,
        user_id: str
    ) -> AsyncGenerator[Dict, None]:
        # Main processing pipeline with streaming output
```

#### Specialized Agents Enhancement
Each existing agent (business, learning, music, dev) will be enhanced with:
- Emotion-aware response generation
- Streaming output capabilities
- Content filtering integration
- Memory context utilization

## Data Models

### User Relationship Model
```python
class UserRelationship(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    relationship_level = models.IntegerField(default=1)  # 1-4 scale
    total_interactions = models.IntegerField(default=0)
    emotional_intimacy_score = models.FloatField(default=0.0)
    last_interaction = models.DateTimeField(auto_now=True)
    progression_milestones = models.JSONField(default=dict)
    user_consent_flags = models.JSONField(default=dict)
```

### Emotion Context Model
```python
class EmotionContext(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_id = models.CharField(max_length=255)
    timestamp = models.DateTimeField(auto_now_add=True)
    audio_emotions = models.JSONField()  # Hume audio analysis
    text_emotions = models.JSONField()   # Text sentiment
    context_emotions = models.JSONField() # Derived emotional state
    confidence_score = models.FloatField()
```

### Streaming Session Model
```python
class StreamingSession(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_id = models.CharField(max_length=255, unique=True)
    websocket_id = models.CharField(max_length=255)
    started_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    performance_metrics = models.JSONField(default=dict)
```

### Performance Metrics Model
```python
class PerformanceMetrics(models.Model):
    session_id = models.CharField(max_length=255)
    timestamp = models.DateTimeField(auto_now_add=True)
    audio_processing_time = models.FloatField()
    emotion_detection_time = models.FloatField()
    llm_first_token_time = models.FloatField()
    tts_first_chunk_time = models.FloatField()
    total_response_time = models.FloatField()
    user_satisfaction_score = models.FloatField(null=True)
```

## Error Handling

### Graceful Degradation Strategy
1. **Hume API Failures**:
   - Fallback to local emotion detection
   - Use cached emotional profiles
   - Continue with neutral emotional context

2. **Groq API Failures**:
   - Fallback to OpenAI GPT-4
   - Use cached responses for common queries
   - Implement circuit breaker pattern

3. **TTS Failures**:
   - Fallback to text-only responses
   - Use alternative TTS providers
   - Cache common phrases

4. **WebSocket Disconnections**:
   - Automatic reconnection with exponential backoff
   - Session state preservation
   - Message queuing during disconnection

### Error Recovery Mechanisms
```python
class ErrorRecoveryManager:
    async def handle_api_failure(
        self,
        service: str,
        error: Exception,
        context: Dict
    ) -> Dict[str, Any]:
        # Implement service-specific recovery strategies
    
    async def fallback_response(
        self,
        user_input: str,
        context: Dict
    ) -> str:
        # Generate fallback responses when primary systems fail
```

## Testing Strategy

### Performance Testing
1. **Latency Testing**:
   - End-to-end response time measurement
   - Component-level performance profiling
   - Load testing with concurrent users
   - Network latency simulation

2. **Stress Testing**:
   - High concurrent user scenarios
   - API rate limit handling
   - Memory usage under load
   - WebSocket connection limits

3. **Integration Testing**:
   - Multi-service interaction testing
   - Failure scenario testing
   - Data consistency verification
   - Real-time streaming validation

### Functional Testing
1. **Emotion Detection Accuracy**:
   - Audio emotion recognition validation
   - Text sentiment analysis verification
   - Cross-modal emotion consistency
   - User feedback integration testing

2. **Content Filtering**:
   - Relationship level progression testing
   - Content appropriateness validation
   - User consent mechanism testing
   - Override functionality verification

3. **Memory System**:
   - Context retrieval accuracy
   - Memory persistence testing
   - Salience scoring validation
   - Cross-session continuity

### User Experience Testing
1. **Conversation Flow**:
   - Natural conversation progression
   - Emotional continuity validation
   - Response relevance testing
   - Personality consistency

2. **Real-time Interaction**:
   - Audio quality assessment
   - Transcription accuracy
   - Response timing validation
   - User satisfaction measurement

### Automated Testing Framework
```python
class RealTimeTestSuite:
    async def test_end_to_end_latency(self):
        # Measure complete pipeline performance
    
    async def test_emotion_integration(self):
        # Validate emotion-aware responses
    
    async def test_streaming_consistency(self):
        # Ensure streaming data integrity
    
    async def test_failure_recovery(self):
        # Validate graceful degradation
```

## Security and Privacy

### Data Protection
- End-to-end encryption for WebSocket communications
- Audio data encryption at rest and in transit
- User consent management for data usage
- GDPR compliance for EU users

### Content Safety
- Real-time content moderation
- User reporting mechanisms
- Automated safety triggers
- Human oversight for edge cases

### API Security
- Rate limiting per user and IP
- API key rotation and management
- Request validation and sanitization
- DDoS protection and mitigation

## Deployment and Scaling

### Infrastructure Requirements
- **WebSocket Servers**: Auto-scaling Django Channels workers
- **Redis Cluster**: For session management and caching
- **Database**: PostgreSQL with read replicas
- **CDN**: For audio file delivery
- **Load Balancer**: For WebSocket connection distribution

### Monitoring and Observability
- Real-time performance dashboards
- User experience metrics tracking
- API health monitoring
- Error rate and latency alerting

### Scaling Strategy
- Horizontal scaling of WebSocket workers
- Database sharding by user ID
- CDN optimization for global users
- Caching layer optimization