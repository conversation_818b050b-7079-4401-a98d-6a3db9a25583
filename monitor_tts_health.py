#!/usr/bin/env python3
"""
Monitor TTS health and automatically reset circuit breakers if needed.
Run this periodically to ensure TTS always works.
"""
import os
import sys
import django
import asyncio
import time

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.services.error_recovery import error_recovery_manager

async def monitor_and_fix_tts():
    """Monitor TTS health and fix issues automatically."""
    print(f"🔍 TTS Health Monitor - {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check circuit breaker status
    status = error_recovery_manager.get_circuit_breaker_status()
    hume_status = status.get('hume', {})
    
    print(f"📊 Hume Circuit Breaker: {hume_status.get('state', 'unknown')} (failures: {hume_status.get('failure_count', 0)})")
    
    # If Hume circuit breaker is open, reset it
    if hume_status.get('state') == 'open':
        print(f"🔧 Hume circuit breaker is open, resetting...")
        try:
            error_recovery_manager.reset_circuit_breaker('hume')
            print(f"✅ Hume circuit breaker reset successfully")
            
            # Verify reset worked
            new_status = error_recovery_manager.get_circuit_breaker_status()
            new_hume_status = new_status.get('hume', {})
            print(f"📊 New Hume status: {new_hume_status.get('state', 'unknown')}")
            
        except Exception as e:
            print(f"❌ Failed to reset Hume circuit breaker: {e}")
    else:
        print(f"✅ Hume circuit breaker is healthy")
    
    return hume_status.get('state') == 'closed'

def main():
    """Main monitoring function."""
    try:
        # Run the async monitor
        is_healthy = asyncio.run(monitor_and_fix_tts())
        
        if is_healthy:
            print(f"🎉 TTS is healthy and ready")
            return 0
        else:
            print(f"⚠️ TTS may have issues")
            return 1
            
    except Exception as e:
        print(f"❌ Monitor error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
