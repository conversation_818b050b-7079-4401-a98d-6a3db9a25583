#!/usr/bin/env python3
"""
Real Audio Expression Measurement Test
Tests expression measurement with actual speech audio.
"""
import os
import sys
import django
import asyncio
import time
import uuid
import tempfile
import subprocess

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.services.expression_measurement_service import expression_measurement_service


def create_speech_audio() -> bytes:
    """Create a simple speech audio using macOS 'say' command."""
    try:
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            temp_path = temp_file.name
        
        # Use macOS 'say' command to generate speech
        text = "Hello, this is a test of emotion detection. I am feeling happy today."
        
        # Generate speech audio
        result = subprocess.run([
            'say', '-o', temp_path, '--data-format=LEI16@16000', text
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Error generating speech: {result.stderr}")
            return None
        
        # Read the audio data
        with open(temp_path, 'rb') as f:
            audio_data = f.read()
        
        # Clean up
        os.unlink(temp_path)
        
        print(f"Generated speech audio: {len(audio_data)} bytes")
        return audio_data
        
    except Exception as e:
        print(f"Error creating speech audio: {e}")
        return None


async def test_with_real_audio():
    """Test expression measurement with real speech audio."""
    print("🎤 REAL AUDIO EXPRESSION MEASUREMENT TEST")
    print("=" * 50)
    
    # Generate real speech audio
    print("🗣️  Generating speech audio...")
    audio_data = create_speech_audio()
    
    if not audio_data:
        print("❌ Failed to generate speech audio")
        return
    
    print(f"✅ Generated {len(audio_data):,} bytes of speech audio")
    
    # Test expression measurement
    chunk_id = f"real_audio_{uuid.uuid4()}"
    
    print(f"\n🔬 Testing expression measurement...")
    start_time = time.time()
    
    try:
        result = await expression_measurement_service.analyze_audio_chunk(audio_data, chunk_id)
        
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        
        if result:
            print(f"✅ SUCCESS!")
            print(f"⏱️  Total latency: {latency_ms:.1f}ms")
            print(f"🎯 Processing time: {result.processing_time_ms:.1f}ms")
            print(f"😊 Dominant emotion: {result.dominant_emotion}")
            print(f"📊 Confidence: {result.confidence:.3f}")
            print(f"🔢 Total emotions: {len(result.emotions)}")
            
            # Show top emotions
            print(f"\n🏆 Top 5 emotions:")
            sorted_emotions = sorted(result.emotions, key=lambda x: x.get('score', 0), reverse=True)
            for i, emotion in enumerate(sorted_emotions[:5]):
                print(f"   {i+1}. {emotion.get('name', 'Unknown')}: {emotion.get('score', 0):.3f}")
            
            # Performance analysis
            target_ms = 100
            print(f"\n📈 Performance Analysis:")
            print(f"   Target: ≤{target_ms}ms")
            print(f"   Actual: {latency_ms:.1f}ms")
            print(f"   Status: {'✅ PASS' if latency_ms <= target_ms else '❌ FAIL'}")
            
            if latency_ms > target_ms:
                overhead = latency_ms - target_ms
                print(f"   Overhead: +{overhead:.1f}ms")
            
            return {
                'success': True,
                'latency_ms': latency_ms,
                'processing_time_ms': result.processing_time_ms,
                'dominant_emotion': result.dominant_emotion,
                'confidence': result.confidence,
                'emotions_count': len(result.emotions)
            }
        else:
            print(f"❌ FAILED - No result returned")
            print(f"⏱️  Latency: {latency_ms:.1f}ms")
            return {'success': False, 'latency_ms': latency_ms}
            
    except Exception as e:
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        print(f"❌ ERROR: {e}")
        print(f"⏱️  Latency: {latency_ms:.1f}ms")
        return {'success': False, 'latency_ms': latency_ms, 'error': str(e)}


async def test_multiple_samples():
    """Test with multiple speech samples."""
    print("\n🔄 MULTIPLE SAMPLES TEST")
    print("=" * 50)
    
    # Different speech samples
    samples = [
        "I am very excited about this project!",
        "This is quite frustrating and annoying.",
        "I feel calm and peaceful today.",
        "Oh no, that's really scary!",
        "What a wonderful surprise this is!"
    ]
    
    results = []
    
    for i, text in enumerate(samples):
        print(f"\n🎤 Sample {i+1}: '{text[:30]}...'")
        
        # Generate audio for this text
        try:
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name
            
            result = subprocess.run([
                'say', '-o', temp_path, '--data-format=LEI16@16000', text
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"   ❌ Failed to generate audio: {result.stderr}")
                continue
            
            with open(temp_path, 'rb') as f:
                audio_data = f.read()
            
            os.unlink(temp_path)
            
            # Test expression measurement
            chunk_id = f"sample_{i}_{uuid.uuid4()}"
            start_time = time.time()
            
            result = await expression_measurement_service.analyze_audio_chunk(audio_data, chunk_id)
            
            latency_ms = (time.time() - start_time) * 1000
            
            if result:
                print(f"   ✅ {result.dominant_emotion} ({result.confidence:.2f}) - {latency_ms:.1f}ms")
                results.append({
                    'text': text,
                    'emotion': result.dominant_emotion,
                    'confidence': result.confidence,
                    'latency_ms': latency_ms,
                    'success': True
                })
            else:
                print(f"   ❌ No emotions detected - {latency_ms:.1f}ms")
                results.append({
                    'text': text,
                    'latency_ms': latency_ms,
                    'success': False
                })
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Summary
    successful = [r for r in results if r.get('success')]
    
    if successful:
        avg_latency = sum(r['latency_ms'] for r in successful) / len(successful)
        print(f"\n📊 Summary:")
        print(f"   Successful: {len(successful)}/{len(results)}")
        print(f"   Average latency: {avg_latency:.1f}ms")
        print(f"   Emotions detected: {[r['emotion'] for r in successful]}")
    
    return results


async def main():
    """Run real audio expression measurement tests."""
    print("🚀 REAL AUDIO EXPRESSION MEASUREMENT TESTS")
    print("=" * 60)
    
    # Check if 'say' command is available (macOS)
    try:
        result = subprocess.run(['which', 'say'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ 'say' command not found. This test requires macOS.")
            return
    except Exception:
        print("❌ Cannot check for 'say' command. This test requires macOS.")
        return
    
    # Test 1: Single real audio sample
    single_result = await test_with_real_audio()
    
    # Test 2: Multiple samples
    multiple_results = await test_multiple_samples()
    
    # Final analysis
    print(f"\n🏆 FINAL ANALYSIS")
    print("=" * 60)
    
    if single_result and single_result.get('success'):
        print(f"✅ Expression measurement is working!")
        print(f"⏱️  Latency: {single_result['latency_ms']:.1f}ms")
        print(f"😊 Detected emotion: {single_result['dominant_emotion']}")
        
        # Latency assessment
        if single_result['latency_ms'] <= 100:
            print(f"🎉 EXCELLENT - Within 100ms target!")
        elif single_result['latency_ms'] <= 200:
            print(f"✅ GOOD - Reasonable latency for real-time use")
        elif single_result['latency_ms'] <= 500:
            print(f"⚠️  ACCEPTABLE - May impact real-time experience")
        else:
            print(f"❌ SLOW - Needs optimization for real-time use")
    else:
        print(f"❌ Expression measurement not working properly")
    
    # Service stats
    stats = expression_measurement_service.get_performance_stats()
    print(f"\n📈 Service Statistics:")
    print(f"   Total requests: {stats['total_requests']}")
    print(f"   Success rate: {stats['success_rate_percent']:.1f}%")
    print(f"   Average processing: {stats['avg_processing_time_ms']:.1f}ms")
    
    print(f"\n✨ Real audio test completed!")


if __name__ == "__main__":
    asyncio.run(main())
