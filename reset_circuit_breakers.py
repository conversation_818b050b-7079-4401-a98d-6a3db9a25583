#!/usr/bin/env python3
"""
Script to reset circuit breakers for testing purposes.
"""
import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.services.error_recovery import error_recovery_manager

def main():
    print("🔧 Circuit Breaker Status Before Reset:")
    status = error_recovery_manager.get_circuit_breaker_status()
    for service, info in status.items():
        print(f"  {service}: {info['state']} (failures: {info['failure_count']})")
    
    print("\n🔄 Resetting all circuit breakers...")
    error_recovery_manager.reset_all_circuit_breakers()
    
    print("\n✅ Circuit Breaker Status After Reset:")
    status = error_recovery_manager.get_circuit_breaker_status()
    for service, info in status.items():
        print(f"  {service}: {info['state']} (failures: {info['failure_count']})")
    
    print("\n🎉 All circuit breakers have been reset!")
    print("You can now try TTS again.")

if __name__ == "__main__":
    main()
