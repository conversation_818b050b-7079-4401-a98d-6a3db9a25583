#!/usr/bin/env python3
"""
Simple Expression Measurement Latency Test
Tests just the expression measurement service latency.
"""
import os
import sys
import django
import asyncio
import time
import math
import uuid

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.services.expression_measurement_service import expression_measurement_service


def generate_test_audio(duration_ms: int = 1000) -> bytes:
    """Generate test audio data (simple sine wave)."""
    sample_rate = 16000  # 16kHz
    samples = int(sample_rate * duration_ms / 1000)
    frequency = 440  # A4 note
    
    audio_data = bytearray()
    for i in range(samples):
        # Generate sine wave
        sample = int(32767 * math.sin(2 * math.pi * frequency * i / sample_rate))
        # Convert to 16-bit little-endian
        audio_data.extend(sample.to_bytes(2, byteorder='little', signed=True))
    
    return bytes(audio_data)


async def test_expression_latency():
    """Test expression measurement latency."""
    print("🎵 EXPRESSION MEASUREMENT LATENCY TEST")
    print("=" * 50)
    
    # Test different audio durations
    durations = [500, 1000, 2000, 3000]  # 0.5s, 1s, 2s, 3s
    results = []
    
    for duration in durations:
        print(f"\n🔬 Testing {duration}ms audio...")
        
        # Generate test audio
        test_audio = generate_test_audio(duration)
        chunk_id = f"test_{duration}ms_{uuid.uuid4()}"
        
        print(f"   📊 Audio size: {len(test_audio):,} bytes")
        
        # Test expression measurement
        start_time = time.time()
        
        try:
            result = await expression_measurement_service.analyze_audio_chunk(test_audio, chunk_id)
            
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            if result:
                print(f"   ✅ Success: {latency_ms:.1f}ms")
                print(f"   😊 Emotion: {result.dominant_emotion} ({result.confidence:.2f})")
                print(f"   📈 Processing: {result.processing_time_ms:.1f}ms")
                print(f"   🔢 Emotions: {len(result.emotions)} detected")
                
                results.append({
                    'duration_ms': duration,
                    'audio_size_bytes': len(test_audio),
                    'latency_ms': latency_ms,
                    'processing_time_ms': result.processing_time_ms,
                    'dominant_emotion': result.dominant_emotion,
                    'confidence': result.confidence,
                    'emotions_count': len(result.emotions),
                    'success': True
                })
            else:
                print(f"   ❌ Failed: {latency_ms:.1f}ms")
                results.append({
                    'duration_ms': duration,
                    'audio_size_bytes': len(test_audio),
                    'latency_ms': latency_ms,
                    'success': False
                })
                
        except Exception as e:
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            print(f"   ❌ Error: {latency_ms:.1f}ms - {e}")
            
            results.append({
                'duration_ms': duration,
                'audio_size_bytes': len(test_audio),
                'latency_ms': latency_ms,
                'error': str(e),
                'success': False
            })
    
    # Performance analysis
    print("\n📊 PERFORMANCE ANALYSIS")
    print("=" * 50)
    
    successful_results = [r for r in results if r.get('success')]
    
    if successful_results:
        avg_latency = sum(r['latency_ms'] for r in successful_results) / len(successful_results)
        min_latency = min(r['latency_ms'] for r in successful_results)
        max_latency = max(r['latency_ms'] for r in successful_results)
        
        print(f"✅ Successful tests: {len(successful_results)}/{len(results)}")
        print(f"⏱️  Average latency: {avg_latency:.1f}ms")
        print(f"⚡ Fastest: {min_latency:.1f}ms")
        print(f"🐌 Slowest: {max_latency:.1f}ms")
        
        # Check against target (100ms from settings)
        target_ms = 100
        target_met = avg_latency <= target_ms
        
        print(f"🎯 Target (≤{target_ms}ms): {'✅ PASS' if target_met else '❌ FAIL'}")
        
        if successful_results[0].get('dominant_emotion'):
            emotions = [r['dominant_emotion'] for r in successful_results]
            print(f"😊 Emotions detected: {', '.join(set(emotions))}")
    else:
        print("❌ No successful tests")
    
    # Service statistics
    stats = expression_measurement_service.get_performance_stats()
    print(f"\n📈 SERVICE STATISTICS")
    print(f"   Total requests: {stats['total_requests']}")
    print(f"   Success rate: {stats['success_rate_percent']:.1f}%")
    print(f"   Avg processing: {stats['avg_processing_time_ms']:.1f}ms")
    print(f"   API enabled: {'✅' if stats['api_enabled'] else '❌'}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS")
    if not stats['api_enabled']:
        print("   ⚠️  Hume API key not configured - expression measurement disabled")
    elif len(successful_results) == 0:
        print("   ❌ All tests failed - check API connectivity and credentials")
    elif successful_results and avg_latency > 100:
        print(f"   ⚠️  Average latency ({avg_latency:.1f}ms) exceeds 100ms target")
        print("   💡 Consider optimizing audio chunk size or using parallel processing")
    else:
        print("   ✅ Expression measurement is working well within performance targets!")
    
    return results


async def test_parallel_processing():
    """Test parallel processing of multiple audio chunks."""
    print("\n🔄 PARALLEL PROCESSING TEST")
    print("=" * 50)
    
    # Create multiple audio chunks
    chunk_count = 3
    chunks = []
    
    for i in range(chunk_count):
        audio = generate_test_audio(1000)  # 1 second each
        chunk_id = f"parallel_{i}"
        chunks.append((audio, chunk_id))
    
    print(f"🎵 Testing {chunk_count} audio chunks in parallel...")
    
    # Test parallel processing
    start_time = time.time()
    
    try:
        results = await expression_measurement_service.analyze_audio_batch(chunks)
        
        end_time = time.time()
        total_time = (end_time - start_time) * 1000
        
        print(f"✅ Parallel processing completed in {total_time:.1f}ms")
        print(f"📊 Results: {len(results)}/{chunk_count} successful")
        
        if results:
            avg_individual = sum(r.processing_time_ms for r in results) / len(results)
            print(f"⏱️  Average individual processing: {avg_individual:.1f}ms")
            print(f"🚀 Speedup: {(avg_individual * len(results) / total_time):.1f}x")
            
            for i, result in enumerate(results):
                print(f"   Chunk {i}: {result.dominant_emotion} ({result.confidence:.2f})")
        
        return total_time, len(results)
        
    except Exception as e:
        end_time = time.time()
        total_time = (end_time - start_time) * 1000
        print(f"❌ Parallel processing failed in {total_time:.1f}ms: {e}")
        return total_time, 0


async def main():
    """Run expression measurement tests."""
    print("🎯 Starting Expression Measurement Tests...")
    
    # Test 1: Basic latency test
    results = await test_expression_latency()
    
    # Test 2: Parallel processing test
    parallel_time, parallel_success = await test_parallel_processing()
    
    # Final summary
    print("\n🏆 FINAL SUMMARY")
    print("=" * 50)
    
    successful_count = sum(1 for r in results if r.get('success'))
    
    if successful_count > 0:
        avg_latency = sum(r['latency_ms'] for r in results if r.get('success')) / successful_count
        print(f"✅ Expression measurement is working!")
        print(f"⏱️  Average latency: {avg_latency:.1f}ms")
        print(f"🔄 Parallel processing: {parallel_success} chunks in {parallel_time:.1f}ms")
        
        if avg_latency <= 100:
            print("🎉 Performance target met - ready for production!")
        else:
            print("⚠️  Performance target exceeded - consider optimization")
    else:
        print("❌ Expression measurement not working - check configuration")
    
    print("\n✨ Test completed!")


if __name__ == "__main__":
    asyncio.run(main())
