#!/usr/bin/env python3
"""
Audio Format Converter for Hume AI
Converts ElevenLabs MP3 files to Hume-compatible format.
"""
import os
import subprocess
import json
from pathlib import Path

# Directories
AUDIO_LIBRARY_DIR = Path("expressive_audio_library")
CONVERTED_AUDIO_DIR = Path("hume_compatible_audio")
CONVERTED_AUDIO_DIR.mkdir(exist_ok=True)


def convert_mp3_to_wav(mp3_file: Path, output_file: Path) -> bool:
    """Convert MP3 to WAV format compatible with Hume AI."""
    try:
        # Use ffmpeg to convert to WAV with specific parameters for Hume
        # <PERSON> typically expects: 16-bit PCM, 16kHz or 44.1kHz sample rate
        cmd = [
            'ffmpeg', '-i', str(mp3_file),
            '-acodec', 'pcm_s16le',  # 16-bit PCM
            '-ar', '16000',          # 16kHz sample rate
            '-ac', '1',              # Mono channel
            '-y',                    # Overwrite output file
            str(output_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"   ✅ Converted: {mp3_file.name} → {output_file.name}")
            return True
        else:
            print(f"   ❌ Error converting {mp3_file.name}: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception converting {mp3_file.name}: {e}")
        return False


def check_ffmpeg_availability():
    """Check if ffmpeg is available."""
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ ffmpeg is available")
            return True
        else:
            print("❌ ffmpeg not found")
            return False
    except FileNotFoundError:
        print("❌ ffmpeg not installed")
        return False


def convert_audio_library():
    """Convert all MP3 files to Hume-compatible WAV format."""
    print("🔄 CONVERTING AUDIO LIBRARY FOR HUME COMPATIBILITY")
    print("=" * 60)
    
    # Check ffmpeg
    if not check_ffmpeg_availability():
        print("Please install ffmpeg: brew install ffmpeg")
        return False
    
    # Get all MP3 files
    mp3_files = list(AUDIO_LIBRARY_DIR.glob("*.mp3"))
    
    if not mp3_files:
        print("❌ No MP3 files found in audio library")
        return False
    
    print(f"📁 Found {len(mp3_files)} MP3 files to convert")
    
    successful_conversions = 0
    
    for mp3_file in mp3_files:
        # Create output filename
        wav_filename = mp3_file.stem + ".wav"
        wav_file = CONVERTED_AUDIO_DIR / wav_filename
        
        print(f"\n🔄 Converting: {mp3_file.name}")
        
        if convert_mp3_to_wav(mp3_file, wav_file):
            successful_conversions += 1
    
    print(f"\n🏆 CONVERSION COMPLETE")
    print(f"=" * 60)
    print(f"✅ Successfully converted: {successful_conversions}/{len(mp3_files)} files")
    print(f"📁 Converted files location: {CONVERTED_AUDIO_DIR}")
    
    # Copy metadata
    metadata_source = AUDIO_LIBRARY_DIR / "library_metadata.json"
    metadata_dest = CONVERTED_AUDIO_DIR / "library_metadata.json"
    
    if metadata_source.exists():
        import shutil
        shutil.copy2(metadata_source, metadata_dest)
        print(f"📄 Metadata copied to: {metadata_dest}")
    
    return successful_conversions == len(mp3_files)


def test_audio_format():
    """Test a single converted audio file."""
    print("\n🧪 TESTING CONVERTED AUDIO FORMAT")
    print("=" * 60)
    
    wav_files = list(CONVERTED_AUDIO_DIR.glob("*.wav"))
    
    if not wav_files:
        print("❌ No converted WAV files found")
        return
    
    # Test first file
    test_file = wav_files[0]
    
    try:
        # Use ffprobe to check audio properties
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', str(test_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            info = json.loads(result.stdout)
            
            print(f"📊 Audio format analysis for: {test_file.name}")
            
            if 'streams' in info and info['streams']:
                stream = info['streams'][0]
                print(f"   Codec: {stream.get('codec_name', 'Unknown')}")
                print(f"   Sample rate: {stream.get('sample_rate', 'Unknown')} Hz")
                print(f"   Channels: {stream.get('channels', 'Unknown')}")
                print(f"   Bit depth: {stream.get('bits_per_sample', 'Unknown')} bits")
                print(f"   Duration: {stream.get('duration', 'Unknown')} seconds")
            
            if 'format' in info:
                format_info = info['format']
                file_size = int(format_info.get('size', 0)) / 1024  # KB
                print(f"   File size: {file_size:.1f} KB")
            
            print("✅ Audio format looks good for Hume AI")
        else:
            print(f"❌ Error analyzing audio: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Exception analyzing audio: {e}")


if __name__ == "__main__":
    print("🎵 AUDIO FORMAT CONVERTER FOR HUME AI")
    print("=" * 70)
    
    # Convert the library
    success = convert_audio_library()
    
    if success:
        # Test the format
        test_audio_format()
        
        print(f"\n🚀 READY FOR HUME TESTING")
        print(f"Use the converted WAV files in: {CONVERTED_AUDIO_DIR}")
        print(f"These should be compatible with Hume AI's emotion detection.")
    else:
        print(f"\n❌ CONVERSION FAILED")
        print(f"Please check ffmpeg installation and try again.")
