import asyncio
import logging
import time
import uuid
from typing import Dict, List, Any, Optional, Tuple
from django.conf import settings
from django.utils import timezone
from asgiref.sync import sync_to_async
import openai
from openai import AsyncOpenAI
import chromadb
from chromadb.config import Settings
import numpy as np
from .models import Memory, MemoryRetrieval, MemoryConfiguration, MemoryType
import json
import hashlib

logger = logging.getLogger(__name__)


class MemorySalienceScorer:
    """Scores text for memory salience using LLM-based evaluation."""
    
    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def score_text(self, text: str, user_context: Optional[str] = None) -> Dict[str, Any]:
        """Score text for importance, personalness, and actionability."""
        try:
            prompt = self._build_scoring_prompt(text, user_context)
            
            response = await self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.0,
                max_tokens=200
            )
            
            # Parse the response
            result = self._parse_scoring_response(response.choices[0].message.content)
            return result
            
        except Exception as e:
            logger.error(f"Error scoring text for salience: {e}")
            # Return default scores if LLM fails
            return {
                'importance_score': 0.5,
                'personalness_score': 0.5,
                'actionability_score': 0.5,
                'reasoning': 'Default scores due to scoring error'
            }
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for salience scoring."""
        return """You are an expert at evaluating the salience of information for an AI assistant's memory. 
Your goal is to help the assistant decide what is worth remembering long-term about its user or tasks.

Analyze the provided text based on three criteria:
- Importance: Is this new fact crucial for future help or understanding the user deeply? (0.0-1.0)
- Personalness: Does this reveal stable preferences, biographical details, or goals for the user? (0.0-1.0)  
- Actionability: Will this be needed to complete an open task or reminder? (0.0-1.0)

Respond ONLY with a JSON object in this exact format:
{
  "importance_score": 0.0-1.0,
  "personalness_score": 0.0-1.0,
  "actionability_score": 0.0-1.0,
  "reasoning": "Brief explanation of scores"
}"""
    
    def _build_scoring_prompt(self, text: str, user_context: Optional[str] = None) -> str:
        """Build prompt for scoring text."""
        prompt = f"Please evaluate the following text for memory salience:\n\nTEXT: {text}\n\n"
        
        if user_context:
            prompt += f"USER CONTEXT: {user_context}\n\n"
        
        prompt += """Consider:
- Importance: Is this crucial for future help or understanding?
- Personalness: Does this reveal stable user preferences, bio details, or goals?
- Actionability: Is this needed for completing tasks or reminders?

Provide scores between 0.0 and 1.0 for each criterion."""
        
        return prompt
    
    def _parse_scoring_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response into structured scores."""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                
                # Validate scores are in range
                for score_key in ['importance_score', 'personalness_score', 'actionability_score']:
                    if score_key in result:
                        result[score_key] = max(0.0, min(1.0, float(result[score_key])))
                
                return result
        except Exception as e:
            logger.error(f"Error parsing scoring response: {e}")
        
        # Fallback parsing or default scores
        return {
            'importance_score': 0.5,
            'personalness_score': 0.5,
            'actionability_score': 0.5,
            'reasoning': 'Could not parse LLM response'
        }


class VectorStoreManager:
    """Manages ChromaDB vector store operations."""
    
    def __init__(self):
        self.client = None
        self.collection = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize ChromaDB client and collection."""
        try:
            # Initialize ChromaDB client
            persist_directory = settings.VECTOR_STORE_PERSIST_DIRECTORY
            
            self.client = chromadb.PersistentClient(
                path=persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Get or create collection
            self.collection = self.client.get_or_create_collection(
                name="ellahai_memories",
                metadata={"hnsw:space": "cosine"}
            )
            
            logger.info(f"Vector store initialized with {self.collection.count()} existing memories")
            
        except Exception as e:
            logger.error(f"Error initializing vector store: {e}")
            raise
    
    async def add_memory(self, memory_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """Add a memory to the vector store."""
        try:
            # Generate embedding using OpenAI
            embedding = await self._get_embedding(content)
            
            # Add to ChromaDB
            self.collection.add(
                ids=[memory_id],
                documents=[content],
                embeddings=[embedding],
                metadatas=[metadata]
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding memory to vector store: {e}")
            return False
    
    async def search_memories(self, query: str, user_id: str, k: int = 5, 
                            min_similarity: float = 0.7) -> List[Dict[str, Any]]:
        """Search for similar memories in the vector store."""
        try:
            # Generate query embedding
            query_embedding = await self._get_embedding(query)
            
            # Search in ChromaDB
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=k * 2,  # Get more results to filter by user
                where={"user_id": user_id}
            )
            
            # Process results
            memories = []
            if results['ids'] and results['ids'][0]:
                for i, memory_id in enumerate(results['ids'][0]):
                    distance = results['distances'][0][i]
                    similarity = 1 - distance  # Convert distance to similarity
                    
                    if similarity >= min_similarity:
                        memories.append({
                            'memory_id': memory_id,
                            'content': results['documents'][0][i],
                            'metadata': results['metadatas'][0][i],
                            'similarity_score': similarity
                        })
            
            # Sort by similarity and limit results
            memories.sort(key=lambda x: x['similarity_score'], reverse=True)
            return memories[:k]
            
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            return []
    
    async def update_memory(self, memory_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """Update a memory in the vector store."""
        try:
            # Delete old version
            self.collection.delete(ids=[memory_id])
            
            # Add updated version
            return await self.add_memory(memory_id, content, metadata)
            
        except Exception as e:
            logger.error(f"Error updating memory in vector store: {e}")
            return False
    
    async def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory from the vector store."""
        try:
            self.collection.delete(ids=[memory_id])
            return True
            
        except Exception as e:
            logger.error(f"Error deleting memory from vector store: {e}")
            return False
    
    async def _get_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using OpenAI."""
        try:
            client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
            response = await client.embeddings.create(
                model=settings.EMBEDDING_MODEL,
                input=text
            )
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            raise


class MemoryManager:
    """High-level memory management service."""
    
    def __init__(self, user):
        self.user = user
        self.vector_store = VectorStoreManager()
        self.salience_scorer = MemorySalienceScorer()
        self._embedding_cache = {}
    
    async def store_memory(self, content: str, memory_type: str, 
                          source_conversation=None, source_message=None,
                          metadata: Optional[Dict[str, Any]] = None) -> Optional[Memory]:
        """Store a new memory after salience evaluation."""
        try:
            # Get user's memory configuration
            config = await self._get_memory_config()
            
            if not config.allow_memory_storage:
                logger.info(f"Memory storage disabled for user: {self.user.email}")
                return None
            
            # Score the content for salience
            user_context = await self._get_user_context()
            scores = await self.salience_scorer.score_text(content, user_context)
            
            # Check if memory meets storage thresholds
            if not config.should_store_memory(
                scores['importance_score'],
                scores['personalness_score'], 
                scores['actionability_score']
            ):
                logger.info(f"Memory below threshold, not storing: {content[:50]}...")
                return None
            
            # Create memory record
            memory = await self._create_memory_record(
                content=content,
                memory_type=memory_type,
                scores=scores,
                source_conversation=source_conversation,
                source_message=source_message,
                metadata=metadata or {}
            )
            
            # Add to vector store
            vector_metadata = {
                'user_id': str(self.user.id),
                'memory_type': memory_type,
                'importance_score': scores['importance_score'],
                'personalness_score': scores['personalness_score'],
                'actionability_score': scores['actionability_score'],
                'created_at': memory.created_at.isoformat()
            }
            if metadata:
                vector_metadata.update(metadata)
            
            success = await self.vector_store.add_memory(
                memory_id=str(memory.id),
                content=content,
                metadata=vector_metadata
            )
            
            if success:
                logger.info(f"Memory stored successfully: {memory.id}")
                return memory
            else:
                # Delete DB record if vector store failed
                await sync_to_async(memory.delete)()
                logger.error("Failed to store memory in vector store")
                return None
                
        except Exception as e:
            logger.error(f"Error storing memory: {e}")
            return None
    
    async def retrieve_memories(self, query: str, k: int = 5, 
                               memory_types: Optional[List[str]] = None,
                               min_importance: float = 0.3) -> List[Dict[str, Any]]:
        """Retrieve relevant memories for a query."""
        try:
            config = await self._get_memory_config()
            
            if not config.allow_cross_conversation_memory:
                k = min(k, 3)  # Limit retrieval if cross-conversation disabled
            
            # Search in vector store
            vector_results = await self.vector_store.search_memories(
                query=query,
                user_id=str(self.user.id),
                k=k * 2,  # Get more to filter
                min_similarity=config.similarity_threshold
            )
            
            # Get corresponding Memory objects and filter
            memories = []
            for result in vector_results:
                try:
                    memory = await sync_to_async(Memory.objects.get)(
                        id=result['memory_id'],
                        user=self.user,
                        is_active=True
                    )
                    
                    # Apply filters
                    if memory_types and memory.memory_type not in memory_types:
                        continue
                    
                    if memory.importance_score < min_importance:
                        continue
                    
                    # Update access time
                    await sync_to_async(memory.update_access_time)()
                    
                    # Record retrieval
                    await self._record_retrieval(memory, query, result['similarity_score'], len(memories))
                    
                    memories.append({
                        'memory': memory,
                        'similarity_score': result['similarity_score'],
                        'content': memory.content,
                        'memory_type': memory.memory_type,
                        'salience_score': memory.get_salience_score()
                    })
                    
                except Memory.DoesNotExist:
                    # Memory was deleted from DB but still in vector store
                    await self.vector_store.delete_memory(result['memory_id'])
                    continue
            
            # Sort by combined score (similarity + salience)
            memories.sort(
                key=lambda x: (x['similarity_score'] * 0.7 + x['salience_score'] * 0.3),
                reverse=True
            )
            
            return memories[:k]
            
        except Exception as e:
            logger.error(f"Error retrieving memories: {e}")
            return []
    
    async def get_memory_context(self, query: str, conversation_history: Optional[List] = None) -> str:
        """Get formatted memory context for LLM prompts."""
        try:
            # Retrieve relevant memories
            memories = await self.retrieve_memories(query, k=5)
            
            if not memories:
                return ""
            
            # Format memories for context
            context_parts = ["## Relevant Memories:"]
            
            for i, mem_data in enumerate(memories, 1):
                memory = mem_data['memory']
                context_parts.append(
                    f"{i}. [{memory.memory_type.replace('_', ' ').title()}] {memory.content}"
                )
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting memory context: {e}")
            return ""
    
    async def cleanup_old_memories(self, days_threshold: int = 365) -> int:
        """Clean up old, low-importance memories."""
        try:
            cutoff_date = timezone.now() - timezone.timedelta(days=days_threshold)
            
            # Find old, low-importance memories
            old_memories = await sync_to_async(list)(
                Memory.objects.filter(
                    user=self.user,
                    created_at__lt=cutoff_date,
                    importance_score__lt=0.4,
                    is_active=True
                )
            )
            
            deleted_count = 0
            for memory in old_memories:
                # Delete from vector store
                await self.vector_store.delete_memory(str(memory.id))
                
                # Soft delete from DB
                memory.is_active = False
                await sync_to_async(memory.save)(update_fields=['is_active'])
                
                deleted_count += 1
            
            logger.info(f"Cleaned up {deleted_count} old memories for user: {self.user.email}")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up memories: {e}")
            return 0
    
    # Helper methods
    
    @sync_to_async
    def _get_memory_config(self):
        """Get user's memory configuration."""
        config, created = MemoryConfiguration.objects.get_or_create(user=self.user)
        return config
    
    @sync_to_async
    def _create_memory_record(self, content, memory_type, scores, source_conversation, source_message, metadata):
        """Create memory record in database."""
        return Memory.objects.create(
            user=self.user,
            content=content,
            memory_type=memory_type,
            importance_score=scores['importance_score'],
            personalness_score=scores['personalness_score'],
            actionability_score=scores['actionability_score'],
            vector_id=str(uuid.uuid4()),
            source_conversation=source_conversation,
            source_message=source_message,
            metadata={
                **metadata,
                'salience_reasoning': scores.get('reasoning', ''),
                'stored_at': timezone.now().isoformat()
            }
        )
    
    @sync_to_async
    def _record_retrieval(self, memory, query, similarity_score, rank):
        """Record memory retrieval for analytics."""
        MemoryRetrieval.objects.create(
            user=self.user,
            memory=memory,
            query=query,
            similarity_score=similarity_score,
            rank=rank
        )
    
    async def _get_user_context(self) -> str:
        """Get user context for salience scoring."""
        return f"User: {self.user.get_full_name()}, Personality: {self.user.companion_personality}"
