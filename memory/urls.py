from django.urls import path
from . import views

app_name = 'memory'

urlpatterns = [
    # Memory CRUD operations
    path('memories/', views.MemoryListCreateView.as_view(), name='memory-list'),
    path('memories/<uuid:pk>/', views.MemoryDetailView.as_view(), name='memory-detail'),
    
    # Memory retrieval and search
    path('retrieve/', views.retrieve_memories, name='retrieve-memories'),
    path('search/', views.search_memories, name='search-memories'),
    
    # Memory management
    path('cleanup/', views.cleanup_memories, name='cleanup-memories'),
    path('stats/', views.memory_stats, name='memory-stats'),
    
    # User memory configuration
    path('config/', views.UserMemoryConfigView.as_view(), name='memory-config'),
    
    # Memory clusters
    path('clusters/', views.MemoryClusterListView.as_view(), name='cluster-list'),
    path('clusters/<uuid:pk>/', views.MemoryClusterDetailView.as_view(), name='cluster-detail'),
]
