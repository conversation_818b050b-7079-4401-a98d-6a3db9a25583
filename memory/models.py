from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid
import json


class MemoryType(models.TextChoices):
    """Types of memories that can be stored."""
    SEMANTIC_PROFILE = 'semantic_profile', 'User-specific stable preferences and characteristics'
    EPISODIC_SUMMARY = 'episodic_summary', 'Summaries of conversations or interactions'
    GENERAL_KNOWLEDGE = 'general_knowledge', 'General facts or domain knowledge'
    EXPLICIT_MEMORY = 'explicit_memory', 'Specific facts or notes explicitly saved'
    PERSONAL_FACT = 'personal_fact', 'Personal information about the user'
    PREFERENCE = 'preference', 'User preferences and choices'
    GOAL = 'goal', 'User goals and aspirations'
    RELATIONSHIP = 'relationship', 'Information about user relationships'
    SKILL = 'skill', 'User skills and abilities'
    INTEREST = 'interest', 'User interests and hobbies'


class Memory(models.Model):
    """Represents a memory stored in the vector database."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='memories')
    
    # Memory content
    content = models.TextField(help_text="The actual memory content/text")
    memory_type = models.CharField(max_length=50, choices=MemoryType.choices)
    
    # Salience scores (0.0 to 1.0)
    importance_score = models.FloatField(
        default=0.5,
        help_text="How crucial this memory is for future help (0.0-1.0)"
    )
    personalness_score = models.FloatField(
        default=0.5,
        help_text="How personal/specific to the user this memory is (0.0-1.0)"
    )
    actionability_score = models.FloatField(
        default=0.5,
        help_text="How actionable/task-related this memory is (0.0-1.0)"
    )
    
    # Vector store reference
    vector_id = models.CharField(
        max_length=255,
        unique=True,
        help_text="ID of this memory in the vector store"
    )
    
    # Metadata
    source_conversation = models.ForeignKey(
        'chat.Conversation',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Conversation this memory originated from"
    )
    source_message = models.ForeignKey(
        'chat.Message',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Message this memory originated from"
    )
    
    # Additional metadata as JSON
    metadata = models.JSONField(
        default=dict,
        help_text="Additional metadata about this memory"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_accessed = models.DateTimeField(default=timezone.now)
    
    # Status
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(
        default=False,
        help_text="Whether this memory has been verified as accurate"
    )
    
    class Meta:
        db_table = 'memory_memories'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'memory_type']),
            models.Index(fields=['user', 'importance_score']),
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['vector_id']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.memory_type} - {self.content[:50]}..."
    
    def update_access_time(self):
        """Update the last accessed timestamp."""
        self.last_accessed = timezone.now()
        self.save(update_fields=['last_accessed'])
    
    def get_salience_score(self, weights=None):
        """Calculate weighted salience score."""
        if weights is None:
            weights = {'importance': 0.5, 'personalness': 0.3, 'actionability': 0.2}
        
        return (
            weights['importance'] * self.importance_score +
            weights['personalness'] * self.personalness_score +
            weights['actionability'] * self.actionability_score
        )


class MemoryRetrieval(models.Model):
    """Track memory retrievals for analytics and optimization."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    memory = models.ForeignKey(Memory, on_delete=models.CASCADE, related_name='retrievals')
    
    # Retrieval context
    query = models.TextField(help_text="The query that retrieved this memory")
    similarity_score = models.FloatField(
        help_text="Similarity score from vector search (0.0-1.0)"
    )
    rank = models.IntegerField(
        help_text="Rank of this memory in the retrieval results"
    )
    
    # Context information
    conversation = models.ForeignKey(
        'chat.Conversation',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'memory_retrievals'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['memory', 'created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.memory.memory_type} - Score: {self.similarity_score}"


class MemoryCluster(models.Model):
    """Groups related memories together for better organization."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='memory_clusters')
    
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    
    # Cluster metadata
    cluster_type = models.CharField(
        max_length=50,
        choices=[
            ('topic', 'Topic-based cluster'),
            ('temporal', 'Time-based cluster'),
            ('relationship', 'Relationship-based cluster'),
            ('project', 'Project-based cluster'),
        ],
        default='topic'
    )
    
    memories = models.ManyToManyField(Memory, related_name='clusters', blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'memory_clusters'
        ordering = ['-updated_at']
        unique_together = ['user', 'name']
    
    def __str__(self):
        return f"{self.user.email} - {self.name}"
    
    def get_memory_count(self):
        """Get count of memories in this cluster."""
        return self.memories.count()


class MemoryConfiguration(models.Model):
    """User-specific memory system configuration."""
    
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='memory_config'
    )
    
    # Storage thresholds
    min_importance_threshold = models.FloatField(
        default=0.3,
        help_text="Minimum importance score to store a memory"
    )
    min_personalness_threshold = models.FloatField(
        default=0.2,
        help_text="Minimum personalness score to store a memory"
    )
    min_actionability_threshold = models.FloatField(
        default=0.2,
        help_text="Minimum actionability score to store a memory"
    )
    
    # Retrieval settings
    max_memories_per_retrieval = models.IntegerField(
        default=5,
        help_text="Maximum number of memories to retrieve per query"
    )
    similarity_threshold = models.FloatField(
        default=0.7,
        help_text="Minimum similarity score for memory retrieval"
    )
    
    # Privacy settings
    allow_memory_storage = models.BooleanField(
        default=True,
        help_text="Whether to store memories for this user"
    )
    allow_cross_conversation_memory = models.BooleanField(
        default=True,
        help_text="Whether to use memories from other conversations"
    )
    
    # Automatic cleanup
    auto_cleanup_enabled = models.BooleanField(
        default=True,
        help_text="Whether to automatically clean up old/irrelevant memories"
    )
    memory_retention_days = models.IntegerField(
        default=365,
        help_text="How long to keep memories (in days)"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'memory_configurations'
    
    def __str__(self):
        return f"{self.user.email} - Memory Config"
    
    def should_store_memory(self, importance, personalness, actionability):
        """Check if a memory meets the storage thresholds."""
        return (
            importance >= self.min_importance_threshold and
            personalness >= self.min_personalness_threshold and
            actionability >= self.min_actionability_threshold
        )


# Alias for backward compatibility
UserMemoryConfig = MemoryConfiguration
