"""
Signals for the memory app.
"""
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import MemoryRetrieval


@receiver(post_save, sender=MemoryRetrieval)
def update_memory_last_accessed(sender, instance, created, **kwargs):
    """
    Update the last_accessed timestamp of the associated Memory
    whenever a MemoryRetrieval is created.
    """
    if created:
        memory = instance.memory
        memory.update_access_time()
