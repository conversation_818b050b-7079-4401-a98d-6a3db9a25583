# Generated by Django 4.2.7 on 2025-07-19 04:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("chat", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Memory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "content",
                    models.TextField(help_text="The actual memory content/text"),
                ),
                (
                    "memory_type",
                    models.CharField(
                        choices=[
                            (
                                "semantic_profile",
                                "User-specific stable preferences and characteristics",
                            ),
                            (
                                "episodic_summary",
                                "Summaries of conversations or interactions",
                            ),
                            ("general_knowledge", "General facts or domain knowledge"),
                            (
                                "explicit_memory",
                                "Specific facts or notes explicitly saved",
                            ),
                            ("personal_fact", "Personal information about the user"),
                            ("preference", "User preferences and choices"),
                            ("goal", "User goals and aspirations"),
                            ("relationship", "Information about user relationships"),
                            ("skill", "User skills and abilities"),
                            ("interest", "User interests and hobbies"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "importance_score",
                    models.FloatField(
                        default=0.5,
                        help_text="How crucial this memory is for future help (0.0-1.0)",
                    ),
                ),
                (
                    "personalness_score",
                    models.FloatField(
                        default=0.5,
                        help_text="How personal/specific to the user this memory is (0.0-1.0)",
                    ),
                ),
                (
                    "actionability_score",
                    models.FloatField(
                        default=0.5,
                        help_text="How actionable/task-related this memory is (0.0-1.0)",
                    ),
                ),
                (
                    "vector_id",
                    models.CharField(
                        help_text="ID of this memory in the vector store",
                        max_length=255,
                        unique=True,
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        default=dict, help_text="Additional metadata about this memory"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "last_accessed",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this memory has been verified as accurate",
                    ),
                ),
                (
                    "source_conversation",
                    models.ForeignKey(
                        blank=True,
                        help_text="Conversation this memory originated from",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="chat.conversation",
                    ),
                ),
                (
                    "source_message",
                    models.ForeignKey(
                        blank=True,
                        help_text="Message this memory originated from",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="chat.message",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="memories",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "memory_memories",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MemoryConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "min_importance_threshold",
                    models.FloatField(
                        default=0.3,
                        help_text="Minimum importance score to store a memory",
                    ),
                ),
                (
                    "min_personalness_threshold",
                    models.FloatField(
                        default=0.2,
                        help_text="Minimum personalness score to store a memory",
                    ),
                ),
                (
                    "min_actionability_threshold",
                    models.FloatField(
                        default=0.2,
                        help_text="Minimum actionability score to store a memory",
                    ),
                ),
                (
                    "max_memories_per_retrieval",
                    models.IntegerField(
                        default=5,
                        help_text="Maximum number of memories to retrieve per query",
                    ),
                ),
                (
                    "similarity_threshold",
                    models.FloatField(
                        default=0.7,
                        help_text="Minimum similarity score for memory retrieval",
                    ),
                ),
                (
                    "allow_memory_storage",
                    models.BooleanField(
                        default=True,
                        help_text="Whether to store memories for this user",
                    ),
                ),
                (
                    "allow_cross_conversation_memory",
                    models.BooleanField(
                        default=True,
                        help_text="Whether to use memories from other conversations",
                    ),
                ),
                (
                    "auto_cleanup_enabled",
                    models.BooleanField(
                        default=True,
                        help_text="Whether to automatically clean up old/irrelevant memories",
                    ),
                ),
                (
                    "memory_retention_days",
                    models.IntegerField(
                        default=365, help_text="How long to keep memories (in days)"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="memory_config",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "memory_configurations",
            },
        ),
        migrations.CreateModel(
            name="MemoryRetrieval",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "query",
                    models.TextField(help_text="The query that retrieved this memory"),
                ),
                (
                    "similarity_score",
                    models.FloatField(
                        help_text="Similarity score from vector search (0.0-1.0)"
                    ),
                ),
                (
                    "rank",
                    models.IntegerField(
                        help_text="Rank of this memory in the retrieval results"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "conversation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="chat.conversation",
                    ),
                ),
                (
                    "memory",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="retrievals",
                        to="memory.memory",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "memory_retrievals",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "created_at"],
                        name="memory_retr_user_id_1061ed_idx",
                    ),
                    models.Index(
                        fields=["memory", "created_at"],
                        name="memory_retr_memory__979e93_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="MemoryCluster",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "cluster_type",
                    models.CharField(
                        choices=[
                            ("topic", "Topic-based cluster"),
                            ("temporal", "Time-based cluster"),
                            ("relationship", "Relationship-based cluster"),
                            ("project", "Project-based cluster"),
                        ],
                        default="topic",
                        max_length=50,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "memories",
                    models.ManyToManyField(
                        blank=True, related_name="clusters", to="memory.memory"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="memory_clusters",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "memory_clusters",
                "ordering": ["-updated_at"],
                "unique_together": {("user", "name")},
            },
        ),
        migrations.AddIndex(
            model_name="memory",
            index=models.Index(
                fields=["user", "memory_type"], name="memory_memo_user_id_6b00dc_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="memory",
            index=models.Index(
                fields=["user", "importance_score"],
                name="memory_memo_user_id_8780b4_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="memory",
            index=models.Index(
                fields=["user", "is_active"], name="memory_memo_user_id_beeeeb_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="memory",
            index=models.Index(
                fields=["vector_id"], name="memory_memo_vector__cd964f_idx"
            ),
        ),
    ]
