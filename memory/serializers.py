from rest_framework import serializers
from .models import Memory, MemoryRetrieval, MemoryCluster, MemoryConfiguration, MemoryType


class MemorySerializer(serializers.ModelSerializer):
    """Serializer for memory objects."""
    
    salience_score = serializers.SerializerMethodField()
    age_days = serializers.SerializerMethodField()
    
    class Meta:
        model = Memory
        fields = [
            'id', 'content', 'memory_type', 'importance_score',
            'personalness_score', 'actionability_score', 'salience_score',
            'source_conversation', 'source_message', 'metadata',
            'created_at', 'updated_at', 'last_accessed', 'age_days',
            'is_active', 'is_verified'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'last_accessed', 'vector_id'
        ]
    
    def get_salience_score(self, obj):
        """Get weighted salience score."""
        return obj.get_salience_score()
    
    def get_age_days(self, obj):
        """Get age of memory in days."""
        from django.utils import timezone
        return (timezone.now() - obj.created_at).days


class MemoryCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating memories."""
    
    class Meta:
        model = Memory
        fields = ['content', 'memory_type', 'metadata']
    
    def validate_memory_type(self, value):
        """Validate memory type."""
        if value not in [choice[0] for choice in MemoryType.choices]:
            raise serializers.ValidationError("Invalid memory type.")
        return value


class MemoryRetrievalSerializer(serializers.ModelSerializer):
    """Serializer for memory retrieval records."""
    
    memory_content = serializers.CharField(source='memory.content', read_only=True)
    memory_type = serializers.CharField(source='memory.memory_type', read_only=True)
    
    class Meta:
        model = MemoryRetrieval
        fields = [
            'id', 'query', 'similarity_score', 'rank',
            'memory_content', 'memory_type', 'conversation', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class MemoryClusterSerializer(serializers.ModelSerializer):
    """Serializer for memory clusters."""
    
    memory_count = serializers.SerializerMethodField()
    recent_memories = serializers.SerializerMethodField()
    
    class Meta:
        model = MemoryCluster
        fields = [
            'id', 'name', 'description', 'cluster_type',
            'memory_count', 'recent_memories', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_memory_count(self, obj):
        """Get count of memories in cluster."""
        return obj.get_memory_count()
    
    def get_recent_memories(self, obj):
        """Get recent memories in cluster."""
        recent = obj.memories.filter(is_active=True).order_by('-created_at')[:3]
        return MemorySerializer(recent, many=True, context=self.context).data


class UserMemoryConfigSerializer(serializers.ModelSerializer):
    """Serializer for user memory configuration."""
    
    class Meta:
        model = MemoryConfiguration
        fields = [
            'min_importance_threshold', 'min_personalness_threshold',
            'min_actionability_threshold', 'max_memories_per_retrieval',
            'similarity_threshold', 'allow_memory_storage',
            'allow_cross_conversation_memory', 'auto_cleanup_enabled',
            'memory_retention_days', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_min_importance_threshold(self, value):
        """Validate importance threshold."""
        if not (0.0 <= value <= 1.0):
            raise serializers.ValidationError("Threshold must be between 0.0 and 1.0")
        return value
    
    def validate_min_personalness_threshold(self, value):
        """Validate personalness threshold."""
        if not (0.0 <= value <= 1.0):
            raise serializers.ValidationError("Threshold must be between 0.0 and 1.0")
        return value
    
    def validate_min_actionability_threshold(self, value):
        """Validate actionability threshold."""
        if not (0.0 <= value <= 1.0):
            raise serializers.ValidationError("Threshold must be between 0.0 and 1.0")
        return value
    
    def validate_similarity_threshold(self, value):
        """Validate similarity threshold."""
        if not (0.0 <= value <= 1.0):
            raise serializers.ValidationError("Threshold must be between 0.0 and 1.0")
        return value


class MemorySearchSerializer(serializers.Serializer):
    """Serializer for memory search requests."""
    
    query = serializers.CharField(max_length=500)
    memory_types = serializers.ListField(
        child=serializers.ChoiceField(choices=MemoryType.choices),
        required=False,
        allow_empty=True
    )
    k = serializers.IntegerField(default=5, min_value=1, max_value=20)
    min_importance = serializers.FloatField(default=0.3, min_value=0.0, max_value=1.0)
    min_similarity = serializers.FloatField(default=0.7, min_value=0.0, max_value=1.0)


class MemoryStatsSerializer(serializers.Serializer):
    """Serializer for memory statistics."""
    
    total_memories = serializers.IntegerField()
    memories_by_type = serializers.DictField()
    avg_importance_score = serializers.FloatField()
    avg_personalness_score = serializers.FloatField()
    avg_actionability_score = serializers.FloatField()
    recent_retrievals = serializers.IntegerField()
    storage_enabled = serializers.BooleanField()
    
    def to_representation(self, instance):
        """Custom representation for stats."""
        return instance
