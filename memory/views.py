from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import Memory, MemoryCluster, MemoryRetrieval
from .serializers import (
    MemorySerializer, MemoryClusterSerializer, MemoryRetrievalSerializer,
    UserMemoryConfigSerializer
)


class MemoryListCreateView(generics.ListCreateAPIView):
    """List and create memories"""
    serializer_class = MemorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = Memory.objects.filter(user=self.request.user)
        
        # Filter by memory type
        memory_type = self.request.query_params.get('type')
        if memory_type:
            queryset = queryset.filter(memory_type=memory_type)
        
        # Filter by importance score
        min_importance = self.request.query_params.get('min_importance')
        if min_importance:
            try:
                queryset = queryset.filter(importance_score__gte=float(min_importance))
            except ValueError:
                pass
        
        return queryset.order_by('-created_at')
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class MemoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a memory"""
    serializer_class = MemorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Memory.objects.filter(user=self.request.user)


class MemoryClusterListView(generics.ListCreateAPIView):
    """List and create memory clusters"""
    serializer_class = MemoryClusterSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return MemoryCluster.objects.filter(user=self.request.user)
    
    def perform_create(self, serializer):
        serializer.save(user=self.request.user)


class MemoryClusterDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a memory cluster"""
    serializer_class = MemoryClusterSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return MemoryCluster.objects.filter(user=self.request.user)


class UserMemoryConfigView(generics.RetrieveUpdateAPIView):
    """Get and update user memory configuration"""
    serializer_class = UserMemoryConfigSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        from .models import MemoryConfiguration
        config, created = MemoryConfiguration.objects.get_or_create(
            user=self.request.user
        )
        return config


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def retrieve_memories(request):
    """Retrieve memories based on query"""
    query = request.GET.get('query', '')
    limit = min(int(request.GET.get('limit', 10)), 50)  # Cap at 50
    
    if not query:
        return Response({'error': 'Query parameter is required'}, 
                       status=status.HTTP_400_BAD_REQUEST)
    
    # For now, use simple text search since we don't have vector store setup
    memories = Memory.objects.filter(
        user=request.user,
        content__icontains=query
    ).order_by('-importance_score', '-created_at')[:limit]
    
    serializer = MemorySerializer(memories, many=True)
    
    return Response({
        'query': query,
        'memories': serializer.data,
        'count': len(serializer.data)
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_memories(request):
    """Search memories with advanced filtering"""
    query = request.GET.get('q', '')
    memory_type = request.GET.get('type')
    min_importance = request.GET.get('min_importance')
    limit = min(int(request.GET.get('limit', 20)), 100)
    
    queryset = Memory.objects.filter(user=request.user)
    
    if query:
        queryset = queryset.filter(
            Q(content__icontains=query)
        )
    
    if memory_type:
        queryset = queryset.filter(memory_type=memory_type)
    
    if min_importance:
        try:
            queryset = queryset.filter(importance_score__gte=float(min_importance))
        except ValueError:
            pass
    
    memories = queryset.order_by('-importance_score', '-created_at')[:limit]
    serializer = MemorySerializer(memories, many=True)
    
    return Response({
        'results': serializer.data,
        'count': len(serializer.data),
        'query': query
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def cleanup_memories(request):
    """Clean up old or low-importance memories"""
    user = request.user
    
    # Get user config
    from .models import MemoryConfiguration
    config, _ = MemoryConfiguration.objects.get_or_create(user=user)
    
    # Delete memories older than retention period with low importance
    cutoff_date = timezone.now() - timedelta(days=config.memory_retention_days)
    
    old_memories = Memory.objects.filter(
        user=user,
        created_at__lt=cutoff_date,
        importance_score__lt=config.min_importance_threshold
    )
    
    cleaned_count = old_memories.count()
    old_memories.delete()
    
    # Note: MemoryConfiguration doesn't have max_memories field
    # This cleanup only handles retention-based cleanup
    
    return Response({
        'message': f'Cleaned up {cleaned_count} memories',
        'cleaned_count': cleaned_count
    })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def memory_stats(request):
    """Get memory statistics for user"""
    user = request.user
    
    # Basic counts
    total_memories = Memory.objects.filter(user=user).count()
    
    # Memory types breakdown
    memory_types = Memory.objects.filter(user=user).values('memory_type').annotate(
        count=Count('memory_type')
    ).order_by('-count')
    
    # Calculate average importance
    from django.db.models import Avg
    avg_importance = Memory.objects.filter(user=user).aggregate(
        avg=Avg('importance_score')
    )['avg'] or 0.0
    
    # Importance distribution
    high_importance = Memory.objects.filter(
        user=user, importance_score__gte=0.8
    ).count()
    medium_importance = Memory.objects.filter(
        user=user, importance_score__gte=0.5, importance_score__lt=0.8
    ).count()
    low_importance = Memory.objects.filter(
        user=user, importance_score__lt=0.5
    ).count()
    
    # Recent activity
    last_week = timezone.now() - timedelta(days=7)
    recent_memories = Memory.objects.filter(
        user=user, created_at__gte=last_week
    ).count()
    
    # Clusters
    total_clusters = MemoryCluster.objects.filter(user=user).count()
    
    return Response({
        'total_memories': total_memories,
        'memory_types': list(memory_types),
        'average_importance': avg_importance,
        'importance_distribution': {
            'high': high_importance,
            'medium': medium_importance,
            'low': low_importance
        },
        'recent_memories': recent_memories,
        'total_clusters': total_clusters
    })
