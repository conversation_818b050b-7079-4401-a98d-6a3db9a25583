#!/usr/bin/env python3
"""
Simple Memory Cache Stress Test
Tests memory cache performance with realistic memory loads.
"""
import os
import sys
import django
import asyncio
import time
import random
from typing import Dict, List, Any

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from memory.services import MemoryManager
from chat.services.memory_cache_service import memory_cache_service


class SimpleMemoryCacheStressTest:
    """Simple stress test for memory cache performance."""
    
    def __init__(self):
        self.test_configs = [
            {'name': 'Light Load', 'memory_count': 5, 'query_count': 10},
            {'name': 'Medium Load', 'memory_count': 15, 'query_count': 20},
            {'name': 'Heavy Load', 'memory_count': 30, 'query_count': 30}
        ]
    
    async def setup_test_user(self, email: str) -> User:
        """Setup test user."""
        try:
            user = await sync_to_async(User.objects.get)(email=email)
        except User.DoesNotExist:
            user = await sync_to_async(User.objects.create_user)(
                username=email,
                email=email,
                password="testpass123"
            )
        return user
    
    def generate_test_memories(self, count: int) -> List[str]:
        """Generate realistic test memories."""
        memory_templates = [
            "I love {food} food and eat it {frequency}",
            "My favorite hobby is {hobby} and I do it {frequency}",
            "I work as a {job} and have been doing it for {years} years",
            "I live in {city} and really enjoy the {feature}",
            "I have a {pet} named {name} who is very {trait}",
            "I studied {subject} at {university}",
            "My goal this year is to {goal}",
            "I'm allergic to {allergen} so I avoid it",
            "I exercise by {exercise} {frequency}",
            "I prefer {season} weather because it's {reason}"
        ]
        
        sample_data = {
            'food': ['Italian', 'Japanese', 'Mexican', 'Indian'],
            'frequency': ['daily', 'weekly', 'often', 'sometimes'],
            'hobby': ['reading', 'hiking', 'photography', 'cooking'],
            'job': ['engineer', 'designer', 'teacher', 'doctor'],
            'years': ['2', '5', '10', '15'],
            'city': ['San Francisco', 'New York', 'Seattle', 'Austin'],
            'feature': ['weather', 'culture', 'food scene', 'nature'],
            'pet': ['cat', 'dog', 'bird'],
            'name': ['Max', 'Luna', 'Charlie'],
            'trait': ['playful', 'calm', 'energetic'],
            'subject': ['Computer Science', 'Biology', 'Psychology'],
            'university': ['Stanford', 'MIT', 'Berkeley'],
            'goal': ['learn Spanish', 'run a marathon', 'travel to Japan'],
            'allergen': ['peanuts', 'shellfish', 'dairy'],
            'exercise': ['running', 'swimming', 'cycling'],
            'season': ['spring', 'summer', 'fall', 'winter'],
            'reason': ['comfortable', 'beautiful', 'perfect for activities']
        }
        
        memories = []
        for i in range(count):
            template = random.choice(memory_templates)
            content = template
            
            # Fill template with random data
            for key, values in sample_data.items():
                if f'{{{key}}}' in content:
                    content = content.replace(f'{{{key}}}', random.choice(values))
            
            memories.append(content)
        
        return memories
    
    async def create_memories_for_user(self, user: User, memory_count: int) -> Dict[str, Any]:
        """Create memories for a user using the actual memory manager."""
        print(f"   📚 Creating {memory_count} memories for {user.email}...")
        
        start_time = time.time()
        memories = self.generate_test_memories(memory_count)
        
        memory_manager = MemoryManager(user=user)
        created_count = 0
        
        for content in memories:
            try:
                # Use the correct method signature
                memory = await memory_manager.store_memory(
                    content=content,
                    memory_type="explicit_memory"  # Use a valid memory type
                )
                if memory:
                    created_count += 1
            except Exception as e:
                print(f"      ⚠️ Error creating memory: {e}")
                # Continue with other memories
        
        creation_time = (time.time() - start_time) * 1000
        
        result = {
            'user_email': user.email,
            'requested_count': memory_count,
            'created_count': created_count,
            'creation_time_ms': creation_time,
            'avg_time_per_memory_ms': creation_time / created_count if created_count > 0 else 0
        }
        
        print(f"      ✅ Created {created_count}/{memory_count} memories in {creation_time:.1f}ms")
        return result
    
    async def test_cache_performance(self, user: User, memory_count: int, query_count: int) -> Dict[str, Any]:
        """Test cache performance with specific memory load."""
        print(f"   ⚡ Testing cache with {memory_count} memories, {query_count} queries...")
        
        session_id = f"stress_test_{int(time.time())}"
        
        # Preload memories
        preload_start = time.time()
        preload_success = await memory_cache_service.preload_user_memories(
            user_id=str(user.id),
            session_id=session_id
        )
        preload_time = (time.time() - preload_start) * 1000
        
        if not preload_success:
            return {'error': 'Failed to preload memories'}
        
        # Test queries
        test_queries = [
            "What are my hobbies?", "Tell me about my work", "What foods do I like?",
            "Where do I live?", "What are my goals?", "Do I have any allergies?",
            "What's my educational background?", "What pets do I have?",
            "What's my exercise routine?", "What weather do I prefer?"
        ]
        
        query_times = []
        memories_found = []
        
        for i in range(query_count):
            query = random.choice(test_queries)
            
            start_time = time.time()
            memories = await memory_cache_service.search_cached_memories(
                user_id=str(user.id),
                session_id=session_id,
                query=query,
                k=5
            )
            query_time = (time.time() - start_time) * 1000
            
            query_times.append(query_time)
            memories_found.append(len(memories))
        
        # Cleanup
        await memory_cache_service.invalidate_user_cache(str(user.id), session_id)
        
        result = {
            'memory_count': memory_count,
            'query_count': query_count,
            'preload_time_ms': preload_time,
            'avg_query_time_ms': sum(query_times) / len(query_times),
            'min_query_time_ms': min(query_times),
            'max_query_time_ms': max(query_times),
            'avg_memories_found': sum(memories_found) / len(memories_found),
            'total_memories_found': sum(memories_found)
        }
        
        print(f"      ⚡ Avg query: {result['avg_query_time_ms']:.1f}ms | Memories found: {result['avg_memories_found']:.1f}")
        return result
    
    async def run_stress_test_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Run stress test for a specific configuration."""
        print(f"\n🔥 {config['name']} Stress Test")
        print(f"   📊 {config['memory_count']} memories, {config['query_count']} queries")
        print("-" * 50)
        
        # Create test user
        user = await self.setup_test_user(f"stress_test_{config['name'].lower().replace(' ', '_')}@example.com")
        
        # Create memories
        memory_result = await self.create_memories_for_user(user, config['memory_count'])
        
        # Test cache performance
        cache_result = await self.test_cache_performance(user, config['memory_count'], config['query_count'])
        
        config_result = {
            'config': config,
            'memory_creation': memory_result,
            'cache_performance': cache_result
        }
        
        print(f"   📊 Results Summary:")
        print(f"      Memory Creation: {memory_result['created_count']} memories in {memory_result['creation_time_ms']:.1f}ms")
        if 'error' not in cache_result:
            print(f"      Cache Performance: {cache_result['avg_query_time_ms']:.1f}ms avg query")
            print(f"      Memory Retrieval: {cache_result['avg_memories_found']:.1f} memories found per query")
        
        return config_result
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive stress test."""
        print("🚀 MEMORY CACHE STRESS TEST")
        print("=" * 50)
        
        all_results = {}
        
        for config in self.test_configs:
            try:
                result = await self.run_stress_test_config(config)
                all_results[config['name']] = result
            except Exception as e:
                print(f"❌ Error in {config['name']} test: {e}")
                all_results[config['name']] = {'error': str(e)}
        
        return all_results
    
    def analyze_results(self, results: Dict[str, Any]) -> None:
        """Analyze and display results."""
        print("\n📊 STRESS TEST ANALYSIS")
        print("=" * 50)
        
        # Performance comparison
        print("\n📈 Performance Comparison:")
        print(f"{'Load Level':<15} {'Memories':<10} {'Created':<10} {'Cache Query':<12} {'Found/Query':<12}")
        print("-" * 65)
        
        for config_name, result in results.items():
            if 'error' in result:
                print(f"{config_name:<15} {'ERROR':<10} {'ERROR':<10} {'ERROR':<12} {'ERROR':<12}")
                continue
            
            memory_count = result['config']['memory_count']
            created_count = result['memory_creation']['created_count']
            
            if 'error' not in result['cache_performance']:
                cache_query_ms = result['cache_performance']['avg_query_time_ms']
                memories_found = result['cache_performance']['avg_memories_found']
                print(f"{config_name:<15} {memory_count:<10} {created_count:<10} {cache_query_ms:<12.1f} {memories_found:<12.1f}")
            else:
                print(f"{config_name:<15} {memory_count:<10} {created_count:<10} {'ERROR':<12} {'ERROR':<12}")
        
        # Performance targets
        print("\n🎯 Performance Target Analysis:")
        target_cache_ms = 10  # 10ms target
        
        for config_name, result in results.items():
            if 'error' in result or 'error' in result.get('cache_performance', {}):
                continue
            
            cache_ms = result['cache_performance']['avg_query_time_ms']
            status = "✅ PASS" if cache_ms <= target_cache_ms else "❌ FAIL"
            
            print(f"   {config_name}: {cache_ms:.1f}ms (target: ≤{target_cache_ms}ms) {status}")
        
        # Memory efficiency
        print("\n💾 Memory Efficiency:")
        for config_name, result in results.items():
            if 'error' in result:
                continue
            
            created = result['memory_creation']['created_count']
            requested = result['memory_creation']['requested_count']
            success_rate = (created / requested * 100) if requested > 0 else 0
            
            print(f"   {config_name}: {success_rate:.1f}% memory creation success rate")
        
        print("\n🎉 Stress test analysis completed!")


async def main():
    """Run the simple memory cache stress test."""
    test = SimpleMemoryCacheStressTest()
    
    # Run the stress test
    results = await test.run_comprehensive_test()
    
    # Analyze results
    test.analyze_results(results)
    
    return results


if __name__ == "__main__":
    asyncio.run(main())
