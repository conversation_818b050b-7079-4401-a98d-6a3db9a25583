from django.test import TestCase
from django.test import Test<PERSON>ase
import json
import uuid
import asyncio
from unittest.mock import patch, MagicMock
from channels.testing import Webso<PERSON><PERSON>ommunicator
from rest_framework_simplejwt.tokens import AccessToken
from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import re_path
from channels.routing import URLRouter
from asgiref.sync import sync_to_async

from chat.consumers import ChatConsumer

User = get_user_model()

class WebSocketRoutingTest(TestCase):
    """Test WebSocket routing configuration."""
    
    def setUp(self):
        """Set up test data."""
        # Create a test user with unique username
        unique_username = f"testuser_{uuid.uuid4().hex[:8]}"
        self.user = User.objects.create_user(
            username=unique_username,
            email=f"{unique_username}@example.com",
            password='testpassword'
        )
        
        # Create application with all routes
        self.application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
            re_path(r"^ws/chat/(?P<conversation_id>[^/]+)/$", ChatConsumer.as_asgi()),
        ])
    
    def test_websocket_routing(self):
        """Test WebSocket routing configuration."""
        async def run_tests():
            # Mock the database operations
            with patch('chat.consumers.ChatConsumer.create_chat_session') as mock_create_chat_session, \
                 patch('chat.consumers.ChatConsumer.create_streaming_session') as mock_create_streaming_session, \
                 patch('chat.consumers.ChatConsumer.check_agents_availability') as mock_check_agents:
                
                # Configure mocks
                mock_chat_session = MagicMock()
                mock_chat_session.id = uuid.uuid4()
                mock_create_chat_session.return_value = mock_chat_session
                
                mock_streaming_session = MagicMock()
                mock_streaming_session.id = uuid.uuid4()
                mock_create_streaming_session.return_value = mock_streaming_session
                
                mock_check_agents.return_value = True
                
                # Test main chat endpoint
                communicator = WebsocketCommunicator(
                    application=self.application,
                    path="/ws/chat/"
                )
                communicator.scope["user"] = self.user
                connected, _ = await communicator.connect()
                self.assertTrue(connected)
                
                # Receive connection established message
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'connection_established')
                
                await communicator.disconnect()
                
                # Test conversation-specific endpoint
                conversation_id = str(uuid.uuid4())
                communicator = WebsocketCommunicator(
                    application=self.application,
                    path=f"/ws/chat/{conversation_id}/"
                )
                communicator.scope["user"] = self.user
                connected, _ = await communicator.connect()
                self.assertTrue(connected)
                
                # Receive connection established message
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'connection_established')
                
                await communicator.disconnect()
                
                # Test invalid endpoint - this will raise a ValueError which we'll catch
                try:
                    invalid_app = URLRouter([
                        re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
                    ])
                    communicator = WebsocketCommunicator(
                        application=invalid_app,
                        path="/ws/invalid/"
                    )
                    communicator.scope["user"] = self.user
                    connected, _ = await communicator.connect()
                    # If we get here, the test should fail
                    self.assertFalse(connected, "Should not connect to invalid path")
                except ValueError as e:
                    # This is expected - the router should raise a ValueError for invalid paths
                    self.assertIn("No route found for path", str(e))
        
        # Run the async tests
        asyncio.run(run_tests())