#!/usr/bin/env python
"""
Test runner for WebSocket consumer tests.
"""
import os
import sys
import django
from django.test.runner import DiscoverRunner
from django.conf import settings
from django.test import TestCase

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

class ChatAppTestRunner(DiscoverRunner):
    """Custom test runner for chat app with detailed reporting"""

    def run_tests(self, test_labels, extra_tests=None, **kwargs):
        """
        Run tests and provide detailed reporting
        
        :param test_labels: List of test labels to run
        :param extra_tests: Additional tests to run
        :param kwargs: Additional arguments for test runner
        :return: Number of failures
        """
        # If no specific labels provided, default to chat app tests
        if not test_labels:
            test_labels = ['chat.tests']

        # Configure test settings
        settings.TEST_RUNNER = 'django.test.runner.DiscoverRunner'
        
        # Run tests
        failures = super().run_tests(test_labels, extra_tests, **kwargs)

        return failures

def run_chat_tests():
    """
    Run all tests for the chat application
    
    :return: Test run status
    """
    # Test categories to run
    test_categories = [
        'chat.tests.test_agent_integration',
        'chat.tests.test_conversation_api',
        'chat.tests.test_error_recovery_service',
        'chat.tests.test_websocket_jwt_auth',
        'chat.tests.test_websocket_realtime_messaging',
        'chat.tests.test_websocket_connection_management'
    ]

    # Create test runner
    test_runner = ChatAppTestRunner(
        verbosity=2,  # Detailed output
        interactive=False,  # Non-interactive mode
        failfast=False  # Continue running tests even if some fail
    )

    # Run tests
    failures = test_runner.run_tests(test_categories)

    # Print summary
    print("\n--- Chat App Test Summary ---")
    print(f"Total Test Categories: {len(test_categories)}")
    print(f"Total Failures: {failures}")

    return failures == 0

if __name__ == '__main__':
    # Run tests and exit with appropriate status code
    success = run_chat_tests()
    sys.exit(0 if success else 1)