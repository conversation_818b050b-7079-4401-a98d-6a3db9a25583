"""
Comprehensive WebSocket Speech-to-Speech Integration Test

Tests the full pipeline:
1. Audio input via WebSocket
2. Transcription (Groq Whisper)
3. Emotion detection (Hume)
4. AI response generation
5. TTS output (Hume TTS)
6. Audio streaming back to client

Uses real audio files from conversation_audio_library for testing.
"""

import asyncio
import base64
import json
import os
import time
from datetime import datetime
from typing import Dict, List, Optional

import pytest
from channels.testing import WebsocketCommunicator
from django.contrib.auth import get_user_model
from django.test import TransactionTestCase

from chat.consumers import ChatConsumer
from chat.models import Conversation

User = get_user_model()


class WebSocketSpeechToSpeechTest(TransactionTestCase):
    """Comprehensive test for WebSocket speech-to-speech functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.user = User.objects.create_user(
            username='speechtest',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Test audio files from conversation_audio_library
        self.test_audio_files = [
            {
                'file': 'conversation_audio_library/work_stress_relief_01_initial_stress.mp3',
                'expected_text': "I'm so overwhelmed with this project deadline.",
                'expected_emotion': 'anxiety',
                'scenario': 'work_stress'
            },
            {
                'file': 'conversation_audio_library/sad_to_happy_01_initial_sadness.mp3',
                'expected_text': "I've been feeling really down lately.",
                'expected_emotion': 'sadness',
                'scenario': 'emotional_support'
            },
            {
                'file': 'conversation_audio_library/excitement_building_01_good_news.mp3',
                'expected_text': "I just got some really good news!",
                'expected_emotion': 'excitement',
                'scenario': 'celebration'
            }
        ]
        
        # Filter to only existing files
        self.test_audio_files = [
            audio_file for audio_file in self.test_audio_files
            if os.path.exists(audio_file['file'])
        ]
        
        if not self.test_audio_files:
            self.skipTest("No test audio files found in conversation_audio_library")
    
    async def create_websocket_connection(self) -> WebsocketCommunicator:
        """Create and connect WebSocket communicator."""
        communicator = WebsocketCommunicator(
            ChatConsumer.as_asgi(),
            f"/ws/chat/?user_id={self.user.id}"
        )

        # Set the user in the scope to simulate authentication
        communicator.scope["user"] = self.user

        connected, _ = await communicator.connect()
        self.assertTrue(connected, "Failed to connect to WebSocket")

        return communicator
    
    async def send_audio_chunk(
        self,
        communicator: WebsocketCommunicator,
        audio_data: bytes,
        chunk_id: str,
        is_final: bool = False
    ) -> None:
        """Send audio chunk via WebSocket."""
        audio_b64 = base64.b64encode(audio_data).decode('utf-8')
        
        message = {
            'type': 'audio_chunk',
            'audio_data': audio_b64,
            'chunk_id': chunk_id,
            'is_final': is_final,
            'timestamp': time.time() * 1000,
            'sample_rate': 48000,
            'channels': 1
        }
        
        await communicator.send_json_to(message)
    
    async def wait_for_message_type(
        self,
        communicator: WebsocketCommunicator,
        message_type: str,
        timeout: float = 10.0
    ) -> Optional[Dict]:
        """Wait for a specific message type from WebSocket."""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = await communicator.receive_json_from(timeout=1.0)
                if response.get('type') == message_type:
                    return response
            except asyncio.TimeoutError:
                continue
        
        return None
    
    async def collect_messages_for_duration(
        self,
        communicator: WebsocketCommunicator,
        duration: float = 5.0
    ) -> List[Dict]:
        """Collect all messages for a specified duration."""
        messages = []
        start_time = time.time()
        
        while time.time() - start_time < duration:
            try:
                response = await communicator.receive_json_from(timeout=0.5)
                messages.append(response)
            except asyncio.TimeoutError:
                continue
        
        return messages
    
    @pytest.mark.asyncio
    async def test_single_audio_file_speech_to_speech(self):
        """Test complete speech-to-speech pipeline with a single audio file."""
        if not self.test_audio_files:
            self.skipTest("No test audio files available")
        
        audio_file_info = self.test_audio_files[0]  # Use first available file
        
        print(f"\n🎵 Testing speech-to-speech with: {os.path.basename(audio_file_info['file'])}")
        print(f"   Expected text: '{audio_file_info['expected_text']}'")
        print(f"   Expected emotion: {audio_file_info['expected_emotion']}")
        
        # Read audio file
        with open(audio_file_info['file'], 'rb') as f:
            audio_data = f.read()
        
        print(f"   Audio size: {len(audio_data)} bytes")
        
        # Create WebSocket connection
        communicator = await self.create_websocket_connection()
        
        try:
            # Send audio chunk
            chunk_id = f"test_chunk_{int(time.time())}"
            await self.send_audio_chunk(
                communicator,
                audio_data,
                chunk_id,
                is_final=True
            )
            
            print(f"   ✅ Audio chunk sent: {chunk_id}")
            
            # Collect all messages for analysis
            messages = await self.collect_messages_for_duration(communicator, 10.0)
            
            print(f"   📨 Received {len(messages)} messages")
            
            # Analyze received messages
            transcription_messages = [m for m in messages if m.get('type') == 'transcription_partial']
            emotion_messages = [m for m in messages if m.get('type') == 'emotion_detected']
            response_messages = [m for m in messages if m.get('type') == 'response_chunk']
            tts_messages = [m for m in messages if m.get('type') == 'tts_chunk']
            
            print(f"   🎤 Transcription messages: {len(transcription_messages)}")
            print(f"   😊 Emotion messages: {len(emotion_messages)}")
            print(f"   💬 Response messages: {len(response_messages)}")
            print(f"   🔊 TTS messages: {len(tts_messages)}")
            
            # Test transcription
            if transcription_messages:
                final_transcription = transcription_messages[-1]
                transcribed_text = final_transcription.get('text', '').strip()
                print(f"   📝 Transcribed: '{transcribed_text}'")
                
                # Check if transcription is reasonable
                expected_text = audio_file_info['expected_text'].lower()
                transcribed_lower = transcribed_text.lower()
                
                if transcribed_lower == expected_text:
                    print(f"   ✅ Transcription EXACT MATCH!")
                elif any(word in transcribed_lower for word in expected_text.split()[:3]):
                    print(f"   🟡 Transcription PARTIAL MATCH")
                elif transcribed_text and transcribed_text != "[Audio transcription unavailable]":
                    print(f"   🟠 Transcription DIFFERENT but working")
                else:
                    print(f"   ❌ Transcription FAILED")
                    self.fail(f"Transcription failed: got '{transcribed_text}'")
            else:
                print(f"   ❌ No transcription messages received")
                self.fail("No transcription messages received")
            
            # Test emotion detection
            if emotion_messages:
                emotion_msg = emotion_messages[-1]
                detected_emotions = emotion_msg.get('emotions', [])
                print(f"   😊 Detected emotions: {[e.get('name') for e in detected_emotions[:3]]}")
            else:
                print(f"   ⚠️ No emotion detection messages")
            
            # Test AI response
            if response_messages:
                response_text = ''.join([m.get('content', '') for m in response_messages])
                print(f"   🤖 AI Response: '{response_text[:100]}...'")
                self.assertGreater(len(response_text), 0, "AI response should not be empty")
            else:
                print(f"   ❌ No AI response messages")
                self.fail("No AI response messages received")
            
            # Test TTS output
            if tts_messages:
                total_audio_bytes = sum(len(base64.b64decode(m.get('audio_data', ''))) for m in tts_messages if m.get('audio_data'))
                print(f"   🔊 TTS audio generated: {total_audio_bytes} bytes")
                self.assertGreater(total_audio_bytes, 0, "TTS should generate audio data")
            else:
                print(f"   ❌ No TTS messages")
                self.fail("No TTS messages received")
            
            print(f"   🎉 Speech-to-speech test PASSED!")
            
        finally:
            await communicator.disconnect()
    
    @pytest.mark.asyncio
    async def test_multiple_audio_files_conversation(self):
        """Test conversation flow with multiple audio files."""
        if len(self.test_audio_files) < 2:
            self.skipTest("Need at least 2 test audio files for conversation test")
        
        print(f"\n💬 Testing conversation with {len(self.test_audio_files)} audio files")
        
        # Create WebSocket connection
        communicator = await self.create_websocket_connection()
        
        try:
            conversation_results = []
            
            for i, audio_file_info in enumerate(self.test_audio_files[:2]):  # Test first 2 files
                print(f"\n   Turn {i+1}: {os.path.basename(audio_file_info['file'])}")
                
                # Read audio file
                with open(audio_file_info['file'], 'rb') as f:
                    audio_data = f.read()
                
                # Send audio chunk
                chunk_id = f"conv_chunk_{i}_{int(time.time())}"
                await self.send_audio_chunk(
                    communicator,
                    audio_data,
                    chunk_id,
                    is_final=True
                )
                
                # Wait for complete response cycle
                await asyncio.sleep(3.0)  # Allow processing time
                
                # Collect messages
                messages = await self.collect_messages_for_duration(communicator, 5.0)
                
                # Analyze turn
                transcription_msgs = [m for m in messages if m.get('type') == 'transcription_partial']
                tts_msgs = [m for m in messages if m.get('type') == 'tts_chunk']
                
                turn_result = {
                    'turn': i + 1,
                    'file': audio_file_info['file'],
                    'transcription_count': len(transcription_msgs),
                    'tts_count': len(tts_msgs),
                    'transcribed_text': transcription_msgs[-1].get('text', '') if transcription_msgs else '',
                    'has_audio_response': len(tts_msgs) > 0
                }
                
                conversation_results.append(turn_result)
                
                print(f"      📝 Transcribed: '{turn_result['transcribed_text']}'")
                print(f"      🔊 TTS chunks: {turn_result['tts_count']}")
            
            # Verify conversation flow
            print(f"\n   📊 Conversation Summary:")
            for result in conversation_results:
                print(f"      Turn {result['turn']}: ✅ Transcription, ✅ TTS" if result['transcribed_text'] and result['has_audio_response'] else f"      Turn {result['turn']}: ❌ Issues")
            
            # Assert all turns worked
            for result in conversation_results:
                self.assertGreater(len(result['transcribed_text']), 0, f"Turn {result['turn']} should have transcription")
                self.assertTrue(result['has_audio_response'], f"Turn {result['turn']} should have TTS response")
            
            print(f"   🎉 Multi-turn conversation test PASSED!")
            
        finally:
            await communicator.disconnect()
    
    @pytest.mark.asyncio
    async def test_audio_streaming_performance(self):
        """Test performance metrics for audio streaming."""
        if not self.test_audio_files:
            self.skipTest("No test audio files available")
        
        audio_file_info = self.test_audio_files[0]
        
        print(f"\n⚡ Testing performance with: {os.path.basename(audio_file_info['file'])}")
        
        # Read audio file
        with open(audio_file_info['file'], 'rb') as f:
            audio_data = f.read()
        
        # Create WebSocket connection
        communicator = await self.create_websocket_connection()
        
        try:
            start_time = time.time()
            
            # Send audio chunk
            chunk_id = f"perf_test_{int(time.time())}"
            await self.send_audio_chunk(
                communicator,
                audio_data,
                chunk_id,
                is_final=True
            )
            
            # Wait for first transcription
            transcription_msg = await self.wait_for_message_type(communicator, 'transcription_partial', timeout=10.0)
            transcription_time = (time.time() - start_time) * 1000 if transcription_msg else None
            
            # Wait for first TTS chunk
            tts_msg = await self.wait_for_message_type(communicator, 'tts_chunk', timeout=15.0)
            first_audio_time = (time.time() - start_time) * 1000 if tts_msg else None
            
            print(f"   ⏱️ Transcription time: {transcription_time:.1f}ms" if transcription_time else "   ❌ No transcription received")
            print(f"   ⏱️ First audio chunk time: {first_audio_time:.1f}ms" if first_audio_time else "   ❌ No audio response received")
            
            # Performance assertions
            if transcription_time:
                self.assertLess(transcription_time, 5000, "Transcription should complete within 5 seconds")
            
            if first_audio_time:
                self.assertLess(first_audio_time, 10000, "First audio chunk should arrive within 10 seconds")
            
            print(f"   🎉 Performance test PASSED!")
            
        finally:
            await communicator.disconnect()


    def test_transcription_debugging(self):
        """Debug transcription issues by testing the exact flow."""
        async def debug_transcription():
            if not self.test_audio_files:
                print("❌ No test audio files available")
                return

            audio_file_info = self.test_audio_files[0]

            print(f"\n🔍 DEBUGGING TRANSCRIPTION ISSUE")
            print(f"   File: {audio_file_info['file']}")
            print(f"   Expected: '{audio_file_info['expected_text']}'")

            # Read audio file
            with open(audio_file_info['file'], 'rb') as f:
                audio_data = f.read()

            print(f"   Audio size: {len(audio_data)} bytes")

            # Create WebSocket connection
            communicator = await self.create_websocket_connection()

            try:
                # Send audio chunk
                chunk_id = f"debug_chunk_{int(time.time())}"
                print(f"   Sending chunk: {chunk_id}")

                await self.send_audio_chunk(
                    communicator,
                    audio_data,
                    chunk_id,
                    is_final=True
                )

                # Monitor all messages with timestamps
                print(f"\n   📨 Monitoring WebSocket messages...")
                start_time = time.time()

                for i in range(50):  # Monitor for up to 25 seconds
                    try:
                        response = await communicator.receive_json_from(timeout=0.5)
                        elapsed = (time.time() - start_time) * 1000
                        msg_type = response.get('type', 'unknown')

                        if msg_type == 'transcription_partial':
                            text = response.get('text', '')
                            confidence = response.get('confidence', 0)
                            print(f"   [{elapsed:6.1f}ms] 🎤 TRANSCRIPTION: '{text}' (confidence: {confidence:.3f})")
                        elif msg_type == 'emotion_detected':
                            emotions = response.get('emotions', [])
                            primary = emotions[0].get('name') if emotions else 'none'
                            print(f"   [{elapsed:6.1f}ms] 😊 EMOTION: {primary}")
                        elif msg_type == 'response_chunk':
                            content = response.get('content', '')
                            print(f"   [{elapsed:6.1f}ms] 🤖 RESPONSE: '{content[:50]}...'")
                        elif msg_type == 'tts_chunk':
                            is_final = response.get('is_final', False)
                            audio_size = len(base64.b64decode(response.get('audio_data', ''))) if response.get('audio_data') else 0
                            print(f"   [{elapsed:6.1f}ms] 🔊 TTS: {audio_size} bytes {'(FINAL)' if is_final else ''}")
                        else:
                            print(f"   [{elapsed:6.1f}ms] 📨 {msg_type.upper()}")

                        # Stop if we get a final TTS chunk
                        if msg_type == 'tts_chunk' and response.get('is_final'):
                            print(f"   🏁 Received final TTS chunk, stopping monitoring")
                            break

                    except asyncio.TimeoutError:
                        continue

                print(f"\n   ✅ Debugging complete")

            finally:
                await communicator.disconnect()

        # Run the async debug function
        asyncio.run(debug_transcription())


if __name__ == '__main__':
    # Run the tests
    import django
    from django.conf import settings
    from django.test.utils import get_runner

    if not settings.configured:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
        django.setup()

    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(["chat.tests.test_websocket_speech_to_speech"])
