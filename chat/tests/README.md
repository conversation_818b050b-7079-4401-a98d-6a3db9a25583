# WebSocket Consumer Tests

This directory contains comprehensive tests for the WebSocket consumer functionality in the chat app.

## Test Files

- `test_websocket_consumer.py` - Basic WebSocket consumer tests
- `test_websocket_realtime_messaging.py` - Tests for real-time messaging functionality
- `test_websocket_connection_management.py` - Tests for connection state management
- `test_websocket_jwt_auth.py` - Tests for JWT authentication in WebSockets
- `test_websocket_routing.py` - Tests for WebSocket routing
- `test_error_recovery_service.py` - Tests for error recovery service
- `test_agent_integration.py` - Tests for integration with agent coordinator
- `test_load_testing.py` - Load tests for WebSocket consumer

## Running Tests

### Run All WebSocket Tests

```bash
python chat/tests/run_websocket_tests.py
```

### Run Specific Test Class

```bash
python chat/tests/run_websocket_tests.py --class chat.tests.test_websocket_realtime_messaging.TypingIndicatorTest
```

### Run Load Tests

```bash
python chat/tests/run_websocket_tests.py --load
```

### Run with Different Verbosity

```bash
python chat/tests/run_websocket_tests.py --verbosity 3
```

### Stop on First Failure

```bash
python chat/tests/run_websocket_tests.py --failfast
```

## Using Django Test Runner

You can also run the tests using Django's test runner:

```bash
python manage.py test chat.tests.test_websocket_realtime_messaging
```

## Test Coverage

To run tests with coverage:

```bash
coverage run --source='chat' manage.py test chat.tests.test_websocket_realtime_messaging
coverage report
```

## Test Structure

The tests are organized into the following categories:

1. **Basic Functionality Tests** - Test basic WebSocket connection, authentication, and message handling
2. **Real-time Messaging Tests** - Test typing indicators, streaming responses, and message delivery
3. **AI Integration Tests** - Test integration with agent coordinator, memory manager, and emotion context
4. **Error Handling Tests** - Test error recovery, fallback mechanisms, and circuit breakers
5. **Performance Tests** - Test performance monitoring, metrics recording, and load handling

## Adding New Tests

When adding new tests, follow these guidelines:

1. Use the appropriate base test case class (`WebSocketRealtimeMessagingTestCase`, `AgentIntegrationTestCase`, etc.)
2. Use async/await for WebSocket communication
3. Use proper mocking for external services
4. Include both success and error cases
5. Verify database state after operations
6. Clean up resources in tearDown methods