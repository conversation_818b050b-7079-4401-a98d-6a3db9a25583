from django.test import TestCase
from django.test import TestCase
"""
Tests for error recovery and fallback mechanisms.
"""
import unittest
from chat.services.error_recovery import get_fallback_response


class ErrorRecoveryTest(unittest.TestCase):
    """Test error recovery functionality."""
    
    def test_general_fallback_response(self):
        """Test general fallback response."""
        response = get_fallback_response('general')
        
        self.assertIsInstance(response, str)
        self.assertGreater(len(response), 0)
        
        # Should contain apologetic language or mention trouble
        self.assertTrue(
            any(word in response.lower() for word in ['sorry', 'apologize', 'trouble', 'unable', 'can\'t', 'difficulties', 'issues'])
        )
    
    def test_timeout_fallback_response(self):
        """Test timeout-specific fallback response."""
        response = get_fallback_response('timeout')
        
        self.assertIsInstance(response, str)
        self.assertGreater(len(response), 0)
        
        # Should mention timeout or delay
        self.assertTrue(
            any(word in response.lower() for word in ['timeout', 'delay', 'long', 'timed out'])
        )
    
    def test_api_failure_fallback_response(self):
        """Test API failure fallback response."""
        response = get_fallback_response('api_failure')
        
        self.assertIsInstance(response, str)
        self.assertGreater(len(response), 0)
        
        # Should mention connection or service issues
        self.assertTrue(
            any(word in response.lower() for word in ['connection', 'service', 'trouble'])
        )
    
    def test_memory_failure_fallback_response(self):
        """Test memory failure fallback response."""
        response = get_fallback_response('memory_failure')
        
        self.assertIsInstance(response, str)
        self.assertGreater(len(response), 0)
        
        # Should mention memory issues
        self.assertTrue(
            any(word in response.lower() for word in ['memory', 'recall', 'remember'])
        )
    
    def test_unknown_error_type_fallback(self):
        """Test fallback for unknown error types."""
        response = get_fallback_response('unknown_error_type')
        
        # Should default to general response
        self.assertIsInstance(response, str)
        self.assertGreater(len(response), 0)
    
    def test_fallback_response_with_context(self):
        """Test fallback response with additional context."""
        context = {
            'user_id': 'test_user',
            'error_details': 'Connection timeout'
        }
        
        response = get_fallback_response('timeout', context)
        
        self.assertIsInstance(response, str)
        self.assertGreater(len(response), 0)
    
    def test_fallback_response_randomization(self):
        """Test that fallback responses are randomized."""
        responses = set()
        
        # Generate multiple responses
        for _ in range(20):
            response = get_fallback_response('general')
            responses.add(response)
        
        # Should have some variety (not all the same)
        self.assertGreater(len(responses), 1)
    
    def test_all_error_types_have_responses(self):
        """Test that all error types have appropriate responses."""
        error_types = ['general', 'timeout', 'api_failure', 'memory_failure']
        
        for error_type in error_types:
            response = get_fallback_response(error_type)
            self.assertIsInstance(response, str)
            self.assertGreater(len(response), 0)
            
            # Each response should be appropriate for its error type
            if error_type == 'timeout':
                self.assertTrue(
                    any(word in response.lower() for word in ['timeout', 'delay', 'long', 'wait', 'timed out'])
                )
            elif error_type == 'api_failure':
                self.assertTrue(
                    any(word in response.lower() for word in ['connection', 'service', 'access'])
                )
            elif error_type == 'memory_failure':
                self.assertTrue(
                    any(word in response.lower() for word in ['memory', 'recall', 'remember'])
                )