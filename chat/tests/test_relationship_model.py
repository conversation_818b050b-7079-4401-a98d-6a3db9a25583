from django.test import TestCase
from django.test import TestCase
from django.test import TestCase
from django.contrib.auth import get_user_model
from chat.models import Relationship

User = get_user_model()


class RelationshipModelTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.relationship = Relationship.objects.create(user=self.user)

    def test_relationship_creation(self):
        """Test that a Relationship instance can be created"""
        self.assertEqual(self.relationship.user, self.user)
        self.assertEqual(self.relationship.level, 1)
        self.assertEqual(self.relationship.xp, 0)
        self.assertEqual(self.relationship.xp_to_next_level, 100)

    def test_add_xp(self):
        """Test adding XP and leveling up"""
        # Add 50 XP (not enough to level up)
        self.relationship.add_xp(50)
        self.assertEqual(self.relationship.xp, 50)
        self.assertEqual(self.relationship.level, 1)
        
        # Add 60 more XP (enough to level up)
        self.relationship.add_xp(60)
        self.assertEqual(self.relationship.level, 2)
        self.assertEqual(self.relationship.xp, 10)  # 110 - 100 = 10
        
        # Check that xp_to_next_level has been updated
        self.assertEqual(self.relationship.xp_to_next_level, 300)  # Level 3 requires 300 XP
        
        # Add enough XP to level up multiple times
        self.relationship.add_xp(500)
        self.assertEqual(self.relationship.level, 3)
        self.assertEqual(self.relationship.xp_to_next_level, 400)  # Level 4 requires 400 XP