from django.test import TestCase
from django.test import TestCase
"""
Test runner for comprehensive WebSocket consumer testing.
"""
import os
import sys
import django
from django.test.utils import get_runner
from django.conf import settings

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()


def run_websocket_tests():
    """Run all WebSocket-related tests."""
    from django.test.runner import DiscoverRunner
    
    test_runner = DiscoverRunner(verbosity=2, interactive=False, keepdb=True)
    
    # List of test modules to run
    test_modules = [
        'chat.tests.test_websocket_consumer',
        'chat.tests.test_ai_integration', 
        'chat.tests.test_error_recovery',
        'chat.tests.test_websocket_routing',
        'chat.tests.test_websocket_jwt_auth',
    ]
    
    print("Running comprehensive WebSocket consumer tests...")
    print("=" * 60)
    
    failures = 0
    for module in test_modules:
        print(f"\nRunning tests in {module}...")
        print("-" * 40)
        
        try:
            result = test_runner.run_tests([module])
            if result:
                failures += result
                print(f"❌ {module}: {result} test(s) failed")
            else:
                print(f"✅ {module}: All tests passed")
        except Exception as e:
            print(f"❌ {module}: Error running tests - {e}")
            failures += 1
    
    print("\n" + "=" * 60)
    if failures == 0:
        print("🎉 All WebSocket consumer tests passed!")
        return True
    else:
        print(f"❌ {failures} test module(s) had failures")
        return False


def run_specific_test_class(test_class):
    """Run a specific test class."""
    from django.test.runner import DiscoverRunner
    
    test_runner = DiscoverRunner(verbosity=2, interactive=False, keepdb=True)
    result = test_runner.run_tests([test_class])
    return result == 0


def run_performance_tests():
    """Run performance-focused tests."""
    print("Running performance tests...")
    
    performance_tests = [
        'chat.tests.test_websocket_consumer.ChatConsumerPerformanceTest',
        'chat.tests.test_ai_integration.PerformanceMonitoringTest',
    ]
    
    for test in performance_tests:
        print(f"Running {test}...")
        success = run_specific_test_class(test)
        if success:
            print(f"✅ {test}: Passed")
        else:
            print(f"❌ {test}: Failed")


def run_integration_tests():
    """Run integration tests."""
    print("Running integration tests...")
    
    integration_tests = [
        'chat.tests.test_websocket_consumer.ChatConsumerIntegrationTest',
        'chat.tests.test_ai_integration.AgentCoordinatorIntegrationTest',
        'chat.tests.test_ai_integration.MemoryIntegrationTest',
    ]
    
    for test in integration_tests:
        print(f"Running {test}...")
        success = run_specific_test_class(test)
        if success:
            print(f"✅ {test}: Passed")
        else:
            print(f"❌ {test}: Failed")


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Run WebSocket consumer tests')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--performance', action='store_true', help='Run performance tests')
    parser.add_argument('--integration', action='store_true', help='Run integration tests')
    parser.add_argument('--class', dest='test_class', help='Run specific test class')
    
    args = parser.parse_args()
    
    if args.test_class:
        success = run_specific_test_class(args.test_class)
        sys.exit(0 if success else 1)
    elif args.performance:
        run_performance_tests()
    elif args.integration:
        run_integration_tests()
    else:
        # Run all tests by default
        success = run_websocket_tests()
        sys.exit(0 if success else 1)