from django.test import TestCase
from django.test import TestCase
"""
Tests for real-time messaging functionality in the WebSocket consumer.
"""
import json
import uuid
import asyncio
import base64
from unittest.mock import Mock, patch, AsyncMock
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework_simplejwt.tokens import AccessToken
from asgiref.sync import sync_to_async

from chat.consumers import ChatConsumer
from chat.models import Conversation, Message, ChatSession
from chat.models_realtime import StreamingSession, UserRelationship, EmotionContext
from chat.services.error_recovery import get_fallback_response
from ellahai_backend.asgi import application

User = get_user_model()


class WebSocketRealtimeMessagingTestCase(TransactionTestCase):
    """Base test case for WebSocket real-time messaging tests."""
    
    def setUp(self):
        """Set up test data."""
        import uuid
        from django.urls import re_path
        from channels.routing import URLRouter
        
        unique_username = f"testuser_{uuid.uuid4().hex[:8]}"
        self.user = User.objects.create_user(
            username=unique_username,
            email=f"{unique_username}@example.com",
            password="testpassword"
        )
        self.token = str(AccessToken.for_user(self.user))
        
        # Create application with direct routing to consumer
        self.application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
            re_path(r"^ws/chat/(?P<conversation_id>[^/]+)/$", ChatConsumer.as_asgi()),
        ])
        
    async def create_communicator(self, path=None):
        """Create a WebSocket communicator with authentication."""
        if path is None:
            path = "/ws/chat/"
        
        communicator = WebsocketCommunicator(
            application=self.application,
            path=path
        )
        
        # Add user to scope directly (bypass middleware)
        communicator.scope["user"] = self.user
        
        return communicator
    
    async def connect_and_verify(self, communicator):
        """Connect to WebSocket and verify connection."""
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Receive connection established message
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'connection_established')
        
        return response


class TypingIndicatorTest(WebSocketRealtimeMessagingTestCase):
    """Test typing indicator functionality."""
    
    def test_typing_indicator_flow(self):
        """Test the complete typing indicator flow."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Test Conversation"
            )
            
            # Send typing start
            await communicator.send_json_to({
                'type': 'typing_start'
            })
            
            # Receive typing acknowledgment
            response = await communicator.receive_json_from()
            self.assertEqual(response['type'], 'typing_acknowledged')
            self.assertEqual(response['status'], 'started')
            self.assertIn('conversation_id', response)
            self.assertIn('user_id', response)
            self.assertIn('timestamp', response)
            
            # Verify typing indicator was saved to database
            typing_messages = await sync_to_async(list)(
                Message.objects.filter(
                    conversation=conversation,
                    sender_type='user',
                    message_type='typing'
                )
            )
            self.assertEqual(len(typing_messages), 1)
            
            # Send typing stop
            await communicator.send_json_to({
                'type': 'typing_stop'
            })
            
            # Receive typing acknowledgment
            response = await communicator.receive_json_from()
            self.assertEqual(response['type'], 'typing_acknowledged')
            self.assertEqual(response['status'], 'stopped')
            
            # Verify typing indicator was removed from database
            typing_messages = await sync_to_async(list)(
                Message.objects.filter(
                    conversation=conversation,
                    sender_type='user',
                    message_type='typing'
                )
            )
            self.assertEqual(len(typing_messages), 0)
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_typing_indicator_without_conversation(self):
        """Test typing indicator when no conversation exists."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Send typing start without creating a conversation
            await communicator.send_json_to({
                'type': 'typing_start'
            })
            
            # Receive response - could be error or typing acknowledgment with most recent conversation
            response = await communicator.receive_json_from()
            
            # Either way, we should get a valid response
            self.assertIn('type', response)
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_typing_indicator_during_ai_response(self):
        """Test AI typing indicator during response generation."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Test Conversation"
            )
            
            # Mock agent coordinator
            mock_coordinator = Mock()
            
            async def mock_process_query(*args, **kwargs):
                # Simulate delay
                await asyncio.sleep(0.1)
                yield {
                    'type': 'response_complete',
                    'full_content': 'Test response',
                    'domain': 'general',
                    'timestamp': timezone.now().isoformat()
                }
            
            mock_coordinator.process_user_query = mock_process_query
            
            with patch('chat.consumers.AGENTS_AVAILABLE', True), \
                 patch('chat.consumers.get_agent_coordinator', return_value=mock_coordinator):
                
                # Send text message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Hello',
                    'conversation_id': str(conversation.id)
                })
                
                # Receive message acknowledgment
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                
                # Receive typing indicator start
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'ai_typing')
                self.assertEqual(response['status'], 'started')
                
                # Verify AI typing indicator was saved to database
                typing_messages = await sync_to_async(list)(
                    Message.objects.filter(
                        conversation=conversation,
                        sender_type='assistant',
                        message_type='typing'
                    )
                )
                self.assertEqual(len(typing_messages), 1)
                
                # Wait for response and typing stop
                responses = []
                while True:
                    try:
                        response = await asyncio.wait_for(
                            communicator.receive_json_from(), 
                            timeout=5.0
                        )
                        responses.append(response)
                        
                        if response.get('type') == 'ai_typing' and response.get('status') == 'stopped':
                            break
                    except asyncio.TimeoutError:
                        break
                
                # Verify typing indicator was stopped
                typing_stop = next(
                    (r for r in responses if r.get('type') == 'ai_typing' and r.get('status') == 'stopped'), 
                    None
                )
                self.assertIsNotNone(typing_stop)
                
                # Verify typing indicator was removed from database
                typing_messages = await sync_to_async(list)(
                    Message.objects.filter(
                        conversation=conversation,
                        sender_type='assistant',
                        message_type='typing'
                    )
                )
                self.assertEqual(len(typing_messages), 0)
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class AIResponseGenerationTest(WebSocketRealtimeMessagingTestCase):
    """Test AI response generation functionality."""
    
    def test_ai_response_streaming(self):
        """Test streaming AI response generation."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Test Conversation"
            )
            
            # Mock agent coordinator
            mock_coordinator = Mock()
            
            async def mock_process_query(*args, **kwargs):
                yield {
                    'type': 'domain_classification',
                    'domain': 'general',
                    'confidence': 0.8,
                    'timestamp': timezone.now().isoformat()
                }
                
                # Simulate streaming chunks
                yield {
                    'type': 'response_chunk',
                    'content': 'Hello! ',
                    'timestamp': timezone.now().isoformat()
                }
                
                await asyncio.sleep(0.1)
                
                yield {
                    'type': 'response_chunk',
                    'content': 'How can I help you today?',
                    'timestamp': timezone.now().isoformat()
                }
                
                yield {
                    'type': 'response_complete',
                    'full_content': 'Hello! How can I help you today?',
                    'domain': 'general',
                    'timestamp': timezone.now().isoformat()
                }
            
            async def mock_health_check():
                return {
                    'overall_status': 'healthy',
                    'agents': {
                        'domain_router': {
                            'status': 'healthy'
                        }
                    },
                    'timestamp': timezone.now().isoformat()
                }
            
            mock_coordinator.process_user_query = mock_process_query
            mock_coordinator.health_check = mock_health_check
            
            with patch('chat.consumers.AGENTS_AVAILABLE', True), \
                 patch('chat.consumers.get_agent_coordinator', return_value=mock_coordinator):
                
                # Send text message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Hello',
                    'conversation_id': str(conversation.id)
                })
                
                # Receive message acknowledgment
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                
                # Receive typing indicator start
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'ai_typing')
                self.assertEqual(response['status'], 'started')
                
                # Collect response chunks
                chunks = []
                while True:
                    try:
                        response = await asyncio.wait_for(
                            communicator.receive_json_from(), 
                            timeout=5.0
                        )
                        
                        if response.get('type') == 'llm_response_chunk':
                            chunks.append(response)
                        
                        if response.get('type') == 'ai_typing' and response.get('status') == 'stopped':
                            break
                    except asyncio.TimeoutError:
                        break
                
                # Verify we received chunks
                self.assertGreater(len(chunks), 0)
                
                # Verify final chunk has is_final=True
                final_chunk = next(
                    (c for c in chunks if c.get('is_final')), 
                    None
                )
                self.assertIsNotNone(final_chunk)
                
                # Wait a bit for async operations to complete
                await asyncio.sleep(0.5)
                
                # Verify message was saved to database
                messages = await sync_to_async(list)(
                    Message.objects.filter(
                        conversation=conversation,
                        sender_type='assistant',
                        message_type='text'
                    )
                )
                self.assertEqual(len(messages), 1)
                self.assertEqual(messages[0].content, 'Hello! How can I help you today?')
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_fallback_response_generation(self):
        """Test fallback response generation when agents are unavailable."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Test Conversation"
            )
            
            with patch('chat.consumers.AGENTS_AVAILABLE', False), \
                 patch('chat.consumers.ChatConsumer.check_agents_availability', return_value=False):
                # Send text message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Hello',
                    'conversation_id': str(conversation.id)
                })
                
                # Receive message acknowledgment
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                
                # Wait a bit for async operations to complete
                await asyncio.sleep(3.0)
                
                # Verify fallback message was saved to database
                messages = await sync_to_async(list)(
                    Message.objects.filter(
                        conversation=conversation,
                        sender_type='assistant',
                        message_type='text',
                        model_used='fallback'
                    )
                )
                
                print(f"Found {len(messages)} fallback messages in database")
                for msg in messages:
                    print(f"Message: {msg.content}, Model: {msg.model_used}, Type: {msg.message_type}")
                
                self.assertEqual(len(messages), 1)
                content_lower = messages[0].content.lower()
                self.assertTrue(
                    'trouble' in content_lower or 'unavailable' in content_lower or 'issues' in content_lower,
                    f"Expected 'trouble', 'unavailable', or 'issues' in message content: {messages[0].content}"
                )
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_cached_response_handling(self):
        """Test handling of cached responses."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Test Conversation"
            )
            
            cached_response = "This is a cached response for testing"
            
            with patch('chat.consumers.cache_service_instance') as mock_cache:
                mock_cache.get_cached_query_response.return_value = cached_response
                
                # Send text message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Hello',
                    'conversation_id': str(conversation.id)
                })
                
                # Receive message acknowledgment
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                
                # Collect response chunks
                chunks = []
                while True:
                    try:
                        response = await asyncio.wait_for(
                            communicator.receive_json_from(), 
                            timeout=5.0
                        )
                        
                        if response.get('type') == 'llm_response_chunk':
                            chunks.append(response)
                        
                        if response.get('type') == 'ai_typing' and response.get('status') == 'stopped':
                            break
                    except asyncio.TimeoutError:
                        break
                
                # Verify we received chunks
                self.assertGreater(len(chunks), 0)
                
                # Verify message was saved to database
                messages = await sync_to_async(list)(
                    Message.objects.filter(
                        conversation=conversation,
                        sender_type='assistant',
                        message_type='text'
                    )
                )
                self.assertEqual(len(messages), 1)
                self.assertEqual(messages[0].content, cached_response)
                self.assertEqual(messages[0].model_used, 'cached')
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class MemoryIntegrationTest(WebSocketRealtimeMessagingTestCase):
    """Test memory integration functionality."""
    
    def test_memory_context_retrieval(self):
        """Test memory context retrieval."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Test Conversation"
            )
            
            # Mock memory manager
            mock_memory_manager = Mock()
            mock_memories = [
                {
                    'text': 'User likes pizza',
                    'type': 'preference',
                    'importance': 0.8
                }
            ]
            mock_memory_manager.async_search_memories = AsyncMock(return_value=mock_memories)
            
            # Mock agent coordinator
            mock_coordinator = Mock()
            
            async def mock_process_query(*args, **kwargs):
                # Verify memory context was passed
                memory_context = kwargs.get('memory_context')
                self.assertIsNotNone(memory_context)
                self.assertIn('memories', memory_context)
                self.assertEqual(len(memory_context['memories']), 1)
                
                yield {
                    'type': 'response_complete',
                    'full_content': 'I remember you like pizza!',
                    'domain': 'general',
                    'timestamp': timezone.now().isoformat()
                }
            
            async def mock_health_check():
                return {
                    'overall_status': 'healthy',
                    'agents': {
                        'domain_router': {
                            'status': 'healthy'
                        }
                    },
                    'timestamp': timezone.now().isoformat()
                }
            
            mock_coordinator.process_user_query = mock_process_query
            mock_coordinator.health_check = mock_health_check
            
            with patch('chat.consumers.AGENTS_AVAILABLE', True), \
                 patch('chat.consumers.get_agent_coordinator', return_value=mock_coordinator), \
                 patch('agents.services.memory_manager.get_memory_manager', return_value=mock_memory_manager):
                
                # Send text message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'What do you remember about me?',
                    'conversation_id': str(conversation.id)
                })
                
                # Wait for processing
                await asyncio.sleep(0.5)
                
                # Verify memory manager was called
                mock_memory_manager.async_search_memories.assert_called_once()
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class RelationshipMetricsTest(WebSocketRealtimeMessagingTestCase):
    """Test relationship metrics functionality."""
    
    def test_relationship_metrics_update(self):
        """Test relationship metrics update."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Test Conversation"
            )
            
            # Create relationship
            relationship = await sync_to_async(UserRelationship.objects.create)(
                user=self.user,
                relationship_level=1,
                total_interactions=0,
                emotional_intimacy_score=0.0
            )
            
            # Send typing start
            await communicator.send_json_to({
                'type': 'typing_start'
            })
            
            # Receive typing acknowledgment
            await communicator.receive_json_from()
            
            # Verify relationship metrics were updated
            updated_relationship = await sync_to_async(UserRelationship.objects.get)(
                user=self.user
            )
            self.assertGreater(updated_relationship.total_interactions, 0)
            self.assertGreater(updated_relationship.emotional_intimacy_score, 0.0)
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class ErrorHandlingTest(WebSocketRealtimeMessagingTestCase):
    """Test error handling functionality."""
    
    def test_error_recovery_manager(self):
        """Test error recovery manager functionality."""
        from chat.services.error_recovery import error_recovery_manager
        
        # Record an error
        error_recovery_manager.record_error(
            'test_service',
            'api_failure',
            'Test error message'
        )
        
        # Get error stats
        stats = error_recovery_manager.get_error_stats('test_service')
        
        # Verify error was recorded
        self.assertIn('error_counts', stats)
        self.assertIn('api_failure', stats['error_counts'])
        self.assertEqual(stats['error_counts']['api_failure'], 1)
        
        # Verify last error was recorded
        self.assertIn('last_error', stats)
        self.assertEqual(stats['last_error']['type'], 'api_failure')
        self.assertEqual(stats['last_error']['message'], 'Test error message')
    
    def test_fallback_response_generation(self):
        """Test fallback response generation."""
        # Test different error types
        for error_type in ['general', 'timeout', 'api_failure', 'memory_failure']:
            response = get_fallback_response(error_type)
            self.assertIsInstance(response, str)
            self.assertGreater(len(response), 0)
    
    def test_error_handling_during_message_processing(self):
        """Test error handling during message processing."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Test Conversation"
            )
            
            with patch('chat.consumers.ChatConsumer.generate_streaming_ai_response') as mock_generate:
                # Simulate error
                mock_generate.side_effect = Exception("Test error")
                
                # Send text message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Hello',
                    'conversation_id': str(conversation.id)
                })
                
                # Receive message acknowledgment
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                
                # Receive typing indicator start
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'ai_typing')
                self.assertEqual(response['status'], 'started')
                
                # Should receive error
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'error')
                self.assertIn('Failed to process message', response['message'])
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class PerformanceMonitoringTest(WebSocketRealtimeMessagingTestCase):
    """Test performance monitoring functionality."""
    
    def test_performance_metrics_recording(self):
        """Test performance metrics recording."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Test Conversation"
            )
            
            with patch('chat.consumers.performance_monitor') as mock_monitor:
                mock_monitor.start_timer = Mock()
                mock_monitor.end_timer = Mock(return_value=100.0)  # 100ms
                mock_monitor.record_metric = Mock()
                
                # Mock agent coordinator
                mock_coordinator = Mock()
                
                async def mock_process_query(*args, **kwargs):
                    yield {
                        'type': 'response_complete',
                        'full_content': 'Test response',
                        'domain': 'general',
                        'timestamp': timezone.now().isoformat()
                    }
                
                mock_coordinator.process_user_query = mock_process_query
                
                with patch('chat.consumers.AGENTS_AVAILABLE', True), \
                     patch('chat.consumers.get_agent_coordinator', return_value=mock_coordinator):
                    
                    # Send text message
                    await communicator.send_json_to({
                        'type': 'text_message',
                        'content': 'Test performance monitoring',
                        'conversation_id': str(conversation.id)
                    })
                    
                    # Wait for processing
                    await asyncio.sleep(0.5)
                    
                    # Verify performance monitoring was called
                    mock_monitor.start_timer.assert_called()
                    mock_monitor.end_timer.assert_called()
                    mock_monitor.record_metric.assert_called()
            
            await communicator.disconnect()
        
        asyncio.run(run_test())