from django.test import TestCase
from django.test import TestCase
"""
Comprehensive tests for the ChatConsumer WebSocket functionality.
"""
import json
import uuid
import asyncio
import base64
from unittest.mock import Mock, patch, AsyncMock
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.urls import re_path
from channels.routing import URLRouter
from asgiref.sync import sync_to_async

from chat.consumers import ChatConsumer
from chat.models import Conversation, Message, ChatSession
from chat.models_realtime import StreamingSession, UserRelationship

User = get_user_model()


class WebSocketConsumerTestCase(TransactionTestCase):
    """Base test case for WebSocket consumer tests."""
    
    def setUp(self):
        """Set up test data."""
        # Create unique username to avoid conflicts
        unique_username = f"testuser_{uuid.uuid4().hex[:8]}"
        self.user = User.objects.create_user(
            username=unique_username,
            email=f"{unique_username}@example.com",
            password="testpassword"
        )
        
        # Create application with direct routing to consumer
        self.application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
            re_path(r"^ws/chat/(?P<conversation_id>[^/]+)/$", ChatConsumer.as_asgi()),
        ])
        
        # Set AGENTS_AVAILABLE to True for testing
        import sys
        sys.modules['chat.consumers'].AGENTS_AVAILABLE = True
    
    async def create_communicator(self, path=None):
        """Create a WebSocket communicator with authentication."""
        if path is None:
            path = "/ws/chat/"
        
        communicator = WebsocketCommunicator(
            application=self.application,
            path=path
        )
        
        # Add user to scope directly (bypass middleware)
        communicator.scope["user"] = self.user
        
        return communicator
    
    async def connect_and_verify(self, communicator):
        """Connect to WebSocket and verify connection."""
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)
        
        # Receive connection established message
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'connection_established')
        
        return response
    
    @staticmethod
    async def create_conversation(user):
        """Create a conversation for testing."""
        conversation = await sync_to_async(Conversation.objects.create)(
            user=user,
            title="Test Conversation"
        )
        return conversation
class ChatConsumerConnectionTest(WebSocketConsumerTestCase):
    """Test WebSocket connection and disconnection."""
    
    def test_successful_connection(self):
        """Test successful WebSocket connection."""
        async def run_test():
            communicator = await self.create_communicator()
            response = await self.connect_and_verify(communicator)
            
            # Verify response structure
            expected_keys = [
                'type', 'message', 'session_id', 'connection_id',
                'chat_session_id', 'streaming_enabled', 'heartbeat_interval',
                'agents_available', 'supported_message_types'
            ]
            for key in expected_keys:
                self.assertIn(key, response)
            
            # Verify supported message types
            expected_types = [
                'text_message', 'audio_chunk', 'emotion_feedback',
                'typing_start', 'typing_stop', 'conversation_switch',
                'connection_heartbeat', 'reconnection_request'
            ]
            for msg_type in expected_types:
                self.assertIn(msg_type, response['supported_message_types'])
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_heartbeat_functionality(self):
        """Test heartbeat functionality."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Send heartbeat
            await communicator.send_json_to({
                'type': 'connection_heartbeat',
                'timestamp': timezone.now().timestamp() * 1000
            })
            
            # Receive heartbeat response
            response = await communicator.receive_json_from()
            self.assertEqual(response['type'], 'heartbeat_response')
            self.assertIn('timestamp', response)
            self.assertIn('session_id', response)
            self.assertIn('connection_id', response)
            self.assertEqual(response['connection_state'], 'active')
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class ChatConsumerMessageHandlingTest(WebSocketConsumerTestCase):
    """Test message handling functionality."""
    
    def test_text_message_handling(self):
        """Test handling of text messages."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await self.create_conversation(self.user)
            
            # Mock agent coordinator
            mock_coordinator = Mock()
            
            async def mock_process_query(*args, **kwargs):
                yield {
                    'type': 'response_complete',
                    'full_content': 'Test response',
                    'domain': 'general',
                    'timestamp': timezone.now().isoformat()
                }
            
            mock_coordinator.process_user_query = mock_process_query
            
            with patch('chat.consumers.get_agent_coordinator', return_value=mock_coordinator):
                # Send text message
                test_message = "Hello, this is a test message"
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': test_message,
                    'conversation_id': str(conversation.id),
                    'timestamp': timezone.now().timestamp() * 1000
                })
                
                # Receive message acknowledgment
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                self.assertIn('message_id', response)
                self.assertIn('timestamp', response)
                self.assertTrue(response['processing_started'])
                self.assertIn('request_id', response)
                
                # Receive typing indicator
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'ai_typing')
                self.assertEqual(response['status'], 'started')
                
                # Verify message was saved to database
                messages = await sync_to_async(list)(
                    Message.objects.filter(content=test_message)
                )
                self.assertEqual(len(messages), 1)
                self.assertEqual(messages[0].sender_type, 'user')
                self.assertEqual(messages[0].message_type, 'text')
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_empty_message_handling(self):
        """Test handling of empty messages."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await self.create_conversation(self.user)
            
            # Send empty message
            await communicator.send_json_to({
                'type': 'text_message',
                'content': '',
                'conversation_id': str(conversation.id)
            })
            
            # Should receive error response
            response = await communicator.receive_json_from()
            self.assertEqual(response['type'], 'error')
            self.assertIn('Message content cannot be empty', response['message'])
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_typing_indicators(self):
        """Test typing indicator functionality."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation first
            conversation = await self.create_conversation(self.user)
            
            # Send typing start
            await communicator.send_json_to({
                'type': 'typing_start',
                'conversation_id': str(conversation.id)
            })
            
            # Receive typing acknowledgment
            response = await communicator.receive_json_from()
            self.assertEqual(response['type'], 'typing_acknowledged')
            self.assertEqual(response['status'], 'started')
            self.assertIn('conversation_id', response)
            self.assertIn('user_id', response)
            
            # Send typing stop
            await communicator.send_json_to({
                'type': 'typing_stop'
            })
            
            # Receive typing acknowledgment
            response = await communicator.receive_json_from()
            self.assertEqual(response['type'], 'typing_acknowledged')
            self.assertEqual(response['status'], 'stopped')
            
            await communicator.disconnect()
        
        asyncio.run(run_test())