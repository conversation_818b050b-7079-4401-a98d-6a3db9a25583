from django.test import TestCase
from django.contrib.auth import get_user_model
from chat.services.error_recovery import ErrorRecoveryManager, get_fallback_response
from chat.models import Conversation, Message
from unittest.mock import patch, Mock

User = get_user_model()

class ErrorRecoveryTestCase(TestCase):
    """Comprehensive tests for Error Recovery Manager"""

    def setUp(self):
        """Set up test environment"""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser', 
            password='testpassword'
        )

        # Create test conversation
        self.conversation = Conversation.objects.create(
            user=self.user,
            title='Test Conversation for Error Recovery'
        )

        # Create test messages
        self.messages = [
            Message.objects.create(
                conversation=self.conversation,
                sender_type='user',
                message_type='text',
                content=f'Test message {i}'
            ) for i in range(3)
        ]

        # Initialize error recovery manager
        self.error_recovery_manager = ErrorRecoveryManager()

    def test_record_error(self):
        """Test recording an error"""
        error_details = {
            'service_name': 'test_service',
            'error_type': 'ConnectionError',
            'error_message': 'Failed to connect to service'
        }

        # Record the error
        self.error_recovery_manager.record_error(
            error_details['service_name'], 
            error_details['error_type'], 
            error_details['error_message']
        )

        # Retrieve error stats
        stats = self.error_recovery_manager.get_error_stats('test_service')

        # Assertions
        self.assertIn('error_counts', stats)
        self.assertIn('ConnectionError', stats['error_counts'])
        self.assertEqual(stats['error_counts']['ConnectionError'], 1)

    def test_fallback_response(self):
        """Test generating fallback responses"""
        # Test different error types
        error_types = ['general', 'timeout', 'api_failure', 'memory_failure']
        
        for error_type in error_types:
            response = get_fallback_response(error_type)
            
            # Assertions
            self.assertIsNotNone(response)
            self.assertIsInstance(response, str)
            self.assertTrue(len(response) > 0)

    def test_error_threshold(self):
        """Test error threshold mechanism"""
        # Simulate multiple errors
        service_name = 'test_service'
        for i in range(6):
            self.error_recovery_manager.record_error(
                service_name, 
                'api_failure', 
                f'Test error {i}'
            )

        # Check if fallback should be used
        should_use_fallback = self.error_recovery_manager.should_use_fallback(service_name)
        self.assertTrue(should_use_fallback, "Should use fallback after exceeding error threshold")

    def test_recovery_suggestion(self):
        """Test getting recovery suggestions"""
        # Test various error types
        error_types = ['timeout', 'api_failure', 'memory_failure', 'unknown']
        
        for error_type in error_types:
            suggestion = self.error_recovery_manager.get_recovery_suggestion(
                'test_service', 
                error_type
            )
            
            # Assertions
            self.assertIsNotNone(suggestion)
            self.assertIsInstance(suggestion, str)
            self.assertTrue(len(suggestion) > 0)

    def test_error_stats_retrieval(self):
        """Test retrieving error statistics"""
        # Record errors for multiple services
        services = ['service1', 'service2']
        for service in services:
            self.error_recovery_manager.record_error(
                service, 
                'test_error', 
                f'Error for {service}'
            )

        # Retrieve all error stats
        all_stats = self.error_recovery_manager.get_error_stats()

        # Assertions
        self.assertIn('error_counts', all_stats)
        for service in services:
            self.assertIn(service, all_stats['error_counts'])
            self.assertIn('test_error', all_stats['error_counts'][service])

    def test_error_reset(self):
        """Test resetting error state"""
        service_name = 'test_service'
        
        # Record multiple errors
        for i in range(5):
            self.error_recovery_manager.record_error(
                service_name, 
                'api_failure', 
                f'Test error {i}'
            )

        # Check initial state
        self.assertTrue(self.error_recovery_manager.should_use_fallback(service_name))

        # Simulate service recovery (manual reset)
        self.error_recovery_manager.error_counts[service_name] = {}
        self.error_recovery_manager.last_errors.pop(service_name, None)

        # Check reset state
        self.assertFalse(self.error_recovery_manager.should_use_fallback(service_name))