from django.test import TestCase
from django.test import Test<PERSON>ase
import json
import uuid
import asyncio
from unittest.mock import patch, MagicMock
from channels.testing import Webso<PERSON><PERSON>ommunicator
from rest_framework_simplejwt.tokens import AccessToken
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.test import TestCase
from django.urls import re_path
from channels.routing import URLRouter
from asgiref.sync import sync_to_async

from chat.consumers import ChatConsumer

User = get_user_model()

class WebSocketJWTAuthTest(TestCase):
    """Test WebSocket JWT authentication."""
    
    def setUp(self):
        """Set up test data."""
        # Create a test user with unique username
        unique_username = f"testuser_{uuid.uuid4().hex[:8]}"
        self.user = User.objects.create_user(
            username=unique_username,
            email=f"{unique_username}@example.com",
            password='testpassword'
        )
        
        # Get JWT token
        self.token = str(AccessToken.for_user(self.user))
        
        # Create application with direct routing to consumer
        self.application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
        ])
    
    def test_websocket_auth(self):
        """Test WebSocket authentication."""
        async def run_tests():
            # Mock the create_chat_session and create_streaming_session methods
            with patch('chat.consumers.ChatConsumer.create_chat_session') as mock_create_chat_session, \
                 patch('chat.consumers.ChatConsumer.create_streaming_session') as mock_create_streaming_session, \
                 patch('chat.consumers.ChatConsumer.check_agents_availability') as mock_check_agents:
                
                # Configure mocks
                mock_chat_session = MagicMock()
                mock_chat_session.id = uuid.uuid4()
                mock_create_chat_session.return_value = mock_chat_session
                
                mock_streaming_session = MagicMock()
                mock_streaming_session.id = uuid.uuid4()
                mock_create_streaming_session.return_value = mock_streaming_session
                
                mock_check_agents.return_value = True
                
                # Test with authenticated user
                communicator = WebsocketCommunicator(
                    application=self.application,
                    path="/ws/chat/"
                )
                communicator.scope["user"] = self.user
                connected, _ = await communicator.connect()
                self.assertTrue(connected)
                
                # Receive connection established message
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'connection_established')
                
                await communicator.disconnect()
                
                # Test with anonymous user (simulating invalid token)
                communicator = WebsocketCommunicator(
                    application=self.application,
                    path="/ws/chat/"
                )
                communicator.scope["user"] = AnonymousUser()
                connected, _ = await communicator.connect()
                self.assertFalse(connected)
        
        # Run the async tests
        asyncio.run(run_tests())