"""
WebSocket TTS Integration Test
Tests the real-time TTS streaming through WebSocket consumer.
"""
import json
import uuid
import asyncio
import time
from unittest.mock import Mock, patch
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model
from django.urls import re_path
from channels.routing import URLRouter

from chat.consumers import ChatConsumer

User = get_user_model()


class WebSocketTTSIntegrationTest(TransactionTestCase):
    """Test TTS integration through WebSocket consumer."""
    
    def setUp(self):
        """Set up test data."""
        # Create unique username to avoid conflicts
        unique_username = f"ttsuser_{uuid.uuid4().hex[:8]}"
        self.user = User.objects.create_user(
            username=unique_username,
            email=f"{unique_username}@example.com",
            password="testpassword"
        )
        
        # Create application with direct routing to consumer
        self.application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
        ])
        
        # Set AGENTS_AVAILABLE to True for testing
        import sys
        sys.modules['chat.consumers'].AGENTS_AVAILABLE = True
    
    async def create_communicator(self):
        """Create WebSocket communicator."""
        communicator = WebsocketCommunicator(self.application, "ws/chat/")
        communicator.scope["user"] = self.user
        return communicator
    
    async def test_tts_streaming_performance(self):
        """Test TTS streaming performance through WebSocket."""
        print("\n🧪 Testing TTS Streaming Performance via WebSocket")
        print("=" * 60)

        communicator = await self.create_communicator()
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)

        # Receive connection confirmation
        response = await communicator.receive_json_from()
        self.assertEqual(response['type'], 'connection_established')
        print(f"✅ WebSocket connected: {response.get('message', 'Connected')}")

        try:
            # Test cases with increasing text lengths
            test_cases = [
                {
                    'name': 'Very Short (3 chars)',
                    'message': 'Hi!',
                    'expected_tts_ms': 1000,
                    'char_count': 3
                },
                {
                    'name': 'Short (25 chars)',
                    'message': 'Hello! How are you today?',
                    'expected_tts_ms': 1200,
                    'char_count': 25
                },
                {
                    'name': 'Medium (50 chars)',
                    'message': 'Can you help me understand how this system works?',
                    'expected_tts_ms': 1500,
                    'char_count': 49
                },
                {
                    'name': 'Long (100 chars)',
                    'message': 'I need help with understanding how artificial intelligence works and what the future holds.',
                    'expected_tts_ms': 2000,
                    'char_count': 91
                },
                {
                    'name': 'Very Long (200 chars)',
                    'message': 'Could you please explain in detail how machine learning algorithms work, what are the different types of neural networks, and how they are applied in real-world scenarios today?',
                    'expected_tts_ms': 2500,
                    'char_count': 175
                },
                {
                    'name': 'Extra Long (300+ chars)',
                    'message': 'I am very interested in learning about the latest developments in artificial intelligence, particularly in the areas of natural language processing, computer vision, and reinforcement learning. Could you provide a comprehensive overview of these fields and their current applications in industry?',
                    'expected_tts_ms': 3000,
                    'char_count': 295
                }
            ]
            
            results = []

            for i, test_case in enumerate(test_cases, 1):
                print(f"\n🧪 Test Case {i}: {test_case['name']} ({test_case['char_count']} chars)")
                print(f"📝 Message: '{test_case['message'][:60]}{'...' if len(test_case['message']) > 60 else ''}'")

                try:
                    # Send text message
                    start_time = time.time()
                    await communicator.send_json_to({
                        'type': 'text_message',
                        'content': test_case['message'],
                        'conversation_id': str(uuid.uuid4())
                    })

                    # Track response metrics
                    first_text_time = None
                    first_tts_time = None
                    text_chunks = 0
                    tts_chunks = 0
                    response_complete = False
                    total_audio_size = 0

                    # Collect responses with timeout
                    timeout_seconds = 45  # Longer timeout for longer texts
                    while not response_complete and (time.time() - start_time) < timeout_seconds:
                        try:
                            response = await asyncio.wait_for(
                                communicator.receive_json_from(),
                                timeout=8.0  # Longer individual timeout
                            )

                            current_time = time.time()
                            elapsed_ms = (current_time - start_time) * 1000

                            response_type = response.get('type')

                            if response_type == 'ai_message_chunk':
                                if first_text_time is None:
                                    first_text_time = elapsed_ms
                                    print(f"📝 First text chunk: {first_text_time:.1f}ms")
                                text_chunks += 1
                                content = response.get('content', '')
                                if text_chunks <= 3:  # Only show first few chunks
                                    print(f"   Text chunk {text_chunks}: '{content[:40]}{'...' if len(content) > 40 else ''}'")

                            elif response_type == 'audio_chunk':
                                if first_tts_time is None:
                                    first_tts_time = elapsed_ms
                                    print(f"🎵 First TTS chunk: {first_tts_time:.1f}ms")
                                tts_chunks += 1
                                audio_data = response.get('data', '')
                                total_audio_size += len(audio_data)
                                is_final = response.get('is_final', False)

                                if tts_chunks <= 3 or is_final:  # Show first few and final
                                    print(f"   TTS chunk {tts_chunks}: {len(audio_data)} chars (final: {is_final})")

                                if is_final:
                                    print(f"🎵 TTS streaming complete ({tts_chunks} total chunks)")

                            elif response_type == 'tts_error':
                                print(f"❌ TTS Error: {response.get('error')}")

                            elif response_type == 'ai_message_complete':
                                print(f"✅ AI response complete")
                                response_complete = True
                                break

                            elif response_type in ['ai_typing', 'message_received', 'llm_response_chunk']:
                                # Skip common non-essential responses
                                continue

                            else:
                                # Only log unexpected response types
                                if response_type not in ['connection_established']:
                                    print(f"📦 Other: {response_type}")

                        except asyncio.TimeoutError:
                            print("⏰ Individual response timeout - checking if complete...")
                            # Check if we got TTS chunks, might be complete
                            if tts_chunks > 0:
                                print(f"✅ Got {tts_chunks} TTS chunks, considering complete")
                                response_complete = True
                            break
                        except Exception as e:
                            print(f"❌ Error receiving response: {e}")
                            break

                    # Calculate total time
                    total_time = (time.time() - start_time) * 1000

                    # Store results
                    result = {
                        'name': test_case['name'],
                        'char_count': test_case['char_count'],
                        'total_time': total_time,
                        'first_text_time': first_text_time,
                        'first_tts_time': first_tts_time,
                        'text_chunks': text_chunks,
                        'tts_chunks': tts_chunks,
                        'total_audio_size': total_audio_size,
                        'expected_tts_ms': test_case['expected_tts_ms']
                    }
                    results.append(result)

                    # Print individual results
                    print(f"\n📊 Results for {test_case['name']}:")
                    print(f"   Total time: {total_time:.1f}ms")
                    print(f"   Text chunks: {text_chunks}")
                    print(f"   TTS chunks: {tts_chunks}")
                    print(f"   Audio size: {total_audio_size:,} chars")

                    if first_text_time:
                        status = "✅" if first_text_time <= 450 else "⚠️"
                        print(f"   {status} First text: {first_text_time:.1f}ms (target: ≤450ms)")
                    else:
                        print(f"   ❌ No text chunks received")

                    if first_tts_time:
                        expected = test_case['expected_tts_ms']
                        status = "✅" if first_tts_time <= expected else "⚠️"
                        print(f"   {status} First TTS: {first_tts_time:.1f}ms (expected: ≤{expected}ms)")
                    else:
                        print(f"   ❌ No TTS chunks received")

                    # Wait between tests
                    await asyncio.sleep(2)

                except Exception as e:
                    print(f"❌ Test case failed: {e}")
                    continue

            # Print summary analysis
            self._print_performance_analysis(results)
        
        finally:
            try:
                await communicator.disconnect()
            except Exception as e:
                print(f"⚠️ Disconnect error (expected): {e}")

        print(f"\n🏁 WebSocket TTS Integration Test Complete")

    def _print_performance_analysis(self, results):
        """Print comprehensive performance analysis."""
        if not results:
            print("\n❌ No results to analyze")
            return

        print(f"\n📈 PERFORMANCE ANALYSIS SUMMARY")
        print("=" * 80)

        # Table header
        print(f"{'Test Case':<20} {'Chars':<6} {'Text(ms)':<10} {'TTS(ms)':<10} {'Chunks':<8} {'Audio(KB)':<10} {'Status':<8}")
        print("-" * 80)

        # Results table
        for result in results:
            name = result['name'][:19]
            chars = result['char_count']
            text_time = f"{result['first_text_time']:.0f}" if result['first_text_time'] else "N/A"
            tts_time = f"{result['first_tts_time']:.0f}" if result['first_tts_time'] else "N/A"
            chunks = result['tts_chunks']
            audio_kb = f"{result['total_audio_size']/1024:.1f}" if result['total_audio_size'] else "0"

            # Status based on TTS performance
            if result['first_tts_time']:
                status = "✅ GOOD" if result['first_tts_time'] <= result['expected_tts_ms'] else "⚠️ SLOW"
            else:
                status = "❌ FAIL"

            print(f"{name:<20} {chars:<6} {text_time:<10} {tts_time:<10} {chunks:<8} {audio_kb:<10} {status:<8}")

        # Performance insights
        print("\n🎯 PERFORMANCE INSIGHTS:")

        # Text performance
        text_times = [r['first_text_time'] for r in results if r['first_text_time']]
        if text_times:
            avg_text = sum(text_times) / len(text_times)
            print(f"   📝 Text Response: Avg {avg_text:.0f}ms (Target: ≤450ms)")

        # TTS performance
        tts_times = [r['first_tts_time'] for r in results if r['first_tts_time']]
        if tts_times:
            avg_tts = sum(tts_times) / len(tts_times)
            print(f"   🎵 TTS Response: Avg {avg_tts:.0f}ms")

        # Character vs TTS time correlation
        char_tts_pairs = [(r['char_count'], r['first_tts_time']) for r in results if r['first_tts_time']]
        if len(char_tts_pairs) >= 2:
            chars, times = zip(*char_tts_pairs)
            # Simple correlation analysis
            if max(times) > min(times):
                ratio = (max(times) - min(times)) / (max(chars) - min(chars))
                print(f"   📊 TTS Scaling: ~{ratio:.1f}ms per character")

        # Success rate
        successful_tts = len([r for r in results if r['first_tts_time']])
        success_rate = (successful_tts / len(results)) * 100
        print(f"   ✅ Success Rate: {success_rate:.0f}% ({successful_tts}/{len(results)})")

        print("\n🚀 RECOMMENDATIONS:")
        if avg_tts < 1500:
            print("   ✅ TTS performance is excellent for real-time applications")
        elif avg_tts < 2500:
            print("   ⚠️ TTS performance is good but could be optimized for better UX")
        else:
            print("   ❌ TTS performance needs optimization for real-time applications")
    
    async def test_tts_error_handling(self):
        """Test TTS error handling through WebSocket."""
        print("\n🧪 Testing TTS Error Handling via WebSocket")

        communicator = await self.create_communicator()
        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)

        # Receive connection confirmation
        await communicator.receive_json_from()

        try:
            # Send a normal message to test graceful handling
            await communicator.send_json_to({
                'type': 'text_message',
                'content': 'Test message for error handling',
                'conversation_id': str(uuid.uuid4())
            })

            # Monitor for error handling
            error_handled = False
            tts_received = False
            start_time = time.time()

            while (time.time() - start_time) < 15:  # 15 second timeout
                try:
                    response = await asyncio.wait_for(
                        communicator.receive_json_from(),
                        timeout=3.0
                    )

                    response_type = response.get('type')

                    if response_type == 'tts_error':
                        print(f"✅ TTS error properly handled: {response.get('error')}")
                        error_handled = True
                        break
                    elif response_type == 'audio_chunk':
                        print(f"✅ TTS working normally")
                        tts_received = True
                    elif response_type == 'ai_message_complete':
                        print(f"✅ Message completed")
                        break

                except asyncio.TimeoutError:
                    print("⏰ Timeout waiting for response")
                    break
                except Exception as e:
                    print(f"⚠️ Exception during test: {e}")
                    break

            if tts_received:
                print(f"📊 Error handling test: ✅ TTS system working normally")
            elif error_handled:
                print(f"📊 Error handling test: ✅ Errors handled gracefully")
            else:
                print(f"📊 Error handling test: ⚠️ No TTS response received")

        finally:
            try:
                await communicator.disconnect()
            except Exception as e:
                print(f"⚠️ Disconnect error (expected): {e}")


if __name__ == '__main__':
    import django
    import os
    import sys
    
    # Set up Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
    django.setup()
    
    # Run the test
    import unittest
    unittest.main()
