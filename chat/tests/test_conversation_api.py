from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from chat.models import Conversation, Message
from chat.serializers import ConversationSerializer
import uuid

User = get_user_model()

class ConversationAPITestCase(TestCase):
    """Comprehensive tests for Conversation API endpoints"""

    def setUp(self):
        """Set up test environment"""
        # Create test users with unique emails
        self.user1 = User.objects.create_user(
            username='testuser1', 
            email=f'testuser1_{uuid.uuid4().hex}@example.com',
            password='testpassword1'
        )
        self.user2 = User.objects.create_user(
            username='testuser2', 
            email=f'testuser2_{uuid.uuid4().hex}@example.com',
            password='testpassword2'
        )

        # Create test conversations
        self.conversations = [
            Conversation.objects.create(
                user=self.user1,
                title=f'Test Conversation {i}'
            ) for i in range(3)
        ]

        # Create test messages
        for conversation in self.conversations:
            Message.objects.create(
                conversation=conversation,
                sender_type='user',
                message_type='text',
                content=f'Test message for {conversation.title}'
            )

        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user1)

    def test_list_conversations(self):
        """Test retrieving list of conversations"""
        url = reverse('chat:conversation-list')
        response = self.client.get(url)

        # Assertions
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Ensure only conversations for the authenticated user are returned
        user_conversations = Conversation.objects.filter(user=self.user1, is_archived=False)
        self.assertEqual(len(response.data.get('results', [])), len(user_conversations), "Should return all user's conversations")

    def test_create_conversation(self):
        """Test creating a new conversation"""
        url = reverse('chat:conversation-list')
        data = {
            'title': 'New Test Conversation',
        }
        response = self.client.post(url, data)

        # Assertions
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['title'], 'New Test Conversation')
        self.assertEqual(response.data['user'], self.user1.id)

    def test_retrieve_conversation(self):
        """Test retrieving a specific conversation"""
        conversation = self.conversations[0]
        url = reverse('chat:conversation-detail', kwargs={'pk': conversation.id})
        response = self.client.get(url)

        # Assertions
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], conversation.title)
        self.assertEqual(response.data['user'], self.user1.id)

    def test_update_conversation(self):
        """Test updating a conversation"""
        conversation = self.conversations[0]
        url = reverse('chat:conversation-detail', kwargs={'pk': conversation.id})
        data = {
            'title': 'Updated Conversation Title',
        }
        response = self.client.patch(url, data)

        # Assertions
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Updated Conversation Title')

    def test_delete_conversation(self):
        """Test deleting a conversation"""
        conversation = self.conversations[0]
        url = reverse('chat:conversation-detail', kwargs={'pk': conversation.id})
        
        # First, delete the conversation with permanent flag in query params
        response = self.client.delete(url + '?permanent=true')

        # Assertions
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify conversation was permanently deleted
        with self.assertRaises(Conversation.DoesNotExist):
            Conversation.objects.get(id=conversation.id)

    def test_conversation_permissions(self):
        """Test that users can only access their own conversations"""
        # Attempt to access conversation of another user
        conversation = Conversation.objects.create(
            user=self.user2,
            title='User2 Conversation'
        )
        url = reverse('chat:conversation-detail', kwargs={'pk': conversation.id})
        response = self.client.get(url)

        # Assertions
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_conversation_with_messages(self):
        """Test retrieving a conversation with its messages"""
        conversation = self.conversations[0]
        url = reverse('chat:conversation-detail', kwargs={'pk': conversation.id})
        response = self.client.get(url, {'include_messages': 'true'})

        # Assertions
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('messages', response.data)
        self.assertTrue(len(response.data['messages']) > 0)