from django.test import TestCase
"""
Tests for performance monitoring and error recovery systems.
"""
import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
from django.contrib.auth import get_user_model

from chat.services.performance_monitor import performance_monitor, PerformanceMonitor
from chat.services.cache_service import cache_service_instance, CacheService
from chat.services.error_recovery import error_recovery_manager, ErrorRecoveryManager
from chat.services.groq_service import CircuitBreaker
from chat.models_realtime import StreamingSession, PerformanceMetrics

User = get_user_model()


class PerformanceMonitorTestCase(TestCase):
    """Test cases for PerformanceMonitor service."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.session = StreamingSession.objects.create(
            user=self.user,
            session_id='test-session-123',
            websocket_id='ws-123'
        )
        self.monitor = PerformanceMonitor()
    
    def test_timer_functionality(self):
        """Test timer start/end functionality."""
        request_id = 'test-request-123'
        stage = 'test_stage'
        
        # Start timer
        self.monitor.start_timer(request_id, stage)
        
        # Simulate some processing time
        import time
        time.sleep(0.01)  # 10ms
        
        # End timer
        duration = self.monitor.end_timer(request_id, stage)
        
        # Verify duration is reasonable (should be around 10ms)
        self.assertIsNotNone(duration)
        self.assertGreater(duration, 5)  # At least 5ms
        self.assertLess(duration, 50)    # Less than 50ms
    
    def test_performance_metrics_recording(self):
        """Test recording performance metrics."""
        request_id = 'test-request-456'
        
        # Record a metric
        self.monitor.record_metric(
            request_id, self.session, self.user, 
            'total_response_time', 250.0
        )
        
        # Verify metric was recorded in session
        self.session.refresh_from_db()
        metrics = self.session.performance_metrics.get('metrics', [])
        self.assertTrue(len(metrics) > 0)
        
        # Find our metric
        our_metric = next((m for m in metrics if m['name'] == 'total_response_time'), None)
        self.assertIsNotNone(our_metric)
        self.assertEqual(our_metric['value'], 250.0)
    
    def test_performance_summary(self):
        """Test performance summary generation."""
        # Create some test metrics
        PerformanceMetrics.objects.create(
            session=self.session,
            user=self.user,
            request_id='req-1',
            total_response_time=300.0,
            llm_first_token_time=150.0
        )
        
        PerformanceMetrics.objects.create(
            session=self.session,
            user=self.user,
            request_id='req-2',
            total_response_time=400.0,
            llm_first_token_time=200.0
        )
        
        # Get summary
        summary = self.monitor.get_performance_summary(days=1, user=self.user)
        
        # Verify summary contains expected data
        self.assertIn('avg_total_response_time', summary)
        self.assertIn('total_requests', summary)
        self.assertEqual(summary['total_requests'], 2)
        self.assertEqual(summary['avg_total_response_time'], 350.0)  # (300 + 400) / 2
    
    def test_bottleneck_detection(self):
        """Test bottleneck detection."""
        # Create metrics with one slow component
        PerformanceMetrics.objects.create(
            session=self.session,
            user=self.user,
            request_id='req-1',
            total_response_time=500.0,
            llm_first_token_time=400.0,  # Very slow
            audio_processing_time=50.0
        )
        
        # Get bottlenecks
        bottlenecks = self.monitor.get_bottlenecks(days=1)
        
        # Should identify LLM first token as a bottleneck
        llm_bottleneck = next((b for b in bottlenecks if b['metric'] == 'llm_first_token_time'), None)
        self.assertIsNotNone(llm_bottleneck)
        self.assertEqual(llm_bottleneck['severity'], 'high')  # 400ms > 200ms target


class CacheServiceTestCase(TestCase):
    """Test cases for CacheService."""
    
    def setUp(self):
        self.cache_service = CacheService()
        self.user_id = 123
    
    def test_user_profile_caching(self):
        """Test user profile caching."""
        profile_data = {
            'name': 'Test User',
            'preferences': {'theme': 'dark'},
            'relationship_level': 2
        }
        
        # Cache profile
        self.cache_service.cache_user_profile(self.user_id, profile_data)
        
        # Retrieve profile
        cached_profile = self.cache_service.get_cached_user_profile(self.user_id)
        
        # Verify data matches
        self.assertEqual(cached_profile, profile_data)
    
    def test_emotion_context_caching(self):
        """Test emotion context caching."""
        session_id = 'session-123'
        emotion_data = {
            'primary_emotion': 'joy',
            'emotions': [{'name': 'joy', 'score': 0.8}],
            'confidence_score': 0.9
        }
        
        # Cache emotion context
        self.cache_service.cache_emotion_context(self.user_id, session_id, emotion_data)
        
        # Retrieve emotion context
        cached_emotion = self.cache_service.get_cached_emotion_context(self.user_id, session_id)
        
        # Verify data matches
        self.assertEqual(cached_emotion, emotion_data)
    
    def test_query_response_caching(self):
        """Test query-response caching."""
        query = "What is the weather like?"
        response = "I don't have access to current weather data."
        
        # Cache query-response pair
        self.cache_service.cache_query_response(self.user_id, query, response)
        
        # Retrieve cached response
        cached_response = self.cache_service.get_cached_query_response(self.user_id, query)
        
        # Verify response matches
        self.assertEqual(cached_response, response)
    
    def test_text_hashing(self):
        """Test text hashing for cache keys."""
        text1 = "Hello world"
        text2 = "Hello world"
        text3 = "Hello World"  # Different case
        
        hash1 = self.cache_service.compute_text_hash(text1)
        hash2 = self.cache_service.compute_text_hash(text2)
        hash3 = self.cache_service.compute_text_hash(text3)
        
        # Same text should produce same hash
        self.assertEqual(hash1, hash2)
        
        # Different text should produce different hash
        self.assertNotEqual(hash1, hash3)


class CircuitBreakerTestCase(TestCase):
    """Test cases for CircuitBreaker functionality."""
    
    def setUp(self):
        self.circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=1)
    
    def test_circuit_breaker_states(self):
        """Test circuit breaker state transitions."""
        # Initially closed
        self.assertEqual(self.circuit_breaker.state, 'closed')
        self.assertTrue(self.circuit_breaker.can_execute())
        
        # Record failures until threshold
        for i in range(3):
            self.circuit_breaker.record_failure()
        
        # Should be open now
        self.assertEqual(self.circuit_breaker.state, 'open')
        self.assertFalse(self.circuit_breaker.can_execute())
        
        # Wait for recovery timeout
        import time
        time.sleep(1.1)  # Wait slightly longer than recovery timeout
        
        # Call can_execute to trigger state change to half-open
        can_execute = self.circuit_breaker.can_execute()
        self.assertEqual(self.circuit_breaker.state, 'half-open')
        self.assertTrue(can_execute)
        
        # Record success
        self.circuit_breaker.record_success()
        self.assertEqual(self.circuit_breaker.state, 'closed')
    
    def test_circuit_breaker_failure_threshold(self):
        """Test failure threshold behavior."""
        # Record failures below threshold
        for i in range(2):
            self.circuit_breaker.record_failure()
        
        # Should still be closed
        self.assertEqual(self.circuit_breaker.state, 'closed')
        self.assertTrue(self.circuit_breaker.can_execute())
        
        # Record one more failure to reach threshold
        self.circuit_breaker.record_failure()
        
        # Should be open now
        self.assertEqual(self.circuit_breaker.state, 'open')
        self.assertFalse(self.circuit_breaker.can_execute())


class ErrorRecoveryManagerTestCase(TestCase):
    """Test cases for ErrorRecoveryManager functionality."""
    
    def setUp(self):
        self.error_manager = ErrorRecoveryManager()
    
    def test_service_health_tracking(self):
        """Test service health tracking."""
        # Record some errors
        self.error_manager.record_error('groq', 'timeout', 'Request timed out')
        self.error_manager.record_error('groq', 'api_failure', 'API key invalid')
        
        # Check error stats
        stats = self.error_manager.get_error_stats('groq')
        self.assertIn('error_counts', stats)
        self.assertIn('last_error', stats)
        self.assertEqual(stats['error_counts']['timeout'], 1)
        self.assertEqual(stats['error_counts']['api_failure'], 1)
        
        # Check if fallback should be used
        self.assertFalse(self.error_manager.should_use_fallback('groq', error_threshold=5))
        self.assertTrue(self.error_manager.should_use_fallback('groq', error_threshold=2))
    
    @pytest.mark.asyncio
    async def test_retry_with_backoff(self):
        """Test retry mechanism with exponential backoff."""
        # This test would need to be implemented if retry functionality is added
        # For now, just test that the error manager works
        self.error_manager.record_error('test_service', 'api_failure', 'Test error')
        stats = self.error_manager.get_error_stats('test_service')
        self.assertEqual(stats['error_counts']['api_failure'], 1)
    
    @pytest.mark.asyncio
    async def test_fallback_response_generation(self):
        """Test fallback response generation."""
        # Test the global fallback response function
        from chat.services.error_recovery import get_fallback_response
        
        fallback = get_fallback_response('api_failure')
        self.assertIsInstance(fallback, str)
        self.assertGreater(len(fallback), 0)
        
        # Test with context
        context = {'user_id': 'test_user'}
        fallback_with_context = get_fallback_response('timeout', context)
        self.assertIsInstance(fallback_with_context, str)
    
    def test_circuit_breaker_integration(self):
        """Test circuit breaker integration."""
        # Create a circuit breaker
        circuit_breaker = CircuitBreaker(failure_threshold=2, recovery_timeout=1)
        
        # Test integration with error manager
        self.error_manager.record_error('groq', 'api_failure', 'Test error')
        
        # Circuit breaker should allow execution initially
        self.assertTrue(circuit_breaker.can_execute())
        
        # Record failures
        circuit_breaker.record_failure()
        circuit_breaker.record_failure()
        
        # Circuit breaker should be open
        self.assertFalse(circuit_breaker.can_execute())


# Integration test to verify everything works together
class IntegrationTestCase(TestCase):
    """Integration tests for performance monitoring and error recovery."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='integrationuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.session = StreamingSession.objects.create(
            user=self.user,
            session_id='integration-session',
            websocket_id='ws-integration'
        )
    
    def test_performance_monitoring_integration(self):
        """Test complete performance monitoring flow."""
        request_id = 'integration-test-123'
        
        # Start timing various stages
        performance_monitor.start_timer(request_id, 'total_response_time')
        performance_monitor.start_timer(request_id, 'llm_first_token_time')
        
        # Simulate some processing
        import time
        time.sleep(0.01)
        
        # End timers
        llm_time = performance_monitor.end_timer(request_id, 'llm_first_token_time')
        total_time = performance_monitor.end_timer(request_id, 'total_response_time')
        
        # Record metrics
        performance_monitor.record_metric(
            request_id, self.session, self.user,
            'total_response_time', total_time
        )
        
        # Verify metrics were recorded
        self.session.refresh_from_db()
        metrics = self.session.performance_metrics.get('metrics', [])
        self.assertTrue(len(metrics) > 0)
    
    def test_cache_and_performance_integration(self):
        """Test caching integration with performance monitoring."""
        # Cache a query response
        query = "Test integration query"
        response = "Test integration response"
        
        cache_service_instance.cache_query_response(
            self.user.id, query, response
        )
        
        # Retrieve cached response
        cached = cache_service_instance.get_cached_query_response(
            self.user.id, query
        )
        
        # Should match original
        self.assertEqual(cached, response)
        
        # Cache user profile
        profile = {'name': 'Integration User', 'level': 1}
        cache_service_instance.cache_user_profile(self.user.id, profile)
        
        # Retrieve cached profile
        cached_profile = cache_service_instance.get_cached_user_profile(self.user.id)
        self.assertEqual(cached_profile, profile)