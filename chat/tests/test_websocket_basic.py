from django.test import TestCase
from django.test import TestCase
"""
Basic test for WebSocket connection.
"""
import json
import uuid
import asyncio
from channels.testing import WebsocketCommunicator
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import AccessToken
from channels.routing import URLRouter
from django.urls import re_path

from chat.consumers import ChatConsumer

User = get_user_model()


class WebSocketBasicTest(TransactionTestCase):
    """Basic test for WebSocket connection."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username=f"testuser_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            password="testpassword"
        )
        self.token = str(AccessToken.for_user(self.user))
        
        # Create application with direct routing to consumer
        self.application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
        ])
    
    def test_websocket_connection(self):
        """Test basic WebSocket connection."""
        async def run_test():
            # Connect to WebSocket
            communicator = WebsocketCommunicator(
                application=self.application,
                path="/ws/chat/"
            )
            
            # Add user to scope directly (bypass middleware)
            communicator.scope["user"] = self.user
            
            connected, _ = await communicator.connect()
            
            # Check if connection was successful
            self.assertTrue(connected, "WebSocket connection failed")
            
            # Receive connection established message
            response = await communicator.receive_json_from()
            self.assertEqual(response['type'], 'connection_established')
            
            # Disconnect
            await communicator.disconnect()
        
        # Run the async test
        asyncio.run(run_test())