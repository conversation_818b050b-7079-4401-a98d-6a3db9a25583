from django.test import TestCase
from django.test import TestCase
"""
Tests for the Chat Stats and Search API endpoints.
"""
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from chat.models import Conversation, Message

User = get_user_model()


class StatsAndSearchAPITestCase(TestCase):
    """Test case for Chat Stats and Search API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create conversations
        self.conversation1 = Conversation.objects.create(
            user=self.user,
            title='Test Conversation 1'
        )
        
        self.conversation2 = Conversation.objects.create(
            user=self.user,
            title='Test Conversation 2'
        )
        
        self.archived_conversation = Conversation.objects.create(
            user=self.user,
            title='Archived Conversation',
            is_archived=True
        )
        
        # Create messages
        self.user_message1 = Message.objects.create(
            conversation=self.conversation1,
            sender_type='user',
            message_type='text',
            content='Hello, this is a test message'
        )
        
        self.assistant_message1 = Message.objects.create(
            conversation=self.conversation1,
            sender_type='assistant',
            message_type='text',
            content='Hello, I am the assistant'
        )
        
        self.user_message2 = Message.objects.create(
            conversation=self.conversation2,
            sender_type='user',
            message_type='text',
            content='Another test message with keyword'
        )
        
        self.assistant_message2 = Message.objects.create(
            conversation=self.conversation2,
            sender_type='assistant',
            message_type='text',
            content='I can help you with your keyword search'
        )
        
        # Authenticate client
        self.client.force_authenticate(user=self.user)
    
    def test_chat_stats(self):
        """Test getting chat statistics."""
        url = reverse('chat:chat-stats')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that the response contains the expected keys
        self.assertIn('total_conversations', response.data)
        self.assertIn('active_conversations', response.data)
        self.assertIn('total_messages', response.data)
        self.assertIn('user_messages', response.data)
        self.assertIn('ai_messages', response.data)
        self.assertIn('recent_conversations', response.data)
        
        # Check that the counts are reasonable
        self.assertTrue(response.data['total_conversations'] >= 3)
        self.assertTrue(response.data['active_conversations'] >= 2)
        self.assertTrue(response.data['total_messages'] >= 4)
        self.assertTrue(response.data['user_messages'] >= 2)
        self.assertTrue(response.data['ai_messages'] >= 2)
        self.assertTrue(len(response.data['recent_conversations']) > 0)
    
    def test_search_conversations(self):
        """Test searching conversations."""
        url = reverse('chat:message-search')
        response = self.client.get(url, {'q': 'test'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['conversations']), 2)  # Both non-archived conversations
        self.assertEqual(len(response.data['messages']), 2)  # Two messages with 'test'
    
    def test_search_with_keyword(self):
        """Test searching with specific keyword."""
        url = reverse('chat:message-search')
        response = self.client.get(url, {'q': 'keyword'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['conversations']), 1)  # Only conversation2
        self.assertEqual(len(response.data['messages']), 2)  # Both messages in conversation2
    
    def test_search_with_archived(self):
        """Test searching including archived conversations."""
        url = reverse('chat:message-search')
        response = self.client.get(url, {'q': 'Archived', 'include_archived': 'true'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['conversations']), 1)
        self.assertEqual(response.data['conversations'][0]['title'], 'Archived Conversation')
    
    def test_search_without_query(self):
        """Test search without query parameter."""
        url = reverse('chat:message-search')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_unauthenticated_access(self):
        """Test unauthenticated access to stats and search."""
        self.client.logout()
        
        # Test stats
        url = reverse('chat:chat-stats')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Test search
        url = reverse('chat:message-search')
        response = self.client.get(url, {'q': 'test'})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)