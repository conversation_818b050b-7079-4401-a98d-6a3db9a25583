from django.test import TestCase
from django.test import TestCase
"""
Tests for WebSocket connection management functionality.
"""
import json
import uuid
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.urls import re_path
from channels.routing import URLRouter
from asgiref.sync import sync_to_async

from chat.consumers import ChatConsumer
from chat.models import Conversation, Message, ChatSession
from chat.models_realtime import StreamingSession

User = get_user_model()


class WebSocketConnectionManagementTest(TransactionTestCase):
    """Test WebSocket connection management functionality."""
    
    def setUp(self):
        """Set up test data."""
        # Create unique username to avoid conflicts
        unique_username = f"testuser_{uuid.uuid4().hex[:8]}"
        self.user = User.objects.create_user(
            username=unique_username,
            email=f"{unique_username}@example.com",
            password="testpassword"
        )
        
        # Create application with direct routing to consumer
        self.application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
            re_path(r"^ws/chat/(?P<conversation_id>[^/]+)/$", ChatConsumer.as_asgi()),
        ])
    
    def test_connection_session_creation(self):
        """Test session creation on connection."""
        async def run_test():
            # Connect to WebSocket
            communicator = WebsocketCommunicator(
                application=self.application,
                path="/ws/chat/"
            )
            
            # Add user to scope directly (bypass middleware)
            communicator.scope["user"] = self.user
            
            # Connect
            connected, _ = await communicator.connect()
            self.assertTrue(connected)
            
            # Receive connection established message
            response = await communicator.receive_json_from()
            self.assertEqual(response['type'], 'connection_established')
            
            # Verify chat session was created
            chat_sessions = await sync_to_async(list)(
                ChatSession.objects.filter(user=self.user)
            )
            self.assertEqual(len(chat_sessions), 1)
            
            # Verify streaming session was created
            streaming_sessions = await sync_to_async(list)(
                StreamingSession.objects.filter(user=self.user)
            )
            self.assertEqual(len(streaming_sessions), 1)
            
            # Disconnect
            await communicator.disconnect()
            
            # Verify streaming session was updated
            streaming_session = await sync_to_async(StreamingSession.objects.get)(
                id=streaming_sessions[0].id
            )
            self.assertIsNotNone(streaming_session.ended_at)
        
        asyncio.run(run_test())
    
    def test_reconnection_handling(self):
        """Test reconnection handling."""
        async def run_test():
            # First connection
            communicator1 = WebsocketCommunicator(
                application=self.application,
                path="/ws/chat/"
            )
            communicator1.scope["user"] = self.user
            connected, _ = await communicator1.connect()
            self.assertTrue(connected)
            
            # Receive connection established message
            response1 = await communicator1.receive_json_from()
            self.assertEqual(response1['type'], 'connection_established')
            
            # Get session ID
            session_id = response1['session_id']
            
            # Disconnect
            await communicator1.disconnect()
            
            # Reconnect with previous session ID
            communicator2 = WebsocketCommunicator(
                application=self.application,
                path="/ws/chat/"
            )
            communicator2.scope["user"] = self.user
            connected, _ = await communicator2.connect()
            self.assertTrue(connected)
            
            # Receive connection established message
            response2 = await communicator2.receive_json_from()
            self.assertEqual(response2['type'], 'connection_established')
            
            # Send reconnection request
            await communicator2.send_json_to({
                'type': 'reconnection_request',
                'previous_session_id': session_id,
                'timestamp': timezone.now().timestamp() * 1000
            })
            
            # Receive reconnection response
            response3 = await communicator2.receive_json_from()
            self.assertEqual(response3['type'], 'reconnection_accepted')
            
            # Disconnect
            await communicator2.disconnect()
        
        asyncio.run(run_test())
    
    def test_conversation_switching(self):
        """Test conversation switching."""
        async def run_test():
            # Create two conversations
            conversation1 = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Conversation 1"
            )
            
            conversation2 = await sync_to_async(Conversation.objects.create)(
                user=self.user,
                title="Conversation 2"
            )
            
            # Connect to WebSocket
            communicator = WebsocketCommunicator(
                application=self.application,
                path="/ws/chat/"
            )
            communicator.scope["user"] = self.user
            connected, _ = await communicator.connect()
            self.assertTrue(connected)
            
            # Receive connection established message
            await communicator.receive_json_from()
            
            # Switch to conversation 1
            await communicator.send_json_to({
                'type': 'conversation_switch',
                'conversation_id': str(conversation1.id)
            })
            
            # Receive switch acknowledgment
            response1 = await communicator.receive_json_from()
            self.assertEqual(response1['type'], 'conversation_switched')
            self.assertEqual(response1['conversation_id'], str(conversation1.id))
            
            # Switch to conversation 2
            await communicator.send_json_to({
                'type': 'conversation_switch',
                'conversation_id': str(conversation2.id)
            })
            
            # Receive switch acknowledgment
            response2 = await communicator.receive_json_from()
            self.assertEqual(response2['type'], 'conversation_switched')
            self.assertEqual(response2['conversation_id'], str(conversation2.id))
            
            # Disconnect
            await communicator.disconnect()
        
        asyncio.run(run_test())