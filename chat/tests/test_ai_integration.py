from django.test import TestCase
"""
Tests for AI integration in the WebSocket consumer.
"""
import json
import uuid
import asyncio
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from channels.testing import WebsocketCommunicator
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.urls import re_path
from channels.routing import URLRouter
from asgiref.sync import sync_to_async
from rest_framework_simplejwt.tokens import RefreshToken

from chat.models import Conversation, Message
from chat.consumers import ChatConsumer

User = get_user_model()


class AIIntegrationTestCase(TransactionTestCase):
    """Base test case for AI integration tests."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Generate JWT token
        refresh = RefreshToken.for_user(self.user)
        self.token = str(refresh.access_token)
        
        # Create application with direct routing to consumer
        self.application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
        ])
        
    async def create_communicator(self):
        """Create a WebSocket communicator."""
        # Use direct URLRouter like the working tests
        application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi())
        ])
        
        communicator = WebsocketCommunicator(
            application=application,
            path="/ws/chat/"
        )
        
        # Set the user and token in scope like the working test
        communicator.scope["user"] = self.user
        communicator.scope["headers"] = [
            (b"authorization", f"Bearer {self.token}".encode())
        ]
        
        return communicator
    
    async def create_conversation(self, user):
        """Create a test conversation."""
        return await sync_to_async(Conversation.objects.create)(
            user=user,
            title="Test Conversation",
            is_archived=False
        )
    
    async def connect_and_verify(self, communicator):
        """Connect to WebSocket and verify connection."""
        # Mock the necessary methods for connection
        with patch('chat.consumers.ChatConsumer.create_chat_session') as mock_create_chat_session, \
             patch('chat.consumers.ChatConsumer.create_streaming_session') as mock_create_streaming_session, \
             patch('chat.consumers.ChatConsumer.check_agents_availability') as mock_check_agents:
            
            # Configure mocks
            mock_chat_session = MagicMock()
            mock_chat_session.id = uuid.uuid4()
            mock_create_chat_session.return_value = mock_chat_session
            
            mock_streaming_session = MagicMock()
            mock_streaming_session.id = uuid.uuid4()
            mock_create_streaming_session.return_value = mock_streaming_session
            
            mock_check_agents.return_value = True
            
            connected, subprotocol = await communicator.connect()
            self.assertTrue(connected)
            
            # Receive connection established message
            response = await communicator.receive_json_from()
            self.assertEqual(response['type'], 'connection_established')
            return response


class AgentCoordinatorIntegrationTest(AIIntegrationTestCase):
    """Test integration with the agent coordinator."""
    
    def test_agent_coordinator_available(self):
        """Test when agent coordinator is available."""
        async def run_test():
            communicator = await self.create_communicator()
            connection_response = await self.connect_and_verify(communicator)
            
            # Mock agent coordinator
            mock_coordinator = Mock()
            mock_response_chunks = [
                {
                    'type': 'domain_classification',
                    'domain': 'general',
                    'confidence': 0.8,
                    'timestamp': timezone.now().isoformat()
                },
                {
                    'type': 'response_chunk',
                    'content': 'Hello! ',
                    'timestamp': timezone.now().isoformat()
                },
                {
                    'type': 'response_chunk',
                    'content': 'How can I help you today?',
                    'timestamp': timezone.now().isoformat()
                },
                {
                    'type': 'response_complete',
                    'full_content': 'Hello! How can I help you today?',
                    'domain': 'general',
                    'timestamp': timezone.now().isoformat()
                }
            ]
            
            async def mock_process_query(*args, **kwargs):
                for chunk in mock_response_chunks:
                    yield chunk
            
            mock_coordinator.process_user_query = mock_process_query
            
            with patch('chat.consumers.AGENTS_AVAILABLE', True), \
                 patch('chat.consumers.get_agent_coordinator', return_value=mock_coordinator):
                
                # Send text message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Hello, how are you?',
                    'conversation_id': None
                })
                
                # Collect responses
                responses = []
                while True:
                    try:
                        response = await asyncio.wait_for(
                            communicator.receive_json_from(), 
                            timeout=5.0
                        )
                        responses.append(response)
                        
                        if response.get('type') == 'ai_typing' and response.get('status') == 'stopped':
                            break
                    except asyncio.TimeoutError:
                        break
                
                # Verify we got the expected responses
                message_received = next(
                    (r for r in responses if r.get('type') == 'message_received'), 
                    None
                )
                self.assertIsNotNone(message_received)
                
                typing_started = next(
                    (r for r in responses if r.get('type') == 'ai_typing' and r.get('status') == 'started'), 
                    None
                )
                self.assertIsNotNone(typing_started)
                
                response_chunks = [
                    r for r in responses if r.get('type') == 'llm_response_chunk'
                ]
                self.assertGreater(len(response_chunks), 0)
                
                typing_stopped = next(
                    (r for r in responses if r.get('type') == 'ai_typing' and r.get('status') == 'stopped'), 
                    None
                )
                self.assertIsNotNone(typing_stopped)
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_agent_coordinator_error_handling(self):
        """Test error handling when agent coordinator fails."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await self.create_conversation(self.user)
            
            # Mock agent coordinator to raise an error
            with patch('chat.consumers.get_agent_coordinator') as mock_get_coordinator:
                mock_coordinator = Mock()
                mock_coordinator.process_user_query.side_effect = Exception("Agent processing failed")
                mock_get_coordinator.return_value = mock_coordinator
                
                # Send a message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Test message',
                    'conversation_id': str(conversation.id)
                })
                
                # Should receive message acknowledgment first
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                
                # Then receive typing indicator (started)
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'ai_typing')
                self.assertEqual(response['status'], 'started')
                
                # Then receive typing indicator (stopped due to error)
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'ai_typing')
                self.assertEqual(response['status'], 'stopped')
                
                # Then receive error response
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'error')
                self.assertIn('Failed to generate AI response', response['message'])
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class FallbackMechanismTest(AIIntegrationTestCase):
    """Test fallback mechanisms when AI services are unavailable."""
    
    def test_fallback_when_agents_unavailable(self):
        """Test fallback response when agents are not available."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await self.create_conversation(self.user)
            
            # Mock agents as unavailable
            with patch('chat.consumers.AGENTS_AVAILABLE', False):
                # Send a message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Test message',
                    'conversation_id': str(conversation.id)
                })
                
                # Should receive message acknowledgment first
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                
                # Then receive typing indicator
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'ai_typing')
                self.assertEqual(response['status'], 'started')
                
                # Collect response chunks
                chunks = []
                while True:
                    try:
                        response = await asyncio.wait_for(
                            communicator.receive_json_from(), 
                            timeout=5.0
                        )
                        
                        if response.get('type') == 'llm_response_chunk':
                            chunks.append(response)
                        
                        if response.get('type') == 'ai_typing' and response.get('status') == 'stopped':
                            break
                    except asyncio.TimeoutError:
                        break
                
                # Verify we received chunks
                self.assertGreater(len(chunks), 0)
                
                # Verify fallback content
                full_content = ' '.join([chunk.get('content', '') for chunk in chunks])
                # Check for any common word in fallback responses
                self.assertTrue(
                    any(word in full_content.lower() for word in ['trouble', 'issues', 'unavailable', 'sorry', 'apologize']),
                    f"Fallback response should contain common error words. Got: {full_content}"
                )
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_fallback_response_content(self):
        """Test that fallback responses are appropriate."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await self.create_conversation(self.user)
            
            # Mock agents as unavailable
            with patch('chat.consumers.AGENTS_AVAILABLE', False):
                
                # Send a message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Test message',
                    'conversation_id': str(conversation.id)
                })
                
                # Should receive message acknowledgment first
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                
                # Then receive typing indicator
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'ai_typing')
                self.assertEqual(response['status'], 'started')
                
                # Collect response chunks
                chunks = []
                while True:
                    try:
                        response = await asyncio.wait_for(
                            communicator.receive_json_from(), 
                            timeout=5.0
                        )
                        
                        if response.get('type') == 'llm_response_chunk':
                            chunks.append(response)
                        
                        if response.get('type') == 'ai_typing' and response.get('status') == 'stopped':
                            break
                    except asyncio.TimeoutError:
                        break
                
                # Verify we received chunks
                self.assertGreater(len(chunks), 0)
                
                # Verify fallback content
                full_content = ' '.join([chunk.get('content', '') for chunk in chunks])
                # Check for any common word in fallback responses
                self.assertTrue(
                    any(word in full_content.lower() for word in ['trouble', 'issues', 'unavailable', 'sorry', 'apologize']),
                    f"Fallback response should contain common error words. Got: {full_content}"
                )
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class MemoryIntegrationTest(AIIntegrationTestCase):
    """Test memory integration functionality."""
    
    def test_memory_context_retrieval(self):
        """Test memory context retrieval."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Create a conversation
            conversation = await self.create_conversation(self.user)
            
            # Mock memory manager
            with patch('chat.consumers.ChatConsumer.get_memory_context') as mock_get_memory_context:
                mock_get_memory_context.return_value = {
                    'memories': [{'content': 'Previous conversation about AI', 'relevance': 0.8}]
                }
                
                # Send a message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Tell me about AI',
                    'conversation_id': str(conversation.id)
                })
                
                # Wait for message acknowledgment
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'message_received')
                
                # Wait for typing indicator
                response = await communicator.receive_json_from()
                self.assertEqual(response['type'], 'ai_typing')
                self.assertEqual(response['status'], 'started')
                
                # Wait for AI response chunks or completion
                while True:
                    try:
                        response = await asyncio.wait_for(
                            communicator.receive_json_from(), 
                            timeout=5.0
                        )
                        
                        if response.get('type') == 'ai_typing' and response.get('status') == 'stopped':
                            break
                    except asyncio.TimeoutError:
                        break
                
                # Verify memory context was retrieved
                mock_get_memory_context.assert_called_once()
            
            await communicator.disconnect()
        
        asyncio.run(run_test())
    
    def test_memory_context_error_handling(self):
        """Test error handling in memory context retrieval."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            # Mock memory manager that raises an error
            mock_memory_manager = Mock()
            mock_memory_manager.async_search_memories = AsyncMock(
                side_effect=Exception("Memory service unavailable")
            )
            
            # Mock agent coordinator
            mock_coordinator = Mock()
            
            async def mock_process_query(*args, **kwargs):
                # Memory context should be None due to error
                memory_context = kwargs.get('memory_context')
                self.assertIsNone(memory_context)
                
                yield {
                    'type': 'response_complete',
                    'full_content': 'I can help you with that!',
                    'domain': 'general',
                    'timestamp': timezone.now().isoformat()
                }
            
            mock_coordinator.process_user_query = mock_process_query
            
            with patch('chat.consumers.AGENTS_AVAILABLE', True), \
                 patch('chat.consumers.get_agent_coordinator', return_value=mock_coordinator), \
                 patch('chat.consumers.get_memory_manager', return_value=mock_memory_manager):
                
                # Send text message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Tell me something',
                    'conversation_id': None
                })
                
                # Wait for processing
                await asyncio.sleep(0.5)
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class CacheIntegrationTest(AIIntegrationTestCase):
    """Test cache integration functionality."""
    
    def test_cached_response_handling(self):
        """Test handling of cached responses."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            cached_response = "This is a cached response for testing"
            
            with patch('chat.consumers.cache_service_instance') as mock_cache:
                mock_cache.get_cached_query_response.return_value = cached_response
                
                # Send text message
                await communicator.send_json_to({
                    'type': 'text_message',
                    'content': 'Hello',
                    'conversation_id': None
                })
                
                # Collect responses
                responses = []
                while True:
                    try:
                        response = await asyncio.wait_for(
                            communicator.receive_json_from(), 
                            timeout=5.0
                        )
                        responses.append(response)
                        
                        if response.get('type') == 'ai_typing' and response.get('status') == 'stopped':
                            break
                    except asyncio.TimeoutError:
                        break
                
                # Verify cached response was used
                response_chunks = [
                    r for r in responses if r.get('type') == 'llm_response_chunk'
                ]
                self.assertGreater(len(response_chunks), 0)
                
                # Check if any chunk is marked as cached
                cached_chunks = [
                    r for r in response_chunks if r.get('is_cached')
                ]
                self.assertGreater(len(cached_chunks), 0)
            
            await communicator.disconnect()
        
        asyncio.run(run_test())


class PerformanceMonitoringTest(AIIntegrationTestCase):
    """Test performance monitoring integration."""
    
    def test_performance_metrics_with_ai(self):
        """Test performance metrics recording with AI integration."""
        async def run_test():
            communicator = await self.create_communicator()
            await self.connect_and_verify(communicator)
            
            with patch('chat.consumers.performance_monitor') as mock_monitor:
                mock_monitor.start_timer = Mock()
                mock_monitor.end_timer = Mock(return_value=100.0)  # 100ms
                mock_monitor.record_metric = Mock()
                mock_monitor.record_error = Mock()
                
                # Mock agent coordinator
                mock_coordinator = Mock()
                
                async def mock_process_query(*args, **kwargs):
                    yield {
                        'type': 'response_complete',
                        'full_content': 'Test response',
                        'domain': 'general',
                        'timestamp': timezone.now().isoformat()
                    }
                
                mock_coordinator.process_user_query = mock_process_query
                
                with patch('chat.consumers.AGENTS_AVAILABLE', True), \
                     patch('chat.consumers.get_agent_coordinator', return_value=mock_coordinator):
                    
                    # Send text message
                    await communicator.send_json_to({
                        'type': 'text_message',
                        'content': 'Test performance monitoring',
                        'conversation_id': None
                    })
                    
                    # Wait for processing
                    await asyncio.sleep(0.5)
                    
                    # Verify performance monitoring was called
                    mock_monitor.start_timer.assert_called()
                    mock_monitor.end_timer.assert_called()
            
            await communicator.disconnect()
        
        asyncio.run(run_test())