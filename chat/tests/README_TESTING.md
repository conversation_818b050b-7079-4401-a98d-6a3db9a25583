# Chat Application Testing Guide

## Overview
This document provides comprehensive guidance for testing the chat application using Django's testing framework.

## Prerequisites
- Python 3.8+
- Django 3.2+
- Installed project dependencies (`requirements.txt`)

## Running Tests

### All Chat App Tests
To run all tests for the chat application:

```bash
python chat/tests/run_websocket_tests.py
```

### Test Categories
1. Agent Integration Tests
2. Conversation API Tests
3. Error Recovery Service Tests
4. WebSocket JWT Authentication Tests
5. WebSocket Realtime Messaging Tests
6. WebSocket Connection Management Tests

## Test Coverage

### Agent Integration
- Message processing
- Agent capability routing
- Conversation context maintenance
- Fallback mechanism
- Performance metrics

### WebSocket Tests
- Connection management
- Authentication
- Message routing
- Realtime messaging
- JWT token validation

### Error Recovery
- Error logging
- Message retry
- Circuit breaker functionality
- Bulk error recovery

## Best Practices
- Always run tests before committing changes
- Add new tests for new features
- Maintain high test coverage
- Keep tests independent and focused

## Troubleshooting
- Ensure all dependencies are installed
- Check Django settings
- Verify database configuration

## Continuous Integration
This test suite is designed to be integrated with CI/CD pipelines.

## Contributing
When adding new features:
1. Write comprehensive tests
2. Ensure all existing tests pass
3. Maintain or improve test coverage