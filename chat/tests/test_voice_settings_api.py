from django.test import TestCase
from django.test import TestCase
"""
Tests for the Voice Settings API endpoints.
"""
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from chat.models import VoiceSettings

User = get_user_model()


class VoiceSettingsAPITestCase(TestCase):
    """Test case for Voice Settings API endpoints."""
    
    def setUp(self):
        """Set up test data."""
        self.client = APIClient()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create voice settings
        self.voice_settings = VoiceSettings.objects.create(
            user=self.user,
            voice_model='tts-1',
            voice_name='alloy',
            speech_speed=1.2,
            language='en',
            auto_transcribe=True
        )
        
        # Authenticate client
        self.client.force_authenticate(user=self.user)
    
    def test_get_voice_settings(self):
        """Test getting user's voice settings."""
        url = reverse('chat:voice-settings')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['voice_model'], 'tts-1')
        self.assertEqual(response.data['voice_name'], 'alloy')
        self.assertEqual(response.data['speech_speed'], 1.2)
        self.assertEqual(response.data['language'], 'en')
        self.assertTrue(response.data['auto_transcribe'])
    
    def test_update_voice_settings(self):
        """Test updating voice settings."""
        url = reverse('chat:voice-settings')
        data = {
            'voice_name': 'nova',
            'speech_speed': 1.5,
            'auto_transcribe': False
        }
        response = self.client.patch(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['voice_name'], 'nova')
        self.assertEqual(response.data['speech_speed'], 1.5)
        self.assertFalse(response.data['auto_transcribe'])
        
        # Verify settings were updated in database
        self.voice_settings.refresh_from_db()
        self.assertEqual(self.voice_settings.voice_name, 'nova')
        self.assertEqual(self.voice_settings.speech_speed, 1.5)
        self.assertFalse(self.voice_settings.auto_transcribe)
    
    def test_invalid_speech_speed(self):
        """Test validation for speech speed."""
        url = reverse('chat:voice-settings')
        data = {'speech_speed': 5.0}  # Too high
        response = self.client.patch(url, data)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        # Check that there's an error in the response
        self.assertTrue('error' in response.data or 'speech_speed' in response.data or 'details' in response.data)
        
        # Verify settings were not updated
        self.voice_settings.refresh_from_db()
        self.assertEqual(self.voice_settings.speech_speed, 1.2)
    
    def test_voice_settings_creation_on_first_access(self):
        """Test voice settings are created on first access if they don't exist."""
        # Delete existing voice settings
        self.voice_settings.delete()
        
        url = reverse('chat:voice-settings')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['voice_model'], 'tts-1')  # Default value
        self.assertEqual(response.data['voice_name'], 'nova')    # Default value
        
        # Verify voice settings were created
        self.assertTrue(VoiceSettings.objects.filter(user=self.user).exists())
    
    def test_unauthenticated_access(self):
        """Test unauthenticated access to voice settings."""
        self.client.logout()
        url = reverse('chat:voice-settings')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)