"""
Conversation endpoint aliases for frontend compatibility.

These URLs provide the exact conversation endpoints documented in BACKEND_INTEGRATION_READINESS.md
while redirecting to the actual implementation in the chat app.
"""

from django.urls import path
from . import views

app_name = 'conversations'

urlpatterns = [
    # Conversation endpoints (as documented in BACKEND_INTEGRATION_READINESS.md)
    path('conversations/', views.ConversationListCreateView.as_view(), name='conversation-list'),
    path('conversations/<uuid:pk>/', views.ConversationDetailView.as_view(), name='conversation-detail'),
    path('conversations/<uuid:conversation_id>/messages/', views.ConversationMessagesView.as_view(), name='conversation-messages'),
]
