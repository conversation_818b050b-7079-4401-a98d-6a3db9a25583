import logging
from urllib.parse import parse_qs
from channels.middleware import BaseMiddleware
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from django.conf import settings
from rest_framework_simplejwt.tokens import AccessToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.contrib.auth import get_user_model

User = get_user_model()
logger = logging.getLogger(__name__)

class JWTAuthMiddleware(BaseMiddleware):
    """
    Custom middleware for JWT authentication in WebSocket connections.
    
    This middleware extracts the JWT token from the WebSocket connection's query parameters
    and authenticates the user based on the token.
    """
    
    async def __call__(self, scope, receive, send):
        # Extract token from query string
        query_params = parse_qs(scope["query_string"].decode())
        token = query_params.get("token", [None])[0]

        # Set default user as AnonymousUser
        scope["user"] = AnonymousUser()

        if token:
            # Authenticate with token
            user = await self.get_user_from_token(token)
            if user:
                scope["user"] = user
                logger.info(f"WebSocket authenticated for user: {user.email}")
            else:
                logger.warning("Invalid token provided for WebSocket connection")
        else:
            logger.warning("No token provided for WebSocket connection")

        return await super().__call__(scope, receive, send)
    
    @database_sync_to_async
    def get_user_from_token(self, token):
        """
        Get user from JWT token.

        Args:
            token (str): JWT token

        Returns:
            User or None: User object if token is valid, None otherwise
        """
        try:
            # Check for dummy token for testing (only in debug mode)
            if token == "dummy_token_for_testing" and getattr(settings, 'ENABLE_DUMMY_TOKEN', False):
                logger.info("Using dummy token for testing - creating/getting test user")
                # Get or create a test user for development/testing
                test_user, created = User.objects.get_or_create(
                    email='<EMAIL>',
                    defaults={
                        'username': 'testuser',
                        'first_name': 'Test',
                        'last_name': 'User',
                        'ai_companion_name': 'Ella'
                    }
                )
                if created:
                    logger.info("Created new test user for dummy token")
                return test_user

            # Validate real JWT token
            access_token = AccessToken(token)
            user_id = access_token.payload.get("user_id")

            # Get user from database
            if user_id:
                try:
                    return User.objects.get(id=user_id)
                except User.DoesNotExist:
                    logger.warning(f"User with ID {user_id} from token does not exist")
                    return None
            else:
                logger.warning("Token payload does not contain user_id")
                return None

        except (InvalidToken, TokenError) as e:
            logger.warning(f"Invalid token: {e}")
            # Log more details about token expiration for debugging
            if "expired" in str(e).lower():
                logger.warning(f"Token expired for WebSocket connection. Token: {token[:20]}...")
            return None
        except Exception as e:
            logger.error(f"Error authenticating WebSocket connection: {e}")
            return None