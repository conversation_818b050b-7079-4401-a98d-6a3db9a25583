# Generated by Django 4.2.7 on 2025-07-19 07:22

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("chat", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "relationship_level",
                    models.IntegerField(
                        choices=[
                            (1, "Acquaintance - Basic conversation, general topics"),
                            (2, "Friend - Personal topics, mild flirtation allowed"),
                            (
                                3,
                                "Close Friend - Intimate conversation, emotional support",
                            ),
                            (4, "Intimate - Adult content with explicit consent"),
                        ],
                        default=1,
                        help_text="Current relationship level (1-4)",
                    ),
                ),
                (
                    "total_interactions",
                    models.IntegerField(
                        default=0,
                        help_text="Total number of interactions with AI companion",
                    ),
                ),
                (
                    "total_conversation_time",
                    models.DurationField(
                        default=datetime.timedelta(0),
                        help_text="Total time spent in conversations",
                    ),
                ),
                (
                    "emotional_intimacy_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Calculated emotional intimacy score (0.0-1.0)",
                    ),
                ),
                (
                    "average_emotion_intensity",
                    models.FloatField(
                        default=0.0,
                        help_text="Average intensity of emotions expressed (0.0-1.0)",
                    ),
                ),
                (
                    "progression_milestones",
                    models.JSONField(
                        default=dict,
                        help_text="Milestones achieved in relationship progression",
                    ),
                ),
                (
                    "last_milestone_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Date of last milestone achievement",
                        null=True,
                    ),
                ),
                (
                    "user_consent_flags",
                    models.JSONField(
                        default=dict,
                        help_text="User consent for different types of content",
                    ),
                ),
                (
                    "explicit_progression_request",
                    models.BooleanField(
                        default=False,
                        help_text="User has explicitly requested relationship progression",
                    ),
                ),
                ("relationship_started", models.DateTimeField(auto_now_add=True)),
                ("last_interaction", models.DateTimeField(auto_now=True)),
                ("last_level_change", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ai_relationship",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User AI Relationship",
                "verbose_name_plural": "User AI Relationships",
                "db_table": "chat_user_relationships",
            },
        ),
        migrations.CreateModel(
            name="StreamingSession",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "session_id",
                    models.CharField(
                        help_text="Unique session identifier",
                        max_length=255,
                        unique=True,
                    ),
                ),
                (
                    "websocket_id",
                    models.CharField(
                        help_text="WebSocket connection identifier", max_length=255
                    ),
                ),
                (
                    "session_type",
                    models.CharField(
                        choices=[
                            ("voice", "Voice conversation"),
                            ("text", "Text conversation"),
                            ("mixed", "Mixed voice and text"),
                        ],
                        default="mixed",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("connecting", "Connecting"),
                            ("active", "Active"),
                            ("idle", "Idle"),
                            ("disconnected", "Disconnected"),
                            ("error", "Error"),
                        ],
                        default="connecting",
                        max_length=20,
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                (
                    "device_info",
                    models.JSONField(
                        default=dict, help_text="Device and browser information"
                    ),
                ),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("last_activity", models.DateTimeField(auto_now=True)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "performance_metrics",
                    models.JSONField(
                        default=dict,
                        help_text="Real-time performance metrics for this session",
                    ),
                ),
                ("audio_enabled", models.BooleanField(default=False)),
                (
                    "voice_settings",
                    models.JSONField(
                        default=dict, help_text="Voice settings for this session"
                    ),
                ),
                ("error_count", models.IntegerField(default=0)),
                ("last_error", models.TextField(blank=True)),
                ("last_error_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="streaming_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "chat_streaming_sessions",
                "ordering": ["-started_at"],
            },
        ),
        migrations.CreateModel(
            name="PerformanceMetrics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "request_id",
                    models.CharField(
                        help_text="Unique identifier for this request", max_length=255
                    ),
                ),
                (
                    "audio_processing_time",
                    models.FloatField(
                        blank=True,
                        help_text="Time to process audio input (ms)",
                        null=True,
                    ),
                ),
                (
                    "emotion_detection_time",
                    models.FloatField(
                        blank=True,
                        help_text="Time for emotion detection (ms)",
                        null=True,
                    ),
                ),
                (
                    "transcription_time",
                    models.FloatField(
                        blank=True, help_text="Time for speech-to-text (ms)", null=True
                    ),
                ),
                (
                    "memory_retrieval_time",
                    models.FloatField(
                        blank=True,
                        help_text="Time to retrieve relevant memories (ms)",
                        null=True,
                    ),
                ),
                (
                    "llm_first_token_time",
                    models.FloatField(
                        blank=True, help_text="Time to first LLM token (ms)", null=True
                    ),
                ),
                (
                    "llm_total_time",
                    models.FloatField(
                        blank=True,
                        help_text="Total LLM processing time (ms)",
                        null=True,
                    ),
                ),
                (
                    "tts_first_chunk_time",
                    models.FloatField(
                        blank=True,
                        help_text="Time to first TTS audio chunk (ms)",
                        null=True,
                    ),
                ),
                (
                    "tts_total_time",
                    models.FloatField(
                        blank=True,
                        help_text="Total TTS processing time (ms)",
                        null=True,
                    ),
                ),
                (
                    "total_response_time",
                    models.FloatField(help_text="Total end-to-end response time (ms)"),
                ),
                (
                    "audio_quality_score",
                    models.FloatField(
                        blank=True,
                        help_text="Audio input quality score (0.0-1.0)",
                        null=True,
                    ),
                ),
                (
                    "transcription_confidence",
                    models.FloatField(
                        blank=True,
                        help_text="Transcription confidence score (0.0-1.0)",
                        null=True,
                    ),
                ),
                (
                    "emotion_confidence",
                    models.FloatField(
                        blank=True,
                        help_text="Emotion detection confidence (0.0-1.0)",
                        null=True,
                    ),
                ),
                (
                    "user_satisfaction_score",
                    models.FloatField(
                        blank=True,
                        help_text="User satisfaction rating (1.0-5.0)",
                        null=True,
                    ),
                ),
                (
                    "user_feedback",
                    models.TextField(blank=True, help_text="Optional user feedback"),
                ),
                (
                    "server_load",
                    models.FloatField(
                        blank=True,
                        help_text="Server load at time of request",
                        null=True,
                    ),
                ),
                (
                    "concurrent_users",
                    models.IntegerField(
                        blank=True, help_text="Number of concurrent users", null=True
                    ),
                ),
                (
                    "groq_tokens_used",
                    models.IntegerField(
                        blank=True, help_text="Tokens used in Groq API call", null=True
                    ),
                ),
                (
                    "hume_api_calls",
                    models.IntegerField(
                        default=0, help_text="Number of Hume API calls made"
                    ),
                ),
                ("had_errors", models.BooleanField(default=False)),
                (
                    "error_details",
                    models.JSONField(
                        default=dict, help_text="Details of any errors encountered"
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="detailed_metrics",
                        to="chat.streamingsession",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_metrics",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "chat_performance_metrics",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="EmotionContext",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "session_id",
                    models.CharField(help_text="WebSocket session ID", max_length=255),
                ),
                (
                    "emotion_source",
                    models.CharField(
                        choices=[
                            ("audio", "Audio emotion detection"),
                            ("text", "Text sentiment analysis"),
                            ("combined", "Combined audio and text analysis"),
                            ("historical", "Historical emotion pattern"),
                        ],
                        default="combined",
                        max_length=20,
                    ),
                ),
                (
                    "audio_emotions",
                    models.JSONField(
                        default=dict, help_text="Raw emotion scores from audio analysis"
                    ),
                ),
                (
                    "text_emotions",
                    models.JSONField(
                        default=dict, help_text="Raw emotion scores from text analysis"
                    ),
                ),
                (
                    "primary_emotion",
                    models.CharField(
                        help_text="Primary detected emotion", max_length=50
                    ),
                ),
                (
                    "emotion_intensity",
                    models.FloatField(
                        default=0.0, help_text="Overall emotion intensity (0.0-1.0)"
                    ),
                ),
                (
                    "emotion_valence",
                    models.FloatField(
                        default=0.0,
                        help_text="Emotion valence: negative (-1.0) to positive (1.0)",
                    ),
                ),
                (
                    "emotion_arousal",
                    models.FloatField(
                        default=0.0,
                        help_text="Emotion arousal: calm (0.0) to excited (1.0)",
                    ),
                ),
                (
                    "context_emotions",
                    models.JSONField(
                        default=dict, help_text="Processed emotions for AI context"
                    ),
                ),
                (
                    "confidence_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Confidence in emotion detection (0.0-1.0)",
                    ),
                ),
                (
                    "audio_quality_score",
                    models.FloatField(
                        blank=True,
                        help_text="Audio quality score for emotion detection",
                        null=True,
                    ),
                ),
                (
                    "processing_time_ms",
                    models.IntegerField(
                        default=0,
                        help_text="Time taken to process emotions (milliseconds)",
                    ),
                ),
                (
                    "hume_request_id",
                    models.CharField(
                        blank=True,
                        help_text="Hume API request ID for debugging",
                        max_length=255,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "conversation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="emotion_contexts",
                        to="chat.conversation",
                    ),
                ),
                (
                    "message",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="emotion_contexts",
                        to="chat.message",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="emotion_contexts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "chat_emotion_contexts",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="streamingsession",
            index=models.Index(
                fields=["user", "is_active"], name="chat_stream_user_id_3839bf_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="streamingsession",
            index=models.Index(
                fields=["session_id"], name="chat_stream_session_1a9fba_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="streamingsession",
            index=models.Index(
                fields=["websocket_id"], name="chat_stream_websock_8a38c9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="streamingsession",
            index=models.Index(
                fields=["status", "started_at"], name="chat_stream_status_3420c3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="performancemetrics",
            index=models.Index(
                fields=["user", "timestamp"], name="chat_perfor_user_id_7271d5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="performancemetrics",
            index=models.Index(
                fields=["session", "timestamp"], name="chat_perfor_session_321282_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="performancemetrics",
            index=models.Index(
                fields=["total_response_time"], name="chat_perfor_total_r_c4f067_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="performancemetrics",
            index=models.Index(
                fields=["had_errors", "timestamp"],
                name="chat_perfor_had_err_23dcec_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="emotioncontext",
            index=models.Index(
                fields=["user", "session_id"], name="chat_emotio_user_id_c9b625_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="emotioncontext",
            index=models.Index(
                fields=["user", "created_at"], name="chat_emotio_user_id_8bbc3b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="emotioncontext",
            index=models.Index(
                fields=["session_id", "created_at"],
                name="chat_emotio_session_5bdfce_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="emotioncontext",
            index=models.Index(
                fields=["primary_emotion"], name="chat_emotio_primary_e21a6b_idx"
            ),
        ),
    ]
