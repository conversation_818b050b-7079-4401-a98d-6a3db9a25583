# Generated by Django 4.2.7 on 2025-07-21 22:19

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("chat", "0003_relationship"),
    ]

    operations = [
        migrations.AddField(
            model_name="message",
            name="emotion",
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="message",
            name="media_url",
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="message",
            name="metadata",
            field=models.JSONField(default=dict),
        ),
        migrations.AlterField(
            model_name="message",
            name="message_type",
            field=models.CharField(
                choices=[
                    ("text", "Text"),
                    ("audio", "Audio"),
                    ("voice", "Voice"),
                    ("image", "Image"),
                    ("system", "System"),
                    ("typing", "Typing"),
                ],
                default="text",
                max_length=20,
            ),
        ),
    ]
