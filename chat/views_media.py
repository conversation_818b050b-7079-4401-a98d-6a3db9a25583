"""
Views for serving media files in the chat app.
"""
import logging
import mimetypes
import os
from django.conf import settings
from django.http import HttpR<PERSON>ponse, Http404, FileResponse
from django.core.exceptions import PermissionDenied
from django.views import View
from django.core.files.storage import default_storage
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from .utils.file_utils import get_file_url
from .models import Message

logger = logging.getLogger(__name__)

class MediaFileView(APIView):
    """
    View for serving media files from Backblaze B2 or local storage.
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, file_id, *args, **kwargs):
        """
        Handle GET requests to serve a media file.
        
        Args:
            request: The HTTP request
            file_id: The ID of the file to serve
            
        Returns:
            Response: The HTTP response with the file URL
        """
        try:
            # Try to find a message with this media URL
            message = Message.objects.filter(
                media_url__contains=file_id
            ).first()
            
            if not message:
                return Response(
                    {'error': 'File not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Check if the user has access to this message
            if message.conversation.user != request.user:
                return Response(
                    {'error': 'Permission denied'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Get the file URL
            file_url = message.media_url
            
            # If we're using B2 storage, we might need to generate a signed URL
            if settings.USE_B2_STORAGE and not file_url:
                # Extract the file path from the message
                file_path = message.metadata.get('file_path')
                if file_path:
                    file_url = get_file_url(file_path, user=request.user)
            
            if not file_url:
                return Response(
                    {'error': 'File URL not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            
            # Return the file URL with appropriate cache headers
            response = Response({'url': file_url})
            
            # Set cache headers based on file type
            if 'audio' in message.message_type:
                # Cache audio files for 1 day
                response['Cache-Control'] = 'public, max-age=86400'
            elif 'image' in message.message_type:
                # Cache images for 7 days
                response['Cache-Control'] = 'public, max-age=604800'
            else:
                # Default cache for 1 hour
                response['Cache-Control'] = 'public, max-age=3600'
            
            return response
            
        except Exception as e:
            logger.error(f"Error serving media file: {str(e)}")
            return Response(
                {'error': 'Error serving file'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DirectMediaServeView(View):
    """
    View for directly serving media files from local storage.
    
    This view is only used when not using Backblaze B2 storage.
    """
    def get(self, request, path, *args, **kwargs):
        """
        Handle GET requests to serve a media file directly.
        
        Args:
            request: The HTTP request
            path: The path to the file
            
        Returns:
            FileResponse: The HTTP response with the file
        """
        if settings.USE_B2_STORAGE:
            # If using B2 storage, this view should not be used
            raise Http404("This view is not available when using B2 storage")
        
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            raise PermissionDenied("Authentication required")
        
        # Construct the full path
        full_path = os.path.join(settings.MEDIA_ROOT, path)
        
        # Check if the file exists
        if not os.path.exists(full_path):
            raise Http404("File not found")
        
        # Check if the user has permission to access this file
        # This is a simple check - in a real app, you would check if the user
        # has access to the conversation or message that contains this file
        
        # Get the content type
        content_type, encoding = mimetypes.guess_type(full_path)
        content_type = content_type or 'application/octet-stream'
        
        # Serve the file
        response = FileResponse(open(full_path, 'rb'), content_type=content_type)
        
        # Set cache headers based on file type
        if 'audio' in content_type:
            # Cache audio files for 1 day
            response['Cache-Control'] = 'public, max-age=86400'
        elif 'image' in content_type:
            # Cache images for 7 days
            response['Cache-Control'] = 'public, max-age=604800'
        else:
            # Default cache for 1 hour
            response['Cache-Control'] = 'public, max-age=3600'
        
        return response