"""
Real-time AI companion models for emotion tracking, relationship management, and streaming sessions.
"""
from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid
import json


class UserRelationship(models.Model):
    """Tracks the relationship development between user and AI companion."""
    
    RELATIONSHIP_LEVELS = [
        (1, 'Acquaintance - Basic conversation, general topics'),
        (2, 'Friend - Personal topics, mild flirtation allowed'),
        (3, 'Close Friend - Intimate conversation, emotional support'),
        (4, 'Intimate - Adult content with explicit consent'),
    ]
    
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='ai_relationship'
    )
    
    # Relationship progression
    relationship_level = models.IntegerField(
        choices=RELATIONSHIP_LEVELS,
        default=1,
        help_text="Current relationship level (1-4)"
    )
    
    # Interaction metrics
    total_interactions = models.IntegerField(
        default=0,
        help_text="Total number of interactions with AI companion"
    )
    total_conversation_time = models.DurationField(
        default=timezone.timedelta(0),
        help_text="Total time spent in conversations"
    )
    
    # Emotional metrics
    emotional_intimacy_score = models.FloatField(
        default=0.0,
        help_text="Calculated emotional intimacy score (0.0-1.0)"
    )
    average_emotion_intensity = models.FloatField(
        default=0.0,
        help_text="Average intensity of emotions expressed (0.0-1.0)"
    )
    
    # Progression tracking
    progression_milestones = models.JSONField(
        default=dict,
        help_text="Milestones achieved in relationship progression"
    )
    last_milestone_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Date of last milestone achievement"
    )
    
    # User consent and preferences
    user_consent_flags = models.JSONField(
        default=dict,
        help_text="User consent for different types of content"
    )
    explicit_progression_request = models.BooleanField(
        default=False,
        help_text="User has explicitly requested relationship progression"
    )
    
    # Timestamps
    relationship_started = models.DateTimeField(auto_now_add=True)
    last_interaction = models.DateTimeField(auto_now=True)
    last_level_change = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'chat_user_relationships'
        verbose_name = 'User AI Relationship'
        verbose_name_plural = 'User AI Relationships'
    
    def __str__(self):
        return f"{self.user.email} - Level {self.relationship_level}"
    
    def can_access_content_level(self, content_level):
        """Check if user can access content at given level."""
        return self.relationship_level >= content_level
    
    def update_interaction_metrics(self, conversation_duration=None, emotion_intensity=None):
        """Update interaction metrics after a conversation."""
        self.total_interactions += 1
        
        if conversation_duration:
            self.total_conversation_time += conversation_duration
        
        if emotion_intensity is not None:
            # Update running average of emotion intensity
            current_avg = self.average_emotion_intensity
            total = self.total_interactions
            self.average_emotion_intensity = ((current_avg * (total - 1)) + emotion_intensity) / total
            
            # Update emotional intimacy score based on emotion intensity
            intimacy_boost = 0.0
            if emotion_intensity > 0.7:
                intimacy_boost = 0.05
            elif emotion_intensity > 0.5:
                intimacy_boost = 0.03
            elif emotion_intensity > 0.3:
                intimacy_boost = 0.02
            
            self.emotional_intimacy_score = min(1.0, self.emotional_intimacy_score + intimacy_boost)
        
        self.save(update_fields=[
            'total_interactions', 
            'total_conversation_time', 
            'average_emotion_intensity',
            'emotional_intimacy_score',
            'last_interaction'
        ])
    
    def check_progression_eligibility(self):
        """Check if user is eligible for relationship level progression."""
        if self.relationship_level >= 4:
            return False, "Already at maximum relationship level"
        
        # Define progression criteria
        criteria = {
            2: {  # Friend level
                'min_interactions': 10,
                'min_conversation_time': timezone.timedelta(hours=2),
                'min_emotional_intimacy': 0.3,
            },
            3: {  # Close friend level
                'min_interactions': 50,
                'min_conversation_time': timezone.timedelta(hours=10),
                'min_emotional_intimacy': 0.6,
            },
            4: {  # Intimate level
                'min_interactions': 100,
                'min_conversation_time': timezone.timedelta(hours=25),
                'min_emotional_intimacy': 0.8,
                'explicit_consent_required': True,
            }
        }
        
        next_level = self.relationship_level + 1
        if next_level not in criteria:
            return False, "Invalid progression level"
        
        reqs = criteria[next_level]
        
        # Check each requirement
        if self.total_interactions < reqs['min_interactions']:
            return False, f"Need {reqs['min_interactions']} interactions (have {self.total_interactions})"
        
        if self.total_conversation_time < reqs['min_conversation_time']:
            return False, f"Need {reqs['min_conversation_time']} conversation time"
        
        if self.emotional_intimacy_score < reqs['min_emotional_intimacy']:
            return False, f"Need emotional intimacy score of {reqs['min_emotional_intimacy']}"
        
        if reqs.get('explicit_consent_required'):
            if not self.explicit_progression_request:
                return False, "Explicit user consent required for this level"
            if not self.user_consent_flags.get('adult_content_consent', False):
                return False, "Adult content consent required for this level"
        
        return True, "Eligible for progression"
    
    def progress_relationship(self, force=False):
        """Progress to next relationship level if eligible."""
        if not force:
            eligible, reason = self.check_progression_eligibility()
            if not eligible:
                return False, reason
        
        if self.relationship_level >= 4:
            return False, "Already at maximum level"
        
        old_level = self.relationship_level
        self.relationship_level += 1
        self.last_level_change = timezone.now()
        
        # Record milestone
        milestone_key = f"level_{self.relationship_level}_achieved"
        self.progression_milestones[milestone_key] = timezone.now().isoformat()
        self.last_milestone_date = timezone.now()
        
        self.save(update_fields=[
            'relationship_level', 
            'last_level_change', 
            'progression_milestones',
            'last_milestone_date'
        ])
        
        return True, f"Progressed from level {old_level} to {self.relationship_level}"


class EmotionContext(models.Model):
    """Stores emotion analysis results for user interactions."""
    
    EMOTION_SOURCES = [
        ('audio', 'Audio emotion detection'),
        ('text', 'Text sentiment analysis'),
        ('combined', 'Combined audio and text analysis'),
        ('historical', 'Historical emotion pattern'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='emotion_contexts'
    )
    
    # Session and conversation context
    session_id = models.CharField(
        max_length=255,
        help_text="WebSocket session ID"
    )
    conversation = models.ForeignKey(
        'chat.Conversation',
        on_delete=models.CASCADE,
        related_name='emotion_contexts',
        null=True,
        blank=True
    )
    message = models.ForeignKey(
        'chat.Message',
        on_delete=models.CASCADE,
        related_name='emotion_contexts',
        null=True,
        blank=True
    )
    
    # Emotion data
    emotion_source = models.CharField(
        max_length=20,
        choices=EMOTION_SOURCES,
        default='combined'
    )
    
    # Raw emotion scores from Hume API
    audio_emotions = models.JSONField(
        default=dict,
        help_text="Raw emotion scores from audio analysis"
    )
    text_emotions = models.JSONField(
        default=dict,
        help_text="Raw emotion scores from text analysis"
    )
    
    # Processed emotion context
    primary_emotion = models.CharField(
        max_length=50,
        help_text="Primary detected emotion"
    )
    emotion_intensity = models.FloatField(
        default=0.0,
        help_text="Overall emotion intensity (0.0-1.0)"
    )
    emotion_valence = models.FloatField(
        default=0.0,
        help_text="Emotion valence: negative (-1.0) to positive (1.0)"
    )
    emotion_arousal = models.FloatField(
        default=0.0,
        help_text="Emotion arousal: calm (0.0) to excited (1.0)"
    )
    
    # Context-aware emotions (processed for AI response)
    context_emotions = models.JSONField(
        default=dict,
        help_text="Processed emotions for AI context"
    )
    
    # Confidence and quality metrics
    confidence_score = models.FloatField(
        default=0.0,
        help_text="Confidence in emotion detection (0.0-1.0)"
    )
    audio_quality_score = models.FloatField(
        null=True,
        blank=True,
        help_text="Audio quality score for emotion detection"
    )
    
    # Processing metadata
    processing_time_ms = models.IntegerField(
        default=0,
        help_text="Time taken to process emotions (milliseconds)"
    )
    hume_request_id = models.CharField(
        max_length=255,
        blank=True,
        help_text="Hume API request ID for debugging"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'chat_emotion_contexts'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'session_id']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['session_id', 'created_at']),
            models.Index(fields=['primary_emotion']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.primary_emotion} ({self.emotion_intensity:.2f})"
    
    def get_top_emotions(self, n=3, source='combined'):
        """Get top N emotions from specified source."""
        if source == 'audio' and self.audio_emotions:
            emotions = self.audio_emotions
        elif source == 'text' and self.text_emotions:
            emotions = self.text_emotions
        elif source == 'combined' and self.context_emotions:
            emotions = self.context_emotions
        else:
            return []
        
        # Sort emotions by score and return top N
        if isinstance(emotions, dict):
            sorted_emotions = sorted(emotions.items(), key=lambda x: x[1], reverse=True)
            return sorted_emotions[:n]
        
        return []
    
    def update_relationship_metrics(self):
        """Update user relationship metrics based on this emotion context."""
        try:
            relationship = self.user.ai_relationship
            
            # Update emotional intimacy score based on emotion intensity and type
            intimacy_boost = 0.0
            
            # Higher intensity emotions contribute more to intimacy
            if self.emotion_intensity > 0.7:
                intimacy_boost += 0.02
            elif self.emotion_intensity > 0.5:
                intimacy_boost += 0.01
            
            # Certain emotions contribute more to intimacy
            intimate_emotions = ['love', 'affection', 'trust', 'vulnerability', 'sadness', 'joy']
            if self.primary_emotion.lower() in intimate_emotions:
                intimacy_boost += 0.01
            
            # Update the relationship's emotional intimacy score
            current_score = relationship.emotional_intimacy_score
            new_score = min(1.0, current_score + intimacy_boost)
            relationship.emotional_intimacy_score = new_score
            relationship.save(update_fields=['emotional_intimacy_score'])
            
        except UserRelationship.DoesNotExist:
            # Create relationship if it doesn't exist
            UserRelationship.objects.create(user=self.user)


class StreamingSession(models.Model):
    """Tracks active WebSocket streaming sessions for real-time AI companion."""
    
    SESSION_TYPES = [
        ('voice', 'Voice conversation'),
        ('text', 'Text conversation'),
        ('mixed', 'Mixed voice and text'),
    ]
    
    SESSION_STATUS = [
        ('connecting', 'Connecting'),
        ('active', 'Active'),
        ('idle', 'Idle'),
        ('disconnected', 'Disconnected'),
        ('error', 'Error'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='streaming_sessions'
    )
    
    # Session identification
    session_id = models.CharField(
        max_length=255, 
        unique=True,
        help_text="Unique session identifier"
    )
    websocket_id = models.CharField(
        max_length=255,
        help_text="WebSocket connection identifier"
    )
    
    # Session metadata
    session_type = models.CharField(
        max_length=20,
        choices=SESSION_TYPES,
        default='mixed'
    )
    status = models.CharField(
        max_length=20,
        choices=SESSION_STATUS,
        default='connecting'
    )
    
    # Connection information
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    device_info = models.JSONField(
        default=dict,
        help_text="Device and browser information"
    )
    
    # Session activity
    started_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    # Performance metrics
    performance_metrics = models.JSONField(
        default=dict,
        help_text="Real-time performance metrics for this session"
    )
    
    # Audio/voice specific
    audio_enabled = models.BooleanField(default=False)
    voice_settings = models.JSONField(
        default=dict,
        help_text="Voice settings for this session"
    )
    
    # Error tracking
    error_count = models.IntegerField(default=0)
    last_error = models.TextField(blank=True)
    last_error_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'chat_streaming_sessions'
        ordering = ['-started_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_id']),
            models.Index(fields=['websocket_id']),
            models.Index(fields=['status', 'started_at']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.session_type} - {self.status}"
    
    def update_activity(self):
        """Update last activity timestamp."""
        self.last_activity = timezone.now()
        self.save(update_fields=['last_activity'])
    
    def add_performance_metric(self, metric_name, value, timestamp=None):
        """Add a performance metric to this session."""
        if timestamp is None:
            timestamp = timezone.now().isoformat()
        
        if 'metrics' not in self.performance_metrics:
            self.performance_metrics['metrics'] = []
        
        self.performance_metrics['metrics'].append({
            'name': metric_name,
            'value': value,
            'timestamp': timestamp
        })
        
        # Keep only last 100 metrics to prevent unbounded growth
        if len(self.performance_metrics['metrics']) > 100:
            self.performance_metrics['metrics'] = self.performance_metrics['metrics'][-100:]
        
        # Save with async context handling
        try:
            from asgiref.sync import sync_to_async
            import asyncio
            
            def _save_metrics():
                self.save(update_fields=['performance_metrics'])
            
            # Check if we're in an async context
            try:
                loop = asyncio.get_running_loop()
                # We're in an async context, schedule the sync operation
                asyncio.create_task(sync_to_async(_save_metrics)())
            except RuntimeError:
                # We're not in an async context, run directly
                _save_metrics()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error saving performance metrics: {e}")
    
    def get_average_response_time(self):
        """Calculate average response time for this session."""
        metrics = self.performance_metrics.get('metrics', [])
        response_times = [m['value'] for m in metrics if m['name'] == 'total_response_time']
        
        if not response_times:
            return None
        
        return sum(response_times) / len(response_times)
    
    def record_error(self, error_message):
        """Record an error for this session."""
        self.error_count += 1
        self.last_error = error_message
        self.last_error_at = timezone.now()
        self.save(update_fields=['error_count', 'last_error', 'last_error_at'])
    
    def end_session(self, reason='user_disconnect'):
        """End the streaming session."""
        self.is_active = False
        self.status = 'disconnected'
        self.ended_at = timezone.now()
        
        # Record session duration in performance metrics
        if self.started_at:
            duration = (self.ended_at - self.started_at).total_seconds()
            self.performance_metrics['session_duration'] = duration
            self.performance_metrics['end_reason'] = reason
        
        self.save(update_fields=['is_active', 'status', 'ended_at', 'performance_metrics'])


class PerformanceMetrics(models.Model):
    """Detailed performance metrics for real-time AI companion interactions."""
    
    METRIC_TYPES = [
        ('response_time', 'Total response time'),
        ('audio_processing', 'Audio processing time'),
        ('emotion_detection', 'Emotion detection time'),
        ('llm_first_token', 'LLM first token time'),
        ('tts_first_chunk', 'TTS first chunk time'),
        ('transcription', 'Speech-to-text time'),
        ('memory_retrieval', 'Memory retrieval time'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Session context
    session = models.ForeignKey(
        StreamingSession,
        on_delete=models.CASCADE,
        related_name='detailed_metrics'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='performance_metrics'
    )
    
    # Request identification
    request_id = models.CharField(
        max_length=255,
        help_text="Unique identifier for this request"
    )
    
    # Timing metrics (all in milliseconds)
    audio_processing_time = models.FloatField(
        null=True,
        blank=True,
        help_text="Time to process audio input (ms)"
    )
    emotion_detection_time = models.FloatField(
        null=True,
        blank=True,
        help_text="Time for emotion detection (ms)"
    )
    transcription_time = models.FloatField(
        null=True,
        blank=True,
        help_text="Time for speech-to-text (ms)"
    )
    memory_retrieval_time = models.FloatField(
        null=True,
        blank=True,
        help_text="Time to retrieve relevant memories (ms)"
    )
    llm_first_token_time = models.FloatField(
        null=True,
        blank=True,
        help_text="Time to first LLM token (ms)"
    )
    llm_total_time = models.FloatField(
        null=True,
        blank=True,
        help_text="Total LLM processing time (ms)"
    )
    tts_first_chunk_time = models.FloatField(
        null=True,
        blank=True,
        help_text="Time to first TTS audio chunk (ms)"
    )
    tts_total_time = models.FloatField(
        null=True,
        blank=True,
        help_text="Total TTS processing time (ms)"
    )
    total_response_time = models.FloatField(
        help_text="Total end-to-end response time (ms)"
    )
    
    # Quality metrics
    audio_quality_score = models.FloatField(
        null=True,
        blank=True,
        help_text="Audio input quality score (0.0-1.0)"
    )
    transcription_confidence = models.FloatField(
        null=True,
        blank=True,
        help_text="Transcription confidence score (0.0-1.0)"
    )
    emotion_confidence = models.FloatField(
        null=True,
        blank=True,
        help_text="Emotion detection confidence (0.0-1.0)"
    )
    
    # User satisfaction
    user_satisfaction_score = models.FloatField(
        null=True,
        blank=True,
        help_text="User satisfaction rating (1.0-5.0)"
    )
    user_feedback = models.TextField(
        blank=True,
        help_text="Optional user feedback"
    )
    
    # System information
    server_load = models.FloatField(
        null=True,
        blank=True,
        help_text="Server load at time of request"
    )
    concurrent_users = models.IntegerField(
        null=True,
        blank=True,
        help_text="Number of concurrent users"
    )
    
    # API usage
    groq_tokens_used = models.IntegerField(
        null=True,
        blank=True,
        help_text="Tokens used in Groq API call"
    )
    hume_api_calls = models.IntegerField(
        default=0,
        help_text="Number of Hume API calls made"
    )
    
    # Error information
    had_errors = models.BooleanField(default=False)
    error_details = models.JSONField(
        default=dict,
        help_text="Details of any errors encountered"
    )
    
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'chat_performance_metrics'
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['session', 'timestamp']),
            models.Index(fields=['total_response_time']),
            models.Index(fields=['had_errors', 'timestamp']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.total_response_time}ms - {self.timestamp}"
    
    def meets_performance_target(self, target_ms=450):
        """Check if this interaction met the performance target."""
        return self.total_response_time <= target_ms
    
    def get_performance_breakdown(self):
        """Get a breakdown of where time was spent."""
        breakdown = {}
        
        if self.audio_processing_time:
            breakdown['audio_processing'] = self.audio_processing_time
        if self.emotion_detection_time:
            breakdown['emotion_detection'] = self.emotion_detection_time
        if self.transcription_time:
            breakdown['transcription'] = self.transcription_time
        if self.memory_retrieval_time:
            breakdown['memory_retrieval'] = self.memory_retrieval_time
        if self.llm_first_token_time:
            breakdown['llm_first_token'] = self.llm_first_token_time
        if self.tts_first_chunk_time:
            breakdown['tts_first_chunk'] = self.tts_first_chunk_time
        
        return breakdown
    
    @classmethod
    def get_average_metrics(cls, user=None, days=7):
        """Get average performance metrics for a user or system-wide."""
        from django.utils import timezone
        from django.db.models import Avg
        
        since = timezone.now() - timezone.timedelta(days=days)
        queryset = cls.objects.filter(timestamp__gte=since)
        
        if user:
            queryset = queryset.filter(user=user)
        
        return queryset.aggregate(
            avg_total_response_time=Avg('total_response_time'),
            avg_audio_processing_time=Avg('audio_processing_time'),
            avg_emotion_detection_time=Avg('emotion_detection_time'),
            avg_llm_first_token_time=Avg('llm_first_token_time'),
            avg_tts_first_chunk_time=Avg('tts_first_chunk_time'),
            avg_user_satisfaction=Avg('user_satisfaction_score'),
        )