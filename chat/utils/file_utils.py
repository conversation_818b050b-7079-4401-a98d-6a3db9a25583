"""
Utility functions for file handling and validation.
"""
import os
import uuid
import magic
import logging
import hashlib
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.files.storage import default_storage
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)

# Define allowed file types
ALLOWED_IMAGE_TYPES = {
    'image/jpeg': '.jpg',
    'image/png': '.png',
    'image/gif': '.gif',
    'image/webp': '.webp',
}

ALLOWED_AUDIO_TYPES = {
    'audio/mpeg': '.mp3',
    'audio/mp4': '.m4a',
    'audio/wav': '.wav',
    'audio/x-wav': '.wav',
    'audio/ogg': '.ogg',
    'audio/webm': '.webm',
}

# Maximum file sizes (in bytes)
MAX_IMAGE_SIZE = 5 * 1024 * 1024  # 5MB
MAX_AUDIO_SIZE = settings.MAX_AUDIO_FILE_SIZE  # From settings

# Security settings
SCAN_FILES_FOR_VIRUSES = getattr(settings, 'SCAN_FILES_FOR_VIRUSES', False)
ALLOWED_FILE_EXTENSIONS = {
    **{ext: mime for mime, ext in ALLOWED_IMAGE_TYPES.items()},
    **{ext: mime for mime, ext in ALLOWED_AUDIO_TYPES.items()},
}

def validate_file_type(file, allowed_types):
    """
    Validate that the file is of an allowed type.
    
    Args:
        file: The uploaded file
        allowed_types: Dictionary of allowed MIME types and their extensions
        
    Returns:
        tuple: (is_valid, file_extension, error_message)
    """
    try:
        # Read the first 2048 bytes to determine file type
        file_header = file.read(2048)
        file.seek(0)  # Reset file pointer
        
        # Use python-magic to determine file type
        mime_type = magic.from_buffer(file_header, mime=True)
        
        if mime_type not in allowed_types:
            return False, None, f"Unsupported file type: {mime_type}. Allowed types: {', '.join(allowed_types.keys())}"
        
        return True, allowed_types[mime_type], None
    except Exception as e:
        logger.error(f"Error validating file type: {str(e)}")
        return False, None, f"Error validating file type: {str(e)}"

def validate_file_size(file, max_size):
    """
    Validate that the file size is within the allowed limit.
    
    Args:
        file: The uploaded file
        max_size: Maximum allowed file size in bytes
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if file.size > max_size:
        return False, f"File too large. Maximum size is {max_size / (1024 * 1024):.1f}MB."
    return True, None

def generate_unique_filename(original_filename, prefix=''):
    """
    Generate a unique filename to avoid collisions.
    
    Args:
        original_filename: The original filename
        prefix: Optional prefix for the filename
        
    Returns:
        str: A unique filename
    """
    # Get file extension
    _, ext = os.path.splitext(original_filename)
    
    # Generate a UUID
    unique_id = str(uuid.uuid4())
    
    # Create a unique filename
    if prefix:
        return f"{prefix}_{unique_id}{ext}"
    return f"{unique_id}{ext}"

def calculate_file_hash(file):
    """
    Calculate the SHA-256 hash of a file.
    
    Args:
        file: The file to hash
        
    Returns:
        str: The SHA-256 hash of the file
    """
    try:
        sha256_hash = hashlib.sha256()
        for chunk in file.chunks():
            sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating file hash: {str(e)}")
        return None

def scan_file_for_viruses(file):
    """
    Scan a file for viruses.
    
    Args:
        file: The file to scan
        
    Returns:
        tuple: (is_safe, error_message)
    """
    # This is a placeholder for actual virus scanning
    # In a production environment, you would integrate with a virus scanning service
    # such as ClamAV or a cloud-based service
    
    if not SCAN_FILES_FOR_VIRUSES:
        return True, None
    
    try:
        # Placeholder for virus scanning logic
        # For now, we'll just return True
        return True, None
    except Exception as e:
        logger.error(f"Error scanning file for viruses: {str(e)}")
        return False, f"Error scanning file for viruses: {str(e)}"

def save_uploaded_file(file, directory, allowed_types, max_size, prefix=''):
    """
    Save an uploaded file after validation.
    
    Args:
        file: The uploaded file
        directory: Directory to save the file in
        allowed_types: Dictionary of allowed MIME types and their extensions
        max_size: Maximum allowed file size in bytes
        prefix: Optional prefix for the filename
        
    Returns:
        tuple: (success, file_path or error_message)
    """
    try:
        # Validate file size
        size_valid, size_error = validate_file_size(file, max_size)
        if not size_valid:
            return False, size_error
        
        # Validate file type
        type_valid, file_ext, type_error = validate_file_type(file, allowed_types)
        if not type_valid:
            return False, type_error
        
        # Scan file for viruses if enabled
        if SCAN_FILES_FOR_VIRUSES:
            is_safe, virus_error = scan_file_for_viruses(file)
            if not is_safe:
                return False, virus_error
        
        # Calculate file hash for integrity
        file_hash = calculate_file_hash(file)
        file.seek(0)  # Reset file pointer after hashing
        
        # Generate a unique filename
        unique_filename = generate_unique_filename(file.name, prefix)
        
        # Create the full path
        file_path = os.path.join(directory, unique_filename)
        
        # Save the file using the configured storage backend
        file_path = default_storage.save(file_path, file)
        
        # Log the file upload with hash for audit trail
        logger.info(f"File uploaded: {file_path}, Hash: {file_hash}")
        
        # Return the file path
        return True, file_path
    except Exception as e:
        logger.error(f"Error saving uploaded file: {str(e)}")
        return False, f"Error saving file: {str(e)}"

def set_secure_permissions(file_path, user_id=None):
    """
    Set secure permissions on a file in Backblaze B2.
    
    Args:
        file_path: The file path
        user_id: The ID of the user who owns the file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # This is a placeholder for setting secure permissions
        # In a production environment with Backblaze B2, you would use their API
        # to set appropriate permissions on the file
        
        # If we're using B2 storage, we can set permissions
        if settings.USE_B2_STORAGE:
            # For now, we'll just log that we would set permissions
            logger.info(f"Setting secure permissions on {file_path} for user {user_id}")
            
            # In a real implementation, you would use the B2 API to set permissions
            # Example (pseudocode):
            # b2_client = get_b2_client()
            # b2_client.set_file_permissions(file_path, user_id=user_id)
            
            return True
        
        return True
    except Exception as e:
        logger.error(f"Error setting secure permissions: {str(e)}")
        return False

def get_file_url(file_path, user=None, expiration=3600):
    """
    Get the URL for a file.
    
    Args:
        file_path: The file path
        user: The user requesting the file
        expiration: The expiration time for the URL in seconds (default: 1 hour)
        
    Returns:
        str: The URL for the file
    """
    try:
        # If we're using B2 storage and the file is private, generate a signed URL
        if settings.USE_B2_STORAGE and settings.B2_DEFAULT_ACL == 'private':
            # In a real implementation, you would use the B2 API to generate a signed URL
            # For now, we'll just use the default URL
            return default_storage.url(file_path)
        
        # Otherwise, return the regular URL
        return default_storage.url(file_path)
    except Exception as e:
        logger.error(f"Error getting file URL: {str(e)}")
        return None