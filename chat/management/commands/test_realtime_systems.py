"""
Management command to test real-time AI companion systems.
"""
import asyncio
import time
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

from chat.services.performance_monitor import performance_monitor
from chat.services.cache_service import cache_service_instance
from chat.services.error_recovery import error_recovery_manager
from chat.models_realtime import StreamingSession

User = get_user_model()


class Command(BaseCommand):
    help = 'Test real-time AI companion systems (performance monitoring, caching, error recovery)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-performance',
            action='store_true',
            help='Test performance monitoring system',
        )
        parser.add_argument(
            '--test-cache',
            action='store_true',
            help='Test caching system',
        )
        parser.add_argument(
            '--test-error-recovery',
            action='store_true',
            help='Test error recovery system',
        )
        parser.add_argument(
            '--test-all',
            action='store_true',
            help='Test all systems',
        )

    def handle(self, *args, **options):
        if options['test_all']:
            options['test_performance'] = True
            options['test_cache'] = True
            options['test_error_recovery'] = True

        if options['test_performance']:
            self.test_performance_monitoring()

        if options['test_cache']:
            self.test_caching_system()

        if options['test_error_recovery']:
            self.test_error_recovery_system()

        if not any([options['test_performance'], options['test_cache'], options['test_error_recovery']]):
            self.stdout.write(
                self.style.WARNING('No tests specified. Use --test-all or specific test flags.')
            )

    def test_performance_monitoring(self):
        """Test performance monitoring system."""
        self.stdout.write(self.style.SUCCESS('Testing Performance Monitoring System...'))
        
        try:
            # Test timer functionality
            request_id = 'test-perf-123'
            
            # Start multiple timers
            performance_monitor.start_timer(request_id, 'total_response_time')
            performance_monitor.start_timer(request_id, 'llm_first_token_time')
            performance_monitor.start_timer(request_id, 'audio_processing_time')
            
            # Simulate processing time
            time.sleep(0.01)  # 10ms
            
            # End timers
            audio_time = performance_monitor.end_timer(request_id, 'audio_processing_time')
            llm_time = performance_monitor.end_timer(request_id, 'llm_first_token_time')
            total_time = performance_monitor.end_timer(request_id, 'total_response_time')
            
            self.stdout.write(f'  ✓ Audio processing time: {audio_time:.2f}ms')
            self.stdout.write(f'  ✓ LLM first token time: {llm_time:.2f}ms')
            self.stdout.write(f'  ✓ Total response time: {total_time:.2f}ms')
            
            # Test performance summary
            summary = performance_monitor.get_performance_summary(days=1)
            self.stdout.write(f'  ✓ Performance summary generated: {len(summary)} metrics')
            
            # Test bottleneck detection
            bottlenecks = performance_monitor.get_bottlenecks(days=1)
            self.stdout.write(f'  ✓ Bottleneck detection: {len(bottlenecks)} potential issues')
            
            # Test alert checking
            alerts = performance_monitor.check_performance_alerts()
            self.stdout.write(f'  ✓ Performance alerts: {len(alerts)} active alerts')
            
            self.stdout.write(self.style.SUCCESS('  Performance monitoring system: PASSED'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  Performance monitoring system: FAILED - {e}'))

    def test_caching_system(self):
        """Test caching system."""
        self.stdout.write(self.style.SUCCESS('Testing Caching System...'))
        
        try:
            user_id = 12345
            
            # Test user profile caching
            profile_data = {
                'name': 'Test User',
                'preferences': {'theme': 'dark', 'language': 'en'},
                'relationship_level': 2,
                'last_active': time.time()
            }
            
            cache_service_instance.cache_user_profile(user_id, profile_data)
            cached_profile = cache_service_instance.get_cached_user_profile(user_id)
            
            if cached_profile == profile_data:
                self.stdout.write('  ✓ User profile caching: PASSED')
            else:
                self.stdout.write('  ❌ User profile caching: FAILED')
            
            # Test emotion context caching
            session_id = 'test-session-456'
            emotion_data = {
                'primary_emotion': 'joy',
                'emotions': [
                    {'name': 'joy', 'score': 0.8},
                    {'name': 'excitement', 'score': 0.6}
                ],
                'confidence_score': 0.9,
                'timestamp': time.time()
            }
            
            cache_service_instance.cache_emotion_context(user_id, session_id, emotion_data)
            cached_emotion = cache_service_instance.get_cached_emotion_context(user_id, session_id)
            
            if cached_emotion == emotion_data:
                self.stdout.write('  ✓ Emotion context caching: PASSED')
            else:
                self.stdout.write('  ❌ Emotion context caching: FAILED')
            
            # Test query-response caching
            query = "What is the weather like today?"
            response = "I don't have access to current weather data, but I can help you find weather information."
            
            cache_service_instance.cache_query_response(user_id, query, response)
            cached_response = cache_service_instance.get_cached_query_response(user_id, query)
            
            if cached_response == response:
                self.stdout.write('  ✓ Query-response caching: PASSED')
            else:
                self.stdout.write('  ❌ Query-response caching: FAILED')
            
            # Test memory embedding caching
            text = "This is a test text for embedding"
            text_hash = cache_service_instance.compute_text_hash(text)
            fake_embedding = [0.1, 0.2, 0.3, 0.4, 0.5]  # Mock embedding
            
            cache_service_instance.cache_memory_embeddings(user_id, text_hash, fake_embedding)
            cached_embedding = cache_service_instance.get_cached_memory_embedding(user_id, text_hash)
            
            if cached_embedding == fake_embedding:
                self.stdout.write('  ✓ Memory embedding caching: PASSED')
            else:
                self.stdout.write('  ❌ Memory embedding caching: FAILED')
            
            self.stdout.write(self.style.SUCCESS('  Caching system: PASSED'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  Caching system: FAILED - {e}'))

    def test_error_recovery_system(self):
        """Test error recovery system."""
        self.stdout.write(self.style.SUCCESS('Testing Error Recovery System...'))
        
        try:
            # Test circuit breaker functionality
            groq_circuit = error_recovery_manager.circuit_breakers['groq']
            hume_circuit = error_recovery_manager.circuit_breakers['hume']
            
            # Test initial state
            if groq_circuit.allow_request() and hume_circuit.allow_request():
                self.stdout.write('  ✓ Circuit breakers initial state: PASSED')
            else:
                self.stdout.write('  ❌ Circuit breakers initial state: FAILED')
            
            # Test failure recording
            original_groq_state = groq_circuit.state
            
            # Record multiple failures to trigger circuit breaker
            for i in range(6):  # More than the default threshold of 5
                groq_circuit.record_failure()
            
            if not groq_circuit.allow_request():
                self.stdout.write('  ✓ Circuit breaker failure detection: PASSED')
            else:
                self.stdout.write('  ❌ Circuit breaker failure detection: FAILED')
            
            # Test success recording (reset circuit)
            # First we need to wait for the recovery timeout or manually reset
            groq_circuit.state = groq_circuit.CircuitState.CLOSED if hasattr(groq_circuit, 'CircuitState') else 'closed'
            groq_circuit.failure_count = 0
            groq_circuit.record_success()
            if groq_circuit.allow_request():
                self.stdout.write('  ✓ Circuit breaker recovery: PASSED')
            else:
                self.stdout.write('  ❌ Circuit breaker recovery: FAILED')
            
            # Test service health tracking
            initial_groq_health = error_recovery_manager.check_service_health('groq')
            initial_hume_health = error_recovery_manager.check_service_health('hume')
            
            if initial_groq_health and initial_hume_health:
                self.stdout.write('  ✓ Service health tracking: PASSED')
            else:
                self.stdout.write('  ❌ Service health tracking: FAILED')
            
            # Test fallback response generation
            async def test_fallback():
                fallback = await error_recovery_manager.fallback_response(
                    "Hello, how are you?", 
                    {'error_type': 'api_failure'}
                )
                return fallback
            
            fallback_response = asyncio.run(test_fallback())
            
            if fallback_response and len(fallback_response) > 10:
                self.stdout.write('  ✓ Fallback response generation: PASSED')
            else:
                self.stdout.write('  ❌ Fallback response generation: FAILED')
            
            # Test retry mechanism
            async def test_retry():
                call_count = 0
                
                async def failing_function():
                    nonlocal call_count
                    call_count += 1
                    if call_count < 3:
                        raise Exception("Temporary failure")
                    return "success"
                
                try:
                    result = await error_recovery_manager.retry_with_backoff(
                        failing_function, max_retries=3
                    )
                    return result == "success" and call_count == 3
                except:
                    return False
            
            retry_success = asyncio.run(test_retry())
            
            if retry_success:
                self.stdout.write('  ✓ Retry mechanism: PASSED')
            else:
                self.stdout.write('  ❌ Retry mechanism: FAILED')
            
            self.stdout.write(self.style.SUCCESS('  Error recovery system: PASSED'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'  Error recovery system: FAILED - {e}'))
            import traceback
            traceback.print_exc()