"""
Django management command to validate real-time AI companion configuration.
"""
from django.core.management.base import BaseCommand
from chat.services.config_validator import validate_configuration_command


class Command(BaseCommand):
    help = 'Validate configuration for real-time AI companion services'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-connectivity',
            action='store_true',
            help='Skip API connectivity tests',
        )
    
    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Validating real-time AI companion configuration...')
        )
        
        try:
            is_valid = validate_configuration_command()
            
            if is_valid:
                self.stdout.write(
                    self.style.SUCCESS('\n✅ Configuration validation passed!')
                )
                return
            else:
                self.stdout.write(
                    self.style.ERROR('\n❌ Configuration validation failed!')
                )
                self.stdout.write(
                    self.style.WARNING('Please fix the errors above before running the real-time AI companion.')
                )
                exit(1)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during validation: {e}')
            )
            exit(1)