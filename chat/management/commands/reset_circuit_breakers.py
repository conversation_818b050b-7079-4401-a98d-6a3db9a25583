"""
Django management command to reset circuit breakers.
"""
from django.core.management.base import BaseCommand
from chat.services.error_recovery import error_recovery_manager


class Command(BaseCommand):
    help = 'Reset all circuit breakers to ensure TTS and other services work properly'

    def add_arguments(self, parser):
        parser.add_argument(
            '--service',
            type=str,
            help='Reset specific service circuit breaker (hume, groq, openai)',
        )

    def handle(self, *args, **options):
        service = options.get('service')
        
        if service:
            if service in error_recovery_manager.circuit_breakers:
                error_recovery_manager.reset_circuit_breaker(service)
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully reset {service} circuit breaker')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'Unknown service: {service}. Available: hume, groq, openai')
                )
        else:
            # Reset all circuit breakers
            error_recovery_manager.reset_all_circuit_breakers()
            self.stdout.write(
                self.style.SUCCESS('Successfully reset all circuit breakers')
            )
        
        # Show status
        status = error_recovery_manager.get_circuit_breaker_status()
        self.stdout.write('\nCircuit Breaker Status:')
        for service_name, info in status.items():
            self.stdout.write(f"  {service_name}: {info['state']} (failures: {info['failure_count']})")
