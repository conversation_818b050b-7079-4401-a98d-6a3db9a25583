from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from django.shortcuts import get_object_or_404
from django.db.models import Q
from .models import Conversation, Message, MessageReaction, ChatSession, VoiceSettings, Relationship
from .models_realtime import UserRelationship, EmotionContext, StreamingSession, PerformanceMetrics
from .serializers import (
    ConversationSerializer, ConversationCreateSerializer, MessageSerializer,
    MessageCreateSerializer, MessageReactionSerializer, ChatSessionSerializer,
    VoiceSettingsSerializer, AudioUploadSerializer, RelationshipSerializer,
    UserRelationshipSerializer, EmotionContextSerializer
)
from .services import ChatService
import logging

logger = logging.getLogger(__name__)


class ConversationListCreateView(generics.ListCreateAPIView):
    """List user's conversations or create a new one."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ConversationCreateSerializer
        return ConversationSerializer
    
    def get_queryset(self):
        # Filter by active status if provided
        is_active = self.request.query_params.get('is_active')
        queryset = Conversation.objects.filter(user=self.request.user)
        
        # Filter by archive status
        show_archived = self.request.query_params.get('show_archived', 'false').lower() == 'true'
        if not show_archived:
            queryset = queryset.filter(is_archived=False)
            
        # Filter by active status if specified
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active_bool)
            
        return queryset.order_by('-last_message_at')
    
    def perform_create(self, serializer):
        conversation = serializer.save(user=self.request.user)
        
        # Return the created conversation with full serialization
        return conversation
        
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        conversation = self.perform_create(serializer)
        
        # Use the full serializer for the response
        response_serializer = ConversationSerializer(conversation, context={'request': request})
        headers = self.get_success_headers(serializer.data)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED, headers=headers)


class ConversationDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a conversation."""
    
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Conversation.objects.filter(user=self.request.user)
    
    def retrieve(self, request, *args, **kwargs):
        """Customize retrieve to optionally include messages"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        # Check if messages should be included
        include_messages = request.query_params.get('include_messages', 'false').lower() == 'true'
        
        response_data = serializer.data
        
        if include_messages:
            # Get messages for this conversation
            messages = instance.messages.filter(is_deleted=False).order_by('created_at')
            response_data['messages'] = MessageSerializer(
                messages, 
                many=True, 
                context={'request': request}
            ).data
        
        return Response(response_data)
    
    def perform_destroy(self, instance):
        # Check if we should permanently delete or archive
        permanent = self.request.query_params.get('permanent', 'false').lower() == 'true'
        
        if permanent:
            # Permanently delete the conversation and its messages
            instance.delete()
        else:
            # Soft delete by archiving
            instance.is_archived = True
            instance.save()
            
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        # Handle archive/unarchive actions
        if 'is_archived' in request.data:
            instance.is_archived = request.data['is_archived']
            instance.save(update_fields=['is_archived'])
            
        # Handle active/inactive actions
        if 'is_active' in request.data:
            instance.is_active = request.data['is_active']
            instance.save(update_fields=['is_active'])
            
        return Response(serializer.data)


class ConversationMessagesView(generics.ListAPIView):
    """List messages in a conversation."""
    
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        conversation_id = self.kwargs['conversation_id']
        conversation = get_object_or_404(
            Conversation,
            id=conversation_id,
            user=self.request.user
        )
        
        # Get pagination parameters
        limit = self.request.query_params.get('limit', 50)
        try:
            limit = int(limit)
            if limit > 100:  # Cap at 100 messages
                limit = 100
        except ValueError:
            limit = 50
            
        # Get before/after timestamps for pagination
        before = self.request.query_params.get('before')
        after = self.request.query_params.get('after')
        
        # Filter by message type if specified
        message_type = self.request.query_params.get('message_type')
        sender_type = self.request.query_params.get('sender_type')
        
        # Start with base query
        queryset = conversation.messages.filter(is_deleted=False)
        
        # Apply filters
        if before:
            queryset = queryset.filter(created_at__lt=before)
        if after:
            queryset = queryset.filter(created_at__gt=after)
        if message_type:
            queryset = queryset.filter(message_type=message_type)
        if sender_type:
            queryset = queryset.filter(sender_type=sender_type)
            
        # Order by creation time and limit results
        return queryset.order_by('created_at')[:limit]


class MessageCreateView(generics.CreateAPIView):
    """Create a new message in a conversation."""
    
    serializer_class = MessageCreateSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Save user message
        message = serializer.save()
        
        # Update conversation's last message time
        message.conversation.update_last_message_time()
        
        # If this is a user message, generate AI response
        if message.sender_type == 'user':
            self._generate_ai_response_async(message)
        
        return Response(
            MessageSerializer(message, context={'request': request}).data,
            status=status.HTTP_201_CREATED
        )
    
    def _generate_ai_response_async(self, user_message):
        """Generate AI response asynchronously."""
        # In production, this would be handled by Celery task
        # For now, we'll handle it synchronously
        try:
            chat_service = ChatService(user=user_message.conversation.user)
            # This would be async in real implementation
            logger.info(f"Generating AI response for message: {user_message.id}")
            
            # Update relationship XP when user sends a message
            try:
                relationship = Relationship.objects.get(user=user_message.conversation.user)
                relationship.add_xp(1)  # Add 1 XP for each message
            except Relationship.DoesNotExist:
                # Create relationship if it doesn't exist
                relationship = Relationship.objects.create(user=user_message.conversation.user)
                
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")


class MessageDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update, or delete a message."""
    
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Message.objects.filter(
            conversation__user=self.request.user,
            is_deleted=False
        )
    
    def perform_destroy(self, instance):
        # Soft delete the message
        instance.is_deleted = True
        instance.save()
        
    def perform_update(self, serializer):
        # Mark message as edited
        serializer.save(is_edited=True)


class MessageBulkDeleteView(generics.GenericAPIView):
    """Bulk delete messages."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        message_ids = request.data.get('message_ids', [])
        permanent = request.data.get('permanent', False)
        
        if not message_ids:
            return Response(
                {'error': 'No message IDs provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Get messages from conversations owned by the user
        messages = Message.objects.filter(
            id__in=message_ids,
            conversation__user=request.user
        )
        
        if permanent:
            # Permanently delete messages
            count, _ = messages.delete()
        else:
            # Soft delete messages
            count = messages.update(is_deleted=True)
            
        return Response({
            'message': f"{count} messages {'deleted' if permanent else 'marked as deleted'}",
            'count': count
        })


class MessageReactionView(generics.CreateAPIView, generics.DestroyAPIView):
    """Add or remove reaction to a message."""
    
    serializer_class = MessageReactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def create(self, request, *args, **kwargs):
        message_id = kwargs['message_id']
        message = get_object_or_404(Message, id=message_id)
        
        # Check if user owns the conversation
        if message.conversation.user != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Remove existing reaction if any
        MessageReaction.objects.filter(
            message=message,
            user=request.user
        ).delete()
        
        # Create new reaction
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(message=message, user=request.user)
        
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    def delete(self, request, *args, **kwargs):
        message_id = kwargs['message_id']
        message = get_object_or_404(Message, id=message_id)
        
        # Check if user owns the conversation
        if message.conversation.user != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Remove reaction
        deleted_count, _ = MessageReaction.objects.filter(
            message=message,
            user=request.user
        ).delete()
        
        if deleted_count > 0:
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(
                {'error': 'No reaction found'},
                status=status.HTTP_404_NOT_FOUND
            )


class ChatSessionListView(generics.ListAPIView):
    """List user's chat sessions."""
    
    serializer_class = ChatSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return ChatSession.objects.filter(user=self.request.user)


class VoiceSettingsView(generics.RetrieveUpdateAPIView):
    """Get or update user's voice settings."""
    
    serializer_class = VoiceSettingsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        voice_settings, created = VoiceSettings.objects.get_or_create(
            user=self.request.user
        )
        return voice_settings


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def typing_indicator(request):
    """Handle typing indicator updates."""
    
    conversation_id = request.data.get('conversation_id')
    is_typing = request.data.get('is_typing', True)
    
    if not conversation_id:
        return Response(
            {'error': 'Conversation ID is required'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    try:
        # Get conversation
        conversation = get_object_or_404(
            Conversation,
            id=conversation_id,
            user=request.user
        )
        
        # In a real implementation, this would send a WebSocket message
        # For now, just log it
        logger.info(f"User {request.user.id} is {'typing' if is_typing else 'not typing'} in conversation {conversation_id}")
        
        return Response({
            'status': 'success',
            'is_typing': is_typing
        })
        
    except Exception as e:
        logger.error(f"Error processing typing indicator: {e}")
        return Response(
            {'error': 'Failed to process typing indicator'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def upload_audio(request):
    """Handle audio file upload for voice messages."""
    
    # Handle both 'audio' and 'audio_file' field names for compatibility
    audio_file = request.FILES.get('audio') or request.FILES.get('audio_file')
    conversation_id = request.data.get('conversation_id')
    
    if not audio_file:
        return Response(
            {'error': 'Audio file is required'},
            status=status.HTTP_400_BAD_REQUEST
        )
        
    if not conversation_id:
        return Response(
            {'error': 'Conversation ID is required'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    try:
        # Validate conversation ownership
        conversation = get_object_or_404(
            Conversation,
            id=conversation_id,
            user=request.user
        )
        
        # Process audio with ChatService
        chat_service = ChatService(user=request.user)
        
        # Create a new message with the audio file
        message = Message.objects.create(
            conversation=conversation,
            sender_type='user',
            message_type='voice',
            content='Voice message',  # Placeholder until transcription
            audio_file=audio_file
        )
        
        # In a real implementation, this would be async
        # For now, return success response with the created message
        return Response(
            MessageSerializer(message, context={'request': request}).data,
            status=status.HTTP_201_CREATED
        )
        
    except Exception as e:
        logger.error(f"Error processing audio upload: {e}")
        return Response(
            {'error': 'Failed to process audio'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def chat_stats(request):
    """Get user's chat statistics."""
    
    user = request.user
    
    # Calculate statistics
    total_conversations = Conversation.objects.filter(user=user).count()
    active_conversations = Conversation.objects.filter(
        user=user,
        is_active=True,
        is_archived=False
    ).count()
    total_messages = Message.objects.filter(
        conversation__user=user,
        is_deleted=False
    ).count()
    user_messages = Message.objects.filter(
        conversation__user=user,
        sender_type='user',
        is_deleted=False
    ).count()
    ai_messages = Message.objects.filter(
        conversation__user=user,
        sender_type='assistant',
        is_deleted=False
    ).count()
    
    # Get recent activity
    recent_conversations = Conversation.objects.filter(
        user=user,
        is_archived=False
    ).order_by('-last_message_at')[:5]
    
    stats = {
        'total_conversations': total_conversations,
        'active_conversations': active_conversations,
        'total_messages': total_messages,
        'user_messages': user_messages,
        'ai_messages': ai_messages,
        'recent_conversations': ConversationSerializer(
            recent_conversations,
            many=True,
            context={'request': request}
        ).data
    }
    
    return Response(stats)


class ConversationArchiveView(generics.UpdateAPIView):
    """Archive or unarchive a conversation."""
    
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Conversation.objects.filter(user=self.request.user)
    
    def update(self, request, *args, **kwargs):
        conversation = self.get_object()
        
        # Toggle archive status if not specified
        archive = request.data.get('archive')
        if archive is None:
            conversation.is_archived = not conversation.is_archived
        else:
            conversation.is_archived = archive
            
        conversation.save(update_fields=['is_archived'])
        
        serializer = self.get_serializer(conversation)
        return Response(serializer.data)


class ConversationBulkDeleteView(generics.GenericAPIView):
    """Bulk delete (archive) multiple conversations."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        conversation_ids = request.data.get('conversation_ids', [])
        permanent = request.data.get('permanent', False)
        
        if not conversation_ids:
            return Response(
                {'error': 'No conversation IDs provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Get conversations owned by the user
        conversations = Conversation.objects.filter(
            id__in=conversation_ids,
            user=request.user
        )
        
        if permanent:
            # Permanently delete conversations
            count, _ = conversations.delete()
        else:
            # Archive conversations
            count = conversations.update(is_archived=True)
            
        return Response({
            'message': f"{count} conversations {'deleted' if permanent else 'archived'}",
            'count': count
        })


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def search_conversations(request):
    """Search conversations and messages."""
    
    query = request.GET.get('q', '').strip()
    if not query:
        return Response(
            {'error': 'Query parameter is required'},
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Include archived conversations in search if specified
    include_archived = request.GET.get('include_archived', 'false').lower() == 'true'
    
    # Build base query
    conversation_query = Q(user=request.user)
    if not include_archived:
        conversation_query &= Q(is_archived=False)
    
    # Add search criteria
    conversation_query &= (
        Q(title__icontains=query) |
        Q(messages__content__icontains=query)
    )
    
    # Search in conversation titles and message content
    conversations = Conversation.objects.filter(conversation_query).distinct().order_by('-last_message_at')[:20]
    
    # Search in messages
    messages = Message.objects.filter(
        Q(conversation__user=request.user) &
        Q(is_deleted=False) &
        Q(content__icontains=query)
    ).order_by('-created_at')[:50]
    
    return Response({
        'conversations': ConversationSerializer(
            conversations,
            many=True,
            context={'request': request}
        ).data,
        'messages': MessageSerializer(
            messages,
            many=True,
            context={'request': request}
        ).data
    })

class RelationshipView(generics.RetrieveAPIView):
    """Get user's relationship with AI companion."""
    
    serializer_class = RelationshipSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        relationship, created = Relationship.objects.get_or_create(
            user=self.request.user
        )
        return relationship


class UserRelationshipView(generics.RetrieveAPIView):
    """Get detailed user-AI relationship."""
    
    serializer_class = UserRelationshipSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        relationship, created = UserRelationship.objects.get_or_create(
            user=self.request.user
        )
        return relationship


class EmotionContextListView(generics.ListAPIView):
    """List emotion contexts for the user."""
    
    serializer_class = EmotionContextSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Get optional filters
        conversation_id = self.request.query_params.get('conversation_id')
        message_id = self.request.query_params.get('message_id')
        limit = self.request.query_params.get('limit', 20)
        
        try:
            limit = int(limit)
            if limit > 100:  # Cap at 100 records
                limit = 100
        except ValueError:
            limit = 20
            
        # Start with base query
        queryset = EmotionContext.objects.filter(user=self.request.user)
        
        # Apply filters
        if conversation_id:
            queryset = queryset.filter(conversation_id=conversation_id)
        if message_id:
            queryset = queryset.filter(message_id=message_id)
            
        # Order by creation time and limit results
        return queryset.order_by('-created_at')[:limit]


class EmotionContextDetailView(generics.RetrieveAPIView):
    """Get details of a specific emotion context."""
    
    serializer_class = EmotionContextSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return EmotionContext.objects.filter(user=self.request.user)