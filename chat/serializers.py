from rest_framework import serializers
from .models import Conversation, Message, MessageReaction, ChatSession, VoiceSettings, Relationship
from .models_realtime import UserRelationship, EmotionContext, StreamingSession, PerformanceMetrics


class ConversationSerializer(serializers.ModelSerializer):
    """Serializer for conversation objects."""
    
    message_count = serializers.IntegerField(source='get_message_count', read_only=True)
    last_message_preview = serializers.SerializerMethodField()
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    
    class Meta:
        model = Conversation
        fields = [
            'id', 'user', 'title', 'is_active', 'is_archived',
            'created_at', 'updated_at', 'last_message_at',
            'message_count', 'last_message_preview'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at', 'last_message_at']
    
    def get_last_message_preview(self, obj):
        """Get preview of the last message."""
        last_message = obj.messages.order_by('-created_at').first()
        if last_message:
            return {
                'content': last_message.content[:100] + '...' if len(last_message.content) > 100 else last_message.content,
                'sender_type': last_message.sender_type,
                'created_at': last_message.created_at,
                'message_type': last_message.message_type
            }
        return None


class MessageSerializer(serializers.ModelSerializer):
    """Serializer for message objects."""
    
    reaction_count = serializers.SerializerMethodField()
    user_reaction = serializers.SerializerMethodField()
    emotion_data = serializers.SerializerMethodField()
    
    class Meta:
        model = Message
        fields = [
            'id', 'conversation', 'sender_type', 'message_type', 'content',
            'media_url', 'audio_file', 'audio_duration', 'transcription',
            'emotion', 'processing_time', 'model_used', 'tokens_used',
            'is_edited', 'is_deleted', 'created_at', 'updated_at',
            'reaction_count', 'user_reaction', 'emotion_data', 'metadata'
        ]
        read_only_fields = [
            'id', 'processing_time', 'model_used', 'tokens_used',
            'created_at', 'updated_at', 'emotion_data'
        ]
    
    def get_reaction_count(self, obj):
        """Get count of reactions by type."""
        reactions = obj.reactions.all()
        return {
            'like': reactions.filter(reaction_type='like').count(),
            'dislike': reactions.filter(reaction_type='dislike').count(),
            'love': reactions.filter(reaction_type='love').count(),
            'helpful': reactions.filter(reaction_type='helpful').count(),
            'not_helpful': reactions.filter(reaction_type='not_helpful').count(),
        }
    
    def get_user_reaction(self, obj):
        """Get current user's reaction to this message."""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            reaction = obj.reactions.filter(user=request.user).first()
            return reaction.reaction_type if reaction else None
        return None
        
    def get_emotion_data(self, obj):
        """Get emotion context data if available."""
        # Get the most recent emotion context for this message
        emotion_context = obj.emotion_contexts.first()
        if emotion_context:
            return {
                'primary_emotion': emotion_context.primary_emotion,
                'intensity': emotion_context.emotion_intensity,
                'valence': emotion_context.emotion_valence,
                'arousal': emotion_context.emotion_arousal,
                'confidence': emotion_context.confidence_score
            }
        # Fall back to simple emotion field if no context
        elif obj.emotion:
            return {
                'primary_emotion': obj.emotion,
                'intensity': None,
                'valence': None,
                'arousal': None,
                'confidence': None
            }
        return None


class MessageCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating messages."""
    
    class Meta:
        model = Message
        fields = [
            'conversation', 'sender_type', 'message_type', 'content', 
            'audio_file', 'media_url', 'emotion', 'metadata'
        ]
    
    def validate_conversation(self, value):
        """Ensure user owns the conversation."""
        request = self.context.get('request')
        if request and value.user != request.user:
            raise serializers.ValidationError("You don't have permission to add messages to this conversation.")
        return value
        
    def validate(self, data):
        """Validate message data based on message type."""
        message_type = data.get('message_type', 'text')
        
        # For audio messages, ensure audio_file is provided
        if message_type == 'audio' or message_type == 'voice':
            if not data.get('audio_file') and not data.get('media_url'):
                raise serializers.ValidationError(
                    {"audio_file": "Audio file is required for audio/voice messages."}
                )
        
        # For image messages, ensure media_url is provided
        if message_type == 'image':
            if not data.get('media_url'):
                raise serializers.ValidationError(
                    {"media_url": "Media URL is required for image messages."}
                )
                
        return data


class MessageReactionSerializer(serializers.ModelSerializer):
    """Serializer for message reactions."""
    
    class Meta:
        model = MessageReaction
        fields = ['id', 'reaction_type', 'created_at']
        read_only_fields = ['id', 'created_at']


class ChatSessionSerializer(serializers.ModelSerializer):
    """Serializer for chat sessions."""
    
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = ChatSession
        fields = [
            'id', 'conversation', 'is_active', 'connected_at',
            'last_activity', 'disconnected_at', 'duration'
        ]
        read_only_fields = ['id', 'connected_at', 'last_activity', 'disconnected_at']
    
    def get_duration(self, obj):
        """Calculate session duration."""
        end_time = obj.disconnected_at or obj.last_activity
        if end_time and obj.connected_at:
            return (end_time - obj.connected_at).total_seconds()
        return None


class VoiceSettingsSerializer(serializers.ModelSerializer):
    """Serializer for voice settings."""
    
    class Meta:
        model = VoiceSettings
        fields = [
            'voice_model', 'voice_name', 'speech_speed',
            'language', 'auto_transcribe', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def validate_speech_speed(self, value):
        """Validate speech speed is within acceptable range."""
        if not (0.25 <= value <= 4.0):
            raise serializers.ValidationError("Speech speed must be between 0.25 and 4.0")
        return value


class ConversationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating conversations."""
    
    class Meta:
        model = Conversation
        fields = ['title']
    
    def create(self, validated_data):
        """Create conversation for the current user."""
        request = self.context.get('request')
        validated_data['user'] = request.user
        return super().create(validated_data)


class AudioUploadSerializer(serializers.Serializer):
    """Serializer for audio file uploads."""
    
    audio_file = serializers.FileField()
    conversation_id = serializers.UUIDField()
    message_type = serializers.ChoiceField(
        choices=['audio', 'voice'],
        default='voice'
    )
    
    def validate_audio_file(self, value):
        """Validate audio file."""
        # Check file size
        if value.size > 10 * 1024 * 1024:  # 10MB
            raise serializers.ValidationError("Audio file too large. Maximum size is 10MB.")
        
        # Check file type
        allowed_types = ['audio/wav', 'audio/mp3', 'audio/m4a', 'audio/ogg', 'audio/mpeg', 'audio/webm']
        if value.content_type not in allowed_types:
            raise serializers.ValidationError(f"Unsupported audio format: {value.content_type}. Supported formats: {', '.join(allowed_types)}")
        
        return value


class RelationshipSerializer(serializers.ModelSerializer):
    """Serializer for relationship model."""
    
    class Meta:
        model = Relationship
        fields = [
            'level', 'xp', 'xp_to_next_level', 
            'last_interaction', 'created_at', 'updated_at'
        ]
        read_only_fields = ['level', 'xp', 'xp_to_next_level', 'last_interaction', 'created_at', 'updated_at']


class UserRelationshipSerializer(serializers.ModelSerializer):
    """Serializer for detailed user-AI relationship."""
    
    class Meta:
        model = UserRelationship
        fields = [
            'relationship_level', 'total_interactions', 'total_conversation_time',
            'emotional_intimacy_score', 'average_emotion_intensity',
            'progression_milestones', 'last_milestone_date',
            'relationship_started', 'last_interaction', 'last_level_change'
        ]
        read_only_fields = [
            'relationship_level', 'total_interactions', 'total_conversation_time',
            'emotional_intimacy_score', 'average_emotion_intensity',
            'progression_milestones', 'last_milestone_date',
            'relationship_started', 'last_interaction', 'last_level_change'
        ]


class EmotionContextSerializer(serializers.ModelSerializer):
    """Serializer for emotion context data."""
    
    class Meta:
        model = EmotionContext
        fields = [
            'id', 'primary_emotion', 'emotion_intensity',
            'emotion_valence', 'emotion_arousal', 'confidence_score',
            'audio_emotions', 'text_emotions', 'context_emotions',
            'created_at'
        ]
        read_only_fields = [
            'id', 'primary_emotion', 'emotion_intensity',
            'emotion_valence', 'emotion_arousal', 'confidence_score',
            'audio_emotions', 'text_emotions', 'context_emotions',
            'created_at'
        ]
