from django.urls import path
from django.conf import settings
from django.conf.urls.static import static
from . import views
from .views_upload import AvatarUploadView, AudioUploadView
from .views_media import MediaFileView, DirectMediaServeView

app_name = 'chat'

urlpatterns = [
    # Conversation endpoints
    path('conversations/', views.ConversationListCreateView.as_view(), name='conversation-list'),
    path('conversations/<uuid:pk>/', views.ConversationDetailView.as_view(), name='conversation-detail'),
    path('conversations/<uuid:conversation_id>/messages/', views.ConversationMessagesView.as_view(), name='conversation-messages'),
    path('conversations/<uuid:pk>/archive/', views.ConversationArchiveView.as_view(), name='conversation-archive'),
    path('conversations/bulk-delete/', views.ConversationBulkDeleteView.as_view(), name='conversation-bulk-delete'),
    
    # Message endpoints
    path('messages/', views.MessageCreateView.as_view(), name='message-create'),
    path('messages/<uuid:pk>/', views.MessageDetailView.as_view(), name='message-detail'),
    path('messages/<uuid:message_id>/reaction/', views.MessageReactionView.as_view(), name='message-reaction'),
    path('messages/bulk-delete/', views.MessageBulkDeleteView.as_view(), name='message-bulk-delete'),
    path('typing/', views.typing_indicator, name='typing-indicator'),
    
    # Voice and audio endpoints
    path('voice/settings/', views.VoiceSettingsView.as_view(), name='voice-settings'),
    path('voice/upload/', views.upload_audio, name='audio-upload'),
    
    # Relationship endpoints
    path('relationship/', views.RelationshipView.as_view(), name='relationship'),
    path('relationship/detailed/', views.UserRelationshipView.as_view(), name='user-relationship'),
    
    # Emotion endpoints
    path('emotions/', views.EmotionContextListView.as_view(), name='emotion-list'),
    path('emotions/<uuid:pk>/', views.EmotionContextDetailView.as_view(), name='emotion-detail'),
    
    # Session and stats endpoints
    path('sessions/', views.ChatSessionListView.as_view(), name='chat-sessions'),
    path('stats/', views.chat_stats, name='chat-stats'),
    path('search/', views.search_conversations, name='message-search'),
    
    # File upload endpoints
    path('upload/avatar/', AvatarUploadView.as_view(), name='avatar-upload'),
    path('upload/audio/', AudioUploadView.as_view(), name='audio-upload-class'),
    
    # File serving endpoints
    path('media/<str:file_id>/', MediaFileView.as_view(), name='media-file'),
]

# Add direct media serving URLs only when not using B2 storage
if not settings.USE_B2_STORAGE:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    # Add a fallback for direct media serving
    urlpatterns.append(path('direct-media/<path:path>/', DirectMediaServeView.as_view(), name='direct-media'))
