from django.contrib import admin
from .models import Conversation, Message, MessageReaction, ChatSession, VoiceSettings, Relationship
from .models_realtime import UserRelationship, EmotionContext, StreamingSession, PerformanceMetrics

# Register your models here.
@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'title', 'is_active', 'created_at', 'last_message_at')
    list_filter = ('is_active', 'is_archived', 'created_at')
    search_fields = ('title', 'user__email')
    date_hierarchy = 'created_at'


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'conversation', 'sender_type', 'message_type', 'emotion', 'created_at')
    list_filter = ('sender_type', 'message_type', 'emotion', 'created_at')
    search_fields = ('content', 'conversation__title')
    date_hierarchy = 'created_at'
    readonly_fields = ('metadata',)


@admin.register(Relationship)
class RelationshipAdmin(admin.ModelAdmin):
    list_display = ('user', 'level', 'xp', 'xp_to_next_level', 'last_interaction')
    list_filter = ('level', 'created_at')
    search_fields = ('user__email',)
    date_hierarchy = 'created_at'


@admin.register(UserRelationship)
class UserRelationshipAdmin(admin.ModelAdmin):
    list_display = ('user', 'relationship_level', 'total_interactions', 'emotional_intimacy_score')
    list_filter = ('relationship_level', 'relationship_started')
    search_fields = ('user__email',)
    date_hierarchy = 'relationship_started'
