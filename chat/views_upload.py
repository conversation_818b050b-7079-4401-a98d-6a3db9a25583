"""
Views for file uploads in the chat app.
"""
import logging
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from .utils.file_utils import (
    save_uploaded_file,
    ALLOWED_IMAGE_TYPES,
    ALLOWED_AUDIO_TYPES,
    MAX_IMAGE_SIZE,
    get_file_url,
)

logger = logging.getLogger(__name__)

class AvatarUploadView(APIView):
    """
    View for uploading user avatars.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        """
        Handle POST requests to upload an avatar.
        """
        if 'file' not in request.FILES:
            return Response(
                {'error': 'No file provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        avatar_file = request.FILES['file']
        
        # Save the avatar file
        success, result = save_uploaded_file(
            file=avatar_file,
            directory='avatars',
            allowed_types=ALLOWED_IMAGE_TYPES,
            max_size=MAX_IMAGE_SIZE,
            prefix=f'avatar_{request.user.id}'
        )
        
        if not success:
            return Response(
                {'error': result},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the file URL
        file_url = get_file_url(result)
        
        # Update the user's avatar URL
        user = request.user
        user.avatar_url = file_url
        user.save(update_fields=['avatar_url'])
        
        return Response({
            'avatar_url': file_url
        }, status=status.HTTP_201_CREATED)


class AudioUploadView(APIView):
    """
    View for uploading audio messages.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        """
        Handle POST requests to upload an audio file.
        """
        if 'file' not in request.FILES:
            return Response(
                {'error': 'No file provided'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        audio_file = request.FILES['file']
        conversation_id = request.data.get('conversation_id')
        
        if not conversation_id:
            return Response(
                {'error': 'Conversation ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Save the audio file
        success, result = save_uploaded_file(
            file=audio_file,
            directory='audio',
            allowed_types=ALLOWED_AUDIO_TYPES,
            max_size=settings.MAX_AUDIO_FILE_SIZE,
            prefix=f'audio_{conversation_id}'
        )
        
        if not success:
            return Response(
                {'error': result},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the file URL
        file_url = get_file_url(result)
        
        # Return the file URL
        return Response({
            'audio_url': file_url,
            'file_path': result
        }, status=status.HTTP_201_CREATED)