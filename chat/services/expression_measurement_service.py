"""
Expression Measurement Service
Integrates Hume AI Expression Measurement for real-time emotion detection from audio.
"""
import asyncio
import base64
import json
import time
import logging
import websockets
import ssl
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from django.conf import settings
from datetime import datetime, timedelta
from collections import defaultdict

logger = logging.getLogger(__name__)

# Import actor-critic system (will be added after critic service is created)
# from .emotion_critic_service import emotion_critic_service, ConversationContext


@dataclass
class ExpressionResult:
    """Result from expression measurement analysis."""
    emotions: List[Dict[str, float]]
    dominant_emotion: str
    confidence: float
    processing_time_ms: float
    chunk_id: str
    timestamp: float
    prosody_data: Optional[Dict[str, Any]] = None  # Speech prosody measurements


@dataclass
class SessionEmotionData:
    """Individual emotion detection data for session aggregation."""
    chunk_id: str
    timestamp: float
    emotions: List[Dict[str, float]]
    dominant_emotion: str
    confidence: float
    prosody_data: Optional[Dict[str, Any]]
    processing_time_ms: float
    text: Optional[str] = None  # Text content for actor-critic learning


@dataclass
class AggregatedEmotionProfile:
    """Aggregated emotion profile for a user session."""
    session_id: str
    user_id: str

    # Aggregated emotion scores (weighted averages)
    aggregated_emotions: Dict[str, float]
    dominant_emotion: str
    overall_confidence: float

    # Emotion patterns and trends
    emotion_frequency: Dict[str, int]  # How often each emotion appears
    recent_trend: str  # Recent emotional direction (improving/declining/stable)

    # Session statistics
    total_interactions: int
    first_interaction: float
    last_interaction: float
    session_duration_minutes: float

    # Raw data for analysis
    interaction_history: List[SessionEmotionData]

    # Prosody aggregates
    avg_prosody_metrics: Optional[Dict[str, float]]


class ExpressionMeasurementService:
    """
    Service for real-time expression measurement using Hume AI WebSocket API.
    Processes audio chunks and returns emotion analysis.
    """
    
    def __init__(self):
        self.api_key = getattr(settings, 'HUME_API_KEY', None)
        self.websocket_url = "wss://api.hume.ai/v0/stream/models"
        self.timeout = getattr(settings, 'HUME_TIMEOUT', 30.0)
        self.connection_pool = {}  # Pool of WebSocket connections
        self.max_connections = 5
        self.mock_mode = getattr(settings, 'HUME_MOCK_MODE', False)  # For testing
        self._active_websocket = None  # Persistent WebSocket connection
        self._websocket_lock = asyncio.Lock()  # Thread safety for WebSocket
        self._connection_pool = []  # Pool of WebSocket connections
        self._pool_lock = asyncio.Lock()  # Thread safety for pool
        self._emotion_cache = {}  # Cache for similar audio patterns
        self._cache_lock = asyncio.Lock()  # Thread safety for cache

        # Session-based emotion aggregation
        self._session_emotion_data = {}  # session_id -> List[SessionEmotionData]
        self._session_aggregated_profiles = {}  # session_id -> AggregatedEmotionProfile
        self._session_locks = {}  # session_id -> asyncio.Lock for thread safety
        self._session_cache_lock = asyncio.Lock()  # For managing session locks

        # Fix SSL certificate issues on macOS
        self._setup_ssl_context()
        
        # Performance tracking
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_processing_time_ms': 0,
            'connection_count': 0
        }
        
        if not self.api_key:
            logger.warning("HUME_API_KEY not found in settings. Expression measurement disabled.")
        else:
            logger.info("ExpressionMeasurementService initialized with Hume AI integration")

    def _setup_ssl_context(self):
        """Setup SSL context to fix certificate verification issues on macOS."""
        import os
        import ssl
        import certifi

        try:
            # Set SSL certificate file environment variable
            os.environ['SSL_CERT_FILE'] = certifi.where()
            os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()

            # Also try to set the default SSL context
            ssl._create_default_https_context = ssl._create_unverified_context

            logger.debug("SSL context configured with certifi certificates")

        except Exception as e:
            logger.warning(f"Failed to setup SSL context: {e}")
            # Fallback: disable SSL verification (not recommended for production)
            ssl._create_default_https_context = ssl._create_unverified_context

    def _preprocess_audio(self, audio_data: bytes) -> bytes:
        """Preprocess audio for optimal expression measurement."""
        try:
            # Only preprocess very large audio files (>5 seconds)
            if len(audio_data) < 160000:  # ~5 seconds at 16kHz, 16-bit
                logger.debug(f"Audio size {len(audio_data)} bytes - no preprocessing needed")
                return audio_data

            # For very large audio, take the middle portion (most likely to contain clear speech)
            # Keep more audio to ensure emotion detection works
            start_offset = len(audio_data) // 6  # Skip first 16%
            end_offset = 5 * len(audio_data) // 6  # Take until 84%

            # Ensure we have at least 2 seconds of audio (64000 bytes at 16kHz, 16-bit)
            min_size = 64000
            if end_offset - start_offset < min_size:
                # If the middle section is too small, take from the beginning
                end_offset = min(start_offset + min_size, len(audio_data))
                if end_offset - start_offset < min_size:
                    # If still too small, just use original audio
                    logger.debug("Audio too small after preprocessing, using original")
                    return audio_data

            processed_audio = audio_data[start_offset:end_offset]

            logger.debug(f"Audio preprocessed: {len(audio_data)} → {len(processed_audio)} bytes")
            return processed_audio

        except Exception as e:
            logger.warning(f"Audio preprocessing failed: {e}, using original audio")
            return audio_data

    def _get_audio_hash(self, audio_data: bytes) -> str:
        """Generate a hash for audio data to enable caching."""
        import hashlib

        # Use a sample of the audio for hashing (faster than full audio)
        sample_size = min(1024, len(audio_data))
        sample = audio_data[:sample_size]

        return hashlib.md5(sample).hexdigest()[:16]

    async def _get_session_lock(self, session_id: str) -> asyncio.Lock:
        """Get or create a lock for a specific session."""
        async with self._session_cache_lock:
            if session_id not in self._session_locks:
                self._session_locks[session_id] = asyncio.Lock()
            return self._session_locks[session_id]

    async def _get_cached_result(self, audio_hash: str, chunk_id: str, start_time: float) -> Optional[ExpressionResult]:
        """Get cached result for similar audio pattern."""
        async with self._cache_lock:
            if audio_hash in self._emotion_cache:
                cached_data = self._emotion_cache[audio_hash]

                # Check if cache entry is still fresh (within 1 hour)
                if time.time() - cached_data['timestamp'] < 3600:
                    processing_time = (time.time() - start_time) * 1000

                    # Create new result with current timing
                    result = ExpressionResult(
                        emotions=cached_data['emotions'],
                        dominant_emotion=cached_data['dominant_emotion'],
                        confidence=cached_data['confidence'],
                        processing_time_ms=processing_time,
                        chunk_id=chunk_id,
                        timestamp=time.time()
                    )

                    # Update stats
                    self.stats['successful_requests'] += 1
                    self._update_avg_processing_time(processing_time)

                    logger.debug(f"Cache hit for chunk {chunk_id} in {processing_time:.1f}ms")
                    return result
                else:
                    # Remove stale cache entry
                    del self._emotion_cache[audio_hash]

            return None

    async def _cache_result(self, audio_hash: str, result: ExpressionResult):
        """Cache result for future similar audio patterns."""
        async with self._cache_lock:
            # Limit cache size to prevent memory issues
            if len(self._emotion_cache) >= 100:
                # Remove oldest entry
                oldest_key = min(self._emotion_cache.keys(),
                               key=lambda k: self._emotion_cache[k]['timestamp'])
                del self._emotion_cache[oldest_key]

            # Cache the result
            self._emotion_cache[audio_hash] = {
                'emotions': result.emotions,
                'dominant_emotion': result.dominant_emotion,
                'confidence': result.confidence,
                'timestamp': time.time()
            }

            logger.debug(f"Cached result for audio hash {audio_hash}")

    async def _get_websocket_from_pool(self):
        """Get a WebSocket connection from the pool or create a new one."""
        async with self._pool_lock:
            # Try to get an existing connection from pool
            while self._connection_pool:
                websocket = self._connection_pool.pop(0)
                if not websocket.closed:
                    logger.debug("Reusing WebSocket connection from pool")
                    return websocket
                else:
                    logger.debug("Removing closed WebSocket from pool")

            # Create new connection if pool is empty
            return await self._create_new_websocket()

    async def _return_websocket_to_pool(self, websocket):
        """Return a WebSocket connection to the pool."""
        try:
            async with self._pool_lock:
                if not websocket.closed and len(self._connection_pool) < self.max_connections:
                    self._connection_pool.append(websocket)
                    logger.debug("Returned WebSocket connection to pool")
                else:
                    try:
                        if not websocket.closed:
                            await websocket.close()
                    except Exception as e:
                        logger.debug(f"Error closing WebSocket: {e}")
                    logger.debug("Closed WebSocket connection (pool full or connection closed)")
        except Exception as e:
            logger.error(f"Error returning WebSocket to pool: {e}")

    async def _create_new_websocket(self):
        """Create a new WebSocket connection to Hume API."""
        try:
            import websockets
            import json

            ws_url = "wss://api.hume.ai/v0/stream/models"
            headers = {"X-Hume-Api-Key": self.api_key}

            # Connect with shorter timeout for faster failure
            websocket = await asyncio.wait_for(
                websockets.connect(ws_url, extra_headers=headers),
                timeout=5.0  # Reduced from 10s to 5s
            )

            # Don't send separate config - include models with data in single message

            logger.debug("Created new WebSocket connection to Hume API")
            return websocket

        except Exception as e:
            logger.error(f"Failed to create WebSocket connection: {e}")
            raise
    
    async def analyze_audio_chunk(self, audio_data: bytes, chunk_id: str, session_id: str = None, user_id: str = None, text: str = None) -> Optional[ExpressionResult]:
        """
        Analyze audio chunk for emotional expressions using Hume AI with session aggregation.
        Optionally includes text for multi-modal emotion detection.

        Args:
            audio_data: Raw audio bytes
            chunk_id: Unique identifier for the chunk
            session_id: Session ID for emotion aggregation
            user_id: User ID for session tracking
            text: Transcribed text for multi-modal analysis (optional)

        Returns:
            ExpressionResult with emotion analysis or None if failed
        """
        if not self.api_key and not self.mock_mode:
            logger.debug("Expression measurement disabled - no API key")
            return None

        start_time = time.time()
        self.stats['total_requests'] += 1

        # Mock mode for testing latency without API calls
        if self.mock_mode or not self.api_key:
            return await self._mock_analyze_audio_chunk(audio_data, chunk_id, start_time)

        try:
            # Optimization 1: Preprocess audio for faster processing
            processed_audio = self._preprocess_audio(audio_data)

            # Optimization 2: Skip caching for multi-modal testing accuracy
            # TODO: Re-enable caching after multi-modal testing is complete
            # audio_hash = self._get_audio_hash(processed_audio)
            # cached_result = await self._get_cached_result(audio_hash, chunk_id, start_time)
            # if cached_result:
            #     return cached_result

            # Optimization 3: Use connection pool for better performance
            import json

            # Encode processed audio to base64
            audio_b64 = base64.b64encode(processed_audio).decode('utf-8')

            # Get WebSocket connection from pool
            websocket = await self._get_websocket_from_pool()

            try:
                # Multi-modal analysis: send separate messages for audio and text
                if text and text.strip():
                    logger.debug(f"Sending multi-modal analysis (audio + text): '{text[:50]}...'")

                    # Send audio message first
                    audio_message = {
                        "models": {
                            "prosody": {}
                        },
                        "data": audio_b64
                    }

                    await asyncio.wait_for(
                        websocket.send(json.dumps(audio_message)),
                        timeout=3.0
                    )

                    audio_response_text = await asyncio.wait_for(
                        websocket.recv(),
                        timeout=10.0
                    )

                    # Send text message second with enhanced preprocessing
                    enhanced_text = self._enhance_text_for_emotion_detection(text.strip())
                    text_message = {
                        "models": {
                            "language": {}
                        },
                        "raw_text": True,
                        "data": enhanced_text
                    }

                    await asyncio.wait_for(
                        websocket.send(json.dumps(text_message)),
                        timeout=3.0
                    )

                    text_response_text = await asyncio.wait_for(
                        websocket.recv(),
                        timeout=10.0
                    )

                    # Combine both responses
                    audio_data = json.loads(audio_response_text)
                    text_data = json.loads(text_response_text)

                    # Merge the responses
                    result_data = {}
                    if 'prosody' in audio_data:
                        result_data['prosody'] = audio_data['prosody']
                    if 'language' in text_data:
                        result_data['language'] = text_data['language']

                    logger.debug(f"Multi-modal response combined: prosody={bool('prosody' in result_data)}, language={bool('language' in result_data)}")

                else:
                    # Audio-only message
                    audio_message = {
                        "models": {
                            "prosody": {}
                        },
                        "data": audio_b64
                    }
                    logger.debug(f"Sending audio-only analysis")

                    await asyncio.wait_for(
                        websocket.send(json.dumps(audio_message)),
                        timeout=3.0
                    )

                    response_text = await asyncio.wait_for(
                        websocket.recv(),
                        timeout=10.0
                    )

                    result_data = json.loads(response_text)

                # Debug: log the raw response
                logger.debug(f"Hume WebSocket response for chunk {chunk_id}: {result_data}")

                # Extract emotion data
                emotions = self._extract_emotions(result_data, session_id)

                if emotions:
                    # Find dominant emotion
                    dominant_emotion = max(emotions, key=lambda x: x['score'])

                    processing_time = (time.time() - start_time) * 1000

                    # Extract prosody data if available
                    prosody_data = self._extract_prosody_data(result_data)

                    result = ExpressionResult(
                        emotions=emotions,
                        dominant_emotion=dominant_emotion['name'],
                        confidence=dominant_emotion['score'],
                        processing_time_ms=processing_time,
                        chunk_id=chunk_id,
                        timestamp=time.time(),
                        prosody_data=prosody_data
                    )

                    # Skip caching for accurate multi-modal testing
                    # await self._cache_result(audio_hash, result)

                    # Add to session aggregation if session info provided
                    if session_id and user_id:
                        await self._add_to_session_aggregation(result, session_id, user_id, text)

                    # Actor-Critic Learning Integration (if text provided for context)
                    if text and session_id and user_id:
                        await self._apply_actor_critic_learning(result, text, session_id, user_id)

                    # Update stats
                    self.stats['successful_requests'] += 1
                    self._update_avg_processing_time(processing_time)

                    logger.debug(f"Optimized expression analysis completed for chunk {chunk_id} in {processing_time:.1f}ms")

                    # Return connection to pool
                    await self._return_websocket_to_pool(websocket)

                    return result
                else:
                    logger.warning(f"No emotions detected in audio chunk {chunk_id}")
                    self.stats['failed_requests'] += 1

                    # Return connection to pool even on failure
                    await self._return_websocket_to_pool(websocket)

                    return None

            except Exception as e:
                # Return connection to pool on error
                try:
                    await self._return_websocket_to_pool(websocket)
                except:
                    pass  # Don't let pool errors mask the original error

                logger.error(f"WebSocket communication error for chunk {chunk_id}: {e}")
                self.stats['failed_requests'] += 1
                return None
        
        except asyncio.TimeoutError:
            logger.warning(f"Expression measurement timeout for chunk {chunk_id}")
            self.stats['failed_requests'] += 1
            return None
        except Exception as e:
            logger.error(f"Error in expression measurement for chunk {chunk_id}: {e}")
            self.stats['failed_requests'] += 1
            return None

    def _write_wav_header(self, file, data_length: int, sample_rate: int, channels: int, bits_per_sample: int):
        """Write WAV file header."""
        import struct

        # WAV file header
        file.write(b'RIFF')
        file.write(struct.pack('<I', 36 + data_length))  # File size - 8
        file.write(b'WAVE')
        file.write(b'fmt ')
        file.write(struct.pack('<I', 16))  # Subchunk1Size
        file.write(struct.pack('<H', 1))   # AudioFormat (PCM)
        file.write(struct.pack('<H', channels))
        file.write(struct.pack('<I', sample_rate))
        file.write(struct.pack('<I', sample_rate * channels * bits_per_sample // 8))  # ByteRate
        file.write(struct.pack('<H', channels * bits_per_sample // 8))  # BlockAlign
        file.write(struct.pack('<H', bits_per_sample))
        file.write(b'data')
        file.write(struct.pack('<I', data_length))

    async def _mock_analyze_audio_chunk(self, audio_data: bytes, chunk_id: str, start_time: float) -> ExpressionResult:
        """Mock expression analysis for testing latency without API calls."""
        import random

        # Simulate realistic processing time (50-120ms)
        processing_delay = random.uniform(0.05, 0.12)  # 50-120ms
        await asyncio.sleep(processing_delay)

        # Mock emotion data based on audio characteristics
        emotions = [
            {'name': 'Joy', 'score': random.uniform(0.1, 0.8)},
            {'name': 'Sadness', 'score': random.uniform(0.05, 0.3)},
            {'name': 'Anger', 'score': random.uniform(0.02, 0.2)},
            {'name': 'Fear', 'score': random.uniform(0.01, 0.15)},
            {'name': 'Surprise', 'score': random.uniform(0.05, 0.4)},
            {'name': 'Disgust', 'score': random.uniform(0.01, 0.1)},
            {'name': 'Contempt', 'score': random.uniform(0.01, 0.1)},
            {'name': 'Excitement', 'score': random.uniform(0.1, 0.6)},
            {'name': 'Calmness', 'score': random.uniform(0.2, 0.7)},
            {'name': 'Concentration', 'score': random.uniform(0.1, 0.5)}
        ]

        # Find dominant emotion
        dominant_emotion = max(emotions, key=lambda x: x['score'])

        processing_time = (time.time() - start_time) * 1000

        result = ExpressionResult(
            emotions=emotions,
            dominant_emotion=dominant_emotion['name'],
            confidence=dominant_emotion['score'],
            processing_time_ms=processing_time,
            chunk_id=chunk_id,
            timestamp=time.time()
        )

        # Update stats
        self.stats['successful_requests'] += 1
        self._update_avg_processing_time(processing_time)

        logger.debug(f"Mock expression analysis completed for chunk {chunk_id} in {processing_time:.1f}ms")
        return result
    
    def _extract_emotions(self, result_data: Dict[str, Any], session_id: str = None) -> List[Dict[str, Any]]:
        """Extract emotion data from Hume AI response (multi-modal: prosody + language)."""
        try:
            logger.debug(f"Extracting emotions from result_data keys: {list(result_data.keys())}")

            prosody_emotions = []
            language_emotions = []

            # Extract prosody emotions (from audio)
            if 'prosody' in result_data:
                prosody_data = result_data['prosody']
                logger.debug(f"Prosody data keys: {list(prosody_data.keys()) if isinstance(prosody_data, dict) else type(prosody_data)}")

                predictions = prosody_data.get('predictions', [])
                logger.debug(f"Found {len(predictions)} prosody predictions")

                if predictions:
                    first_prediction = predictions[0]
                    prosody_emotions = first_prediction.get('emotions', [])
                    logger.debug(f"Found {len(prosody_emotions)} prosody emotions")

            # Extract language emotions (from text) - aggregate across all word predictions
            if 'language' in result_data:
                language_data = result_data['language']
                logger.debug(f"Language data keys: {list(language_data.keys()) if isinstance(language_data, dict) else type(language_data)}")

                predictions = language_data.get('predictions', [])
                logger.debug(f"Found {len(predictions)} language predictions")

                if predictions:
                    # Aggregate emotions across all word predictions
                    emotion_totals = {}
                    for prediction in predictions:
                        word_emotions = prediction.get('emotions', [])
                        for emotion in word_emotions:
                            name = emotion['name']
                            score = emotion['score']
                            if name in emotion_totals:
                                emotion_totals[name] += score
                            else:
                                emotion_totals[name] = score

                    # Convert to list format
                    language_emotions = [{'name': name, 'score': score} for name, score in emotion_totals.items()]
                    language_emotions.sort(key=lambda x: x['score'], reverse=True)
                    logger.debug(f"Aggregated {len(language_emotions)} language emotions from {len(predictions)} word predictions")

            # Combine and weight emotions from both modalities
            if prosody_emotions and language_emotions:
                logger.debug(f"MULTI-MODAL: Found prosody emotions: {len(prosody_emotions)}, language emotions: {len(language_emotions)}")
                logger.debug(f"Top prosody: {prosody_emotions[0]['name']} ({prosody_emotions[0]['score']:.3f})")
                logger.debug(f"Top language: {language_emotions[0]['name']} ({language_emotions[0]['score']:.3f})")

                combined_emotions = self._combine_multimodal_emotions(prosody_emotions, language_emotions, session_id)
                logger.debug(f"Combined result: {combined_emotions[0]['name']} ({combined_emotions[0]['score']:.3f}) if combined_emotions else 'None'")
                return combined_emotions
            elif prosody_emotions:
                logger.debug(f"Using prosody-only emotions: {len(prosody_emotions)}")
                return prosody_emotions
            elif language_emotions:
                logger.debug(f"Using language-only emotions: {len(language_emotions)}")
                return language_emotions

            # Try alternative response structures
            if hasattr(result_data, 'prosody') and result_data.prosody:
                predictions = getattr(result_data.prosody, 'predictions', [])
                if predictions and len(predictions) > 0:
                    emotions = getattr(predictions[0], 'emotions', [])
                    if emotions:
                        # Convert to dict format if needed
                        emotion_list = []
                        for emotion in emotions:
                            if hasattr(emotion, 'name') and hasattr(emotion, 'score'):
                                emotion_list.append({'name': emotion.name, 'score': emotion.score})
                            elif isinstance(emotion, dict):
                                emotion_list.append(emotion)
                        return emotion_list

            logger.debug("No emotions found in response")
            return []

        except Exception as e:
            logger.error(f"Error extracting emotions from response: {e}")
            return []

    def _combine_multimodal_emotions(self, prosody_emotions: List[Dict], language_emotions: List[Dict], session_id: str = None) -> List[Dict]:
        """
        Advanced multi-modal emotion combination with actor-critic dynamic weighting.
        """
        try:
            # Get dynamic weights from actor-critic system
            if session_id:
                LANGUAGE_WEIGHT, PROSODY_WEIGHT = self._get_dynamic_weights_for_session(session_id)
            else:
                # Fallback to adaptive weighting based on confidence levels
                language_avg_score = sum(e['score'] for e in language_emotions[:5]) / min(5, len(language_emotions)) if language_emotions else 0
                prosody_avg_score = sum(e['score'] for e in prosody_emotions[:5]) / min(5, len(prosody_emotions)) if prosody_emotions else 0

                # Adaptive weighting: favor the modality with higher confidence
                if language_avg_score > prosody_avg_score * 2:
                    LANGUAGE_WEIGHT = 0.85  # Language is much more confident
                    PROSODY_WEIGHT = 0.15
                elif prosody_avg_score > language_avg_score * 2:
                    LANGUAGE_WEIGHT = 0.4   # Prosody is much more confident
                    PROSODY_WEIGHT = 0.6
                else:
                    LANGUAGE_WEIGHT = 0.7   # Default: language slightly favored
                    PROSODY_WEIGHT = 0.3

            # Create emotion dictionaries
            prosody_dict = {emotion['name']: emotion['score'] for emotion in prosody_emotions}
            language_dict = {emotion['name']: emotion['score'] for emotion in language_emotions}

            # Emotion family mapping for better semantic matching
            emotion_families = self._get_emotion_families()

            # Get all unique emotion names
            all_emotions = set(prosody_dict.keys()) | set(language_dict.keys())

            combined_emotions = []
            for emotion_name in all_emotions:
                prosody_score = prosody_dict.get(emotion_name, 0.0)
                language_score = language_dict.get(emotion_name, 0.0)

                # Check for family matches if direct match is weak
                if prosody_score == 0.0:
                    prosody_score = self._find_family_score(emotion_name, prosody_dict, emotion_families)
                if language_score == 0.0:
                    language_score = self._find_family_score(emotion_name, language_dict, emotion_families)

                # Weighted combination with confidence boost
                combined_score = (language_score * LANGUAGE_WEIGHT) + (prosody_score * PROSODY_WEIGHT)

                # Boost score if both modalities agree strongly
                if prosody_score > 0.5 and language_score > 0.5:
                    combined_score *= 1.3  # Strong agreement boost
                elif prosody_score > 0.1 and language_score > 0.1:
                    combined_score *= 1.1  # Moderate agreement boost

                # Confidence threshold filtering
                if language_score > 2.0:  # Very high language confidence
                    combined_score *= 1.2
                elif language_score > 1.0:  # High language confidence
                    combined_score *= 1.1

                # Only include emotions with meaningful scores
                if combined_score > 0.01:
                    combined_emotions.append({
                        'name': emotion_name,
                        'score': combined_score,
                        'prosody_score': prosody_score,
                        'language_score': language_score,
                        'weights_used': f"L:{LANGUAGE_WEIGHT:.2f}/P:{PROSODY_WEIGHT:.2f}"
                    })

            # Sort by combined score (highest first)
            combined_emotions.sort(key=lambda x: x['score'], reverse=True)

            logger.debug(f"Advanced multi-modal: {len(combined_emotions)} emotions, "
                        f"weights: L:{LANGUAGE_WEIGHT:.2f}/P:{PROSODY_WEIGHT:.2f}, "
                        f"top: {combined_emotions[0]['name'] if combined_emotions else 'None'} "
                        f"({combined_emotions[0]['score']:.3f})" if combined_emotions else "")

            return combined_emotions

        except Exception as e:
            logger.error(f"Error in advanced multi-modal combination: {e}")
            return language_emotions if language_emotions else prosody_emotions

    def _get_emotion_families(self) -> Dict[str, List[str]]:
        """Get comprehensive emotion families for semantic matching - ENHANCED TAXONOMY."""
        return {
            # Joy family
            "Joy": ["Joy", "Happiness", "Excitement", "Enthusiasm", "Ecstasy", "Satisfaction", "Contentment"],
            "Excitement": ["Excitement", "Joy", "Enthusiasm", "Anticipation", "Thrill"],
            "Satisfaction": ["Satisfaction", "Contentment", "Joy", "Pride", "Accomplishment"],

            # Anger family - ENHANCED
            "Anger": ["Anger", "Rage", "Fury", "Annoyance", "Irritation", "Frustration", "Resentment"],
            "Annoyance": ["Annoyance", "Irritation", "Anger", "Frustration"],
            "Frustration": ["Frustration", "Annoyance", "Anger", "Irritation"],
            "Resentment": ["Resentment", "Anger", "Bitterness", "Grudge", "Indignation"],  # ADDED
            "Rage": ["Rage", "Fury", "Anger", "Wrath", "Outrage"],

            # Sadness family
            "Sadness": ["Sadness", "Sorrow", "Grief", "Melancholy", "Disappointment", "Distress"],
            "Disappointment": ["Disappointment", "Sadness", "Letdown", "Dissatisfaction"],
            "Distress": ["Distress", "Anguish", "Sadness", "Anxiety", "Worry"],
            "Pain": ["Pain", "Anguish", "Suffering", "Distress", "Hurt"],  # ADDED

            # Fear family
            "Fear": ["Fear", "Terror", "Fright", "Anxiety", "Worry", "Panic"],
            "Anxiety": ["Anxiety", "Worry", "Fear", "Nervousness", "Apprehension"],
            "Panic": ["Panic", "Terror", "Fear", "Anxiety"],

            # Surprise family
            "Surprise (positive)": ["Surprise (positive)", "Amazement", "Wonder", "Astonishment"],
            "Surprise (negative)": ["Surprise (negative)", "Shock", "Startle"],

            # Cognitive family - ENHANCED
            "Interest": ["Interest", "Curiosity", "Fascination", "Engagement"],
            "Curiosity": ["Curiosity", "Interest", "Wonder", "Inquisitiveness"],
            "Concentration": ["Concentration", "Focus", "Attention", "Contemplation"],
            "Contemplation": ["Contemplation", "Reflection", "Thoughtfulness", "Concentration"],
            "Realization": ["Realization", "Understanding", "Insight", "Comprehension"],
            "Flow": ["Flow", "Immersion", "Concentration", "Engagement", "Focus"],  # ADDED

            # Achievement family
            "Pride": ["Pride", "Accomplishment", "Achievement", "Satisfaction", "Triumph"],
            "Triumph": ["Triumph", "Victory", "Success", "Pride", "Accomplishment"],
            "Determination": ["Determination", "Resolve", "Persistence", "Commitment"],

            # Social family - ENHANCED
            "Gratitude": ["Gratitude", "Thankfulness", "Appreciation", "Recognition"],
            "Love": ["Love", "Affection", "Adoration", "Fondness"],
            "Admiration": ["Admiration", "Respect", "Appreciation", "Esteem"],
            "Empathy": ["Empathy", "Compassion", "Understanding", "Sympathy"],  # ADDED
            "Vulnerability": ["Vulnerability", "Openness", "Exposure", "Sensitivity"],  # ADDED

            # Calm family
            "Calmness": ["Calmness", "Serenity", "Peace", "Tranquility", "Relaxation"],
            "Relief": ["Relief", "Ease", "Comfort", "Calmness"],
            "Contentment": ["Contentment", "Satisfaction", "Peace", "Fulfillment"],

            # Negative social family
            "Embarrassment": ["Embarrassment", "Shame", "Awkwardness", "Humiliation"],
            "Shame": ["Shame", "Embarrassment", "Guilt", "Humiliation"],
            "Awkwardness": ["Awkwardness", "Embarrassment", "Discomfort"],

            # Creative/Aesthetic family - ADDED
            "Inspiration": ["Inspiration", "Creativity", "Motivation", "Enthusiasm"],
            "Aesthetic Appreciation": ["Aesthetic Appreciation", "Beauty", "Wonder", "Admiration"],

            # Energy/Vitality family - ADDED
            "Tiredness": ["Tiredness", "Fatigue", "Exhaustion", "Weariness"],
            "Boredom": ["Boredom", "Tedium", "Disinterest", "Apathy"],

            # Complex emotions - ADDED
            "Hope": ["Hope", "Optimism", "Anticipation", "Expectation"],
            "Despair": ["Despair", "Hopelessness", "Dejection", "Despondency"],
            "Confusion": ["Confusion", "Bewilderment", "Perplexity", "Uncertainty"],
            "Disgust": ["Disgust", "Revulsion", "Aversion", "Repugnance"],
            "Envy": ["Envy", "Jealousy", "Resentment", "Covetousness"],
            "Nostalgia": ["Nostalgia", "Longing", "Wistfulness", "Reminiscence"]
        }

    def _find_family_score(self, target_emotion: str, emotion_dict: Dict[str, float], families: Dict[str, List[str]]) -> float:
        """Find the best family match score for an emotion."""
        if target_emotion in families:
            family_emotions = families[target_emotion]
            family_scores = [emotion_dict.get(emotion, 0.0) for emotion in family_emotions]
            return max(family_scores) * 0.8  # Slight penalty for family match vs exact match
        return 0.0

    def _enhance_text_for_emotion_detection(self, text: str) -> str:
        """Enhance text to improve emotion detection accuracy."""
        if not text:
            return text

        # Emotion amplification keywords
        emotion_amplifiers = {
            # Intensity amplifiers
            "really": "extremely",
            "very": "incredibly",
            "quite": "tremendously",
            "pretty": "remarkably",
            "so": "overwhelmingly",

            # Emotional context enhancers
            "happy": "joyful and elated",
            "sad": "deeply saddened",
            "angry": "furious and outraged",
            "scared": "terrified and fearful",
            "excited": "thrilled and exhilarated",
            "worried": "anxious and distressed",
            "proud": "triumphant and accomplished",
            "grateful": "deeply thankful",
            "confused": "bewildered and perplexed",
            "disappointed": "devastated and let down",
            "embarrassed": "mortified and ashamed",
            "calm": "peaceful and serene",
            "bored": "utterly disinterested",
            "surprised": "shocked and amazed"
        }

        enhanced_text = text.lower()

        # Apply emotion amplifiers
        for original, enhanced in emotion_amplifiers.items():
            if original in enhanced_text:
                enhanced_text = enhanced_text.replace(original, enhanced)

        # Add emotional context markers
        emotional_markers = {
            "i feel": "I am experiencing deep feelings of",
            "i'm": "I am genuinely",
            "this is": "this situation is making me feel",
            "i can't": "I am overwhelmed and cannot",
            "i don't": "I am confused and don't"
        }

        for marker, replacement in emotional_markers.items():
            if marker in enhanced_text:
                enhanced_text = enhanced_text.replace(marker, replacement)

        # Preserve original capitalization for proper nouns
        words = text.split()
        enhanced_words = enhanced_text.split()

        # Restore original length if needed
        if len(enhanced_words) > len(words) * 2:  # Prevent over-expansion
            return text

        return enhanced_text.capitalize()

    async def _apply_actor_critic_learning(
        self,
        result: ExpressionResult,
        text: str,
        session_id: str,
        user_id: str
    ):
        """Apply actor-critic learning to improve emotion detection accuracy."""
        try:
            # Import here to avoid circular imports
            from .emotion_critic_service import emotion_critic_service, ConversationContext

            # Get conversation context
            session_data = self._session_emotion_data.get(session_id, [])
            previous_messages = []
            session_emotions = []

            if session_data:
                # Get recent messages and emotions for context
                recent_data = session_data[-5:]  # Last 5 interactions
                previous_messages = [data.text for data in recent_data if data.text]
                session_emotions = [
                    {"emotion": data.dominant_emotion, "confidence": data.confidence}
                    for data in recent_data
                ]

            # Determine conversation stage
            conversation_stage = "opening"
            if len(session_data) > 5:
                conversation_stage = "middle"
            elif len(session_data) > 15:
                conversation_stage = "closing"

            # Calculate user engagement (based on response patterns)
            user_engagement = self._calculate_user_engagement(session_data)

            # Build conversation context
            context = ConversationContext(
                current_message=text,
                previous_messages=previous_messages,
                detected_emotion=result.dominant_emotion,
                confidence=result.confidence,
                user_response_time=None,  # Could be added from frontend
                conversation_stage=conversation_stage,
                session_emotions=session_emotions,
                user_engagement_level=user_engagement
            )

            # Get critic evaluation (async, non-blocking)
            async with emotion_critic_service:
                feedback = await emotion_critic_service.evaluate_emotion_detection(
                    context, user_id, session_id
                )

            # Apply learning adjustments to future predictions
            self._apply_critic_feedback(feedback, session_id)

            logger.debug(f"Actor-critic learning applied: accuracy={feedback.accuracy_score:.2f}, "
                        f"processing_time={feedback.processing_time_ms:.1f}ms")

        except Exception as e:
            logger.warning(f"Actor-critic learning failed (non-critical): {e}")
            # Don't fail the main emotion detection if critic fails

    def _calculate_user_engagement(self, session_data: List) -> float:
        """Calculate user engagement level based on interaction patterns."""
        if not session_data:
            return 0.5  # Neutral engagement

        # Factors for engagement calculation
        recent_data = session_data[-5:]

        # Confidence trend (higher confidence = better engagement detection)
        confidences = [data.confidence for data in recent_data]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.5

        # Interaction frequency (more interactions = higher engagement)
        interaction_score = min(1.0, len(session_data) / 10.0)

        # Emotional variety (more varied emotions = higher engagement)
        emotions = [data.dominant_emotion for data in recent_data]
        unique_emotions = len(set(emotions))
        variety_score = min(1.0, unique_emotions / 3.0)

        # Weighted engagement score
        engagement = (avg_confidence * 0.4 + interaction_score * 0.3 + variety_score * 0.3)
        return max(0.1, min(1.0, engagement))

    def _apply_critic_feedback(self, feedback, session_id: str):
        """Apply critic feedback to improve future predictions."""
        # Store feedback for this session
        if not hasattr(self, '_session_critic_feedback'):
            self._session_critic_feedback = {}

        if session_id not in self._session_critic_feedback:
            self._session_critic_feedback[session_id] = []

        self._session_critic_feedback[session_id].append({
            "timestamp": time.time(),
            "accuracy_score": feedback.accuracy_score,
            "weight_adjustments": feedback.weight_adjustments,
            "emotion_suggestions": feedback.emotion_mapping_suggestions
        })

        # Keep only recent feedback (last 20 interactions)
        self._session_critic_feedback[session_id] = self._session_critic_feedback[session_id][-20:]

    def _get_dynamic_weights_for_session(self, session_id: str) -> Tuple[float, float]:
        """Get dynamic weights based on critic feedback for this session."""
        try:
            from .emotion_critic_service import emotion_critic_service

            # Get current weights from critic service
            current_weights = emotion_critic_service.get_current_weights()
            language_weight = current_weights.get("language_weight", 0.7)
            prosody_weight = current_weights.get("prosody_weight", 0.3)

            # Apply session-specific adjustments if available
            if hasattr(self, '_session_critic_feedback') and session_id in self._session_critic_feedback:
                recent_feedback = self._session_critic_feedback[session_id][-3:]  # Last 3 feedbacks

                # Calculate average weight adjustments
                language_adjustments = []
                prosody_adjustments = []

                for feedback in recent_feedback:
                    weight_adj = feedback.get("weight_adjustments", {})
                    if "language_delta" in weight_adj:
                        language_adjustments.append(weight_adj["language_delta"])
                    if "prosody_delta" in weight_adj:
                        prosody_adjustments.append(weight_adj["prosody_delta"])

                # Apply average adjustments
                if language_adjustments:
                    avg_lang_adj = sum(language_adjustments) / len(language_adjustments)
                    language_weight = max(0.1, min(0.9, language_weight + avg_lang_adj * 0.1))

                if prosody_adjustments:
                    avg_pros_adj = sum(prosody_adjustments) / len(prosody_adjustments)
                    prosody_weight = max(0.1, min(0.9, prosody_weight + avg_pros_adj * 0.1))

                # Ensure weights sum to 1.0
                total = language_weight + prosody_weight
                language_weight /= total
                prosody_weight /= total

            return language_weight, prosody_weight

        except Exception as e:
            logger.debug(f"Error getting dynamic weights, using defaults: {e}")
            return 0.7, 0.3  # Default weights

    def _extract_prosody_data(self, result_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract prosody data from Hume AI response."""
        try:
            if 'prosody' in result_data:
                prosody_data = result_data['prosody']
                predictions = prosody_data.get('predictions', [])

                if predictions:
                    first_prediction = predictions[0]

                    # Extract prosody metrics if available
                    prosody_metrics = {}

                    # Look for prosody-specific data (pitch, tone, rhythm, etc.)
                    if 'prosody' in first_prediction:
                        prosody_info = first_prediction['prosody']

                        # Extract available prosody measurements
                        for key in ['pitch', 'tone', 'rhythm', 'pace', 'volume', 'clarity']:
                            if key in prosody_info:
                                prosody_metrics[key] = prosody_info[key]

                    # Extract timing information
                    if 'time' in first_prediction:
                        prosody_metrics['timing'] = first_prediction['time']

                    # Extract confidence scores for prosody
                    if 'confidence' in first_prediction:
                        prosody_metrics['prosody_confidence'] = first_prediction['confidence']

                    return prosody_metrics if prosody_metrics else None

            return None

        except Exception as e:
            logger.debug(f"Error extracting prosody data: {e}")
            return None

    async def _add_to_session_aggregation(self, result: ExpressionResult, session_id: str, user_id: str, text: str = None):
        """Add emotion result to session aggregation and update profile."""
        session_lock = await self._get_session_lock(session_id)

        async with session_lock:
            # Create session emotion data entry
            session_data = SessionEmotionData(
                chunk_id=result.chunk_id,
                timestamp=result.timestamp,
                emotions=result.emotions,
                dominant_emotion=result.dominant_emotion,
                confidence=result.confidence,
                prosody_data=result.prosody_data,
                processing_time_ms=result.processing_time_ms,
                text=text
            )

            # Add to session data
            if session_id not in self._session_emotion_data:
                self._session_emotion_data[session_id] = []

            self._session_emotion_data[session_id].append(session_data)

            # Update aggregated profile
            await self._update_aggregated_profile(session_id, user_id)

            logger.debug(f"Added emotion data to session {session_id}: {result.dominant_emotion} ({result.confidence:.2f})")

    async def _update_aggregated_profile(self, session_id: str, user_id: str):
        """Update the aggregated emotion profile for a session."""
        try:
            session_data_list = self._session_emotion_data.get(session_id, [])

            if not session_data_list:
                return

            # Calculate aggregated emotions with recency weighting
            aggregated_emotions = self._calculate_weighted_emotions(session_data_list)

            # Find dominant emotion from aggregated scores
            dominant_emotion = max(aggregated_emotions.items(), key=lambda x: x[1])[0]

            # Calculate overall confidence (weighted average)
            total_weight = 0
            weighted_confidence = 0

            for i, data in enumerate(session_data_list):
                weight = self._get_recency_weight(i, len(session_data_list))
                weighted_confidence += data.confidence * weight
                total_weight += weight

            overall_confidence = weighted_confidence / total_weight if total_weight > 0 else 0

            # Calculate emotion frequency
            emotion_frequency = {}
            for data in session_data_list:
                emotion = data.dominant_emotion
                emotion_frequency[emotion] = emotion_frequency.get(emotion, 0) + 1

            # Determine recent trend
            recent_trend = self._calculate_emotion_trend(session_data_list)

            # Calculate session statistics
            first_interaction = session_data_list[0].timestamp
            last_interaction = session_data_list[-1].timestamp
            session_duration = (last_interaction - first_interaction) / 60  # minutes

            # Calculate average prosody metrics
            avg_prosody = self._calculate_avg_prosody(session_data_list)

            # Create aggregated profile
            profile = AggregatedEmotionProfile(
                session_id=session_id,
                user_id=user_id,
                aggregated_emotions=aggregated_emotions,
                dominant_emotion=dominant_emotion,
                overall_confidence=overall_confidence,
                emotion_frequency=emotion_frequency,
                recent_trend=recent_trend,
                total_interactions=len(session_data_list),
                first_interaction=first_interaction,
                last_interaction=last_interaction,
                session_duration_minutes=session_duration,
                interaction_history=session_data_list.copy(),
                avg_prosody_metrics=avg_prosody
            )

            # Store the profile
            self._session_aggregated_profiles[session_id] = profile

            logger.debug(f"Updated aggregated profile for session {session_id}: {dominant_emotion} "
                        f"({overall_confidence:.2f}) - {len(session_data_list)} interactions")

        except Exception as e:
            logger.error(f"Error updating aggregated profile for session {session_id}: {e}")

    def _calculate_weighted_emotions(self, session_data_list: List[SessionEmotionData]) -> Dict[str, float]:
        """Calculate weighted average emotions with recency bias."""
        emotion_totals = {}
        total_weights = {}

        for i, data in enumerate(session_data_list):
            weight = self._get_recency_weight(i, len(session_data_list))

            for emotion_data in data.emotions:
                emotion_name = emotion_data.get('name', '')
                emotion_score = emotion_data.get('score', 0)

                if emotion_name:
                    if emotion_name not in emotion_totals:
                        emotion_totals[emotion_name] = 0
                        total_weights[emotion_name] = 0

                    emotion_totals[emotion_name] += emotion_score * weight
                    total_weights[emotion_name] += weight

        # Calculate weighted averages
        weighted_emotions = {}
        for emotion_name in emotion_totals:
            if total_weights[emotion_name] > 0:
                weighted_emotions[emotion_name] = emotion_totals[emotion_name] / total_weights[emotion_name]

        return weighted_emotions

    def _get_recency_weight(self, index: int, total_count: int) -> float:
        """Calculate recency weight for emotion data with EXPONENTIAL DECAY (more recent = higher weight)."""
        if total_count <= 1:
            return 1.0

        # EXPONENTIAL DECAY: newer interactions get exponentially higher weight
        # This fixes the arc similarity issue by letting old emotions fade properly
        turns_ago = total_count - 1 - index  # How many turns ago this was (0 = most recent)
        decay_factor = 0.8  # Each turn back reduces weight by 20%

        # Exponential decay: weight = decay_factor ^ turns_ago
        weight = decay_factor ** turns_ago

        # Ensure minimum weight to prevent complete loss of historical context
        min_weight = 0.1
        weight = max(min_weight, weight)

        logger.debug(f"Recency weight for index {index}/{total_count}: turns_ago={turns_ago}, weight={weight:.3f}")
        return weight

    def _calculate_emotion_trend(self, session_data_list: List[SessionEmotionData]) -> str:
        """Calculate recent emotion trend direction."""
        if len(session_data_list) < 3:
            return "stable"

        # Look at last 3 interactions vs previous interactions
        recent_interactions = session_data_list[-3:]
        earlier_interactions = session_data_list[:-3] if len(session_data_list) > 3 else []

        if not earlier_interactions:
            return "stable"

        # Calculate average confidence for recent vs earlier
        recent_avg_confidence = sum(data.confidence for data in recent_interactions) / len(recent_interactions)
        earlier_avg_confidence = sum(data.confidence for data in earlier_interactions) / len(earlier_interactions)

        # Analyze dominant emotions
        recent_emotions = [data.dominant_emotion for data in recent_interactions]
        earlier_emotions = [data.dominant_emotion for data in earlier_interactions]

        # Simple trend analysis based on confidence and emotion consistency
        confidence_diff = recent_avg_confidence - earlier_avg_confidence

        # Check for positive vs negative emotion trends
        positive_emotions = {'Joy', 'Excitement', 'Contentment', 'Calmness', 'Interest', 'Satisfaction'}
        negative_emotions = {'Anger', 'Sadness', 'Fear', 'Frustration', 'Anxiety', 'Disgust'}

        recent_positive = sum(1 for emotion in recent_emotions if emotion in positive_emotions)
        recent_negative = sum(1 for emotion in recent_emotions if emotion in negative_emotions)

        earlier_positive = sum(1 for emotion in earlier_emotions if emotion in positive_emotions)
        earlier_negative = sum(1 for emotion in earlier_emotions if emotion in negative_emotions)

        # Determine trend
        if recent_positive > earlier_positive and recent_negative < earlier_negative:
            return "improving"
        elif recent_positive < earlier_positive and recent_negative > earlier_negative:
            return "declining"
        elif abs(confidence_diff) > 0.1:
            return "improving" if confidence_diff > 0 else "declining"
        else:
            return "stable"

    def _calculate_avg_prosody(self, session_data_list: List[SessionEmotionData]) -> Optional[Dict[str, float]]:
        """Calculate average prosody metrics across session."""
        prosody_totals = {}
        prosody_counts = {}

        for data in session_data_list:
            if data.prosody_data:
                for key, value in data.prosody_data.items():
                    if isinstance(value, (int, float)):
                        if key not in prosody_totals:
                            prosody_totals[key] = 0
                            prosody_counts[key] = 0

                        prosody_totals[key] += value
                        prosody_counts[key] += 1

        if not prosody_totals:
            return None

        # Calculate averages
        avg_prosody = {}
        for key in prosody_totals:
            if prosody_counts[key] > 0:
                avg_prosody[key] = prosody_totals[key] / prosody_counts[key]

        return avg_prosody if avg_prosody else None

    async def get_session_emotion_profile(self, session_id: str) -> Optional[AggregatedEmotionProfile]:
        """Get the aggregated emotion profile for a session."""
        session_lock = await self._get_session_lock(session_id)

        async with session_lock:
            return self._session_aggregated_profiles.get(session_id)

    async def get_session_emotion_context(self, session_id: str) -> Dict[str, Any]:
        """Get emotion context in the format expected by fast response service."""
        profile = await self.get_session_emotion_profile(session_id)

        if not profile:
            return {}

        # Format for backward compatibility with existing emotion context
        context = {
            'session_emotion_profile': {
                'dominant_emotion': profile.dominant_emotion,
                'confidence': profile.overall_confidence,
                'aggregated_emotions': profile.aggregated_emotions,
                'emotion_frequency': profile.emotion_frequency,
                'recent_trend': profile.recent_trend,
                'total_interactions': profile.total_interactions,
                'session_duration_minutes': profile.session_duration_minutes,
                'source': 'hume_session_aggregated'
            }
        }

        # Add prosody data if available
        if profile.avg_prosody_metrics:
            context['session_emotion_profile']['avg_prosody'] = profile.avg_prosody_metrics

        # Add recent emotion data for immediate context
        if profile.interaction_history:
            recent_interaction = profile.interaction_history[-1]
            context['recent_emotion'] = {
                'dominant_emotion': recent_interaction.dominant_emotion,
                'confidence': recent_interaction.confidence,
                'timestamp': recent_interaction.timestamp,
                'source': 'hume_recent'
            }

        return context

    async def clear_session_data(self, session_id: str):
        """Clear session emotion data (for session cleanup)."""
        session_lock = await self._get_session_lock(session_id)

        async with session_lock:
            if session_id in self._session_emotion_data:
                del self._session_emotion_data[session_id]

            if session_id in self._session_aggregated_profiles:
                del self._session_aggregated_profiles[session_id]

            logger.debug(f"Cleared session emotion data for session {session_id}")

        # Clean up the lock
        async with self._session_cache_lock:
            if session_id in self._session_locks:
                del self._session_locks[session_id]
    
    def _update_avg_processing_time(self, processing_time: float):
        """Update average processing time."""
        current_avg = self.stats['avg_processing_time_ms']
        total_successful = self.stats['successful_requests']
        
        if total_successful == 1:
            self.stats['avg_processing_time_ms'] = processing_time
        else:
            # Calculate running average
            self.stats['avg_processing_time_ms'] = (
                (current_avg * (total_successful - 1) + processing_time) / total_successful
            )
    
    async def analyze_audio_batch(self, audio_chunks: List[tuple]) -> List[ExpressionResult]:
        """
        Analyze multiple audio chunks in parallel with optimizations.

        Args:
            audio_chunks: List of (audio_data, chunk_id) tuples

        Returns:
            List of ExpressionResult objects
        """
        if not audio_chunks:
            return []

        # Optimization: Limit concurrent connections to prevent overwhelming the API
        semaphore = asyncio.Semaphore(min(self.max_connections, len(audio_chunks)))

        async def process_chunk_with_semaphore(audio_data, chunk_id):
            async with semaphore:
                return await self.analyze_audio_chunk(audio_data, chunk_id)

        # Create tasks for parallel processing with semaphore
        tasks = []
        for audio_data, chunk_id in audio_chunks:
            task = asyncio.create_task(
                process_chunk_with_semaphore(audio_data, chunk_id)
            )
            tasks.append(task)

        # Wait for all tasks to complete with timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=30.0  # 30 second timeout for batch
            )
        except asyncio.TimeoutError:
            logger.warning("Batch processing timeout, cancelling remaining tasks")
            for task in tasks:
                if not task.done():
                    task.cancel()
            results = [task.result() if task.done() else None for task in tasks]

        # Filter out None results and exceptions
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, ExpressionResult):
                valid_results.append(result)
            elif isinstance(result, Exception):
                logger.error(f"Error in batch processing chunk {i}: {result}")
            elif result is None:
                logger.warning(f"No result for batch chunk {i}")

        logger.info(f"Batch processing completed: {len(valid_results)}/{len(audio_chunks)} successful")
        return valid_results
    
    def get_emotion_context(self, expression_result: ExpressionResult) -> Dict[str, Any]:
        """
        Convert expression result to emotion context for fast response service.
        
        Args:
            expression_result: Result from expression analysis
            
        Returns:
            Emotion context dictionary
        """
        if not expression_result:
            return {}
        
        return {
            'emotions': expression_result.emotions,
            'dominant_emotion': expression_result.dominant_emotion,
            'confidence': expression_result.confidence,
            'source': 'hume_prosody',
            'timestamp': expression_result.timestamp,
            'processing_time_ms': expression_result.processing_time_ms
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get enhanced performance statistics for monitoring."""
        total_requests = self.stats['total_requests']
        success_rate = (
            (self.stats['successful_requests'] / total_requests * 100)
            if total_requests > 0 else 0
        )

        # Get cache statistics
        cache_size = len(self._emotion_cache)
        cache_hit_rate = getattr(self, '_cache_hits', 0) / max(total_requests, 1) * 100

        # Get connection pool statistics
        pool_size = len(self._connection_pool)

        # Session aggregation statistics
        active_sessions = len(self._session_emotion_data)
        total_session_interactions = sum(len(data) for data in self._session_emotion_data.values())

        return {
            'total_requests': total_requests,
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate_percent': success_rate,
            'avg_processing_time_ms': self.stats['avg_processing_time_ms'],
            'api_enabled': self.api_key is not None,
            'cache_size': cache_size,
            'cache_hit_rate_percent': cache_hit_rate,
            'connection_pool_size': pool_size,
            'max_connections': self.max_connections,
            'optimizations_enabled': True,
            'session_aggregation': {
                'active_sessions': active_sessions,
                'total_session_interactions': total_session_interactions,
                'avg_interactions_per_session': total_session_interactions / max(active_sessions, 1)
            }
        }
    
    async def health_check(self) -> bool:
        """
        Perform a health check on the expression measurement service.
        
        Returns:
            True if service is healthy, False otherwise
        """
        if not self.api_key:
            return False
        
        try:
            # Create a small test audio chunk (silence)
            test_audio = b'\x00' * 1024  # 1KB of silence
            
            result = await self.analyze_audio_chunk(test_audio, "health_check")
            return result is not None
            
        except Exception as e:
            logger.error(f"Expression measurement health check failed: {e}")
            return False


# Global instance
expression_measurement_service = ExpressionMeasurementService()
