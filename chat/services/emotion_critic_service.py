#!/usr/bin/env python3
"""
Emotion Critic Service - Groq LLM-based Actor-Critic System
Real-time emotion detection accuracy evaluation and learning system.
"""
import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from django.conf import settings
from agents.services.memory_manager import MemoryManager
from chat.services.memory_cache_service import MemoryCacheService

logger = logging.getLogger(__name__)

# Groq Configuration
GROQ_API_KEY = "gsk_VcaXvkuAbmtrQTCI29D449EuMt2uYHWCFMs8rY4ior6zhFQ5"
GROQ_BASE_URL = "https://api.groq.com/openai/v1"
GROQ_MODEL = "llama-3.1-8b-instant"  # Fast, lightweight model for real-time evaluation

@dataclass
class EmotionCriticFeedback:
    """Feedback from the emotion critic system."""
    accuracy_score: float  # 0.0 to 1.0
    confidence_adjustment: float  # -1.0 to +1.0
    weight_adjustments: Dict[str, float]  # Modality weight changes
    emotion_mapping_suggestions: Dict[str, str]  # Better emotion mappings
    context_appropriateness: float  # 0.0 to 1.0
    reasoning: str  # Explanation of the assessment
    processing_time_ms: float

@dataclass
class MemoryContext:
    """Memory context for emotion evaluation."""
    relevant_memories: List[Dict[str, Any]]
    emotional_patterns: Dict[str, float]  # emotion -> frequency/strength
    relationship_context: List[Dict[str, Any]]  # memories about relationships
    emotional_triggers: List[Dict[str, Any]]  # known emotional triggers
    memory_relevance_scores: Dict[str, float]  # memory_id -> relevance score
    temporal_decay_factor: float  # how much to weight recent vs old memories

@dataclass
class ConversationContext:
    """Enhanced context for emotion evaluation with memory integration."""
    current_message: str
    previous_messages: List[str]
    detected_emotion: str
    confidence: float
    user_response_time: Optional[float]
    conversation_stage: str  # "opening", "middle", "closing"
    session_emotions: List[Dict[str, Any]]
    user_engagement_level: float  # 0.0 to 1.0
    memory_context: Optional[MemoryContext] = None  # Memory-enhanced context
    user_emotional_profile: Optional[Dict[str, Any]] = None  # Long-term emotional patterns

class EmotionCriticService:
    """
    Actor-Critic system for emotion detection improvement using Groq LLM.
    """
    
    def __init__(self):
        self.session = None
        self.learning_history = {}  # session_id -> learning data
        self.weight_adjustments = {
            "language_weight": 0.7,
            "prosody_weight": 0.3,
            "confidence_threshold": 1.0
        }
        self.emotion_mapping_improvements = {}
        self.accuracy_tracking = {}

        # MEMORY CONTROL FLAGS - Fix for noise injection
        self.enable_memory_features = getattr(settings, 'EMOTION_CRITIC_ENABLE_MEMORY', False)  # DISABLED by default
        self.memory_debug_mode = getattr(settings, 'EMOTION_CRITIC_MEMORY_DEBUG', False)

        # Memory integration
        self.memory_manager = MemoryManager()
        self.memory_cache_service = MemoryCacheService()
        self.user_emotional_profiles = {}  # user_id -> emotional profile

        # Performance tracking
        self.total_evaluations = 0
        self.avg_processing_time = 0.0
        self.accuracy_improvements = []

        memory_status = "ENABLED" if self.enable_memory_features else "DISABLED"
        logger.info(f"EmotionCriticService initialized with Groq LLM and Memory integration ({memory_status})")

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def evaluate_emotion_detection(
        self,
        context: ConversationContext,
        user_id: str,
        session_id: str
    ) -> EmotionCriticFeedback:
        """
        Enhanced emotion detection evaluation with memory integration.
        """
        start_time = time.time()

        try:
            # Conditionally retrieve relevant memories for this user
            if self.enable_memory_features:
                memory_context = await self._retrieve_relevant_memories(context, user_id)
                context.memory_context = memory_context

                # Get or build user emotional profile
                emotional_profile = await self._get_user_emotional_profile(user_id)
                context.user_emotional_profile = emotional_profile
            else:
                # Skip memory retrieval to prevent noise injection
                context.memory_context = None
                context.user_emotional_profile = None
                if self.memory_debug_mode:
                    logger.debug("Memory features disabled - skipping memory retrieval")

            # Build enhanced evaluation prompt with memory context
            prompt = self._build_critic_prompt(context)

            # Get Groq LLM evaluation
            evaluation = await self._query_groq_critic(prompt)

            # Parse evaluation results with memory considerations
            feedback = self._parse_critic_response(evaluation, start_time)

            # Apply memory-informed learning adjustments (only if memory enabled)
            if self.enable_memory_features:
                await self._apply_memory_informed_learning(feedback, user_id, session_id, context.memory_context)
                # Update user emotional profile based on this interaction
                await self._update_user_emotional_profile(user_id, context, feedback)

            # Track performance
            self._update_performance_metrics(feedback)

            if self.enable_memory_features and context.memory_context:
                logger.debug(f"Memory-enhanced critic evaluation: {feedback.accuracy_score:.2f} accuracy, "
                            f"{feedback.processing_time_ms:.1f}ms, "
                            f"{len(context.memory_context.relevant_memories)} memories considered")
            else:
                logger.debug(f"Basic critic evaluation: {feedback.accuracy_score:.2f} accuracy, "
                            f"{feedback.processing_time_ms:.1f}ms (memory disabled)")

            return feedback

        except Exception as e:
            logger.error(f"Error in memory-enhanced emotion critic evaluation: {e}")
            # Return neutral feedback on error
            return EmotionCriticFeedback(
                accuracy_score=0.5,
                confidence_adjustment=0.0,
                weight_adjustments={},
                emotion_mapping_suggestions={},
                context_appropriateness=0.5,
                reasoning="Memory-enhanced evaluation failed, using neutral feedback",
                processing_time_ms=(time.time() - start_time) * 1000
            )

    def _build_critic_prompt(self, context: ConversationContext) -> str:
        """Build the enhanced evaluation prompt with memory context for Groq LLM critic."""

        # Get conversation history context
        history_context = ""
        if context.previous_messages:
            recent_messages = context.previous_messages[-3:]  # Last 3 messages
            history_context = f"Recent conversation:\n" + "\n".join([f"- {msg}" for msg in recent_messages])

        # Get session emotion context
        emotion_context = ""
        if context.session_emotions:
            recent_emotions = context.session_emotions[-3:]
            emotion_context = f"Recent detected emotions: {[e['emotion'] for e in recent_emotions]}"

        # Build memory context section - CONDITIONALLY ENABLED
        memory_context_section = ""
        if self.enable_memory_features and context.memory_context and context.memory_context.relevant_memories:
            memory_context_section = self._build_memory_context_section(context.memory_context)
        elif not self.enable_memory_features and self.memory_debug_mode:
            memory_context_section = "MEMORY FEATURES: DISABLED (to prevent noise injection)\n"

        # Build emotional profile section - CONDITIONALLY ENABLED
        emotional_profile_section = ""
        if self.enable_memory_features and context.user_emotional_profile:
            emotional_profile_section = self._build_emotional_profile_section(context.user_emotional_profile)

        prompt = f"""You are an expert emotion detection critic with access to user memory and emotional patterns. Evaluate the accuracy of emotion detection for this conversation.

CURRENT ANALYSIS:
Message: "{context.current_message}"
Detected Emotion: {context.detected_emotion}
Confidence: {context.confidence:.2f}
Conversation Stage: {context.conversation_stage}

CONVERSATION CONTEXT:
{history_context}
{emotion_context}
User Engagement: {context.user_engagement_level:.2f}
Response Time: {context.user_response_time or 'N/A'}

{memory_context_section}

{emotional_profile_section}

EVALUATION CRITERIA:
1. Does the detected emotion match the message content and tone?
2. Is it contextually appropriate given the conversation flow?"""

        # Add memory-specific criteria only if memory features are enabled
        if self.enable_memory_features:
            prompt += """
3. Does it align with the user's historical emotional patterns?
4. Are there relevant memories that suggest a different emotional interpretation?
5. Does the emotion fit the user's known emotional triggers or relationship context?
6. Are there better emotion alternatives based on personal history?"""

        prompt += """

RESPOND WITH JSON:
{{
    "accuracy_score": 0.85,  // 0.0-1.0: How accurate is the detection?
    "confidence_adjustment": 0.1,  // -1.0 to +1.0: Should confidence be adjusted?
    "context_appropriateness": 0.9,  // 0.0-1.0: Contextual fit"""

        # Add memory-specific fields only if memory features are enabled
        if self.enable_memory_features:
            prompt += """,
    "memory_relevance": 0.8,  // 0.0-1.0: How well does this align with user's emotional history?"""

        prompt += """,
    "better_emotion": "alternative_emotion_name",  // If applicable
    "weight_suggestions": {{
        "increase_language": 0.1,  // -0.5 to +0.5: Adjust language weight
        "increase_prosody": -0.05   // -0.5 to +0.5: Adjust prosody weight"""

        if self.enable_memory_features:
            prompt += """,
        "memory_weight": 0.15  // 0.0-0.3: How much to weight memory context"""

        prompt += """
    }},
    "reasoning": "Brief explanation considering conversation"""

        if self.enable_memory_features:
            prompt += """, memory, and emotional patterns"""

        prompt += """"
}}

Focus on accurate emotion detection based on message content and conversation context."""

        return prompt

    def _build_memory_context_section(self, memory_context: MemoryContext) -> str:
        """Build the memory context section for the critic prompt."""
        if not memory_context.relevant_memories:
            return ""

        # Get top 3 most relevant memories
        top_memories = sorted(
            memory_context.relevant_memories,
            key=lambda m: memory_context.memory_relevance_scores.get(m.get('id', ''), 0),
            reverse=True
        )[:3]

        memory_section = "MEMORY CONTEXT:\n"

        # Add relevant memories
        for i, memory in enumerate(top_memories):
            memory_text = memory.get('text', '')[:100]  # Truncate for prompt
            memory_type = memory.get('memory_type', 'unknown')
            relevance = memory_context.memory_relevance_scores.get(memory.get('id', ''), 0)
            memory_section += f"  Memory {i+1} ({memory_type}, relevance: {relevance:.2f}): {memory_text}...\n"

        # Add emotional patterns
        if memory_context.emotional_patterns:
            top_patterns = sorted(memory_context.emotional_patterns.items(), key=lambda x: x[1], reverse=True)[:3]
            memory_section += f"Emotional Patterns: {dict(top_patterns)}\n"

        # Add relationship context
        if memory_context.relationship_context:
            memory_section += f"Relationship Context: {len(memory_context.relationship_context)} relevant relationships\n"

        # Add emotional triggers
        if memory_context.emotional_triggers:
            memory_section += f"Known Triggers: {len(memory_context.emotional_triggers)} potential triggers\n"

        memory_section += f"Temporal Decay Factor: {memory_context.temporal_decay_factor:.2f}\n"

        return memory_section

    def _build_emotional_profile_section(self, emotional_profile: Dict[str, Any]) -> str:
        """Build the emotional profile section for the critic prompt."""
        profile_section = "USER EMOTIONAL PROFILE:\n"

        # Add dominant emotions
        if 'dominant_emotions' in emotional_profile:
            profile_section += f"Dominant Emotions: {emotional_profile['dominant_emotions']}\n"

        # Add emotional stability
        if 'emotional_stability' in emotional_profile:
            profile_section += f"Emotional Stability: {emotional_profile['emotional_stability']:.2f}\n"

        # Add common emotional transitions
        if 'common_transitions' in emotional_profile:
            profile_section += f"Common Transitions: {emotional_profile['common_transitions']}\n"

        # Add emotional triggers
        if 'known_triggers' in emotional_profile:
            profile_section += f"Known Triggers: {emotional_profile['known_triggers']}\n"

        return profile_section

    async def _query_groq_critic(self, prompt: str) -> Dict[str, Any]:
        """Query Groq LLM for emotion detection evaluation (with mock fallback)."""

        # Mock critic evaluation for demonstration (replace with real Groq API when key is available)
        await asyncio.sleep(0.1)  # Simulate API call time

        # Extract emotion from prompt for intelligent mock response
        import re
        emotion_match = re.search(r'Detected Emotion: (\w+)', prompt)
        message_match = re.search(r'Message: "([^"]+)"', prompt)

        detected_emotion = emotion_match.group(1) if emotion_match else "unknown"
        message = message_match.group(1) if message_match else ""

        # Intelligent mock evaluation based on content
        accuracy_score = 0.75  # Default
        confidence_adjustment = 0.0
        context_appropriateness = 0.8
        better_emotion = None
        weight_suggestions = {}

        # Simple heuristic-based evaluation
        if "nervous" in message.lower() or "anxiety" in message.lower():
            if detected_emotion.lower() in ["anxiety", "fear", "worry"]:
                accuracy_score = 0.9
            else:
                accuracy_score = 0.4
                better_emotion = "Anxiety"
        elif "calm" in message.lower() or "breath" in message.lower():
            if detected_emotion.lower() in ["calmness", "peace", "serenity"]:
                accuracy_score = 0.95
            else:
                accuracy_score = 0.3
                better_emotion = "Calmness"
        elif "prepare" in message.lower() or "need to" in message.lower():
            if detected_emotion.lower() in ["determination", "resolve"]:
                accuracy_score = 0.9
            else:
                accuracy_score = 0.5
                better_emotion = "Determination"
        elif "relief" in message.lower() or "over" in message.lower():
            if detected_emotion.lower() in ["relief", "ease"]:
                accuracy_score = 0.95
            else:
                accuracy_score = 0.4
                better_emotion = "Relief"

        # Adjust weights based on accuracy
        if accuracy_score < 0.6:
            weight_suggestions = {
                "increase_language": 0.1,
                "increase_prosody": -0.05
            }

        return {
            "accuracy_score": accuracy_score,
            "confidence_adjustment": confidence_adjustment,
            "context_appropriateness": context_appropriateness,
            "better_emotion": better_emotion,
            "weight_suggestions": weight_suggestions,
            "reasoning": f"Mock evaluation: {detected_emotion} for '{message[:30]}...' scored {accuracy_score:.2f}"
        }

        # Real Groq API implementation (uncomment when valid API key is available)
        """
        if not self.session:
            self.session = aiohttp.ClientSession()

        headers = {
            "Authorization": f"Bearer {GROQ_API_KEY}",
            "Content-Type": "application/json"
        }

        data = {
            "model": GROQ_MODEL,
            "messages": [
                {
                    "role": "system",
                    "content": "You are an expert emotion detection critic. Provide accurate, concise evaluations in valid JSON format."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 300,
            "top_p": 0.9
        }

        async with self.session.post(
            f"{GROQ_BASE_URL}/chat/completions",
            headers=headers,
            json=data,
            timeout=aiohttp.ClientTimeout(total=5.0)
        ) as response:
            if response.status == 200:
                result = await response.json()
                content = result["choices"][0]["message"]["content"]

                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    import re
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        return json.loads(json_match.group())
                    else:
                        raise ValueError("No valid JSON found in response")
            else:
                error_text = await response.text()
                raise Exception(f"Groq API error {response.status}: {error_text}")
        """

    def _parse_critic_response(self, evaluation: Dict[str, Any], start_time: float) -> EmotionCriticFeedback:
        """Parse the critic response into structured feedback."""
        
        processing_time = (time.time() - start_time) * 1000
        
        # Extract weight adjustments
        weight_adjustments = {}
        if "weight_suggestions" in evaluation:
            suggestions = evaluation["weight_suggestions"]
            if "increase_language" in suggestions:
                weight_adjustments["language_delta"] = suggestions["increase_language"]
            if "increase_prosody" in suggestions:
                weight_adjustments["prosody_delta"] = suggestions["increase_prosody"]
        
        # Extract emotion mapping suggestions
        emotion_mapping = {}
        if "better_emotion" in evaluation and evaluation["better_emotion"]:
            emotion_mapping["suggested_alternative"] = evaluation["better_emotion"]
        
        return EmotionCriticFeedback(
            accuracy_score=evaluation.get("accuracy_score", 0.5),
            confidence_adjustment=evaluation.get("confidence_adjustment", 0.0),
            weight_adjustments=weight_adjustments,
            emotion_mapping_suggestions=emotion_mapping,
            context_appropriateness=evaluation.get("context_appropriateness", 0.5),
            reasoning=evaluation.get("reasoning", "No reasoning provided"),
            processing_time_ms=processing_time
        )

    async def _apply_learning_adjustments(
        self, 
        feedback: EmotionCriticFeedback, 
        user_id: str, 
        session_id: str
    ):
        """Apply learning adjustments based on critic feedback."""
        
        # Update weight adjustments with learning rate
        learning_rate = 0.1  # Conservative learning rate
        
        if "language_delta" in feedback.weight_adjustments:
            delta = feedback.weight_adjustments["language_delta"] * learning_rate
            self.weight_adjustments["language_weight"] = max(0.1, min(0.9, 
                self.weight_adjustments["language_weight"] + delta))
            self.weight_adjustments["prosody_weight"] = 1.0 - self.weight_adjustments["language_weight"]
        
        # Track accuracy improvements
        if session_id not in self.accuracy_tracking:
            self.accuracy_tracking[session_id] = []
        
        self.accuracy_tracking[session_id].append({
            "timestamp": time.time(),
            "accuracy_score": feedback.accuracy_score,
            "context_appropriateness": feedback.context_appropriateness,
            "processing_time": feedback.processing_time_ms
        })
        
        # Store learning history
        if session_id not in self.learning_history:
            self.learning_history[session_id] = []
        
        self.learning_history[session_id].append({
            "feedback": feedback,
            "timestamp": time.time(),
            "user_id": user_id
        })

    def _update_performance_metrics(self, feedback: EmotionCriticFeedback):
        """Update performance tracking metrics."""
        self.total_evaluations += 1
        
        # Update average processing time
        self.avg_processing_time = (
            (self.avg_processing_time * (self.total_evaluations - 1) + feedback.processing_time_ms) 
            / self.total_evaluations
        )
        
        # Track accuracy improvements
        self.accuracy_improvements.append(feedback.accuracy_score)

    def get_current_weights(self) -> Dict[str, float]:
        """Get current dynamic weights for multi-modal combination."""
        return self.weight_adjustments.copy()

    async def _retrieve_relevant_memories(
        self,
        context: ConversationContext,
        user_id: str
    ) -> MemoryContext:
        """Retrieve and score relevant memories for emotion evaluation."""
        try:
            # Build memory query from current context
            query_text = f"{context.current_message} {context.detected_emotion}"

            # Add recent conversation context
            if context.previous_messages:
                recent_context = " ".join(context.previous_messages[-3:])
                query_text += f" {recent_context}"

            # Search for relevant memories
            relevant_memories = await self.memory_manager.async_search_memories(
                query=query_text,
                user_id=user_id,
                k=10,  # Get more memories for better context
                min_importance=0.3
            )

            # Calculate memory relevance scores
            memory_relevance_scores = {}
            emotional_patterns = {}
            relationship_context = []
            emotional_triggers = []

            for memory in relevant_memories:
                memory_id = memory.get('id', '')
                memory_text = memory.get('text', '')
                memory_type = memory.get('memory_type', '')
                importance = memory.get('importance_score', 0.5)
                personalness = memory.get('personalness_score', 0.5)

                # Calculate relevance score based on multiple factors
                relevance_score = self._calculate_memory_relevance(
                    memory, context, importance, personalness
                )
                memory_relevance_scores[memory_id] = relevance_score

                # Extract emotional patterns
                if 'emotion' in memory_text.lower() or any(emotion in memory_text.lower()
                    for emotion in ['happy', 'sad', 'angry', 'excited', 'worried', 'calm']):
                    emotion_words = self._extract_emotion_words(memory_text)
                    for emotion in emotion_words:
                        emotional_patterns[emotion] = emotional_patterns.get(emotion, 0) + relevance_score

                # Identify relationship context
                if memory_type == 'semantic_profile' or 'relationship' in memory_text.lower():
                    relationship_context.append(memory)

                # Identify emotional triggers
                if 'trigger' in memory_text.lower() or importance > 0.8:
                    emotional_triggers.append(memory)

            # Calculate temporal decay factor
            temporal_decay = self._calculate_temporal_decay(relevant_memories)

            return MemoryContext(
                relevant_memories=relevant_memories,
                emotional_patterns=emotional_patterns,
                relationship_context=relationship_context,
                emotional_triggers=emotional_triggers,
                memory_relevance_scores=memory_relevance_scores,
                temporal_decay_factor=temporal_decay
            )

        except Exception as e:
            logger.warning(f"Error retrieving memories for user {user_id}: {e}")
            # Return empty memory context on error
            return MemoryContext(
                relevant_memories=[],
                emotional_patterns={},
                relationship_context=[],
                emotional_triggers=[],
                memory_relevance_scores={},
                temporal_decay_factor=1.0
            )

    def _calculate_memory_relevance(
        self,
        memory: Dict[str, Any],
        context: ConversationContext,
        importance: float,
        personalness: float
    ) -> float:
        """Calculate how relevant a memory is to the current emotional context."""

        base_score = importance * 0.4 + personalness * 0.3

        # Boost score if memory contains similar emotions
        memory_text = memory.get('text', '').lower()
        current_emotion = context.detected_emotion.lower()

        if current_emotion in memory_text:
            base_score += 0.2

        # Boost for recent memories
        created_at = memory.get('created_at', '')
        if created_at:
            try:
                memory_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                time_diff = datetime.now() - memory_time.replace(tzinfo=None)
                if time_diff.days < 7:  # Recent memory
                    base_score += 0.1
            except:
                pass

        # Boost for conversation stage relevance
        stage = context.conversation_stage
        if stage == 'opening' and 'greeting' in memory_text:
            base_score += 0.1
        elif stage == 'closing' and 'goodbye' in memory_text:
            base_score += 0.1

        return min(1.0, base_score)

    def _extract_emotion_words(self, text: str) -> List[str]:
        """Extract emotion-related words from text."""
        emotion_keywords = [
            'happy', 'sad', 'angry', 'excited', 'worried', 'calm', 'anxious',
            'joyful', 'frustrated', 'content', 'nervous', 'peaceful', 'upset',
            'delighted', 'disappointed', 'furious', 'serene', 'stressed'
        ]

        text_lower = text.lower()
        found_emotions = []

        for emotion in emotion_keywords:
            if emotion in text_lower:
                found_emotions.append(emotion)

        return found_emotions

    def _calculate_temporal_decay(self, memories: List[Dict[str, Any]]) -> float:
        """Calculate temporal decay factor based on memory ages."""
        if not memories:
            return 1.0

        total_weight = 0
        weighted_decay = 0

        for memory in memories:
            created_at = memory.get('created_at', '')
            weight = memory.get('importance_score', 0.5)

            if created_at:
                try:
                    memory_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    time_diff = datetime.now() - memory_time.replace(tzinfo=None)

                    # Calculate decay: recent memories have higher weight
                    if time_diff.days < 1:
                        decay = 1.0
                    elif time_diff.days < 7:
                        decay = 0.8
                    elif time_diff.days < 30:
                        decay = 0.6
                    else:
                        decay = 0.4

                    weighted_decay += decay * weight
                    total_weight += weight

                except:
                    weighted_decay += 0.5 * weight
                    total_weight += weight

        return weighted_decay / total_weight if total_weight > 0 else 0.7

    async def _get_user_emotional_profile(self, user_id: str) -> Dict[str, Any]:
        """Get or build user emotional profile from memory and interaction history."""

        if user_id in self.user_emotional_profiles:
            return self.user_emotional_profiles[user_id]

        try:
            # Search for emotional pattern memories
            emotional_memories = await self.memory_manager.async_search_memories(
                query="emotion emotional feeling mood",
                user_id=user_id,
                k=20,
                min_importance=0.2
            )

            # Analyze emotional patterns
            emotion_counts = {}
            emotional_transitions = []
            known_triggers = []

            for memory in emotional_memories:
                memory_text = memory.get('text', '').lower()

                # Extract emotions from memory
                emotions = self._extract_emotion_words(memory_text)
                for emotion in emotions:
                    emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1

                # Look for triggers
                if 'trigger' in memory_text or 'upset' in memory_text:
                    known_triggers.append(memory_text[:100])

            # Calculate dominant emotions
            total_emotions = sum(emotion_counts.values())
            dominant_emotions = {}
            if total_emotions > 0:
                for emotion, count in emotion_counts.items():
                    dominant_emotions[emotion] = count / total_emotions

            # Calculate emotional stability (variance in emotions)
            if len(emotion_counts) > 1:
                emotion_values = list(emotion_counts.values())
                mean_val = sum(emotion_values) / len(emotion_values)
                variance = sum((x - mean_val) ** 2 for x in emotion_values) / len(emotion_values)
                emotional_stability = max(0, 1 - (variance / mean_val)) if mean_val > 0 else 0.5
            else:
                emotional_stability = 0.8  # Stable if only one emotion type

            profile = {
                'user_id': user_id,
                'dominant_emotions': dominant_emotions,
                'emotional_stability': emotional_stability,
                'known_triggers': known_triggers[:5],  # Top 5 triggers
                'total_emotional_memories': len(emotional_memories),
                'last_updated': time.time()
            }

            self.user_emotional_profiles[user_id] = profile
            return profile

        except Exception as e:
            logger.warning(f"Error building emotional profile for user {user_id}: {e}")
            # Return default profile
            return {
                'user_id': user_id,
                'dominant_emotions': {},
                'emotional_stability': 0.5,
                'known_triggers': [],
                'total_emotional_memories': 0,
                'last_updated': time.time()
            }

    async def _update_user_emotional_profile(
        self,
        user_id: str,
        context: ConversationContext,
        feedback: EmotionCriticFeedback
    ):
        """Update user emotional profile based on current interaction."""

        if user_id not in self.user_emotional_profiles:
            await self._get_user_emotional_profile(user_id)

        profile = self.user_emotional_profiles[user_id]

        # Update dominant emotions with current detection
        current_emotion = context.detected_emotion.lower()
        if 'dominant_emotions' not in profile:
            profile['dominant_emotions'] = {}

        # Weighted update based on feedback accuracy
        weight = feedback.accuracy_score * 0.1  # Conservative update
        current_count = profile['dominant_emotions'].get(current_emotion, 0)
        profile['dominant_emotions'][current_emotion] = current_count + weight

        # Update emotional stability based on consistency
        if feedback.accuracy_score > 0.8:
            profile['emotional_stability'] = min(1.0, profile['emotional_stability'] + 0.01)
        elif feedback.accuracy_score < 0.4:
            profile['emotional_stability'] = max(0.0, profile['emotional_stability'] - 0.01)

        profile['last_updated'] = time.time()

    async def _apply_memory_informed_learning(
        self,
        feedback: EmotionCriticFeedback,
        user_id: str,
        session_id: str,
        memory_context: MemoryContext
    ):
        """Apply learning adjustments informed by memory context."""

        # Standard learning adjustments
        await self._apply_learning_adjustments(feedback, user_id, session_id)

        # Memory-specific adjustments
        if memory_context.relevant_memories:
            # Adjust weights based on memory relevance
            avg_relevance = sum(memory_context.memory_relevance_scores.values()) / len(memory_context.memory_relevance_scores)

            if avg_relevance > 0.7:  # High memory relevance
                # Increase confidence in memory-informed decisions
                memory_weight = feedback.weight_adjustments.get('memory_weight', 0)
                if memory_weight > 0.1:
                    # Memory suggests this emotion, boost language weight slightly
                    self.weight_adjustments["language_weight"] = min(0.85,
                        self.weight_adjustments["language_weight"] + 0.02)
                    self.weight_adjustments["prosody_weight"] = 1.0 - self.weight_adjustments["language_weight"]

        # Store memory-informed learning data
        if session_id not in self.learning_history:
            self.learning_history[session_id] = []

        self.learning_history[session_id].append({
            "feedback": feedback,
            "memory_context": {
                "num_memories": len(memory_context.relevant_memories),
                "avg_relevance": sum(memory_context.memory_relevance_scores.values()) / len(memory_context.memory_relevance_scores) if memory_context.memory_relevance_scores else 0,
                "emotional_patterns": memory_context.emotional_patterns,
                "temporal_decay": memory_context.temporal_decay_factor
            },
            "timestamp": time.time(),
            "user_id": user_id
        })

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics for monitoring."""
        recent_accuracy = 0.0
        if self.accuracy_improvements:
            recent_accuracy = sum(self.accuracy_improvements[-10:]) / min(10, len(self.accuracy_improvements))
        
        return {
            "total_evaluations": self.total_evaluations,
            "avg_processing_time_ms": self.avg_processing_time,
            "recent_accuracy": recent_accuracy,
            "current_weights": self.weight_adjustments,
            "active_sessions": len(self.learning_history)
        }

# Global instance
emotion_critic_service = EmotionCriticService()
