"""
Memory Cache Service for WebSocket Sessions
Implements session-based memory caching to eliminate VectorDB bottlenecks.
"""
import asyncio
import json
import time
import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from django.core.cache import cache
from django.conf import settings
from asgiref.sync import sync_to_async

logger = logging.getLogger(__name__)


@dataclass
class CachedMemory:
    """Represents a cached memory with metadata."""
    memory_id: str
    content: str
    memory_type: str
    importance_score: float
    similarity_score: float
    salience_score: float
    created_at: str
    last_accessed: str
    access_count: int = 0


@dataclass
class UserMemorySession:
    """Represents a user's memory session cache."""
    user_id: str
    session_id: str
    memories: List[CachedMemory]
    last_updated: float
    cache_hits: int = 0
    cache_misses: int = 0
    total_queries: int = 0


class MemoryCacheService:
    """
    High-performance memory caching service for WebSocket sessions.
    
    Features:
    - Session-based memory preloading
    - Redis-backed fast retrieval
    - Incremental updates
    - Cache invalidation strategies
    """
    
    def __init__(self):
        self.cache_prefix = "memory_cache"
        self.session_timeout = getattr(settings, 'MEMORY_CACHE_SESSION_TIMEOUT', 3600)  # 1 hour
        self.max_memories_per_user = getattr(settings, 'MEMORY_CACHE_MAX_MEMORIES', 100)
        self.similarity_threshold = getattr(settings, 'MEMORY_CACHE_SIMILARITY_THRESHOLD', 0.7)
        self.preload_enabled = getattr(settings, 'MEMORY_CACHE_PRELOAD_ENABLED', True)
        
        # Performance tracking
        self._stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'preload_time_ms': 0,
            'query_time_ms': 0
        }
        
        logger.info("MemoryCacheService initialized with session-based caching")
    
    def _get_session_cache_key(self, user_id: str, session_id: str) -> str:
        """Generate cache key for user session."""
        return f"{self.cache_prefix}:session:{user_id}:{session_id}"
    
    def _get_user_memories_key(self, user_id: str) -> str:
        """Generate cache key for user's all memories."""
        return f"{self.cache_prefix}:user_memories:{user_id}"
    
    def _get_memory_updates_key(self, user_id: str) -> str:
        """Generate cache key for tracking memory updates."""
        return f"{self.cache_prefix}:updates:{user_id}"
    
    async def preload_user_memories(self, user_id: str, session_id: str) -> bool:
        """
        Preload user memories into cache when WebSocket connects.
        
        Args:
            user_id: User ID
            session_id: WebSocket session ID
            
        Returns:
            True if successful, False otherwise
        """
        if not self.preload_enabled:
            return False
        
        start_time = time.time()
        
        try:
            # Check if already cached
            cache_key = self._get_session_cache_key(user_id, session_id)
            existing_session = cache.get(cache_key)
            
            if existing_session:
                logger.debug(f"Memory session already cached for user {user_id}")
                return True
            
            # Import memory service
            from memory.services import MemoryManager
            from authentication.models import User

            # Get user instance
            user = await sync_to_async(User.objects.get)(id=user_id)
            memory_service = MemoryManager(user=user)
            
            # Retrieve user's most important memories
            memories = await memory_service.retrieve_memories(
                query="",  # Empty query to get general memories
                k=self.max_memories_per_user,
                min_importance=0.1  # Lower threshold for preloading
            )
            
            # Convert to cached format
            cached_memories = []
            for memory_data in memories:
                memory = memory_data['memory']
                cached_memory = CachedMemory(
                    memory_id=str(memory.id),
                    content=memory.content,
                    memory_type=memory.memory_type,
                    importance_score=memory.importance_score,
                    similarity_score=memory_data.get('similarity_score', 0.0),
                    salience_score=memory_data.get('salience_score', 0.0),
                    created_at=memory.created_at.isoformat(),
                    last_accessed=datetime.now().isoformat(),
                    access_count=0
                )
                cached_memories.append(cached_memory)
            
            # Create session cache
            memory_session = UserMemorySession(
                user_id=user_id,
                session_id=session_id,
                memories=cached_memories,
                last_updated=time.time()
            )
            
            # Cache the session
            cache.set(cache_key, asdict(memory_session), timeout=self.session_timeout)
            
            # Track performance
            preload_time = (time.time() - start_time) * 1000
            self._stats['preload_time_ms'] = preload_time
            
            logger.info(f"Preloaded {len(cached_memories)} memories for user {user_id} in {preload_time:.1f}ms")
            return True
            
        except Exception as e:
            logger.error(f"Error preloading memories for user {user_id}: {e}")
            return False
    
    async def search_cached_memories(self, user_id: str, session_id: str, 
                                   query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Search for relevant memories in cache.
        
        Args:
            user_id: User ID
            session_id: WebSocket session ID
            query: Search query
            k: Number of memories to return
            
        Returns:
            List of relevant memories
        """
        start_time = time.time()
        
        try:
            # Get cached session
            cache_key = self._get_session_cache_key(user_id, session_id)
            session_data = cache.get(cache_key)
            
            if not session_data:
                logger.warning(f"No cached memories found for user {user_id}, session {session_id}")
                self._stats['cache_misses'] += 1
                return await self._fallback_memory_search(user_id, query, k)
            
            # Convert back to objects
            session = UserMemorySession(**session_data)
            session.total_queries += 1
            
            # Simple text-based similarity search (can be enhanced with embeddings)
            relevant_memories = []
            query_lower = query.lower()
            
            for memory in session.memories:
                # Calculate simple text similarity
                content_lower = memory.content.lower()
                
                # Basic relevance scoring
                relevance_score = 0.0
                
                # Exact phrase match
                if query_lower in content_lower:
                    relevance_score += 0.8
                
                # Word overlap
                query_words = set(query_lower.split())
                content_words = set(content_lower.split())
                word_overlap = len(query_words.intersection(content_words))
                if len(query_words) > 0:
                    relevance_score += (word_overlap / len(query_words)) * 0.5
                
                # Importance boost
                relevance_score += memory.importance_score * 0.3
                
                if relevance_score > 0.2:  # Minimum relevance threshold
                    memory.access_count += 1
                    memory.last_accessed = datetime.now().isoformat()
                    
                    relevant_memories.append({
                        'memory_id': memory.memory_id,
                        'content': memory.content,
                        'memory_type': memory.memory_type,
                        'importance_score': memory.importance_score,
                        'similarity_score': relevance_score,
                        'salience_score': memory.salience_score
                    })
            
            # Sort by relevance and return top k
            relevant_memories.sort(key=lambda x: x['similarity_score'], reverse=True)
            result = relevant_memories[:k]
            
            # Update session cache
            session.cache_hits += 1
            cache.set(cache_key, asdict(session), timeout=self.session_timeout)
            
            # Track performance
            query_time = (time.time() - start_time) * 1000
            self._stats['query_time_ms'] = query_time
            self._stats['cache_hits'] += 1
            
            logger.debug(f"Found {len(result)} cached memories for query '{query[:30]}...' in {query_time:.1f}ms")
            return result
            
        except Exception as e:
            logger.error(f"Error searching cached memories: {e}")
            self._stats['cache_misses'] += 1
            return await self._fallback_memory_search(user_id, query, k)
    
    async def _fallback_memory_search(self, user_id: str, query: str, k: int) -> List[Dict[str, Any]]:
        """Fallback to direct memory service if cache fails."""
        try:
            from memory.services import MemoryManager
            from authentication.models import User

            user = await sync_to_async(User.objects.get)(id=user_id)
            memory_service = MemoryManager(user=user)
            
            memories = await memory_service.retrieve_memories(query=query, k=k)
            
            # Convert to expected format
            result = []
            for memory_data in memories:
                memory = memory_data['memory']
                result.append({
                    'memory_id': str(memory.id),
                    'content': memory.content,
                    'memory_type': memory.memory_type,
                    'importance_score': memory.importance_score,
                    'similarity_score': memory_data.get('similarity_score', 0.0),
                    'salience_score': memory_data.get('salience_score', 0.0)
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Fallback memory search failed: {e}")
            return []
    
    async def add_memory_to_cache(self, user_id: str, session_id: str, 
                                memory_data: Dict[str, Any]) -> bool:
        """
        Add new memory to cache immediately.
        
        Args:
            user_id: User ID
            session_id: WebSocket session ID
            memory_data: Memory data to add
            
        Returns:
            True if successful
        """
        try:
            cache_key = self._get_session_cache_key(user_id, session_id)
            session_data = cache.get(cache_key)
            
            if not session_data:
                return False
            
            session = UserMemorySession(**session_data)
            
            # Create cached memory
            cached_memory = CachedMemory(
                memory_id=memory_data['memory_id'],
                content=memory_data['content'],
                memory_type=memory_data['memory_type'],
                importance_score=memory_data['importance_score'],
                similarity_score=0.0,
                salience_score=memory_data.get('salience_score', 0.0),
                created_at=datetime.now().isoformat(),
                last_accessed=datetime.now().isoformat()
            )
            
            # Add to session
            session.memories.append(cached_memory)
            session.last_updated = time.time()
            
            # Keep only most recent/important memories
            if len(session.memories) > self.max_memories_per_user:
                session.memories.sort(key=lambda m: m.importance_score, reverse=True)
                session.memories = session.memories[:self.max_memories_per_user]
            
            # Update cache
            cache.set(cache_key, asdict(session), timeout=self.session_timeout)
            
            logger.debug(f"Added memory to cache for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding memory to cache: {e}")
            return False
    
    async def invalidate_user_cache(self, user_id: str, session_id: Optional[str] = None) -> bool:
        """
        Invalidate memory cache for user.
        
        Args:
            user_id: User ID
            session_id: Optional specific session ID
            
        Returns:
            True if successful
        """
        try:
            if session_id:
                # Invalidate specific session
                cache_key = self._get_session_cache_key(user_id, session_id)
                cache.delete(cache_key)
            else:
                # Invalidate all sessions for user (pattern delete)
                # Note: This is a simplified approach; in production, you'd want
                # to track active sessions for each user
                pass
            
            logger.info(f"Invalidated memory cache for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error invalidating cache: {e}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        total_requests = self._stats['cache_hits'] + self._stats['cache_misses']
        hit_rate = (self._stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_hits': self._stats['cache_hits'],
            'cache_misses': self._stats['cache_misses'],
            'hit_rate_percent': hit_rate,
            'avg_preload_time_ms': self._stats['preload_time_ms'],
            'avg_query_time_ms': self._stats['query_time_ms']
        }


    async def sync_memory_updates(self, user_id: str, session_id: str) -> bool:
        """
        Sync any pending memory updates from persistent storage.

        Args:
            user_id: User ID
            session_id: WebSocket session ID

        Returns:
            True if successful
        """
        try:
            # Check for pending updates
            updates_key = self._get_memory_updates_key(user_id)
            pending_updates = cache.get(updates_key, [])

            if not pending_updates:
                return True

            # Get current session
            cache_key = self._get_session_cache_key(user_id, session_id)
            session_data = cache.get(cache_key)

            if not session_data:
                return False

            session = UserMemorySession(**session_data)

            # Apply updates
            for update in pending_updates:
                if update['action'] == 'add':
                    await self.add_memory_to_cache(user_id, session_id, update['memory_data'])
                elif update['action'] == 'delete':
                    session.memories = [m for m in session.memories if m.memory_id != update['memory_id']]
                elif update['action'] == 'update':
                    for memory in session.memories:
                        if memory.memory_id == update['memory_id']:
                            memory.content = update['memory_data']['content']
                            memory.importance_score = update['memory_data']['importance_score']
                            break

            # Update session
            session.last_updated = time.time()
            cache.set(cache_key, asdict(session), timeout=self.session_timeout)

            # Clear pending updates
            cache.delete(updates_key)

            logger.debug(f"Synced {len(pending_updates)} memory updates for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error syncing memory updates: {e}")
            return False

    async def queue_memory_update(self, user_id: str, action: str,
                                memory_data: Optional[Dict[str, Any]] = None,
                                memory_id: Optional[str] = None) -> bool:
        """
        Queue a memory update for background processing.

        Args:
            user_id: User ID
            action: 'add', 'update', or 'delete'
            memory_data: Memory data for add/update operations
            memory_id: Memory ID for delete operations

        Returns:
            True if queued successfully
        """
        try:
            updates_key = self._get_memory_updates_key(user_id)
            pending_updates = cache.get(updates_key, [])

            update = {
                'action': action,
                'timestamp': time.time(),
                'memory_id': memory_id,
                'memory_data': memory_data
            }

            pending_updates.append(update)

            # Keep only recent updates (last 100)
            if len(pending_updates) > 100:
                pending_updates = pending_updates[-100:]

            cache.set(updates_key, pending_updates, timeout=3600)  # 1 hour

            logger.debug(f"Queued memory {action} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error queueing memory update: {e}")
            return False


class MemoryDualWriteService:
    """
    Service for dual-write memory operations.
    Writes to cache immediately and queues persistent storage updates.
    """

    def __init__(self):
        self.cache_service = memory_cache_service

    async def create_memory(self, user_id: str, session_id: str,
                          content: str, memory_type: str,
                          importance_score: float = 0.5) -> Optional[str]:
        """
        Create a new memory with dual-write pattern.

        Args:
            user_id: User ID
            session_id: WebSocket session ID
            content: Memory content
            memory_type: Type of memory
            importance_score: Importance score (0.0-1.0)

        Returns:
            Memory ID if successful, None otherwise
        """
        try:
            # Generate memory ID
            import uuid
            memory_id = str(uuid.uuid4())

            # Prepare memory data
            memory_data = {
                'memory_id': memory_id,
                'content': content,
                'memory_type': memory_type,
                'importance_score': importance_score,
                'salience_score': importance_score  # Simple default
            }

            # 1. Write to cache immediately (fast path)
            cache_success = await self.cache_service.add_memory_to_cache(
                user_id, session_id, memory_data
            )

            # 2. Queue persistent storage update (background)
            await self.cache_service.queue_memory_update(
                user_id, 'add', memory_data
            )

            # 3. Async write to persistent storage
            asyncio.create_task(self._write_to_persistent_storage(user_id, memory_data))

            logger.info(f"Created memory {memory_id} for user {user_id} (cache: {cache_success})")
            return memory_id

        except Exception as e:
            logger.error(f"Error creating memory: {e}")
            return None

    async def _write_to_persistent_storage(self, user_id: str, memory_data: Dict[str, Any]) -> bool:
        """Write memory to persistent storage in background."""
        try:
            from memory.services import MemoryManager
            from authentication.models import User

            # Get user
            user = await sync_to_async(User.objects.get)(id=user_id)
            memory_service = MemoryManager(user=user)

            # Store in persistent storage
            await memory_service.store_memory(
                content=memory_data['content'],
                memory_type=memory_data['memory_type'],
                importance_score=memory_data['importance_score']
            )

            logger.debug(f"Persisted memory {memory_data['memory_id']} to storage")
            return True

        except Exception as e:
            logger.error(f"Error persisting memory to storage: {e}")
            return False


# Global instances
memory_cache_service = MemoryCacheService()
memory_dual_write_service = MemoryDualWriteService()
