"""
Groq API service for streaming LLM responses with performance optimization.
"""
import asyncio
import logging
import time
from typing import Dict, List, Any, AsyncGenerator, Optional
from dataclasses import dataclass
import os
from contextlib import asynccontextmanager

import groq
from groq import AsyncGroq
from groq.types.chat import Cha<PERSON><PERSON><PERSON>ple<PERSON>, ChatCompletionChunk

from .error_recovery import error_recovery_manager

logger = logging.getLogger(__name__)


@dataclass
class GroqResponse:
    """Response data from Groq API."""
    content: str
    model: str
    tokens_used: int
    processing_time_ms: float
    first_token_time_ms: Optional[float] = None
    finish_reason: Optional[str] = None
    request_id: Optional[str] = None


@dataclass
class GroqStreamChunk:
    """Individual chunk from streaming response."""
    content: str
    is_final: bool
    chunk_index: int
    timestamp_ms: float
    finish_reason: Optional[str] = None


class CircuitBreaker:
    """Circuit breaker pattern for API failure handling."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'closed'  # closed, open, half-open
    
    def can_execute(self) -> bool:
        """Check if request can be executed."""
        if self.state == 'closed':
            return True
        
        if self.state == 'open':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'half-open'
                return True
            return False
        
        # half-open state
        return True
    
    def record_success(self):
        """Record successful request."""
        self.failure_count = 0
        self.state = 'closed'
    
    def record_failure(self):
        """Record failed request."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'open'


class GroqStreamingClient:
    """
    High-performance Groq API client optimized for real-time streaming.
    
    Features:
    - Streaming chat completions with <200ms first token target
    - Connection pooling and request optimization
    - Circuit breaker pattern for failure handling
    - Performance monitoring and metrics collection
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        max_retries: int = 3,
        timeout: float = 30.0,
        connection_pool_size: int = 10
    ):
        self.api_key = api_key or os.getenv('GROQ_API_KEY')
        if not self.api_key:
            raise ValueError("GROQ_API_KEY must be provided or set as environment variable")
        
        self.base_url = base_url or os.getenv('GROQ_BASE_URL', 'https://api.groq.com')
        self.max_retries = max_retries
        self.timeout = timeout
        
        # Initialize async client with connection pooling
        self.client = AsyncGroq(
            api_key=self.api_key,
            base_url=self.base_url,
            max_retries=max_retries,
            timeout=timeout
        )
        
        # Circuit breaker for failure handling
        self.circuit_breaker = CircuitBreaker()
        
        # Performance tracking
        self.request_count = 0
        self.total_response_time = 0.0
        self.total_first_token_time = 0.0
        
        logger.info(f"Initialized GroqStreamingClient with base_url: {self.base_url}")
    
    async def stream_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "llama-3.3-70b-versatile",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        user_id: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[GroqStreamChunk, None]:
        """
        Stream chat completion with performance optimization.
        
        Args:
            messages: List of chat messages
            model: Groq model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            top_p: Top-p sampling parameter
            frequency_penalty: Frequency penalty
            presence_penalty: Presence penalty
            stop: Stop sequences
            user_id: User ID for tracking
            **kwargs: Additional parameters
        
        Yields:
            GroqStreamChunk: Individual response chunks
        """
        # Check circuit breaker through error recovery manager
        if not error_recovery_manager.circuit_breakers['groq'].allow_request():
            raise groq.APIError("Circuit breaker is open - too many recent failures")
        
        start_time = time.time()
        first_token_time = None
        chunk_index = 0
        
        # Prepare context for potential fallback
        context = {
            'messages': messages,
            'model': model,
            'temperature': temperature,
            'max_tokens': max_tokens,
            'stream': True,
            'user_id': user_id
        }
        
        try:
            # Use retry mechanism with exponential backoff
            async def _make_request():
                # Prepare request parameters
                request_params = {
                    "model": model,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "top_p": top_p,
                    "frequency_penalty": frequency_penalty,
                    "presence_penalty": presence_penalty,
                    "stream": True,
                    **kwargs
                }
                
                if stop:
                    request_params["stop"] = stop
                
                if user_id:
                    request_params["user"] = user_id
                
                return await self.client.chat.completions.create(**request_params)
            
            # Use error recovery manager's retry mechanism
            stream = await error_recovery_manager.retry_with_backoff(
                _make_request, service='groq'
            )
            
            logger.debug(f"Starting Groq streaming request with model: {model}")
            
            async for chunk in stream:
                current_time = time.time()
                
                # Record first token time
                if first_token_time is None:
                    first_token_time = (current_time - start_time) * 1000  # Convert to ms
                    logger.debug(f"First token received in {first_token_time:.2f}ms")
                
                # Extract content from chunk
                content = ""
                finish_reason = None
                
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    if choice.delta and choice.delta.content:
                        content = choice.delta.content
                    finish_reason = choice.finish_reason
                
                # Create chunk response
                chunk_response = GroqStreamChunk(
                    content=content,
                    is_final=finish_reason is not None,
                    chunk_index=chunk_index,
                    timestamp_ms=current_time * 1000,
                    finish_reason=finish_reason
                )
                
                chunk_index += 1
                yield chunk_response
                
                # Break if final chunk
                if finish_reason:
                    break
            
            # Record success in error recovery manager
            error_recovery_manager.circuit_breakers['groq'].record_success()
            
            # Update performance metrics
            total_time = (time.time() - start_time) * 1000
            self._update_performance_metrics(total_time, first_token_time)
            
            logger.debug(f"Groq streaming completed in {total_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"Groq streaming error: {e}")
            
            # Handle API failure through error recovery manager
            recovery_result = await error_recovery_manager.handle_api_failure(
                'groq', e, context
            )
            
            if recovery_result['success'] and recovery_result['fallback_used']:
                # Use fallback response (OpenAI)
                logger.info(f"Using fallback service: {recovery_result['fallback_service']}")
                
                # Convert OpenAI response to Groq format for compatibility
                fallback_response = recovery_result['response']
                
                if hasattr(fallback_response, '__aiter__'):
                    # Streaming response from fallback
                    async for chunk in fallback_response:
                        if hasattr(chunk, 'choices') and chunk.choices:
                            choice = chunk.choices[0]
                            content = ""
                            finish_reason = None
                            
                            if hasattr(choice, 'delta') and choice.delta and choice.delta.content:
                                content = choice.delta.content
                            finish_reason = getattr(choice, 'finish_reason', None)
                            
                            yield GroqStreamChunk(
                                content=content,
                                is_final=finish_reason is not None,
                                chunk_index=chunk_index,
                                timestamp_ms=time.time() * 1000,
                                finish_reason=finish_reason
                            )
                            
                            chunk_index += 1
                            
                            if finish_reason:
                                break
                else:
                    # Non-streaming response from fallback
                    if hasattr(fallback_response, 'choices') and fallback_response.choices:
                        content = fallback_response.choices[0].message.content
                        yield GroqStreamChunk(
                            content=content,
                            is_final=True,
                            chunk_index=0,
                            timestamp_ms=time.time() * 1000,
                            finish_reason='stop'
                        )
            else:
                # No fallback available or fallback failed
                if isinstance(e, groq.APIError):
                    raise
                else:
                    raise groq.APIError(f"Groq streaming failed: {str(e)}")
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "llama-3.3-70b-versatile",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        **kwargs
    ) -> GroqResponse:
        """
        Non-streaming chat completion for cases where full response is needed.
        
        Args:
            messages: List of chat messages
            model: Groq model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters
        
        Returns:
            GroqResponse: Complete response data
        """
        # Check circuit breaker through error recovery manager
        if not error_recovery_manager.circuit_breakers['groq'].allow_request():
            raise groq.APIError("Circuit breaker is open - too many recent failures")
        
        start_time = time.time()
        
        # Prepare context for potential fallback
        context = {
            'messages': messages,
            'model': model,
            'temperature': temperature,
            'max_tokens': max_tokens,
            'stream': False
        }
        
        try:
            # Use retry mechanism with exponential backoff
            async def _make_request():
                return await self.client.chat.completions.create(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    stream=False,
                    **kwargs
                )
            
            # Use error recovery manager's retry mechanism
            response = await error_recovery_manager.retry_with_backoff(
                _make_request, service='groq'
            )
            
            # Extract response data
            content = ""
            if response.choices and len(response.choices) > 0:
                content = response.choices[0].message.content or ""
            
            processing_time = (time.time() - start_time) * 1000
            
            # Create response object
            groq_response = GroqResponse(
                content=content,
                model=response.model,
                tokens_used=response.usage.total_tokens if response.usage else 0,
                processing_time_ms=processing_time,
                finish_reason=response.choices[0].finish_reason if response.choices else None,
                request_id=getattr(response, 'id', None)
            )
            
            # Record success in error recovery manager
            error_recovery_manager.circuit_breakers['groq'].record_success()
            
            logger.debug(f"Groq completion finished in {processing_time:.2f}ms")
            
            return groq_response
            
        except Exception as e:
            logger.error(f"Groq completion error: {e}")
            
            # Handle API failure through error recovery manager
            recovery_result = await error_recovery_manager.handle_api_failure(
                'groq', e, context
            )
            
            if recovery_result['success'] and recovery_result['fallback_used']:
                # Use fallback response (OpenAI)
                logger.info(f"Using fallback service: {recovery_result['fallback_service']}")
                
                fallback_response = recovery_result['response']
                
                # Extract content from fallback response
                content = ""
                if hasattr(fallback_response, 'choices') and fallback_response.choices:
                    content = fallback_response.choices[0].message.content or ""
                
                processing_time = (time.time() - start_time) * 1000
                
                # Create response object with fallback data
                return GroqResponse(
                    content=content,
                    model=f"fallback-{recovery_result['fallback_service']}",
                    tokens_used=getattr(fallback_response.usage, 'total_tokens', 0) if hasattr(fallback_response, 'usage') else 0,
                    processing_time_ms=processing_time,
                    finish_reason=fallback_response.choices[0].finish_reason if hasattr(fallback_response, 'choices') and fallback_response.choices else 'stop',
                    request_id=getattr(fallback_response, 'id', None)
                )
            else:
                # No fallback available or fallback failed
                if isinstance(e, groq.APIError):
                    raise
                else:
                    raise groq.APIError(f"Groq completion failed: {str(e)}")
    
    def _update_performance_metrics(self, total_time_ms: float, first_token_time_ms: Optional[float]):
        """Update internal performance metrics."""
        self.request_count += 1
        self.total_response_time += total_time_ms
        
        if first_token_time_ms:
            self.total_first_token_time += first_token_time_ms
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get performance statistics."""
        if self.request_count == 0:
            return {
                'average_response_time_ms': 0.0,
                'average_first_token_time_ms': 0.0,
                'total_requests': 0,
                'circuit_breaker_state': self.circuit_breaker.state
            }
        
        return {
            'average_response_time_ms': self.total_response_time / self.request_count,
            'average_first_token_time_ms': self.total_first_token_time / self.request_count,
            'total_requests': self.request_count,
            'circuit_breaker_state': self.circuit_breaker.state,
            'failure_count': self.circuit_breaker.failure_count
        }
    
    async def health_check(self) -> bool:
        """Check if Groq API is healthy."""
        try:
            # Simple test request
            response = await self.chat_completion(
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )
            return len(response.content) > 0
        except Exception as e:
            logger.error(f"Groq health check failed: {e}")
            return False
    
    async def close(self):
        """Close the client and cleanup resources."""
        if hasattr(self.client, 'close'):
            await self.client.close()


# Global client instance for reuse
_groq_client: Optional[GroqStreamingClient] = None


@asynccontextmanager
async def get_groq_client():
    """
    Context manager to get a shared Groq client instance.
    
    Usage:
        async with get_groq_client() as client:
            async for chunk in client.stream_chat_completion(messages):
                print(chunk.content)
    """
    global _groq_client
    
    if _groq_client is None:
        _groq_client = GroqStreamingClient()
    
    try:
        yield _groq_client
    finally:
        # Keep client alive for reuse
        pass


async def stream_groq_response(
    messages: List[Dict[str, str]],
    model: str = "llama-3.3-70b-versatile",
    temperature: float = 0.7,
    max_tokens: int = 1000,
    user_id: Optional[str] = None,
    **kwargs
) -> AsyncGenerator[str, None]:
    """
    Convenience function for streaming Groq responses.
    
    Args:
        messages: Chat messages
        model: Model to use
        temperature: Sampling temperature
        max_tokens: Maximum tokens
        user_id: User ID for tracking
        **kwargs: Additional parameters
    
    Yields:
        str: Response content chunks
    """
    async with get_groq_client() as client:
        async for chunk in client.stream_chat_completion(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            user_id=user_id,
            **kwargs
        ):
            if chunk.content:
                yield chunk.content


# Fallback service for when Groq is unavailable
class OpenAIFallbackService:
    """Fallback to OpenAI when Groq is unavailable."""
    
    def __init__(self):
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            logger.warning("OpenAI API key not found - fallback unavailable")
    
    async def stream_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4",
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Stream chat completion using OpenAI as fallback."""
        if not self.api_key:
            raise Exception("OpenAI fallback not configured")
        
        try:
            from openai import AsyncOpenAI
            
            client = AsyncOpenAI(api_key=self.api_key)
            
            stream = await client.chat.completions.create(
                model=model,
                messages=messages,
                stream=True,
                **kwargs
            )
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except ImportError:
            logger.error("OpenAI package not installed - cannot use fallback")
            raise
        except Exception as e:
            logger.error(f"OpenAI fallback error: {e}")
            raise


# Global fallback service
_fallback_service = OpenAIFallbackService()


async def stream_with_fallback(
    messages: List[Dict[str, str]],
    model: str = "llama-3.3-70b-versatile",
    fallback_model: str = "gpt-4",
    **kwargs
) -> AsyncGenerator[str, None]:
    """
    Stream response with automatic fallback to OpenAI if Groq fails.
    
    Args:
        messages: Chat messages
        model: Primary Groq model
        fallback_model: Fallback OpenAI model
        **kwargs: Additional parameters
    
    Yields:
        str: Response content chunks
    """
    try:
        # Try Groq first
        async for chunk in stream_groq_response(messages, model=model, **kwargs):
            yield chunk
    except Exception as e:
        logger.warning(f"Groq failed, falling back to OpenAI: {e}")
        
        try:
            # Fallback to OpenAI
            async for chunk in _fallback_service.stream_chat_completion(
                messages, model=fallback_model, **kwargs
            ):
                yield chunk
        except Exception as fallback_error:
            logger.error(f"Both Groq and OpenAI failed: {fallback_error}")
            # Yield error message as last resort
            yield f"I'm experiencing technical difficulties. Please try again in a moment."