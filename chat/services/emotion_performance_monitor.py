#!/usr/bin/env python3
"""
Emotion Performance Monitor
Tracks and analyzes actor-critic emotion detection performance over time.
"""
import asyncio
import json
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics for emotion detection."""
    timestamp: float
    session_id: str
    user_id: str
    accuracy_score: float
    confidence: float
    processing_time_ms: float
    emotion_detected: str
    emotion_expected: Optional[str]
    critic_feedback_score: Optional[float]
    weight_adjustments: Dict[str, float]

@dataclass
class LearningProgress:
    """Learning progress tracking."""
    session_id: str
    initial_accuracy: float
    current_accuracy: float
    improvement_rate: float
    total_interactions: int
    learning_velocity: float  # Rate of improvement per interaction
    confidence_trend: str  # "improving", "declining", "stable"

class EmotionPerformanceMonitor:
    """
    Monitors and analyzes emotion detection performance with actor-critic learning.
    """
    
    def __init__(self, max_history_size: int = 10000):
        self.max_history_size = max_history_size
        self.performance_history = deque(maxlen=max_history_size)
        self.session_metrics = {}  # session_id -> metrics
        self.learning_progress = {}  # session_id -> LearningProgress
        self.accuracy_trends = defaultdict(list)  # time-based accuracy tracking
        
        # Performance thresholds
        self.accuracy_thresholds = {
            "excellent": 0.85,
            "good": 0.75,
            "acceptable": 0.65,
            "needs_improvement": 0.50
        }
        
        logger.info("EmotionPerformanceMonitor initialized")

    def record_performance(
        self,
        session_id: str,
        user_id: str,
        accuracy_score: float,
        confidence: float,
        processing_time_ms: float,
        emotion_detected: str,
        emotion_expected: Optional[str] = None,
        critic_feedback_score: Optional[float] = None,
        weight_adjustments: Optional[Dict[str, float]] = None
    ):
        """Record a performance measurement."""
        
        metrics = PerformanceMetrics(
            timestamp=time.time(),
            session_id=session_id,
            user_id=user_id,
            accuracy_score=accuracy_score,
            confidence=confidence,
            processing_time_ms=processing_time_ms,
            emotion_detected=emotion_detected,
            emotion_expected=emotion_expected,
            critic_feedback_score=critic_feedback_score,
            weight_adjustments=weight_adjustments or {}
        )
        
        # Add to history
        self.performance_history.append(metrics)
        
        # Update session metrics
        if session_id not in self.session_metrics:
            self.session_metrics[session_id] = []
        self.session_metrics[session_id].append(metrics)
        
        # Update learning progress
        self._update_learning_progress(session_id, metrics)
        
        # Update accuracy trends
        self._update_accuracy_trends(metrics)
        
        logger.debug(f"Recorded performance: {emotion_detected} ({accuracy_score:.2f}) "
                    f"for session {session_id}")

    def _update_learning_progress(self, session_id: str, metrics: PerformanceMetrics):
        """Update learning progress for a session."""
        
        session_data = self.session_metrics[session_id]
        
        if session_id not in self.learning_progress:
            # Initialize learning progress
            self.learning_progress[session_id] = LearningProgress(
                session_id=session_id,
                initial_accuracy=metrics.accuracy_score,
                current_accuracy=metrics.accuracy_score,
                improvement_rate=0.0,
                total_interactions=1,
                learning_velocity=0.0,
                confidence_trend="stable"
            )
        else:
            # Update existing progress
            progress = self.learning_progress[session_id]
            progress.current_accuracy = metrics.accuracy_score
            progress.total_interactions = len(session_data)
            
            # Calculate improvement rate
            if progress.total_interactions > 1:
                progress.improvement_rate = (
                    (progress.current_accuracy - progress.initial_accuracy) / 
                    progress.total_interactions
                )
                progress.learning_velocity = progress.improvement_rate * 100  # Per 100 interactions
            
            # Calculate confidence trend
            if len(session_data) >= 3:
                recent_confidences = [m.confidence for m in session_data[-3:]]
                earlier_confidences = [m.confidence for m in session_data[-6:-3]] if len(session_data) >= 6 else [progress.initial_accuracy]
                
                recent_avg = sum(recent_confidences) / len(recent_confidences)
                earlier_avg = sum(earlier_confidences) / len(earlier_confidences)
                
                if recent_avg > earlier_avg + 0.1:
                    progress.confidence_trend = "improving"
                elif recent_avg < earlier_avg - 0.1:
                    progress.confidence_trend = "declining"
                else:
                    progress.confidence_trend = "stable"

    def _update_accuracy_trends(self, metrics: PerformanceMetrics):
        """Update time-based accuracy trends."""
        
        # Round timestamp to nearest minute for trend analysis
        minute_timestamp = int(metrics.timestamp // 60) * 60
        
        self.accuracy_trends[minute_timestamp].append(metrics.accuracy_score)
        
        # Keep only last 24 hours of trends
        cutoff_time = time.time() - (24 * 60 * 60)
        self.accuracy_trends = {
            ts: scores for ts, scores in self.accuracy_trends.items() 
            if ts >= cutoff_time
        }

    def get_session_performance(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get performance summary for a specific session."""
        
        if session_id not in self.session_metrics:
            return None
        
        session_data = self.session_metrics[session_id]
        progress = self.learning_progress.get(session_id)
        
        # Calculate session statistics
        accuracies = [m.accuracy_score for m in session_data]
        confidences = [m.confidence for m in session_data]
        processing_times = [m.processing_time_ms for m in session_data]
        
        return {
            "session_id": session_id,
            "total_interactions": len(session_data),
            "accuracy_stats": {
                "current": accuracies[-1] if accuracies else 0,
                "average": sum(accuracies) / len(accuracies) if accuracies else 0,
                "min": min(accuracies) if accuracies else 0,
                "max": max(accuracies) if accuracies else 0
            },
            "confidence_stats": {
                "current": confidences[-1] if confidences else 0,
                "average": sum(confidences) / len(confidences) if confidences else 0,
                "trend": progress.confidence_trend if progress else "unknown"
            },
            "performance_stats": {
                "avg_processing_time": sum(processing_times) / len(processing_times) if processing_times else 0,
                "total_duration": session_data[-1].timestamp - session_data[0].timestamp if len(session_data) > 1 else 0
            },
            "learning_progress": asdict(progress) if progress else None
        }

    def get_overall_performance(self) -> Dict[str, Any]:
        """Get overall system performance metrics."""
        
        if not self.performance_history:
            return {"status": "no_data"}
        
        # Calculate overall statistics
        recent_data = list(self.performance_history)[-100:]  # Last 100 measurements
        
        accuracies = [m.accuracy_score for m in recent_data]
        confidences = [m.confidence for m in recent_data]
        processing_times = [m.processing_time_ms for m in recent_data]
        
        # Calculate accuracy distribution
        accuracy_distribution = {
            "excellent": len([a for a in accuracies if a >= self.accuracy_thresholds["excellent"]]),
            "good": len([a for a in accuracies if self.accuracy_thresholds["good"] <= a < self.accuracy_thresholds["excellent"]]),
            "acceptable": len([a for a in accuracies if self.accuracy_thresholds["acceptable"] <= a < self.accuracy_thresholds["good"]]),
            "needs_improvement": len([a for a in accuracies if a < self.accuracy_thresholds["acceptable"]])
        }
        
        # Calculate learning velocity across all sessions
        learning_velocities = [p.learning_velocity for p in self.learning_progress.values() if p.total_interactions > 5]
        avg_learning_velocity = sum(learning_velocities) / len(learning_velocities) if learning_velocities else 0
        
        return {
            "status": "active",
            "total_measurements": len(self.performance_history),
            "active_sessions": len(self.session_metrics),
            "accuracy_stats": {
                "current_average": sum(accuracies) / len(accuracies) if accuracies else 0,
                "distribution": accuracy_distribution,
                "trend": self._calculate_accuracy_trend()
            },
            "confidence_stats": {
                "average": sum(confidences) / len(confidences) if confidences else 0,
                "min": min(confidences) if confidences else 0,
                "max": max(confidences) if confidences else 0
            },
            "performance_stats": {
                "avg_processing_time": sum(processing_times) / len(processing_times) if processing_times else 0,
                "avg_learning_velocity": avg_learning_velocity
            },
            "system_health": self._assess_system_health(accuracies, processing_times)
        }

    def _calculate_accuracy_trend(self) -> str:
        """Calculate overall accuracy trend."""
        
        if len(self.accuracy_trends) < 2:
            return "insufficient_data"
        
        # Get recent trend data
        sorted_timestamps = sorted(self.accuracy_trends.keys())
        recent_timestamps = sorted_timestamps[-10:]  # Last 10 time periods
        
        if len(recent_timestamps) < 3:
            return "insufficient_data"
        
        # Calculate trend
        early_scores = []
        late_scores = []
        
        for ts in recent_timestamps[:len(recent_timestamps)//2]:
            early_scores.extend(self.accuracy_trends[ts])
        
        for ts in recent_timestamps[len(recent_timestamps)//2:]:
            late_scores.extend(self.accuracy_trends[ts])
        
        if not early_scores or not late_scores:
            return "insufficient_data"
        
        early_avg = sum(early_scores) / len(early_scores)
        late_avg = sum(late_scores) / len(late_scores)
        
        if late_avg > early_avg + 0.05:
            return "improving"
        elif late_avg < early_avg - 0.05:
            return "declining"
        else:
            return "stable"

    def _assess_system_health(self, accuracies: List[float], processing_times: List[float]) -> str:
        """Assess overall system health."""
        
        if not accuracies or not processing_times:
            return "unknown"
        
        avg_accuracy = sum(accuracies) / len(accuracies)
        avg_processing_time = sum(processing_times) / len(processing_times)
        
        # Health criteria
        accuracy_healthy = avg_accuracy >= self.accuracy_thresholds["good"]
        performance_healthy = avg_processing_time <= 2000  # 2 seconds
        
        if accuracy_healthy and performance_healthy:
            return "excellent"
        elif accuracy_healthy or performance_healthy:
            return "good"
        elif avg_accuracy >= self.accuracy_thresholds["acceptable"]:
            return "acceptable"
        else:
            return "needs_attention"

    def get_learning_insights(self) -> Dict[str, Any]:
        """Get insights about the learning process."""
        
        insights = {
            "top_performing_sessions": [],
            "learning_patterns": {},
            "improvement_recommendations": []
        }
        
        # Find top performing sessions
        session_performances = []
        for session_id, progress in self.learning_progress.items():
            if progress.total_interactions >= 5:  # Minimum interactions for meaningful analysis
                session_performances.append({
                    "session_id": session_id,
                    "improvement": progress.current_accuracy - progress.initial_accuracy,
                    "learning_velocity": progress.learning_velocity,
                    "total_interactions": progress.total_interactions
                })
        
        # Sort by improvement
        session_performances.sort(key=lambda x: x["improvement"], reverse=True)
        insights["top_performing_sessions"] = session_performances[:5]
        
        # Analyze learning patterns
        if session_performances:
            avg_improvement = sum(s["improvement"] for s in session_performances) / len(session_performances)
            avg_velocity = sum(s["learning_velocity"] for s in session_performances) / len(session_performances)
            
            insights["learning_patterns"] = {
                "average_improvement": avg_improvement,
                "average_learning_velocity": avg_velocity,
                "sessions_improving": len([s for s in session_performances if s["improvement"] > 0]),
                "sessions_declining": len([s for s in session_performances if s["improvement"] < 0])
            }
        
        # Generate recommendations
        if insights["learning_patterns"]:
            patterns = insights["learning_patterns"]
            
            if patterns["average_improvement"] < 0.05:
                insights["improvement_recommendations"].append("Consider adjusting learning rate or weight update frequency")
            
            if patterns["average_learning_velocity"] < 1.0:
                insights["improvement_recommendations"].append("Learning velocity is low - consider more aggressive weight adjustments")
            
            if patterns["sessions_declining"] > patterns["sessions_improving"]:
                insights["improvement_recommendations"].append("More sessions declining than improving - review critic evaluation criteria")
        
        return insights

# Global instance
emotion_performance_monitor = EmotionPerformanceMonitor()
