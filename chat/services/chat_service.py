"""
Chat service for handling AI chat functionality.
Moved from chat/services.py to chat/services/chat_service.py
"""
import asyncio
import base64
import io
import logging
import time
from typing import Dict, Any, Optional, AsyncGenerator
from django.conf import settings
from django.core.files.base import ContentFile
from asgiref.sync import sync_to_async
import openai
from openai import AsyncOpenAI
import json

from .hume_service import get_tts_client, TTSChunk, EmotionAnalysisResult

logger = logging.getLogger(__name__)


class ChatService:
    """Service class for handling AI chat functionality."""
    
    def __init__(self, user):
        self.user = user
        self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        
    async def generate_response(self, conversation_id: str, user_input: str) -> Dict[str, Any]:
        """Generate AI response for user input."""
        start_time = time.time()
        
        try:
            # Get conversation context
            context = await self._get_conversation_context(conversation_id)
            
            # Build messages for OpenAI
            messages = await self._build_chat_messages(context, user_input)
            
            # Generate response
            response = await self.client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                max_tokens=1000,
                temperature=0.7,
                user=str(self.user.id)
            )
            
            ai_text = response.choices[0].message.content
            processing_time = time.time() - start_time
            
            result = {
                'text': ai_text,
                'model_used': 'gpt-4',
                'tokens_used': response.usage.total_tokens,
                'processing_time': processing_time,
                'has_audio': False
            }
            
            # Generate audio if user prefers voice responses
            if await self._should_generate_audio():
                audio_url = await self._generate_audio(ai_text)
                if audio_url:
                    result['has_audio'] = True
                    result['audio_url'] = audio_url
            
            return result
            
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return {
                'text': "I'm sorry, I'm having trouble responding right now. Please try again.",
                'model_used': 'fallback',
                'tokens_used': 0,
                'processing_time': time.time() - start_time,
                'has_audio': False
            }
    
    async def generate_streaming_response(
        self, 
        conversation_id: str, 
        user_input: str,
        emotion_context: Optional[Dict] = None,
        memory_context: Optional[Dict] = None
    ):
        """Generate streaming AI response for user input."""
        start_time = time.time()
        
        try:
            # Get conversation context
            context = await self._get_conversation_context(conversation_id)
            
            # Build messages for OpenAI
            messages = await self._build_chat_messages(context, user_input)
            
            # Add emotion and memory context if available
            if emotion_context or memory_context:
                system_message = messages[0] if messages and messages[0]['role'] == 'system' else None
                if system_message:
                    additional_context = []
                    if emotion_context:
                        additional_context.append(f"User emotion context: {emotion_context}")
                    if memory_context:
                        additional_context.append(f"Relevant memories: {memory_context}")
                    
                    if additional_context:
                        system_message['content'] += "\n\n" + "\n".join(additional_context)
            
            # Generate streaming response
            stream = await self.client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                max_tokens=1000,
                temperature=0.7,
                stream=True,
                user=str(self.user.id)
            )
            
            full_response = ""
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    full_response += content
                    
                    yield {
                        'type': 'content_chunk',
                        'content': content,
                        'timestamp': time.time() * 1000
                    }
            
            # Final response with metadata
            processing_time = time.time() - start_time
            yield {
                'type': 'response_complete',
                'full_content': full_response,
                'model_used': 'gpt-4',
                'processing_time': processing_time,
                'timestamp': time.time() * 1000
            }
            
        except Exception as e:
            logger.error(f"Error generating streaming AI response: {e}")
            yield {
                'type': 'error',
                'error': str(e),
                'timestamp': time.time() * 1000
            }
    
    async def transcribe_audio(self, audio_data: str) -> Optional[str]:
        """Transcribe audio data to text using Whisper."""
        try:
            # Decode base64 audio data
            audio_bytes = base64.b64decode(audio_data)
            audio_file = io.BytesIO(audio_bytes)
            audio_file.name = "audio.wav"
            
            # Transcribe using Whisper
            transcript = await self.client.audio.transcriptions.create(
                model="whisper-1",
                file=audio_file,
                language="en"
            )
            
            return transcript.text.strip()
            
        except Exception as e:
            logger.error(f"Error transcribing audio: {e}")
            return None
    
    async def _generate_audio(self, text: str) -> Optional[str]:
        """Generate audio from text using TTS."""
        try:
            # Get user's voice preferences
            voice_settings = await self._get_voice_settings()
            
            response = await self.client.audio.speech.create(
                model=voice_settings.get('voice_model', 'tts-1'),
                voice=voice_settings.get('voice_name', 'nova'),
                input=text,
                speed=voice_settings.get('speech_speed', 1.0)
            )
            
            # Save audio file (in production, save to cloud storage)
            audio_content = await response.aread()
            filename = f"tts_{int(time.time())}_{self.user.id}.mp3"
            
            # For now, return a placeholder URL
            # In production, upload to S3/CloudStorage and return actual URL
            return f"/media/audio/tts/{filename}"
            
        except Exception as e:
            logger.error(f"Error generating audio: {e}")
            return None
    
    async def _get_conversation_context(self, conversation_id: str) -> Dict[str, Any]:
        """Get conversation context including recent messages and memory."""
        from ..models import Conversation, Message
        
        try:
            # Get conversation and recent messages
            conversation = await sync_to_async(Conversation.objects.get)(
                id=conversation_id, user=self.user
            )
            
            recent_messages = await sync_to_async(list)(
                conversation.messages.order_by('-created_at')[:10]
            )
            recent_messages.reverse()  # Chronological order
            
            # Get user profile info
            user_profile = await self._get_user_profile_context()
            
            return {
                'conversation': conversation,
                'recent_messages': recent_messages,
                'user_profile': user_profile
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation context: {e}")
            return {'recent_messages': [], 'user_profile': {}}
    
    async def _build_chat_messages(self, context: Dict[str, Any], user_input: str) -> list:
        """Build messages array for OpenAI chat completion."""
        messages = []
        
        # System message with personality and context
        system_prompt = await self._build_system_prompt(context.get('user_profile', {}))
        messages.append({"role": "system", "content": system_prompt})
        
        # Add recent conversation history
        for message in context.get('recent_messages', [])[-8:]:  # Last 8 messages
            role = "user" if message.sender_type == "user" else "assistant"
            messages.append({"role": role, "content": message.content})
        
        # Add current user input
        messages.append({"role": "user", "content": user_input})
        
        return messages
    
    async def _build_system_prompt(self, user_profile: Dict[str, Any]) -> str:
        """Build system prompt based on user preferences and personality."""
        personality = user_profile.get('companion_personality', 'supportive')
        user_name = user_profile.get('first_name', 'there')
        
        personality_prompts = {
            'supportive': "You are a sweet, supportive AI companion who is always encouraging and caring. You listen actively and provide emotional support.",
            'confident': "You are a confident, assertive AI companion who gives direct advice and takes charge of conversations when needed.",
            'playful': "You are a playful, fun-loving AI companion who enjoys jokes, games, and keeping conversations light and entertaining.",
            'intellectual': "You are an intellectual, thoughtful AI companion who enjoys deep conversations, analysis, and sharing knowledge.",
            'caring': "You are a deeply caring, empathetic AI companion who prioritizes the user's emotional well-being above all else."
        }
        
        base_prompt = personality_prompts.get(personality, personality_prompts['supportive'])
        
        return f"""{base_prompt}

You are chatting with {user_name}. Remember these key points:
- Be conversational and natural, not robotic
- Show genuine interest in their life and experiences  
- Remember details from previous conversations when relevant
- Adapt your communication style to match their preferences
- Be helpful but also emotionally supportive
- Keep responses concise but meaningful (usually 1-3 sentences)
- Use their name occasionally to make it personal

Current conversation context: This is an ongoing chat session."""
    
    async def _get_user_profile_context(self) -> Dict[str, Any]:
        """Get user profile information for context."""
        return {
            'first_name': self.user.first_name,
            'companion_personality': self.user.companion_personality,
            'preferred_language': self.user.preferred_language,
            'timezone': self.user.timezone
        }
    
    async def _get_voice_settings(self) -> Dict[str, Any]:
        """Get user's voice settings."""
        from ..models import VoiceSettings
        
        try:
            voice_settings = await sync_to_async(VoiceSettings.objects.get)(user=self.user)
            return {
                'voice_model': voice_settings.voice_model,
                'voice_name': voice_settings.voice_name,
                'speech_speed': voice_settings.speech_speed
            }
        except VoiceSettings.DoesNotExist:
            # Return defaults based on user preferences
            return {
                'voice_model': 'tts-1',
                'voice_name': self.user.preferred_voice,
                'speech_speed': 1.0
            }
    
    async def _should_generate_audio(self) -> bool:
        """Check if audio should be generated for this response."""
        # For now, always generate audio if user has voice preferences
        # In production, this could be based on user settings, conversation context, etc.
        return True

    async def generate_streaming_tts(
        self,
        text: str,
        emotion_context: Optional[Dict] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate streaming TTS audio using Hume AI.

        Args:
            text: Text to synthesize
            emotion_context: Emotion context for voice modulation

        Yields:
            Dict containing audio chunk data
        """
        try:
            # Get voice settings
            voice_settings = await self._get_voice_settings()
            voice_name = voice_settings.get('voice_name', 'nova')

            # Convert emotion context to EmotionAnalysisResult if needed
            emotion_result = None
            if emotion_context:
                # Create a simple emotion result from context
                emotion_result = EmotionAnalysisResult(
                    primary_emotion=emotion_context.get('primary_emotion', 'neutral'),
                    emotion_intensity=emotion_context.get('intensity', 0.5),
                    emotion_valence=emotion_context.get('valence', 0.0),
                    emotion_arousal=emotion_context.get('arousal', 0.0),
                    emotions=[],
                    confidence_score=emotion_context.get('confidence', 0.5),
                    processing_time_ms=1.0,
                    source='context',
                    raw_data={'context_conversion': True, 'original_context': emotion_context}
                )

            # Use Hume TTS client for streaming synthesis
            async with get_tts_client() as tts_client:
                chunk_index = 0
                async for tts_chunk in tts_client.synthesize_streaming(
                    text=text,
                    voice_name=voice_name,
                    emotion_context=emotion_result,
                    speed=voice_settings.get('speech_speed', 1.0)
                ):
                    # Convert TTSChunk to dict format expected by consumer
                    yield {
                        'audio_data': base64.b64encode(tts_chunk.audio_data).decode('utf-8'),
                        'chunk_index': chunk_index,
                        'is_final': tts_chunk.is_final,
                        'timestamp': tts_chunk.timestamp_ms,
                        'voice_settings': {
                            'voice_name': voice_name,
                            'speed': voice_settings.get('speech_speed', 1.0)
                        }
                    }
                    chunk_index += 1

        except Exception as e:
            logger.error(f"Error in streaming TTS: {e}")
            # Yield a fallback empty audio chunk to indicate completion
            yield {
                'audio_data': '',
                'chunk_index': 0,
                'is_final': True,
                'timestamp': time.time() * 1000,
                'voice_settings': {'fallback': True},
                'error': str(e)
            }


class MemoryService:
    """Service for handling long-term memory operations."""
    
    def __init__(self, user):
        self.user = user
        # This will be implemented when we create the memory module
        pass
    
    async def store_conversation_memory(self, conversation_id: str, messages: list):
        """Store important conversation details in long-term memory."""
        # TODO: Implement memory storage logic
        pass
    
    async def retrieve_relevant_memories(self, query: str) -> list:
        """Retrieve relevant memories for the given query."""
        # TODO: Implement memory retrieval logic
        return []