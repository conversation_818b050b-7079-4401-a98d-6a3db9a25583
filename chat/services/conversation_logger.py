"""
Conversation Logger for Enhanced Conversational Refinement System.
Provides detailed logging and visual output like the demo for production monitoring.
"""
import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import json


class ConversationLogger:
    """Enhanced conversation logger with visual output and detailed metrics."""
    
    def __init__(self, enable_visual_output: bool = True, log_level: str = 'INFO'):
        """Initialize conversation logger."""
        self.enable_visual_output = enable_visual_output
        self.conversation_stats = {
            'total_conversations': 0,
            'total_turns': 0,
            'total_refinements': 0,
            'avg_response_time': 0,
            'personality_usage': {},
            'domain_usage': {},
            'accuracy_scores': []
        }
        
        # Setup logging
        self.logger = logging.getLogger('conversation_refinement')
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Create handler if not exists
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def print_header(self, title: str):
        """Print a formatted header."""
        if self.enable_visual_output:
            print(f"\n{'='*100}")
            print(f"🎭 {title}")
            print(f"{'='*100}")
        self.logger.info(f"HEADER: {title}")

    def print_section(self, title: str):
        """Print a formatted section."""
        if self.enable_visual_output:
            print(f"\n🔹 {title}")
            print(f"{'-'*80}")
        self.logger.info(f"SECTION: {title}")

    def print_persona_intro(self, user_name: str, personality: str, companion_name: str, agent_type: str = None):
        """Print persona introduction like the demo."""
        if self.enable_visual_output:
            print(f"\n👤 USER: {user_name}")
            print(f"   🧠 Personality: {personality}")
            
            if agent_type and companion_name:
                print(f"\n🤖 COMPANION: {companion_name}")
                print(f"   🎭 Domain: {agent_type}")
                print(f"\n🎬 Starting conversation...")
        
        self.logger.info(f"PERSONA: {user_name} ({personality}) with {companion_name} ({agent_type})")
        
        # Update stats
        self.conversation_stats['personality_usage'][personality] = \
            self.conversation_stats['personality_usage'].get(personality, 0) + 1
        if agent_type:
            self.conversation_stats['domain_usage'][agent_type] = \
                self.conversation_stats['domain_usage'].get(agent_type, 0) + 1

    def print_conversation_turn(
        self, 
        speaker: str, 
        message: str, 
        metadata: Dict[str, Any] = None,
        accuracy_scores: Dict[str, float] = None,
        agent_type: str = None
    ):
        """Print a conversation turn with detailed metadata like the demo."""
        if self.enable_visual_output:
            # Print user message
            if speaker.startswith('👤') or speaker == 'user':
                print(f"\n👤 {speaker.replace('👤 ', '')}: {message}")
            else:
                # Print AI response with metadata
                companion_name = metadata.get('companion_name', 'Assistant') if metadata else 'Assistant'
                print(f"\n🤖 {companion_name}: {message}")
                
                # Print metadata like the demo
                if metadata:
                    status_indicators = []
                    
                    # Accuracy score
                    if accuracy_scores and 'overall' in accuracy_scores:
                        score = accuracy_scores['overall']
                        if score >= 0.8:
                            status_indicators.append(f"🎯 EXCELLENT ({score:.2f})")
                        elif score >= 0.65:
                            status_indicators.append(f"✅ GOOD ({score:.2f})")
                        else:
                            status_indicators.append(f"⚠️ NEEDS_IMPROVEMENT ({score:.2f})")
                    
                    # Personality applied
                    if metadata.get('personality_applied'):
                        status_indicators.append("🎭 PERSONALITY")
                    
                    # Refinement applied
                    if metadata.get('refinement_applied'):
                        status_indicators.append("✨ REFINED")
                    
                    # Agent working
                    if metadata.get('agent_working'):
                        status_indicators.append("🔄 AGENT_WORKING")
                    
                    # Memory used
                    if metadata.get('memory_used'):
                        status_indicators.append("🧠 MEMORY")
                    
                    # Questions asked
                    if metadata.get('questions_asked'):
                        status_indicators.append("❓ QUESTIONS")
                    
                    # Domain expertise
                    if agent_type:
                        status_indicators.append(f"🎯 {agent_type.upper()}_AGENT")
                    
                    # Response time
                    if metadata.get('response_time_ms'):
                        response_time = metadata['response_time_ms']
                        if response_time < 200:
                            status_indicators.append(f"⚡ FAST ({response_time:.0f}ms)")
                        elif response_time < 1000:
                            status_indicators.append(f"🕐 NORMAL ({response_time:.0f}ms)")
                        else:
                            status_indicators.append(f"🐌 SLOW ({response_time:.0f}ms)")
                    
                    if status_indicators:
                        print(f"   └─ {' | '.join(status_indicators)}")
        
        # Log the conversation turn
        log_data = {
            'speaker': speaker,
            'message_length': len(message),
            'metadata': metadata,
            'accuracy_scores': accuracy_scores,
            'agent_type': agent_type,
            'timestamp': datetime.now().isoformat()
        }
        self.logger.info(f"TURN: {json.dumps(log_data, default=str)}")
        
        # Update stats
        self.conversation_stats['total_turns'] += 1
        if accuracy_scores and 'overall' in accuracy_scores:
            self.conversation_stats['accuracy_scores'].append(accuracy_scores['overall'])
        if metadata and metadata.get('refinement_applied'):
            self.conversation_stats['total_refinements'] += 1

    def print_agent_processing(self, agent_type: str, task_description: str, duration_seconds: float = None):
        """Print agent processing status like the demo."""
        if self.enable_visual_output:
            if duration_seconds:
                print(f"   🔄 {task_description}...")
                print(f"   ✅ Complete ({duration_seconds:.1f}s)")
            else:
                print(f"   🔄 {task_description}...")

        self.logger.info(f"AGENT_PROCESSING: {agent_type} - {task_description} ({duration_seconds}s)")

    def print_agent_result(self, agent_type: str, agent_result: Dict[str, Any], processing_time: float = None):
        """Print the full agent result like the demo."""
        if self.enable_visual_output and agent_result:
            # Extract the main response content
            response_content = ""

            # Try different keys that might contain the response
            for key in ['response', 'result', 'content', 'answer', 'output']:
                if key in agent_result:
                    response_content = str(agent_result[key])
                    break

            # If no standard key found, use the whole result
            if not response_content and agent_result:
                response_content = str(agent_result)

            # Truncate very long responses for display
            if len(response_content) > 500:
                response_content = response_content[:500] + "..."

            if response_content:
                print(f"\n🤖 {agent_type.upper()} AGENT RESULT:")
                print(f"   📋 {response_content}")
                if processing_time:
                    print(f"   ⏱️ Processing time: {processing_time:.1f}s")

        self.logger.info(f"AGENT_RESULT: {agent_type} - {len(str(agent_result))} chars - {processing_time}s")

    def print_full_agent_response(self, agent_name: str, full_response: str, metadata: Dict[str, Any] = None):
        """Print the complete agent response with formatting like the demo."""
        if self.enable_visual_output:
            print(f"\n🤖 {agent_name.upper()} FULL RESPONSE:")
            print(f"   💬 {full_response}")

            if metadata:
                details = []
                if metadata.get('processing_time_ms'):
                    details.append(f"⏱️ {metadata['processing_time_ms']:.0f}ms")
                if metadata.get('tokens_used'):
                    details.append(f"🔤 {metadata['tokens_used']} tokens")
                if metadata.get('model'):
                    details.append(f"🧠 {metadata['model']}")
                if metadata.get('confidence'):
                    details.append(f"📊 {metadata['confidence']:.2f} confidence")

                if details:
                    print(f"   └─ {' | '.join(details)}")

        self.logger.info(f"FULL_AGENT_RESPONSE: {agent_name} - {len(full_response)} chars - {metadata}")

    def print_refinement_opportunity(self, request_id: str, refinement_type: str, confidence: float = None):
        """Print refinement opportunity detection."""
        if self.enable_visual_output:
            confidence_str = f" (confidence: {confidence:.2f})" if confidence else ""
            print(f"   🔍 Refinement opportunity detected: {refinement_type}{confidence_str}")
        
        self.logger.info(f"REFINEMENT_OPPORTUNITY: {request_id} - {refinement_type} - {confidence}")

    def print_error(self, error_type: str, error_message: str, context: Dict[str, Any] = None):
        """Print error with context."""
        if self.enable_visual_output:
            print(f"   ❌ ERROR: {error_type} - {error_message}")
        
        self.logger.error(f"ERROR: {error_type} - {error_message} - Context: {context}")

    def print_conversation_summary(self, conversation_id: str, total_turns: int, avg_accuracy: float = None):
        """Print conversation summary."""
        if self.enable_visual_output:
            print(f"\n📊 CONVERSATION SUMMARY:")
            print(f"   • Conversation ID: {conversation_id}")
            print(f"   • Total turns: {total_turns}")
            if avg_accuracy:
                print(f"   • Average accuracy: {avg_accuracy:.2f}")
        
        self.logger.info(f"CONVERSATION_SUMMARY: {conversation_id} - {total_turns} turns - {avg_accuracy} accuracy")
        
        # Update global stats
        self.conversation_stats['total_conversations'] += 1

    def print_system_stats(self):
        """Print overall system statistics like the demo."""
        if self.enable_visual_output:
            print(f"\n📊 SYSTEM STATISTICS:")
            print(f"   • Total conversations: {self.conversation_stats['total_conversations']}")
            print(f"   • Total turns: {self.conversation_stats['total_turns']}")
            print(f"   • Total refinements: {self.conversation_stats['total_refinements']}")
            
            if self.conversation_stats['accuracy_scores']:
                avg_accuracy = sum(self.conversation_stats['accuracy_scores']) / len(self.conversation_stats['accuracy_scores'])
                print(f"   • Average accuracy: {avg_accuracy:.2f}")
            
            print(f"\n🎭 PERSONALITY USAGE:")
            for personality, count in self.conversation_stats['personality_usage'].items():
                print(f"   • {personality}: {count} conversations")
            
            print(f"\n🎯 DOMAIN USAGE:")
            for domain, count in self.conversation_stats['domain_usage'].items():
                print(f"   • {domain}: {count} conversations")
        
        self.logger.info(f"SYSTEM_STATS: {json.dumps(self.conversation_stats, default=str)}")

    def log_llm_request(self, provider: str, model: str, tokens_used: int = None, response_time_ms: float = None):
        """Log LLM API requests."""
        if self.enable_visual_output and response_time_ms:
            print(f"INFO HTTP Request: {provider} {model} \"HTTP/1.1 200 OK\" ({response_time_ms:.0f}ms)")
        
        self.logger.info(f"LLM_REQUEST: {provider} {model} - {tokens_used} tokens - {response_time_ms}ms")

    def log_memory_operation(self, operation: str, user_id: str, memory_count: int = None):
        """Log memory operations."""
        self.logger.info(f"MEMORY_OPERATION: {operation} - {user_id} - {memory_count} memories")

    def log_domain_validation(self, agent_type: str, validation_result: bool, reason: str = None):
        """Log domain validation results."""
        status = "PASSED" if validation_result else "FAILED"
        if self.enable_visual_output and not validation_result:
            print(f"   ⚠️ Domain validation {status}: {agent_type} - {reason}")
        
        self.logger.info(f"DOMAIN_VALIDATION: {agent_type} - {status} - {reason}")

    def get_stats(self) -> Dict[str, Any]:
        """Get current conversation statistics."""
        return self.conversation_stats.copy()

    def reset_stats(self):
        """Reset conversation statistics."""
        self.conversation_stats = {
            'total_conversations': 0,
            'total_turns': 0,
            'total_refinements': 0,
            'avg_response_time': 0,
            'personality_usage': {},
            'domain_usage': {},
            'accuracy_scores': []
        }


# Global conversation logger instance
conversation_logger = ConversationLogger(
    enable_visual_output=True,  # Set to False in production if needed
    log_level='INFO'
)
