"""
Chat services package for real-time AI companion functionality.
"""

from .chat_service import ChatSer<PERSON>, MemoryService
from .groq_service import (
    GroqStreamingClient,
    GroqResponse,
    GroqStreamChunk,
    stream_groq_response,
    stream_with_fallback,
    get_groq_client
)
from .hume_service import (
    HumeEmotionClient,
    HumeTTSClient,
    EmotionScore,
    EmotionAnalysisResult,
    TTSChunk,
    TTSResult,
    analyze_audio_emotions,
    analyze_text_emotions,
    synthesize_speech_streaming,
    get_emotion_client,
    get_tts_client
)
from .audio_service import (
    AudioProcessingService,
    AudioChunk,
    TranscriptionResult,
    AudioProcessingResult,
    get_audio_service
)
# Audio format handling is now part of audio_service.py

__all__ = [
    'ChatService',
    'MemoryService',
    'GroqStreamingClient',
    'GroqResponse', 
    'GroqStreamChunk',
    'stream_groq_response',
    'stream_with_fallback',
    'get_groq_client',
    'HumeEmotionClient',
    'HumeTTSClient',
    'EmotionScore',
    'EmotionAnalysisResult',
    'TTSChunk',
    'TTSResult',
    'analyze_audio_emotions',
    'analyze_text_emotions',
    'synthesize_speech_streaming',
    'get_emotion_client',
    'get_tts_client',
    'AudioProcessingService',
    'AudioChunk',
    'TranscriptionResult',
    'AudioProcessingResult',
    'get_audio_service',
    # Audio format handling classes are available in audio_service module
]