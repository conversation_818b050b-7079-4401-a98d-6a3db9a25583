"""
Audio format handling and validation module.
"""
import base64
import io
import logging
import re
import wave
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Any, Optional, Tuple, Union

import numpy as np

logger = logging.getLogger(__name__)

# Try to import pydub for advanced audio processing
try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    logger.info("pydub not available, using basic audio processing only")


class AudioFormat(Enum):
    """Supported audio formats."""
    WAV = "wav"
    MP3 = "mp3"
    WEBM = "webm"
    OGG = "ogg"
    FLAC = "flac"
    PCM = "pcm"


@dataclass
class AudioMetadata:
    """Audio metadata information."""
    format: AudioFormat
    sample_rate: int
    channels: int
    duration_ms: float
    bit_depth: int
    size_bytes: int
    is_valid: bool
    quality_score: float = 0.0
    error_message: Optional[str] = None


class AudioFormatHandler:
    """Handles audio format detection, validation, and conversion."""
    
    # Default audio parameters for optimal processing
    DEFAULT_SAMPLE_RATE = 16000
    DEFAULT_CHANNELS = 1
    DEFAULT_BIT_DEPTH = 16
    
    # Format detection signatures
    FORMAT_SIGNATURES = {
        b'RIFF': AudioFormat.WAV,
        b'ID3': AudioFormat.MP3,
        b'\xff\xfb': AudioFormat.MP3,  # MP3 frame header
        b'\xff\xf3': AudioFormat.MP3,  # MP3 frame header
        b'OggS': AudioFormat.OGG,
        b'fLaC': AudioFormat.FLAC,
    }
    
    @classmethod
    def decode_base64_audio(cls, base64_data: str) -> Tuple[bytes, Optional[AudioFormat]]:
        """
        Decode base64 audio data and detect format.
        
        Args:
            base64_data: Base64 encoded audio data or data URI
            
        Returns:
            Tuple of (audio_bytes, detected_format)
            
        Raises:
            ValueError: If base64 data is invalid
        """
        try:
            # Handle data URI format (data:audio/wav;base64,...)
            detected_format = None
            if base64_data.startswith('data:'):
                # Extract format from data URI
                match = re.match(r'data:audio/(\w+);base64,(.+)', base64_data)
                if match:
                    format_str, base64_data = match.groups()
                    try:
                        detected_format = AudioFormat(format_str.lower())
                    except ValueError:
                        logger.warning(f"Unknown audio format in data URI: {format_str}")
            
            # Decode base64 data
            audio_bytes = base64.b64decode(base64_data)
            
            # If no format detected from URI, try to detect from bytes
            if detected_format is None:
                detected_format = cls._detect_format_from_bytes(audio_bytes)
            
            return audio_bytes, detected_format
            
        except Exception as e:
            raise ValueError(f"Invalid base64 audio data: {str(e)}")
    
    @classmethod
    def _detect_format_from_bytes(cls, audio_bytes: bytes) -> Optional[AudioFormat]:
        """
        Detect audio format from byte signature.
        
        Args:
            audio_bytes: Raw audio data
            
        Returns:
            Detected AudioFormat or None if unknown
        """
        if len(audio_bytes) < 4:
            return None
        
        # Check for known format signatures
        for signature, format_type in cls.FORMAT_SIGNATURES.items():
            if audio_bytes.startswith(signature):
                return format_type
        
        # Special case for WAV files (check for WAVE format)
        if len(audio_bytes) >= 12 and audio_bytes[8:12] == b'WAVE':
            return AudioFormat.WAV
        
        return None
    
    @classmethod
    def extract_metadata(cls, audio_bytes: bytes, format_hint: Optional[AudioFormat] = None) -> AudioMetadata:
        """
        Extract metadata from audio data.
        
        Args:
            audio_bytes: Raw audio data
            format_hint: Hint about the audio format
            
        Returns:
            AudioMetadata object
        """
        try:
            # Detect format if not provided
            if format_hint is None:
                format_hint = cls._detect_format_from_bytes(audio_bytes)
            
            if format_hint == AudioFormat.WAV:
                return cls._extract_wav_metadata(audio_bytes)
            elif format_hint in [AudioFormat.MP3, AudioFormat.OGG, AudioFormat.FLAC]:
                if PYDUB_AVAILABLE:
                    return cls._extract_metadata_with_pydub(audio_bytes, format_hint)
                else:
                    return cls._create_error_metadata(format_hint, "Advanced format requires pydub")
            else:
                # Assume PCM data
                return cls._extract_pcm_metadata(audio_bytes)
                
        except Exception as e:
            return cls._create_error_metadata(
                format_hint or AudioFormat.PCM,
                f"Failed to extract metadata: {str(e)}"
            )
    
    @classmethod
    def _extract_wav_metadata(cls, wav_bytes: bytes) -> AudioMetadata:
        """Extract metadata from WAV file."""
        try:
            wav_io = io.BytesIO(wav_bytes)
            with wave.open(wav_io, 'rb') as wav_file:
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                bit_depth = wav_file.getsampwidth() * 8
                frames = wav_file.getnframes()
                duration_ms = (frames / sample_rate) * 1000.0
                
                # Read samples for quality assessment
                wav_file.rewind()
                raw_audio = wav_file.readframes(frames)
                samples = np.frombuffer(raw_audio, dtype=np.int16)
                quality_score = cls._assess_audio_quality_from_samples(samples)
                
                return AudioMetadata(
                    format=AudioFormat.WAV,
                    sample_rate=sample_rate,
                    channels=channels,
                    duration_ms=duration_ms,
                    bit_depth=bit_depth,
                    size_bytes=len(wav_bytes),
                    is_valid=True,
                    quality_score=quality_score
                )
        except Exception as e:
            return cls._create_error_metadata(AudioFormat.WAV, f"WAV parsing error: {str(e)}")
    
    @classmethod
    def _extract_pcm_metadata(cls, pcm_bytes: bytes) -> AudioMetadata:
        """Extract metadata from raw PCM data."""
        try:
            # Check if the buffer size is valid for 16-bit PCM (must be multiple of 2 bytes)
            if len(pcm_bytes) % 2 != 0:
                return cls._create_error_metadata(AudioFormat.PCM, "PCM parsing error: buffer size must be a multiple of element size")
                
            # Assume 16-bit PCM
            samples = np.frombuffer(pcm_bytes, dtype=np.int16)
            duration_ms = (len(samples) / cls.DEFAULT_SAMPLE_RATE) * 1000.0
            quality_score = cls._assess_audio_quality_from_samples(samples)
            
            return AudioMetadata(
                format=AudioFormat.PCM,
                sample_rate=cls.DEFAULT_SAMPLE_RATE,
                channels=cls.DEFAULT_CHANNELS,
                duration_ms=duration_ms,
                bit_depth=cls.DEFAULT_BIT_DEPTH,
                size_bytes=len(pcm_bytes),
                is_valid=True,
                quality_score=quality_score
            )
        except Exception as e:
            return cls._create_error_metadata(AudioFormat.PCM, f"PCM parsing error: {str(e)}")
    
    @classmethod
    def _extract_metadata_with_pydub(cls, audio_bytes: bytes, format_type: AudioFormat) -> AudioMetadata:
        """Extract metadata using pydub for advanced formats."""
        try:
            audio_io = io.BytesIO(audio_bytes)
            
            if format_type == AudioFormat.MP3:
                audio = AudioSegment.from_mp3(audio_io)
            elif format_type == AudioFormat.OGG:
                audio = AudioSegment.from_ogg(audio_io)
            elif format_type == AudioFormat.FLAC:
                audio = AudioSegment.from_flac(audio_io)
            else:
                raise ValueError(f"Unsupported format for pydub: {format_type}")
            
            # Extract basic metadata
            sample_rate = audio.frame_rate
            channels = audio.channels
            bit_depth = audio.sample_width * 8
            duration_ms = len(audio)
            
            # Convert to numpy array for quality assessment
            samples = np.array(audio.get_array_of_samples())
            if channels == 2:
                samples = samples.reshape((-1, 2))
                samples = samples.mean(axis=1)  # Convert to mono for quality assessment
            
            quality_score = cls._assess_audio_quality_from_samples(samples.astype(np.int16))
            
            return AudioMetadata(
                format=format_type,
                sample_rate=sample_rate,
                channels=channels,
                duration_ms=duration_ms,
                bit_depth=bit_depth,
                size_bytes=len(audio_bytes),
                is_valid=True,
                quality_score=quality_score
            )
        except Exception as e:
            return cls._create_error_metadata(format_type, f"Pydub parsing error: {str(e)}")
    
    @classmethod
    def _assess_audio_quality_from_samples(cls, samples: np.ndarray) -> float:
        """
        Assess audio quality from sample data.
        
        Args:
            samples: Audio samples as numpy array
            
        Returns:
            Quality score between 0.0 and 1.0
        """
        if len(samples) == 0:
            return 0.0
        
        try:
            # Convert to float for analysis
            samples_float = samples.astype(np.float32)
            
            # Calculate various quality metrics
            
            # 1. Signal-to-noise ratio estimation
            signal_power = np.mean(samples_float ** 2)
            if signal_power == 0:
                return 0.0
            
            # 2. Dynamic range
            max_val = np.max(np.abs(samples_float))
            if max_val == 0:
                return 0.0
            
            dynamic_range = max_val / (np.std(samples_float) + 1e-10)
            
            # 3. Clipping detection
            max_possible = 32767.0 if samples.dtype == np.int16 else 1.0
            clipping_ratio = np.sum(np.abs(samples_float) >= max_possible * 0.95) / len(samples_float)
            
            # 4. Zero-crossing rate (indicates speech/music vs noise)
            zero_crossings = np.sum(np.diff(np.sign(samples_float)) != 0)
            zcr = zero_crossings / len(samples_float)
            
            # Combine metrics into quality score
            quality_score = 0.0
            
            # Signal power component (0-0.3)
            if signal_power > 1000:  # Reasonable signal level
                quality_score += 0.3
            elif signal_power > 100:
                quality_score += 0.15
            
            # Dynamic range component (0-0.3)
            if dynamic_range > 10:
                quality_score += 0.3
            elif dynamic_range > 5:
                quality_score += 0.15
            
            # Clipping penalty (0-0.2)
            if clipping_ratio < 0.01:  # Less than 1% clipping
                quality_score += 0.2
            elif clipping_ratio < 0.05:
                quality_score += 0.1
            
            # Zero-crossing rate component (0-0.2)
            if 0.01 < zcr < 0.3:  # Reasonable for speech/music
                quality_score += 0.2
            elif 0.005 < zcr < 0.5:
                quality_score += 0.1
            
            return min(quality_score, 1.0)
            
        except Exception as e:
            logger.warning(f"Quality assessment failed: {e}")
            return 0.5  # Default moderate quality
    
    @classmethod
    def _create_error_metadata(cls, format_type: AudioFormat, error_message: str) -> AudioMetadata:
        """Create error metadata object."""
        return AudioMetadata(
            format=format_type,
            sample_rate=0,
            channels=0,
            duration_ms=0.0,
            bit_depth=0,
            size_bytes=0,
            is_valid=False,
            quality_score=0.0,
            error_message=error_message
        )
    
    @classmethod
    def validate_audio(
        cls,
        audio_bytes: bytes,
        min_duration_ms: float = 100,
        max_duration_ms: float = 30000,
        min_quality_score: float = 0.1,
        required_sample_rate: Optional[int] = None,
        required_channels: Optional[int] = None,
        required_bit_depth: Optional[int] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate audio data against requirements.
        
        Args:
            audio_bytes: Raw audio data
            min_duration_ms: Minimum duration in milliseconds
            max_duration_ms: Maximum duration in milliseconds
            min_quality_score: Minimum quality score
            required_sample_rate: Required sample rate (optional)
            required_channels: Required channel count (optional)
            required_bit_depth: Required bit depth (optional)
            
        Returns:
            Tuple of (is_valid, validation_results)
        """
        results = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'metadata': None
        }
        
        try:
            # Extract metadata
            metadata = cls.extract_metadata(audio_bytes)
            results['metadata'] = metadata
            
            if not metadata.is_valid:
                results['valid'] = False
                results['errors'].append(f"Invalid audio format: {metadata.error_message}")
                return False, results
            
            # Duration validation
            if metadata.duration_ms < min_duration_ms:
                results['valid'] = False
                results['errors'].append(f"Audio too short: {metadata.duration_ms:.1f}ms < {min_duration_ms}ms")
            
            if metadata.duration_ms > max_duration_ms:
                results['valid'] = False
                results['errors'].append(f"Audio too long: {metadata.duration_ms:.1f}ms > {max_duration_ms}ms")
            
            # Quality validation
            if metadata.quality_score < min_quality_score:
                results['valid'] = False
                results['errors'].append(f"Audio quality too low: {metadata.quality_score:.2f} < {min_quality_score}")
            
            # Sample rate validation
            if required_sample_rate and metadata.sample_rate != required_sample_rate:
                results['valid'] = False
                results['errors'].append(f"Wrong sample rate: {metadata.sample_rate} != {required_sample_rate}")
            
            # Channel count validation
            if required_channels and metadata.channels != required_channels:
                results['valid'] = False
                results['errors'].append(f"Wrong channel count: {metadata.channels} != {required_channels}")
            
            # Bit depth validation
            if required_bit_depth and metadata.bit_depth != required_bit_depth:
                results['valid'] = False
                results['errors'].append(f"Wrong bit depth: {metadata.bit_depth} != {required_bit_depth}")
            
            return results['valid'], results
            
        except Exception as e:
            results['valid'] = False
            results['errors'].append(f"Validation error: {str(e)}")
            return False, results
    
    @classmethod
    def convert_to_optimal_format(
        cls,
        audio_bytes: bytes,
        source_format: AudioFormat,
        target_sample_rate: int = DEFAULT_SAMPLE_RATE,
        target_channels: int = DEFAULT_CHANNELS,
        target_bit_depth: int = DEFAULT_BIT_DEPTH
    ) -> Tuple[bytes, AudioMetadata]:
        """
        Convert audio to optimal format for processing.
        
        Args:
            audio_bytes: Source audio data
            source_format: Source audio format
            target_sample_rate: Target sample rate
            target_channels: Target channel count
            target_bit_depth: Target bit depth
            
        Returns:
            Tuple of (converted_bytes, metadata)
        """
        try:
            # Check if already in optimal format
            current_metadata = cls.extract_metadata(audio_bytes, source_format)
            
            if (current_metadata.is_valid and
                current_metadata.sample_rate == target_sample_rate and
                current_metadata.channels == target_channels and
                current_metadata.bit_depth == target_bit_depth):
                return audio_bytes, current_metadata
            
            # Convert using pydub if available
            if PYDUB_AVAILABLE and source_format != AudioFormat.PCM:
                return cls._convert_with_pydub(
                    audio_bytes, source_format,
                    target_sample_rate, target_channels, target_bit_depth
                )
            else:
                # Basic conversion for WAV/PCM
                return cls._convert_basic(
                    audio_bytes, source_format,
                    target_sample_rate, target_channels, target_bit_depth
                )
                
        except Exception as e:
            logger.error(f"Audio conversion failed: {e}")
            # Return original if conversion fails
            metadata = cls.extract_metadata(audio_bytes, source_format)
            return audio_bytes, metadata
    
    @classmethod
    def _convert_with_pydub(
        cls,
        audio_bytes: bytes,
        source_format: AudioFormat,
        target_sample_rate: int,
        target_channels: int,
        target_bit_depth: int
    ) -> Tuple[bytes, AudioMetadata]:
        """Convert audio using pydub."""
        audio_io = io.BytesIO(audio_bytes)
        
        # Load audio based on format
        if source_format == AudioFormat.WAV:
            audio = AudioSegment.from_wav(audio_io)
        elif source_format == AudioFormat.MP3:
            audio = AudioSegment.from_mp3(audio_io)
        elif source_format == AudioFormat.OGG:
            audio = AudioSegment.from_ogg(audio_io)
        elif source_format == AudioFormat.FLAC:
            audio = AudioSegment.from_flac(audio_io)
        else:
            raise ValueError(f"Unsupported source format: {source_format}")
        
        # Apply conversions
        if audio.frame_rate != target_sample_rate:
            audio = audio.set_frame_rate(target_sample_rate)
        
        if audio.channels != target_channels:
            audio = audio.set_channels(target_channels)
        
        target_sample_width = target_bit_depth // 8
        if audio.sample_width != target_sample_width:
            audio = audio.set_sample_width(target_sample_width)
        
        # Export to WAV
        output_io = io.BytesIO()
        audio.export(output_io, format="wav")
        converted_bytes = output_io.getvalue()
        
        # Get metadata for converted audio
        metadata = cls.extract_metadata(converted_bytes, AudioFormat.WAV)
        
        return converted_bytes, metadata
    
    @classmethod
    def _convert_basic(
        cls,
        audio_bytes: bytes,
        source_format: AudioFormat,
        target_sample_rate: int,
        target_channels: int,
        target_bit_depth: int
    ) -> Tuple[bytes, AudioMetadata]:
        """Basic audio conversion for WAV/PCM."""
        if source_format == AudioFormat.WAV:
            # For WAV, we can only return as-is in basic mode
            metadata = cls.extract_metadata(audio_bytes, source_format)
            return audio_bytes, metadata
        else:
            # For other formats, assume PCM and create WAV wrapper
            metadata = cls.extract_metadata(audio_bytes, AudioFormat.PCM)
            
            # Create WAV file from PCM data
            wav_io = io.BytesIO()
            with wave.open(wav_io, 'wb') as wav_file:
                wav_file.setnchannels(target_channels)
                wav_file.setsampwidth(target_bit_depth // 8)
                wav_file.setframerate(target_sample_rate)
                wav_file.writeframes(audio_bytes)
            
            wav_bytes = wav_io.getvalue()
            wav_metadata = cls.extract_metadata(wav_bytes, AudioFormat.WAV)
            
            return wav_bytes, wav_metadata
    
    @classmethod
    def preprocess_for_transcription(cls, audio_bytes: bytes) -> Tuple[bytes, Dict[str, Any]]:
        """
        Preprocess audio for optimal transcription quality.
        
        Args:
            audio_bytes: Input audio data
            
        Returns:
            Tuple of (processed_bytes, preprocessing_info)
        """
        preprocessing_info = {
            'applied_filters': [],
            'original_quality': 0.0,
            'final_quality': 0.0
        }
        
        try:
            # Get original metadata
            original_metadata = cls.extract_metadata(audio_bytes)
            preprocessing_info['original_quality'] = original_metadata.quality_score
            
            # Convert to optimal format
            processed_bytes, final_metadata = cls.convert_to_optimal_format(
                audio_bytes,
                original_metadata.format,
                target_sample_rate=16000,  # Optimal for speech recognition
                target_channels=1,
                target_bit_depth=16
            )
            
            preprocessing_info['applied_filters'].append('format_conversion')
            preprocessing_info['final_quality'] = final_metadata.quality_score
            
            # TODO: Add noise reduction, normalization, etc. when pydub is available
            
            return processed_bytes, preprocessing_info
            
        except Exception as e:
            logger.error(f"Audio preprocessing failed: {e}")
            return audio_bytes, preprocessing_info


# Convenience functions
def decode_and_validate_audio(
    base64_data: str,
    min_duration_ms: float = 100,
    max_duration_ms: float = 30000,
    min_quality_score: float = 0.1
) -> Tuple[bool, bytes, Optional[AudioMetadata], List[str]]:
    """
    Decode and validate base64 audio data.
    
    Args:
        base64_data: Base64 encoded audio
        min_duration_ms: Minimum duration
        max_duration_ms: Maximum duration
        min_quality_score: Minimum quality score
        
    Returns:
        Tuple of (is_valid, audio_bytes, metadata, errors)
    """
    try:
        # Decode audio
        audio_bytes, detected_format = AudioFormatHandler.decode_base64_audio(base64_data)
        
        # Validate audio
        is_valid, validation_results = AudioFormatHandler.validate_audio(
            audio_bytes,
            min_duration_ms=min_duration_ms,
            max_duration_ms=max_duration_ms,
            min_quality_score=min_quality_score
        )
        
        return is_valid, audio_bytes, validation_results.get('metadata'), validation_results.get('errors', [])
        
    except Exception as e:
        return False, b'', None, [str(e)]


def prepare_audio_for_processing(base64_data: str) -> Tuple[bool, bytes, Dict[str, Any]]:
    """
    Complete audio preparation pipeline.
    
    Args:
        base64_data: Base64 encoded audio
        
    Returns:
        Tuple of (success, processed_bytes, processing_info)
    """
    processing_info = {
        'validation_passed': False,
        'preprocessing_applied': False,
        'final_metadata': None,
        'errors': []
    }
    
    try:
        # Step 1: Decode and validate
        is_valid, audio_bytes, metadata, errors = decode_and_validate_audio(base64_data)
        
        if not is_valid:
            processing_info['errors'] = errors
            return False, b'', processing_info
        
        processing_info['validation_passed'] = True
        
        # Step 2: Preprocess for transcription
        processed_bytes, preprocessing_info = AudioFormatHandler.preprocess_for_transcription(audio_bytes)
        processing_info['preprocessing_applied'] = True
        processing_info.update(preprocessing_info)
        
        # Step 3: Get final metadata
        final_metadata = AudioFormatHandler.extract_metadata(processed_bytes)
        processing_info['final_metadata'] = final_metadata
        
        return True, processed_bytes, processing_info
        
    except Exception as e:
        processing_info['errors'].append(str(e))
        return False, b'', processing_info


def validate_audio_format(audio_data: bytes) -> Dict[str, Any]:
    """
    Validate audio format and return format information.
    
    Args:
        audio_data: Raw audio data bytes
        
    Returns:
        Dictionary with validation results
    """
    try:
        metadata = AudioFormatHandler.extract_metadata(audio_data)
        
        return {
            'valid': metadata.is_valid,
            'format': metadata.format.value if metadata.format else 'unknown',
            'channels': metadata.channels,
            'sample_rate': metadata.sample_rate,
            'sample_width': metadata.bit_depth // 8 if metadata.bit_depth else 0,
            'frame_count': int(metadata.duration_ms * metadata.sample_rate / 1000) if metadata.duration_ms and metadata.sample_rate else 0,
            'duration_ms': metadata.duration_ms,
            'quality_score': metadata.quality_score,
            'error': metadata.error_message
        }
    except Exception as e:
        return {
            'valid': False,
            'format': 'unknown',
            'channels': 0,
            'sample_rate': 0,
            'sample_width': 0,
            'frame_count': 0,
            'duration_ms': 0.0,
            'quality_score': 0.0,
            'error': str(e)
        }


def convert_audio_format(
    audio_data: bytes,
    target_sample_rate: int = 16000,
    target_channels: int = 1,
    target_bit_depth: int = 16
) -> bytes:
    """
    Convert audio to target format.
    
    Args:
        audio_data: Input audio data
        target_sample_rate: Target sample rate
        target_channels: Target number of channels
        target_bit_depth: Target bit depth
        
    Returns:
        Converted audio data
    """
    try:
        # Detect source format
        metadata = AudioFormatHandler.extract_metadata(audio_data)
        
        if not metadata.is_valid:
            # Return original data if we can't process it
            return audio_data
        
        # Convert using AudioFormatHandler
        converted_bytes, _ = AudioFormatHandler.convert_to_optimal_format(
            audio_data,
            metadata.format,
            target_sample_rate=target_sample_rate,
            target_channels=target_channels,
            target_bit_depth=target_bit_depth
        )
        
        return converted_bytes
        
    except Exception as e:
        logger.error(f"Audio conversion failed: {e}")
        # Return original data if conversion fails
        return audio_data