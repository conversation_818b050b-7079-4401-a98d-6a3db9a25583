"""
Performance monitoring service for real-time AI companion.

This module provides tools for tracking, analyzing, and alerting on performance metrics
throughout the real-time AI companion processing pipeline.
"""
import time
import logging
import asyncio
import statistics
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from django.db.models import Avg, Max, Min, Count, F, Q
from django.contrib.auth import get_user_model

from chat.models_realtime import PerformanceMetrics, StreamingSession

User = get_user_model()
logger = logging.getLogger(__name__)

# Performance targets (in milliseconds)
DEFAULT_TARGETS = {
    'total_response_time': 450,
    'audio_processing_time': 50,
    'emotion_detection_time': 100,
    'llm_first_token_time': 200,
    'tts_first_chunk_time': 100,
    'memory_retrieval_time': 50,
    'transcription_time': 100,
}


class PerformanceMonitor:
    """
    Service for monitoring and analyzing performance metrics across the real-time AI companion.
    
    This class provides methods for:
    - Recording performance metrics at each stage of processing
    - Analyzing performance trends and identifying bottlenecks
    - Alerting on performance degradation
    - Generating performance reports
    """
    
    def __init__(self):
        self.targets = getattr(settings, 'PERFORMANCE_TARGETS', DEFAULT_TARGETS)
        self.alert_threshold = getattr(settings, 'PERFORMANCE_ALERT_THRESHOLD', 0.8)
        self._timing_cache = {}
        
    def start_timer(self, request_id: str, stage: str) -> None:
        """Start timing a specific processing stage."""
        cache_key = f"perf_timer:{request_id}:{stage}:start"
        cache.set(cache_key, time.time(), timeout=300)  # 5 minute timeout
        
    def end_timer(self, request_id: str, stage: str) -> float:
        """
        End timing for a specific processing stage and return duration in milliseconds.
        
        Args:
            request_id: Unique identifier for the request
            stage: Processing stage name (e.g., 'audio_processing', 'llm_first_token')
            
        Returns:
            Duration in milliseconds, or None if start time not found
        """
        end_time = time.time()
        cache_key = f"perf_timer:{request_id}:{stage}:start"
        start_time = cache.get(cache_key)
        
        if start_time is None:
            logger.warning(f"No start time found for {stage} in request {request_id}")
            return None
        
        # Calculate duration in milliseconds
        duration_ms = (end_time - start_time) * 1000
        
        # Store in timing cache for later recording
        if request_id not in self._timing_cache:
            self._timing_cache[request_id] = {}
        
        self._timing_cache[request_id][stage] = duration_ms
        
        # Check if this exceeds target
        target = self.targets.get(stage)
        if target and duration_ms > target:
            logger.warning(f"Performance target exceeded for {stage}: {duration_ms:.2f}ms (target: {target}ms)")
        
        return duration_ms
    
    def record_metric(self, request_id: str, session: StreamingSession, user: User, 
                     metric_name: str, value: float) -> None:
        """
        Record a single performance metric.
        
        Args:
            request_id: Unique identifier for the request
            session: StreamingSession instance
            user: User instance
            metric_name: Name of the metric (e.g., 'audio_processing_time')
            value: Metric value (typically duration in milliseconds)
        """
        # Add to session's performance metrics
        session.add_performance_metric(metric_name, value)
        
        # Check if we need to create a new PerformanceMetrics record
        # We'll create it when we have the total_response_time
        if metric_name == 'total_response_time':
            self._create_performance_record(request_id, session, user, value)
    
    def _create_performance_record(self, request_id: str, session: StreamingSession, 
                                  user: User, total_response_time: float) -> None:
        """Create a comprehensive performance record from collected metrics."""
        # Get all timing data for this request
        timing_data = self._timing_cache.get(request_id, {})
        
        # Create performance record
        metrics = PerformanceMetrics(
            session=session,
            user=user,
            request_id=request_id,
            total_response_time=total_response_time,
            # Add all the individual timing metrics we've collected
            audio_processing_time=timing_data.get('audio_processing_time'),
            emotion_detection_time=timing_data.get('emotion_detection_time'),
            transcription_time=timing_data.get('transcription_time'),
            memory_retrieval_time=timing_data.get('memory_retrieval_time'),
            llm_first_token_time=timing_data.get('llm_first_token_time'),
            llm_total_time=timing_data.get('llm_total_time'),
            tts_first_chunk_time=timing_data.get('tts_first_chunk_time'),
            tts_total_time=timing_data.get('tts_total_time'),
        )
        
        # Save the record
        try:
            from asgiref.sync import sync_to_async
            import asyncio
            
            def _save_metrics():
                metrics.save()
            
            # Check if we're in an async context
            try:
                loop = asyncio.get_running_loop()
                # We're in an async context, schedule the sync operation
                asyncio.create_task(sync_to_async(_save_metrics)())
            except RuntimeError:
                # We're not in an async context, run directly
                _save_metrics()
        except Exception as e:
            logger.error(f"Error saving performance metrics: {e}")
        
        # Clean up timing cache
        if request_id in self._timing_cache:
            del self._timing_cache[request_id]
    
    def record_api_usage(self, request_id: str, service: str, usage_data: Dict[str, Any]) -> None:
        """
        Record API usage metrics.
        
        Args:
            request_id: Unique identifier for the request
            service: Service name (e.g., 'groq', 'hume')
            usage_data: Dictionary of usage data (e.g., tokens, calls, etc.)
        """
        try:
            # Find the performance metrics record
            metrics = PerformanceMetrics.objects.filter(request_id=request_id).first()
            if not metrics:
                logger.warning(f"No performance metrics found for request {request_id}")
                return
            
            # Update the appropriate fields based on service
            if service == 'groq':
                metrics.groq_tokens_used = usage_data.get('tokens', 0)
                update_field = 'groq_tokens_used'
            elif service == 'hume':
                metrics.hume_api_calls = usage_data.get('calls', 0)
                update_field = 'hume_api_calls'
            else:
                return
            
            # Save with async context handling
            from asgiref.sync import sync_to_async
            import asyncio
            
            def _save_api_usage():
                metrics.save(update_fields=[update_field])
            
            # Check if we're in an async context
            try:
                loop = asyncio.get_running_loop()
                # We're in an async context, schedule the sync operation
                asyncio.create_task(sync_to_async(_save_api_usage)())
            except RuntimeError:
                # We're not in an async context, run directly
                _save_api_usage()
            
        except Exception as e:
            logger.error(f"Error recording API usage: {e}")
    
    def record_error(self, request_id: str, error_type: str, error_message: str) -> None:
        """
        Record an error that occurred during processing.
        
        Args:
            request_id: Unique identifier for the request
            error_type: Type of error (e.g., 'api_failure', 'timeout')
            error_message: Error message
        """
        try:
            from asgiref.sync import sync_to_async
            import asyncio
            
            def _record_error_sync():
                # Find the performance metrics record
                metrics = PerformanceMetrics.objects.filter(request_id=request_id).first()
                if not metrics:
                    logger.warning(f"No performance metrics found for request {request_id}")
                    return
                
                # Update error fields
                metrics.had_errors = True
                
                # Add to error details
                if not metrics.error_details:
                    metrics.error_details = {}
                
                metrics.error_details[error_type] = error_message
                metrics.save(update_fields=['had_errors', 'error_details'])
            
            # Check if we're in an async context
            try:
                loop = asyncio.get_running_loop()
                # We're in an async context, schedule the sync operation
                asyncio.create_task(sync_to_async(_record_error_sync)())
            except RuntimeError:
                # We're not in an async context, run directly
                _record_error_sync()
            
        except Exception as e:
            logger.error(f"Error recording error details: {e}")
    
    def record_user_feedback(self, request_id: str, satisfaction_score: float, 
                            feedback_text: str = "") -> None:
        """
        Record user feedback on response quality.
        
        Args:
            request_id: Unique identifier for the request
            satisfaction_score: User satisfaction rating (1.0-5.0)
            feedback_text: Optional feedback text
        """
        try:
            # Find the performance metrics record
            metrics = PerformanceMetrics.objects.filter(request_id=request_id).first()
            if not metrics:
                logger.warning(f"No performance metrics found for request {request_id}")
                return
            
            # Update feedback fields
            metrics.user_satisfaction_score = satisfaction_score
            metrics.user_feedback = feedback_text
            metrics.save(update_fields=['user_satisfaction_score', 'user_feedback'])
            
        except Exception as e:
            logger.error(f"Error recording user feedback: {e}")
    
    def get_performance_summary(self, days: int = 7, user: Optional[User] = None) -> Dict[str, Any]:
        """
        Get a summary of performance metrics over the specified time period.
        
        Args:
            days: Number of days to include in summary
            user: Optional user to filter metrics for
            
        Returns:
            Dictionary of performance summary statistics
        """
        since = timezone.now() - timedelta(days=days)
        queryset = PerformanceMetrics.objects.filter(timestamp__gte=since)
        
        if user:
            queryset = queryset.filter(user=user)
        
        # Get aggregate statistics
        stats = queryset.aggregate(
            avg_total_response_time=Avg('total_response_time'),
            avg_audio_processing_time=Avg('audio_processing_time'),
            avg_emotion_detection_time=Avg('emotion_detection_time'),
            avg_llm_first_token_time=Avg('llm_first_token_time'),
            avg_tts_first_chunk_time=Avg('tts_first_chunk_time'),
            avg_user_satisfaction=Avg('user_satisfaction_score'),
            total_requests=Count('id'),
        )
        
        # Calculate rates manually to avoid division by zero
        total_requests = stats['total_requests'] or 0
        if total_requests > 0:
            error_count = queryset.filter(had_errors=True).count()
            target_met_count = queryset.filter(total_response_time__lte=self.targets['total_response_time']).count()
            
            stats['error_rate'] = (error_count / total_requests) * 100.0
            stats['target_met_rate'] = (target_met_count / total_requests) * 100.0
        else:
            stats['error_rate'] = 0.0
            stats['target_met_rate'] = 0.0
        
        # Add performance target information
        stats['targets'] = self.targets
        
        # Add trend information (day by day averages)
        daily_stats = []
        for i in range(days):
            day_start = timezone.now() - timedelta(days=i+1)
            day_end = timezone.now() - timedelta(days=i)
            
            day_queryset = queryset.filter(timestamp__gte=day_start, timestamp__lt=day_end)
            day_avg = day_queryset.aggregate(
                avg_response_time=Avg('total_response_time'),
                request_count=Count('id')
            )
            
            if day_avg['request_count'] and day_avg['request_count'] > 0:
                day_avg['date'] = day_start.date().isoformat()
                daily_stats.append(day_avg)
        
        stats['daily_trends'] = daily_stats
        
        return stats
    
    def check_performance_alerts(self) -> List[Dict[str, Any]]:
        """
        Check for performance issues that should trigger alerts.
        
        Returns:
            List of alert dictionaries with metric, value, threshold, and message
        """
        alerts = []
        
        # Get recent performance data (last hour)
        since = timezone.now() - timedelta(hours=1)
        recent_metrics = PerformanceMetrics.objects.filter(timestamp__gte=since)
        
        if not recent_metrics.exists():
            return alerts
        
        # Check average response time
        avg_response_time = recent_metrics.aggregate(avg=Avg('total_response_time'))['avg']
        if avg_response_time and avg_response_time > self.targets['total_response_time']:
            alerts.append({
                'metric': 'total_response_time',
                'value': avg_response_time,
                'threshold': self.targets['total_response_time'],
                'message': f"Average response time ({avg_response_time:.2f}ms) exceeds target ({self.targets['total_response_time']}ms)"
            })
        
        # Check error rate
        total_count = recent_metrics.count()
        error_count = recent_metrics.filter(had_errors=True).count()
        
        if total_count > 0:
            error_rate = (error_count / total_count) * 100
            if error_rate > 5.0:  # Alert if error rate exceeds 5%
                alerts.append({
                    'metric': 'error_rate',
                    'value': error_rate,
                    'threshold': 5.0,
                    'message': f"Error rate ({error_rate:.2f}%) exceeds threshold (5.0%)"
                })
        
        # Check for specific component slowdowns
        for metric, target in self.targets.items():
            if metric == 'total_response_time':
                continue  # Already checked above
                
            field_name = f"avg_{metric}"
            avg_value = recent_metrics.aggregate(avg=Avg(metric))['avg']
            
            if avg_value and avg_value > target:
                alerts.append({
                    'metric': metric,
                    'value': avg_value,
                    'threshold': target,
                    'message': f"Average {metric.replace('_', ' ')} ({avg_value:.2f}ms) exceeds target ({target}ms)"
                })
        
        return alerts
    
    async def monitor_performance_async(self, interval_seconds: int = 300) -> None:
        """
        Continuously monitor performance and trigger alerts when needed.
        
        Args:
            interval_seconds: How often to check performance metrics (default: 5 minutes)
        """
        while True:
            try:
                alerts = self.check_performance_alerts()
                
                for alert in alerts:
                    logger.warning(f"PERFORMANCE ALERT: {alert['message']}")
                    
                    # Here you would typically send the alert to your monitoring system
                    # For example, sending to Slack, PagerDuty, or other monitoring tools
                    # self._send_alert(alert)
                
                # Generate and cache performance summary every hour
                if timezone.now().minute < 5:  # In the first 5 minutes of each hour
                    summary = self.get_performance_summary(days=1)
                    cache.set('performance_summary_1d', summary, timeout=3600)
                    
                    summary = self.get_performance_summary(days=7)
                    cache.set('performance_summary_7d', summary, timeout=3600)
                    
            except Exception as e:
                logger.error(f"Error in performance monitoring: {e}")
                
            await asyncio.sleep(interval_seconds)
    
    def get_bottlenecks(self, days: int = 1) -> List[Dict[str, Any]]:
        """
        Identify performance bottlenecks in the system.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            List of bottlenecks with metric name, average time, and target
        """
        since = timezone.now() - timedelta(days=days)
        metrics = PerformanceMetrics.objects.filter(timestamp__gte=since)
        
        bottlenecks = []
        
        # Check each metric against its target
        for metric, target in self.targets.items():
            if metric == 'total_response_time':
                continue  # Skip the overall metric
                
            # Get average value for this metric
            avg_value = metrics.aggregate(avg=Avg(metric))['avg']
            
            if avg_value is None:
                continue
                
            # Calculate how close to target we are (percentage)
            target_ratio = (avg_value / target) * 100
            
            if target_ratio > 80:  # If we're using >80% of our target time
                bottlenecks.append({
                    'metric': metric,
                    'avg_value': avg_value,
                    'target': target,
                    'target_ratio': target_ratio,
                    'severity': 'high' if target_ratio > 100 else 'medium' if target_ratio > 90 else 'low'
                })
        
        # Sort by severity (highest first)
        return sorted(bottlenecks, key=lambda x: x['target_ratio'], reverse=True)


# Singleton instance for global use
performance_monitor = PerformanceMonitor()