"""
Configuration validator for real-time AI companion services.
"""
import os
import logging
from typing import Dict, <PERSON>, Tuple, Optional
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

logger = logging.getLogger(__name__)


class ConfigurationValidator:
    """Validates configuration for real-time AI companion services."""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
    
    def validate_all(self) -> Tuple[bool, Dict[str, List[str]]]:
        """
        Validate all configuration settings.
        
        Returns:
            Tuple of (is_valid, results_dict)
        """
        self.errors.clear()
        self.warnings.clear()
        self.info.clear()
        
        # Validate each configuration section
        self._validate_groq_config()
        self._validate_hume_config()
        self._validate_openai_config()
        self._validate_performance_targets()
        self._validate_redis_config()
        self._validate_audio_config()
        
        is_valid = len(self.errors) == 0
        
        results = {
            'errors': self.errors.copy(),
            'warnings': self.warnings.copy(),
            'info': self.info.copy()
        }
        
        return is_valid, results
    
    def _validate_groq_config(self):
        """Validate Groq API configuration."""
        # Check API key
        groq_api_key = getattr(settings, 'GROQ_API_KEY', '')
        if not groq_api_key:
            self.errors.append("GROQ_API_KEY is not set. Real-time LLM functionality will not work.")
        elif groq_api_key == 'your-groq-api-key-here':
            self.errors.append("GROQ_API_KEY is set to placeholder value. Please set a valid API key.")
        else:
            self.info.append("GROQ_API_KEY is configured")
        
        # Check base URL
        groq_base_url = getattr(settings, 'GROQ_BASE_URL', '')
        if not groq_base_url:
            self.warnings.append("GROQ_BASE_URL not set, using default")
        elif not groq_base_url.startswith('https://'):
            self.warnings.append("GROQ_BASE_URL should use HTTPS for security")
        else:
            self.info.append(f"GROQ_BASE_URL: {groq_base_url}")
        
        # Check model
        groq_model = getattr(settings, 'GROQ_DEFAULT_MODEL', '')
        if not groq_model:
            self.warnings.append("GROQ_DEFAULT_MODEL not set, using default")
        else:
            self.info.append(f"GROQ_DEFAULT_MODEL: {groq_model}")
        
        # Check timeout and retries
        groq_timeout = getattr(settings, 'GROQ_TIMEOUT', 30.0)
        if groq_timeout < 5.0:
            self.warnings.append("GROQ_TIMEOUT is very low, may cause timeouts")
        elif groq_timeout > 60.0:
            self.warnings.append("GROQ_TIMEOUT is very high, may impact user experience")
        
        groq_retries = getattr(settings, 'GROQ_MAX_RETRIES', 3)
        if groq_retries < 1:
            self.warnings.append("GROQ_MAX_RETRIES should be at least 1")
        elif groq_retries > 5:
            self.warnings.append("GROQ_MAX_RETRIES is high, may impact response time")
    
    def _validate_hume_config(self):
        """Validate Hume AI configuration."""
        # Check API key
        hume_api_key = getattr(settings, 'HUME_API_KEY', '')
        if not hume_api_key:
            self.errors.append("HUME_API_KEY is not set. Emotion detection and TTS functionality will not work.")
        elif hume_api_key == 'your-hume-api-key-here':
            self.errors.append("HUME_API_KEY is set to placeholder value. Please set a valid API key.")
        else:
            self.info.append("HUME_API_KEY is configured")
        
        # Check timeout
        hume_timeout = getattr(settings, 'HUME_TIMEOUT', 30.0)
        if hume_timeout < 5.0:
            self.warnings.append("HUME_TIMEOUT is very low, may cause timeouts")
        elif hume_timeout > 60.0:
            self.warnings.append("HUME_TIMEOUT is very high, may impact user experience")
        
        # Check feature flags
        enable_prosody = getattr(settings, 'HUME_ENABLE_PROSODY', True)
        enable_language = getattr(settings, 'HUME_ENABLE_LANGUAGE', True)
        
        if not enable_prosody and not enable_language:
            self.warnings.append("Both HUME_ENABLE_PROSODY and HUME_ENABLE_LANGUAGE are disabled")
        
        features = []
        if enable_prosody:
            features.append("prosody")
        if enable_language:
            features.append("language")
        
        if features:
            self.info.append(f"Hume AI features enabled: {', '.join(features)}")
        
        # Check default voice
        default_voice = getattr(settings, 'HUME_DEFAULT_VOICE', '')
        if default_voice:
            self.info.append(f"HUME_DEFAULT_VOICE: {default_voice}")
        else:
            self.info.append("HUME_DEFAULT_VOICE not set, will use Hume's default voice")
    
    def _validate_openai_config(self):
        """Validate OpenAI configuration (for fallback)."""
        openai_api_key = getattr(settings, 'OPENAI_API_KEY', '')
        if not openai_api_key:
            self.warnings.append("OPENAI_API_KEY not set. Fallback functionality will not work.")
        elif openai_api_key == 'your-openai-api-key-here':
            self.warnings.append("OPENAI_API_KEY is set to placeholder value. Fallback will not work.")
        else:
            self.info.append("OPENAI_API_KEY is configured for fallback")
    
    def _validate_performance_targets(self):
        """Validate performance target settings."""
        response_time_target = getattr(settings, 'PERFORMANCE_TARGET_RESPONSE_TIME_MS', 450)
        first_token_target = getattr(settings, 'PERFORMANCE_TARGET_FIRST_TOKEN_MS', 200)
        tts_target = getattr(settings, 'PERFORMANCE_TARGET_TTS_FIRST_CHUNK_MS', 100)
        
        if response_time_target < 100:
            self.warnings.append("PERFORMANCE_TARGET_RESPONSE_TIME_MS is very aggressive")
        elif response_time_target > 2000:
            self.warnings.append("PERFORMANCE_TARGET_RESPONSE_TIME_MS is quite high")
        
        if first_token_target < 50:
            self.warnings.append("PERFORMANCE_TARGET_FIRST_TOKEN_MS is very aggressive")
        elif first_token_target > 1000:
            self.warnings.append("PERFORMANCE_TARGET_FIRST_TOKEN_MS is quite high")
        
        if tts_target < 50:
            self.warnings.append("PERFORMANCE_TARGET_TTS_FIRST_CHUNK_MS is very aggressive")
        elif tts_target > 500:
            self.warnings.append("PERFORMANCE_TARGET_TTS_FIRST_CHUNK_MS is quite high")
        
        self.info.append(f"Performance targets: Response={response_time_target}ms, FirstToken={first_token_target}ms, TTS={tts_target}ms")
    
    def _validate_redis_config(self):
        """Validate Redis configuration for WebSocket sessions."""
        try:
            channel_layers = getattr(settings, 'CHANNEL_LAYERS', {})
            if not channel_layers:
                self.errors.append("CHANNEL_LAYERS not configured. WebSocket functionality will not work.")
                return
            
            default_layer = channel_layers.get('default', {})
            if not default_layer:
                self.errors.append("Default channel layer not configured")
                return
            
            backend = default_layer.get('BACKEND', '')
            if 'redis' not in backend.lower():
                self.warnings.append("Channel layer not using Redis. Performance may be impacted.")
            else:
                self.info.append("Redis channel layer configured")
            
            # Check Redis connection settings
            config = default_layer.get('CONFIG', {})
            hosts = config.get('hosts', [])
            if not hosts:
                self.warnings.append("Redis hosts not specified in channel layer config")
            else:
                self.info.append(f"Redis hosts: {hosts}")
                
        except Exception as e:
            self.errors.append(f"Error validating Redis config: {e}")
    
    def _validate_audio_config(self):
        """Validate audio processing configuration."""
        audio_path = getattr(settings, 'AUDIO_UPLOAD_PATH', '')
        if not audio_path:
            self.warnings.append("AUDIO_UPLOAD_PATH not set")
        else:
            # Check if directory exists or can be created
            try:
                os.makedirs(audio_path, exist_ok=True)
                self.info.append(f"Audio upload path: {audio_path}")
            except Exception as e:
                self.errors.append(f"Cannot create audio upload directory: {e}")
        
        max_size = getattr(settings, 'MAX_AUDIO_FILE_SIZE', 0)
        if max_size <= 0:
            self.warnings.append("MAX_AUDIO_FILE_SIZE not set or invalid")
        elif max_size < 1024 * 1024:  # 1MB
            self.warnings.append("MAX_AUDIO_FILE_SIZE is very small")
        elif max_size > 50 * 1024 * 1024:  # 50MB
            self.warnings.append("MAX_AUDIO_FILE_SIZE is very large")
        else:
            self.info.append(f"Max audio file size: {max_size / (1024*1024):.1f}MB")


async def validate_api_connectivity():
    """
    Test connectivity to external APIs.
    
    Returns:
        Dict with connectivity test results
    """
    results = {
        'groq': {'status': 'unknown', 'message': '', 'response_time_ms': None},
        'hume': {'status': 'unknown', 'message': '', 'response_time_ms': None},
        'openai': {'status': 'unknown', 'message': '', 'response_time_ms': None}
    }
    
    # Test Groq API
    try:
        from .groq_service import GroqStreamingClient
        import time
        
        if getattr(settings, 'GROQ_API_KEY', ''):
            client = GroqStreamingClient()
            start_time = time.time()
            is_healthy = await client.health_check()
            response_time = (time.time() - start_time) * 1000
            
            if is_healthy:
                results['groq'] = {
                    'status': 'healthy',
                    'message': 'API is responding',
                    'response_time_ms': response_time
                }
            else:
                results['groq'] = {
                    'status': 'unhealthy',
                    'message': 'API not responding correctly',
                    'response_time_ms': response_time
                }
        else:
            results['groq'] = {
                'status': 'not_configured',
                'message': 'API key not set',
                'response_time_ms': None
            }
    except Exception as e:
        results['groq'] = {
            'status': 'error',
            'message': str(e),
            'response_time_ms': None
        }
    
    # Test Hume AI API
    try:
        from .hume_service import HumeEmotionClient
        import time
        
        if getattr(settings, 'HUME_API_KEY', ''):
            client = HumeEmotionClient()
            start_time = time.time()
            
            # Simple test with text emotion analysis
            result = await client.analyze_text_emotions("Hello")
            response_time = (time.time() - start_time) * 1000
            
            if result and result.confidence_score >= 0:  # Allow 0 confidence as valid
                results['hume'] = {
                    'status': 'healthy',
                    'message': 'API is responding',
                    'response_time_ms': response_time
                }
            else:
                results['hume'] = {
                    'status': 'unhealthy',
                    'message': 'API not responding correctly',
                    'response_time_ms': response_time
                }
        else:
            results['hume'] = {
                'status': 'not_configured',
                'message': 'API key not set',
                'response_time_ms': None
            }
    except Exception as e:
        error_message = str(e)
        if 'CERTIFICATE_VERIFY_FAILED' in error_message:
            results['hume'] = {
                'status': 'ssl_issue',
                'message': 'SSL certificate verification failed (common on macOS)',
                'response_time_ms': None
            }
        else:
            results['hume'] = {
                'status': 'error',
                'message': error_message,
                'response_time_ms': None
            }
    
    # Test OpenAI API (for fallback)
    try:
        if getattr(settings, 'OPENAI_API_KEY', ''):
            from openai import AsyncOpenAI
            import time
            
            client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
            start_time = time.time()
            
            # Simple test request
            response = await client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Hi"}],
                max_tokens=5
            )
            response_time = (time.time() - start_time) * 1000
            
            if response.choices and response.choices[0].message.content:
                results['openai'] = {
                    'status': 'healthy',
                    'message': 'API is responding',
                    'response_time_ms': response_time
                }
            else:
                results['openai'] = {
                    'status': 'unhealthy',
                    'message': 'API not responding correctly',
                    'response_time_ms': response_time
                }
        else:
            results['openai'] = {
                'status': 'not_configured',
                'message': 'API key not set',
                'response_time_ms': None
            }
    except Exception as e:
        results['openai'] = {
            'status': 'error',
            'message': str(e),
            'response_time_ms': None
        }
    
    return results


def print_validation_results(is_valid: bool, results: Dict[str, List[str]]):
    """Print validation results in a formatted way."""
    print("\n" + "="*60)
    print("REAL-TIME AI COMPANION CONFIGURATION VALIDATION")
    print("="*60)
    
    if results['errors']:
        print("\n❌ ERRORS:")
        for error in results['errors']:
            print(f"  • {error}")
    
    if results['warnings']:
        print("\n⚠️  WARNINGS:")
        for warning in results['warnings']:
            print(f"  • {warning}")
    
    if results['info']:
        print("\n✅ CONFIGURATION INFO:")
        for info in results['info']:
            print(f"  • {info}")
    
    print(f"\n{'✅ CONFIGURATION VALID' if is_valid else '❌ CONFIGURATION INVALID'}")
    print("="*60)


async def print_connectivity_results(results: Dict):
    """Print API connectivity test results."""
    print("\n" + "="*60)
    print("API CONNECTIVITY TEST RESULTS")
    print("="*60)
    
    for service, result in results.items():
        status = result['status']
        message = result['message']
        response_time = result['response_time_ms']
        
        status_icon = {
            'healthy': '✅',
            'unhealthy': '❌',
            'not_configured': '⚠️',
            'ssl_issue': '⚠️',
            'error': '❌',
            'unknown': '❓'
        }.get(status, '❓')
        
        print(f"\n{status_icon} {service.upper()}: {status}")
        print(f"  Message: {message}")
        if response_time is not None:
            print(f"  Response time: {response_time:.2f}ms")
    
    print("="*60)


# Django management command helper
def validate_configuration_command():
    """Helper function for Django management command."""
    import asyncio
    
    # Validate configuration
    validator = ConfigurationValidator()
    is_valid, results = validator.validate_all()
    print_validation_results(is_valid, results)
    
    # Test API connectivity
    print("\nTesting API connectivity...")
    connectivity_results = asyncio.run(validate_api_connectivity())
    asyncio.run(print_connectivity_results(connectivity_results))
    
    # Check that critical APIs (Groq and Hume) are working
    critical_apis = ['groq', 'hume']
    critical_status = all(
        connectivity_results.get(api, {}).get('status') in ['healthy', 'ssl_issue']
        for api in critical_apis
    )
    
    # OpenAI is optional (fallback only)
    return is_valid and critical_status