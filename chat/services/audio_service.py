"""
Audio processing service for real-time transcription and emotion detection.
"""
import asyncio
import base64
import io
import logging
import os
import time
import wave
from typing import Dict, List, Any, Optional, AsyncGenerator, Tu<PERSON>
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import threading
from collections import deque

import numpy as np
from django.conf import settings

from .groq_service import GroqStreamingClient
from .hume_service import HumeEmotionClient, EmotionAnalysisResult
from .audio_format import AudioFormatHandler, AudioMetadata, AudioFormat

# Import Groq for Whisper API
try:
    from groq import Groq
except ImportError:
    Groq = None

logger = logging.getLogger(__name__)


@dataclass
class AudioChunk:
    """Represents an audio chunk for processing."""
    data: bytes
    chunk_id: str
    timestamp_ms: float
    sample_rate: int = 16000
    channels: int = 1
    is_final: bool = False
    user_id: Optional[str] = None


@dataclass
class TranscriptionResult:
    """Result from speech-to-text transcription."""
    text: str
    confidence: float
    is_partial: bool
    processing_time_ms: float
    chunk_id: str
    timestamp_ms: float


@dataclass
class AudioProcessingResult:
    """Combined result from audio processing pipeline."""
    transcription: Optional[TranscriptionResult]
    emotion_analysis: Optional[EmotionAnalysisResult]
    chunk_id: str
    total_processing_time_ms: float
    parallel_processing: bool = True


class AudioBuffer:
    """
    Circular buffer for managing audio chunks with optimal batching.
    
    Features:
    - Automatic chunk aggregation for optimal API calls
    - Sliding window for continuous processing
    - Memory-efficient circular buffer implementation
    """
    
    def __init__(
        self,
        max_buffer_size: int = 10,
        target_chunk_duration_ms: int = 1000,
        sample_rate: int = 16000
    ):
        self.max_buffer_size = max_buffer_size
        self.target_chunk_duration_ms = target_chunk_duration_ms
        self.sample_rate = sample_rate
        self.target_chunk_size = int(sample_rate * target_chunk_duration_ms / 1000)
        
        self.buffer = deque(maxlen=max_buffer_size)
        self.accumulated_data = bytearray()
        self.lock = threading.Lock()
        
        logger.debug(f"AudioBuffer initialized: target_chunk_size={self.target_chunk_size} samples")
    
    def add_chunk(self, chunk: AudioChunk) -> List[AudioChunk]:
        """
        Add audio chunk to buffer and return ready chunks for processing.

        Args:
            chunk: Audio chunk to add

        Returns:
            List of chunks ready for processing (only when final chunk is received)
        """
        with self.lock:
            self.buffer.append(chunk)
            self.accumulated_data.extend(chunk.data)

            ready_chunks = []

            # Only process when we receive a final chunk
            # This prevents multiple transcriptions for a single continuous speech
            if chunk.is_final and len(self.accumulated_data) > 0:
                # Create final chunk with all accumulated data
                final_chunk = AudioChunk(
                    data=bytes(self.accumulated_data),
                    chunk_id=f"final_{chunk.chunk_id}",  # Use original chunk_id for tracking
                    timestamp_ms=chunk.timestamp_ms,
                    sample_rate=chunk.sample_rate,
                    channels=chunk.channels,
                    is_final=True,
                    user_id=chunk.user_id
                )

                ready_chunks.append(final_chunk)

                # Clear the buffer after processing final chunk
                self.accumulated_data.clear()
                self.buffer.clear()

                logger.info(f"🎤 Created final aggregated chunk: {len(final_chunk.data)} bytes from {len(self.buffer)} chunks")

            # For non-final chunks, just accumulate without processing
            # This allows for continuous speech without interruption

            return ready_chunks
    
    def get_buffer_info(self) -> Dict[str, Any]:
        """Get current buffer status information."""
        with self.lock:
            return {
                'buffer_count': len(self.buffer),
                'accumulated_bytes': len(self.accumulated_data),
                'target_chunk_size': self.target_chunk_size,
                'buffer_utilization': len(self.buffer) / self.max_buffer_size
            }


class AudioQualityAssessment:
    """Assess audio quality for optimal processing."""
    
    @staticmethod
    def assess_audio_quality(audio_data: bytes, sample_rate: int = 16000) -> Dict[str, float]:
        """
        Assess audio quality metrics.
        
        Args:
            audio_data: Raw audio bytes
            sample_rate: Audio sample rate
            
        Returns:
            Quality metrics dictionary
        """
        try:
            # Convert bytes to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            if len(audio_array) == 0:
                return {'quality_score': 0.0, 'snr_estimate': 0.0, 'volume_level': 0.0}
            
            # Calculate RMS (volume level)
            rms = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
            volume_level = min(rms / 32767.0, 1.0)  # Normalize to 0-1
            
            # Estimate SNR (simple approach)
            # Calculate signal power vs noise floor
            sorted_samples = np.sort(np.abs(audio_array))
            noise_floor = np.mean(sorted_samples[:len(sorted_samples)//10])  # Bottom 10%
            signal_power = np.mean(sorted_samples[-len(sorted_samples)//10:])  # Top 10%
            
            snr_estimate = 0.0
            if noise_floor > 0:
                snr_estimate = min(20 * np.log10(signal_power / noise_floor), 60.0)  # Cap at 60dB
            
            # Overall quality score (0.0 to 1.0)
            quality_score = min((volume_level * 0.6) + (snr_estimate / 60.0 * 0.4), 1.0)
            
            return {
                'quality_score': float(quality_score),
                'snr_estimate': float(snr_estimate),
                'volume_level': float(volume_level),
                'sample_count': len(audio_array)
            }
            
        except Exception as e:
            logger.warning(f"Audio quality assessment failed: {e}")
            return {'quality_score': 0.5, 'snr_estimate': 20.0, 'volume_level': 0.5}


class AudioProcessingService:
    """
    High-performance audio processing service for real-time AI companion.
    
    Features:
    - Parallel transcription and emotion detection
    - Audio quality assessment and preprocessing
    - Streaming results with <50ms processing target
    - Memory-efficient chunk management
    - Performance monitoring and optimization
    """
    
    def __init__(
        self,
        groq_client: Optional[GroqStreamingClient] = None,
        hume_client: Optional[HumeEmotionClient] = None,
        max_workers: int = 4
    ):
        self.groq_client = groq_client or GroqStreamingClient()
        self.hume_client = hume_client or HumeEmotionClient()
        
        # Thread pool for parallel processing
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # Audio buffer for chunk management
        self.audio_buffer = AudioBuffer()
        
        # Performance tracking
        self.processing_stats = {
            'total_chunks_processed': 0,
            'average_processing_time_ms': 0.0,
            'parallel_processing_count': 0,
            'transcription_accuracy': 0.0,
            'emotion_detection_accuracy': 0.0
        }
        
        logger.info(f"AudioProcessingService initialized with {max_workers} workers")
    
    async def process_audio_chunk(
        self,
        chunk: AudioChunk,
        enable_transcription: bool = True,
        enable_emotion_detection: bool = True,
        parallel_processing: bool = True,
        enable_preprocessing: bool = True
    ) -> AsyncGenerator[AudioProcessingResult, None]:
        """
        Process audio chunk with format validation, preprocessing, and parallel analysis.
        
        Args:
            chunk: Audio chunk to process
            enable_transcription: Enable speech-to-text
            enable_emotion_detection: Enable emotion analysis
            parallel_processing: Process transcription and emotion in parallel
            enable_preprocessing: Enable noise reduction and preprocessing
            
        Yields:
            AudioProcessingResult with transcription and/or emotion data
        """
        start_time = time.time()
        
        # Validate and preprocess audio format
        try:
            processed_chunk = await self._validate_and_preprocess_chunk(chunk, enable_preprocessing)
            if not processed_chunk:
                logger.warning(f"Audio chunk validation failed for chunk {chunk.chunk_id}")
                return
        except Exception as e:
            logger.error(f"Audio preprocessing error: {e}")
            # Use original chunk if preprocessing fails
            processed_chunk = chunk
        
        # Add chunk to buffer and get ready chunks
        ready_chunks = self.audio_buffer.add_chunk(processed_chunk)
        
        if not ready_chunks:
            # No chunks ready for processing yet
            return
        
        for ready_chunk in ready_chunks:
            # Assess audio quality
            quality_metrics = AudioQualityAssessment.assess_audio_quality(
                ready_chunk.data, ready_chunk.sample_rate
            )
            
            # Skip processing if audio quality is too low
            if quality_metrics['quality_score'] < 0.1:
                logger.warning(f"Skipping low-quality audio chunk: {quality_metrics}")
                continue
            
            # Process chunk
            if parallel_processing and enable_transcription and enable_emotion_detection:
                # Parallel processing for maximum speed
                async for result in self._process_chunk_parallel(ready_chunk, quality_metrics):
                    yield result
            else:
                # Sequential processing
                async for result in self._process_chunk_sequential(
                    ready_chunk, quality_metrics, enable_transcription, enable_emotion_detection
                ):
                    yield result
            
            # Update performance stats
            processing_time = (time.time() - start_time) * 1000
            self._update_performance_stats(processing_time, parallel_processing)
    
    async def _validate_and_preprocess_chunk(
        self,
        chunk: AudioChunk,
        enable_preprocessing: bool = True
    ) -> Optional[AudioChunk]:
        """
        Validate audio format and apply preprocessing.
        
        Args:
            chunk: Original audio chunk
            enable_preprocessing: Whether to apply noise reduction
            
        Returns:
            Processed audio chunk or None if validation fails
        """
        try:
            # Validate audio format using the dedicated audio format handler
            is_valid, validation_results = AudioFormatHandler.validate_audio(
                chunk.data,
                min_duration_ms=10,  # Very short minimum for chunks
                max_duration_ms=30000,
                min_quality_score=0.05  # Lower threshold for real-time processing
            )

            if not is_valid:
                errors = validation_results.get('errors', [])
                logger.warning(f"Audio validation issues for chunk {chunk.chunk_id}: {errors}")

                # Check if it's just a format parsing issue (common with WebSocket audio)
                format_parsing_error = any('Pydub parsing error' in str(error) or 'ffprobe' in str(error) for error in errors)

                if format_parsing_error and len(chunk.data) > 1000:  # If we have substantial audio data
                    logger.info(f"Allowing chunk {chunk.chunk_id} to proceed despite format parsing issues (WebSocket audio)")
                    # Continue processing with basic assumptions
                else:
                    logger.warning(f"Rejecting chunk {chunk.chunk_id} due to validation failure")
                    return None
            
            processed_data = chunk.data
            metadata = validation_results.get('metadata')

            # Convert to optimal format if needed (only if we have valid metadata)
            if metadata and hasattr(metadata, 'sample_rate') and (metadata.sample_rate != 16000 or metadata.channels != 1 or metadata.bit_depth != 16):
                try:
                    processed_data, new_metadata = AudioFormatHandler.convert_to_optimal_format(
                        chunk.data,
                        metadata.format,
                        target_sample_rate=16000,
                        target_channels=1,
                        target_bit_depth=16
                    )
                    logger.debug(f"Converted audio format for chunk {chunk.chunk_id}")
                except Exception as e:
                    logger.warning(f"Audio conversion failed, using original data: {e}")
                    processed_data = chunk.data
            
            # Apply preprocessing if enabled
            if enable_preprocessing:
                try:
                    processed_data, preprocessing_info = AudioFormatHandler.preprocess_for_transcription(processed_data)
                    logger.debug(f"Applied preprocessing to chunk {chunk.chunk_id}: {preprocessing_info.get('applied_filters', [])}")
                except Exception as e:
                    logger.warning(f"Audio preprocessing failed, using unprocessed audio: {e}")
            
            # Create processed chunk with updated data
            processed_chunk = AudioChunk(
                data=processed_data,
                chunk_id=chunk.chunk_id,
                timestamp_ms=chunk.timestamp_ms,
                sample_rate=chunk.sample_rate,
                channels=chunk.channels,
                is_final=chunk.is_final,
                user_id=chunk.user_id
            )
            
            return processed_chunk
            
        except Exception as e:
            logger.error(f"Audio validation and preprocessing error: {e}")
            return None
    
    async def _process_chunk_parallel(
        self,
        chunk: AudioChunk,
        quality_metrics: Dict[str, float]
    ) -> AsyncGenerator[AudioProcessingResult, None]:
        """
        Process chunk with optimized parallel transcription and emotion detection.
        Target: <100ms transcription, <50ms emotion detection.
        """
        start_time = time.time()

        # Use asyncio.gather for maximum parallelism
        try:
            # Execute both tasks in parallel
            transcription_task = asyncio.create_task(self._transcribe_audio_chunk(chunk, quality_metrics))
            emotion_task = asyncio.create_task(self._analyze_audio_emotions(chunk, quality_metrics))

            # Wait for tasks with timeout, allowing partial completion
            done, pending = await asyncio.wait(
                [transcription_task, emotion_task],
                timeout=5.0,  # 5 second timeout for both tasks
                return_when=asyncio.ALL_COMPLETED
            )

            # Get results from completed tasks
            transcription_result = None
            emotion_result = None

            # Process completed tasks
            for task in done:
                try:
                    result = task.result()
                    if task == transcription_task:
                        transcription_result = result
                        if result:
                            logger.info(f"🎤 Transcription completed: '{result.text}' (confidence: {result.confidence:.3f})")
                    elif task == emotion_task:
                        emotion_result = result
                        if result:
                            logger.info(f"🎤 Emotion analysis completed: {result.primary_emotion} (confidence: {result.confidence_score:.3f})")
                except Exception as e:
                    if task == transcription_task:
                        logger.error(f"Transcription error: {e}")
                    elif task == emotion_task:
                        logger.error(f"Emotion analysis error: {e}")

            # Cancel any pending tasks
            for task in pending:
                task.cancel()
                if task == transcription_task:
                    logger.warning(f"🎤 Transcription task timed out for chunk {chunk.chunk_id}")
                elif task == emotion_task:
                    logger.warning(f"🎤 Emotion analysis task timed out for chunk {chunk.chunk_id}")

            # Calculate total processing time
            total_time = (time.time() - start_time) * 1000

            # Log performance warnings if targets are missed
            if total_time > 150:  # Combined target: 100ms + 50ms
                logger.warning(f"Parallel processing took {total_time:.2f}ms, exceeding 150ms target")

            yield AudioProcessingResult(
                transcription=transcription_result,
                emotion_analysis=emotion_result,
                chunk_id=chunk.chunk_id,
                total_processing_time_ms=total_time,
                parallel_processing=True
            )
        except Exception as e:
            logger.error(f"Error in parallel processing: {e}")
            yield AudioProcessingResult(
                transcription=None,
                emotion_analysis=None,
                chunk_id=chunk.chunk_id,
                total_processing_time_ms=(time.time() - start_time) * 1000,
                parallel_processing=True
            )
    
    async def _process_chunk_sequential(
        self,
        chunk: AudioChunk,
        quality_metrics: Dict[str, float],
        enable_transcription: bool,
        enable_emotion_detection: bool
    ) -> AsyncGenerator[AudioProcessingResult, None]:
        """Process chunk sequentially."""
        start_time = time.time()
        
        transcription_result = None
        emotion_result = None
        
        # Transcription
        if enable_transcription:
            transcription_result = await self._transcribe_audio_chunk(chunk, quality_metrics)
        
        # Emotion detection
        if enable_emotion_detection:
            emotion_result = await self._analyze_audio_emotions(chunk, quality_metrics)
        
        total_time = (time.time() - start_time) * 1000
        
        yield AudioProcessingResult(
            transcription=transcription_result,
            emotion_analysis=emotion_result,
            chunk_id=chunk.chunk_id,
            total_processing_time_ms=total_time,
            parallel_processing=False
        )
    
    async def _transcribe_audio_chunk(
        self,
        chunk: AudioChunk,
        quality_metrics: Dict[str, float]
    ) -> Optional[TranscriptionResult]:
        """
        Transcribe audio chunk using optimized Groq Whisper.
        Target: <100ms processing time.
        """
        start_time = time.time()

        try:
            # Quick quality check - skip very low quality audio
            quality_score = quality_metrics.get('quality_score', 0)
            if quality_score < 0.15:
                logger.info(f"🎤 Skipping transcription for low quality audio: {chunk.chunk_id} (quality: {quality_score:.3f})")
                return TranscriptionResult(
                    text="",
                    confidence=0.0,
                    is_partial=not chunk.is_final,
                    processing_time_ms=(time.time() - start_time) * 1000,
                    chunk_id=chunk.chunk_id,
                    timestamp_ms=chunk.timestamp_ms
                )

            logger.info(f"🎤 Starting transcription for chunk {chunk.chunk_id} (quality: {quality_score:.3f}, size: {len(chunk.data)} bytes)")

            # Convert audio data to a format suitable for Groq Whisper
            audio_file = self._prepare_audio_for_whisper(chunk)

            if not audio_file:
                logger.warning(f"🎤 Failed to prepare audio for transcription: {chunk.chunk_id}")
                return None

            # Check if Groq is available
            if Groq is None:
                raise ImportError("Groq package not installed. Please install with: pip install groq")

            # Use Groq client for transcription with optimized settings
            groq_client = Groq(api_key=os.getenv('GROQ_API_KEY'))

            logger.info(f"🎤 Sending audio to Groq Whisper for transcription...")

            # Use the fastest Whisper model for real-time performance
            transcription = await asyncio.get_event_loop().run_in_executor(
                self.executor,
                lambda: groq_client.audio.transcriptions.create(
                    file=audio_file,
                    model="whisper-large-v3-turbo",  # Fastest Groq Whisper model
                    response_format="text",  # Faster than verbose_json for real-time
                    language="en",  # Pre-specify language for speed
                    temperature=0.0,  # Deterministic for consistency
                    prompt="Transcribe this audio clearly and accurately."  # Hint for better results
                )
            )

            processing_time = (time.time() - start_time) * 1000

            # Extract text (response_format="text" gives us direct string)
            text = transcription.strip() if isinstance(transcription, str) else transcription.text.strip()

            # Enhanced debugging for transcription results
            logger.info(f"🎤 Transcription completed in {processing_time:.2f}ms")
            logger.info(f"🎤 Raw transcription result: '{text}' (length: {len(text)})")

            # Performance monitoring - log if we exceed target
            if processing_time > 100:
                logger.warning(f"🎤 Transcription took {processing_time:.2f}ms, exceeding 100ms target")

            # Estimate confidence based on audio quality and processing characteristics
            base_confidence = 0.85  # Whisper is generally quite confident
            quality_factor = quality_metrics.get('quality_score', 0.5)

            # Optimized confidence calculation
            if len(text) == 0:
                confidence = 0.0
                logger.warning(f"🎤 Empty transcription result for chunk {chunk.chunk_id}")
            elif len(text) < 3:
                confidence = max(0.3, base_confidence * quality_factor * 0.6)
                logger.info(f"🎤 Short transcription result: '{text}' (confidence: {confidence:.3f})")
            elif processing_time > 200:  # Slower processing might indicate difficulty
                confidence = max(0.5, base_confidence * quality_factor * 0.8)
                logger.info(f"🎤 Slow transcription processing, reduced confidence: {confidence:.3f}")
            else:
                confidence = min(0.98, base_confidence * quality_factor)
                logger.info(f"🎤 Good transcription result: '{text}' (confidence: {confidence:.3f})")

            # Clean up temporary file
            if hasattr(audio_file, 'close'):
                audio_file.close()

            return TranscriptionResult(
                text=text,
                confidence=confidence,
                is_partial=not chunk.is_final,
                processing_time_ms=processing_time,
                chunk_id=chunk.chunk_id,
                timestamp_ms=chunk.timestamp_ms
            )

        except Exception as e:
            logger.error(f"🎤 Groq transcription error for chunk {chunk.chunk_id}: {e}")
            logger.error(f"🎤 Audio chunk details: size={len(chunk.data)}, sample_rate={chunk.sample_rate}, channels={chunk.channels}")

            # Return None instead of fallback text to avoid sending error messages to AI
            # The consumer should handle None transcription results gracefully
            return None
    
    async def _analyze_audio_emotions(
        self,
        chunk: AudioChunk,
        quality_metrics: Dict[str, float]
    ) -> Optional[EmotionAnalysisResult]:
        """
        Analyze emotions from audio chunk using optimized Hume AI.
        Target: <50ms processing time.
        """
        start_time = time.time()

        try:
            # Quick quality check - use neutral emotions for very low quality audio
            if quality_metrics.get('quality_score', 0) < 0.2:
                logger.debug(f"Using neutral emotions for low quality audio: {chunk.chunk_id}")
                return EmotionAnalysisResult(
                    primary_emotion='neutral',
                    emotion_intensity=0.5,
                    emotion_valence=0.0,
                    emotion_arousal=0.0,
                    emotions=[],
                    confidence_score=0.3,
                    processing_time_ms=(time.time() - start_time) * 1000,
                    source='quality_fallback',
                    raw_data={'quality_fallback': True, 'quality_score': quality_metrics.get('quality_score', 0)}
                )

            # Use Hume client for emotion analysis with longer timeout
            result = await asyncio.wait_for(
                self.hume_client.analyze_audio_stream(
                    audio_data=chunk.data,
                    sample_rate=chunk.sample_rate,
                    include_prosody=True,
                    include_language=False  # Audio only for speed
                ),
                timeout=3.0  # Increased to 3 seconds for more reliable emotion detection
            )

            processing_time = (time.time() - start_time) * 1000

            # Performance monitoring
            if processing_time > 50:
                logger.warning(f"Emotion detection took {processing_time:.2f}ms, exceeding 50ms target")

            # Adjust confidence based on audio quality
            if result:
                quality_factor = quality_metrics.get('quality_score', 1.0)
                result.confidence_score *= quality_factor
                result.processing_time_ms = processing_time

            return result

        except asyncio.TimeoutError:
            logger.warning(f"Emotion analysis timeout for chunk {chunk.chunk_id}")
            # Return neutral emotions as fallback
            return EmotionAnalysisResult(
                primary_emotion='neutral',
                emotion_intensity=0.5,
                emotion_valence=0.0,
                emotion_arousal=0.0,
                emotions=[],
                confidence_score=0.2,
                processing_time_ms=(time.time() - start_time) * 1000,
                source='timeout_fallback',
                raw_data={'timeout_fallback': True, 'chunk_id': chunk.chunk_id}
            )
        except Exception as e:
            logger.error(f"Emotion analysis error: {e}")
            # Return neutral emotions as fallback
            return EmotionAnalysisResult(
                primary_emotion='neutral',
                emotion_intensity=0.5,
                emotion_valence=0.0,
                emotion_arousal=0.0,
                emotions=[],
                confidence_score=0.1,
                processing_time_ms=(time.time() - start_time) * 1000,
                source='error_fallback',
                raw_data={'error_fallback': True, 'error': str(e), 'chunk_id': chunk.chunk_id}
            )

    def _prepare_audio_for_whisper(self, chunk: AudioChunk) -> Optional[Any]:
        """
        Prepare audio chunk for Groq Whisper API.

        Groq Whisper expects audio files in common formats (mp3, wav, etc.).
        We need to convert raw audio bytes to a file-like object.
        """
        try:
            import io
            import tempfile
            import wave

            # Create a temporary WAV file from raw audio data
            # Assume the audio data is raw PCM (most common for real-time audio)

            # Create a BytesIO buffer to hold WAV data
            wav_buffer = io.BytesIO()

            # Create WAV file in memory
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(chunk.channels)  # Mono or stereo
                wav_file.setsampwidth(2)  # 16-bit audio (2 bytes per sample)
                wav_file.setframerate(chunk.sample_rate)  # Sample rate
                wav_file.writeframes(chunk.data)

            # Reset buffer position to beginning
            wav_buffer.seek(0)

            # Create a file-like object that Groq can use
            # We need to give it a name with .wav extension
            wav_buffer.name = f"audio_{chunk.chunk_id}.wav"

            return wav_buffer

        except Exception as e:
            logger.error(f"Error preparing audio for Whisper: {e}")
            return None
    
    def _update_performance_stats(self, processing_time_ms: float, parallel: bool):
        """Update internal performance statistics."""
        self.processing_stats['total_chunks_processed'] += 1
        
        # Update average processing time
        current_avg = self.processing_stats['average_processing_time_ms']
        total_chunks = self.processing_stats['total_chunks_processed']
        
        new_avg = ((current_avg * (total_chunks - 1)) + processing_time_ms) / total_chunks
        self.processing_stats['average_processing_time_ms'] = new_avg
        
        if parallel:
            self.processing_stats['parallel_processing_count'] += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        stats = self.processing_stats.copy()
        
        # Add buffer information
        stats['buffer_info'] = self.audio_buffer.get_buffer_info()
        
        # Add performance ratios
        if stats['total_chunks_processed'] > 0:
            stats['parallel_processing_ratio'] = (
                stats['parallel_processing_count'] / stats['total_chunks_processed']
            )
        else:
            stats['parallel_processing_ratio'] = 0.0
        
        return stats
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get service health status."""
        stats = self.get_performance_stats()
        
        # Determine health based on performance
        avg_time = stats['average_processing_time_ms']
        target_time = 50.0  # 50ms target
        
        health_score = 1.0
        if avg_time > target_time:
            health_score = max(0.0, 1.0 - ((avg_time - target_time) / target_time))
        
        status = 'healthy'
        if health_score < 0.5:
            status = 'degraded'
        elif health_score < 0.2:
            status = 'unhealthy'
        
        return {
            'status': status,
            'health_score': health_score,
            'average_processing_time_ms': avg_time,
            'target_processing_time_ms': target_time,
            'total_chunks_processed': stats['total_chunks_processed'],
            'buffer_utilization': stats['buffer_info']['buffer_utilization']
        }


# Audio format handling is now handled by the dedicated audio_format module


# Mock classes for testing compatibility
class ContentFilter:
    """Mock ContentFilter class for test compatibility."""
    
    def filter_content(self, content: str, user, context=None):
        """Mock filter content method."""
        return True, content, {'allowed': True}


class HumeTTSClient:
    """Mock HumeTTSClient class for test compatibility."""
    
    async def synthesize_speech(self, text: str, voice_id: str = None):
        """Mock TTS synthesis."""
        return b"mock_audio_data"


class MemoryManager:
    """Mock MemoryManager class for test compatibility."""
    
    async def retrieve_context(self, user_id: str, query: str, emotion_context=None):
        """Mock memory retrieval."""
        return {
            'memories': [
                {'text': 'Mock memory', 'type': 'general'}
            ]
        }
    
    async def store_memory(self, user_id: str, content: str, memory_type: str = 'general'):
        """Mock memory storage."""
        return True


# Global service instance
_audio_service: Optional[AudioProcessingService] = None


def get_audio_service() -> AudioProcessingService:
    """Get shared audio processing service instance."""
    global _audio_service
    
    if _audio_service is None:
        _audio_service = AudioProcessingService()
    
    return _audio_service