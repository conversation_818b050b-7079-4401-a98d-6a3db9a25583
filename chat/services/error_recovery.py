"""
Error recovery and fallback mechanisms for chat services.
"""
import asyncio
import logging
import random
import time
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class CircuitBreaker:
    """Circuit breaker pattern for API failure handling."""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'closed'  # closed, open, half-open

    def allow_request(self) -> bool:
        """Check if request can be executed."""
        if self.state == 'closed':
            return True

        if self.state == 'open':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'half-open'
                return True
            return False

        # half-open state
        return True

    def record_success(self):
        """Record successful request."""
        self.failure_count = 0
        self.state = 'closed'

    def record_failure(self):
        """Record failed request."""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = 'open'

    def reset(self):
        """Manually reset the circuit breaker to closed state."""
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'closed'
        logger.info("Circuit breaker manually reset to closed state")

# Fallback responses for different error scenarios
FALLBACK_RESPONSES = {
    'general': [
        "I'm having trouble connecting to my services right now. Could you please try again in a moment?",
        "It seems I'm experiencing some technical difficulties. Please bear with me.",
        "I apologize, but I'm unable to process your request right now due to connectivity issues.",
        "My systems are currently experiencing some issues. Could we try again shortly?",
        "I'm sorry, but I can't access my full capabilities at the moment. Please try again soon."
    ],
    'timeout': [
        "I apologize for the delay. My response is taking longer than expected. Please try again.",
        "It seems my thinking process is taking too long. Let's try again with your question.",
        "I'm sorry for keeping you waiting. My response timed out. Could you please try again?",
        "I apologize, but your request timed out. Please try again or rephrase your question."
    ],
    'api_failure': [
        "I'm having trouble connecting to my knowledge services. Let's try again in a moment.",
        "My connection to the AI service is experiencing issues. Please try again shortly.",
        "I apologize, but I'm having trouble accessing my language processing capabilities right now.",
        "It seems my AI services are temporarily unavailable. Please try again soon."
    ],
    'memory_failure': [
        "I'm having trouble accessing my memory at the moment. Let's continue our conversation.",
        "I apologize, but I can't recall our previous conversations right now. Please bear with me.",
        "My memory retrieval system is experiencing issues. Let's focus on your current question."
    ]
}

def get_fallback_response(error_type: str = 'general', context: Optional[Dict[str, Any]] = None) -> str:
    """
    Generate a fallback response when services fail.
    
    Args:
        error_type: Type of error ('general', 'timeout', 'api_failure', 'memory_failure')
        context: Additional context information
        
    Returns:
        Fallback response text
    """
    responses = FALLBACK_RESPONSES.get(error_type, FALLBACK_RESPONSES['general'])
    return random.choice(responses)


class ErrorRecoveryManager:
    """Manager for error recovery and fallback mechanisms."""

    def __init__(self):
        self.error_counts = {}
        self.last_errors = {}

        # Initialize circuit breakers for different services with configurable thresholds
        from django.conf import settings

        self.circuit_breakers = {
            'groq': CircuitBreaker(
                failure_threshold=getattr(settings, 'GROQ_CIRCUIT_BREAKER_FAILURE_THRESHOLD', 10),
                recovery_timeout=getattr(settings, 'GROQ_CIRCUIT_BREAKER_RECOVERY_TIMEOUT', 60)
            ),
            'hume': CircuitBreaker(
                failure_threshold=getattr(settings, 'HUME_CIRCUIT_BREAKER_FAILURE_THRESHOLD', 10),
                recovery_timeout=getattr(settings, 'HUME_CIRCUIT_BREAKER_RECOVERY_TIMEOUT', 30)
            ),
            'openai': CircuitBreaker(failure_threshold=5, recovery_timeout=60),
        }
    
    def record_error(self, service_name: str, error_type: str, error_message: str):
        """Record an error for tracking."""
        if service_name not in self.error_counts:
            self.error_counts[service_name] = {}
        
        if error_type not in self.error_counts[service_name]:
            self.error_counts[service_name][error_type] = 0
        
        self.error_counts[service_name][error_type] += 1
        self.last_errors[service_name] = {
            'type': error_type,
            'message': error_message,
            'timestamp': time.time()
        }
        
        logger.warning(f"Error recorded for {service_name}: {error_type} - {error_message}")
    
    def get_error_stats(self, service_name: str = None) -> Dict[str, Any]:
        """Get error statistics."""
        if service_name:
            return {
                'error_counts': self.error_counts.get(service_name, {}),
                'last_error': self.last_errors.get(service_name)
            }
        
        return {
            'error_counts': self.error_counts,
            'last_errors': self.last_errors
        }
    
    def should_use_fallback(self, service_name: str, error_threshold: int = 5) -> bool:
        """Determine if fallback should be used based on error history."""
        if service_name not in self.error_counts:
            return False
        
        total_errors = sum(self.error_counts[service_name].values())
        return total_errors >= error_threshold
    
    def get_recovery_suggestion(self, service_name: str, error_type: str) -> str:
        """Get a recovery suggestion for a specific error."""
        suggestions = {
            'timeout': 'Consider increasing timeout values or implementing retry logic',
            'api_failure': 'Check API credentials and network connectivity',
            'memory_failure': 'Verify memory service configuration and availability',
            'general': 'Review service logs and configuration'
        }

        return suggestions.get(error_type, suggestions['general'])

    async def handle_api_failure(self, service: str, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle API failure with appropriate recovery strategy.

        Args:
            service: Service name ('groq', 'hume', 'openai')
            error: The exception that occurred
            context: Context information for recovery

        Returns:
            Recovery result with fallback data or error info
        """
        # Record the failure in circuit breaker
        if service in self.circuit_breakers:
            self.circuit_breakers[service].record_failure()

        # Record error for tracking
        self.record_error(service, type(error).__name__, str(error))

        logger.error(f"API failure for {service}: {error}")

        # Service-specific recovery strategies
        if service == 'groq':
            return await self._handle_groq_failure(error, context)
        elif service == 'hume':
            return await self._handle_hume_failure(error, context)
        elif service == 'openai':
            return await self._handle_openai_failure(error, context)
        else:
            return {
                'success': False,
                'fallback_response': get_fallback_response('api_failure', context),
                'error': str(error)
            }

    async def _handle_groq_failure(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Groq API failure with OpenAI fallback."""
        try:
            # Try OpenAI as fallback
            if self.circuit_breakers['openai'].allow_request():
                logger.info("Groq failed, attempting OpenAI fallback")
                return {
                    'success': False,
                    'fallback_service': 'openai',
                    'fallback_response': get_fallback_response('api_failure', context),
                    'should_retry_with_openai': True
                }
        except Exception as fallback_error:
            logger.error(f"OpenAI fallback also failed: {fallback_error}")

        return {
            'success': False,
            'fallback_response': get_fallback_response('api_failure', context),
            'error': str(error)
        }

    async def _handle_hume_failure(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Hume API failure with neutral emotion fallback."""
        operation = context.get('operation', 'unknown')

        if operation == 'emotion_detection':
            # Return neutral emotions as fallback
            return {
                'success': True,
                'fallback_data': {
                    'primary_emotion': 'neutral',
                    'emotion_intensity': 0.5,
                    'emotion_valence': 0.0,
                    'emotion_arousal': 0.0,
                    'emotions': [{'name': 'neutral', 'score': 1.0, 'confidence': 0.5}],
                    'confidence_score': 0.5,
                    'source': 'fallback'
                }
            }
        elif operation == 'tts':
            # Return text-only mode fallback
            return {
                'success': True,
                'fallback_data': {
                    'text_only': True,
                    'message': 'Audio generation temporarily unavailable'
                }
            }

        return {
            'success': False,
            'fallback_response': get_fallback_response('api_failure', context),
            'error': str(error)
        }

    async def _handle_openai_failure(self, error: Exception, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle OpenAI API failure."""
        return {
            'success': False,
            'fallback_response': get_fallback_response('api_failure', context),
            'error': str(error)
        }

    async def retry_with_backoff(self, func, service: str, max_retries: int = 3, base_delay: float = 1.0):
        """
        Retry function with exponential backoff.

        Args:
            func: Async function to retry
            service: Service name for circuit breaker
            max_retries: Maximum number of retries
            base_delay: Base delay in seconds

        Returns:
            Function result or raises last exception
        """
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                # Check circuit breaker
                if service in self.circuit_breakers:
                    if not self.circuit_breakers[service].allow_request():
                        raise Exception(f"Circuit breaker open for {service}")

                result = await func()

                # Record success
                if service in self.circuit_breakers:
                    self.circuit_breakers[service].record_success()

                return result

            except Exception as e:
                last_exception = e

                # Record failure
                if service in self.circuit_breakers:
                    self.circuit_breakers[service].record_failure()

                if attempt < max_retries:
                    # Calculate delay with jitter
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.warning(f"Attempt {attempt + 1} failed for {service}, retrying in {delay:.2f}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"All {max_retries + 1} attempts failed for {service}: {e}")

        raise last_exception

    def reset_circuit_breaker(self, service: str):
        """Reset circuit breaker for a specific service."""
        if service in self.circuit_breakers:
            self.circuit_breakers[service].reset()
            logger.info(f"Circuit breaker reset for service: {service}")
        else:
            logger.warning(f"No circuit breaker found for service: {service}")

    def reset_all_circuit_breakers(self):
        """Reset all circuit breakers."""
        for service, breaker in self.circuit_breakers.items():
            breaker.reset()
        logger.info("All circuit breakers reset")

    def get_circuit_breaker_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all circuit breakers."""
        status = {}
        for service, breaker in self.circuit_breakers.items():
            status[service] = {
                'state': breaker.state,
                'failure_count': breaker.failure_count,
                'last_failure_time': breaker.last_failure_time,
                'failure_threshold': breaker.failure_threshold,
                'recovery_timeout': breaker.recovery_timeout
            }
        return status


# Global error recovery manager instance
error_recovery_manager = ErrorRecoveryManager()