"""
Fast Response Service for immediate conversational responses (≤450ms target).
Uses direct Groq calls for instant responses, then optionally routes to full agent workflow.
Now includes Hume TTS integration for sub-450ms first audio chunk delivery.
"""
import asyncio
import logging
import time
import uuid
import base64
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
from dataclasses import dataclass, field

from langchain_groq import ChatGroq
from langchain_core.messages import HumanMessage, SystemMessage
from django.conf import settings

# Import memory components
from agents.services.memory_manager import MemoryManager
from chat.services.conversation_logger import conversation_logger

# Import TTS components
from chat.services.hume_service import get_tts_client, EmotionAnalysisResult

logger = logging.getLogger(__name__)


@dataclass
class ConversationState:
    """Shared conversation state between fast response service and orchestrator."""
    conversation_id: str
    user_id: str
    user_input: str
    conversation_history: List[Dict] = field(default_factory=list)
    emotion_context: Optional[Dict] = None

    # Fast response state
    fast_response_complete: bool = False
    fast_response_content: str = ""
    fast_response_time_ms: float = 0

    # Background processing state
    background_processing_active: bool = False
    background_processing_complete: bool = False
    background_result: Optional[Dict] = None
    background_start_time: Optional[float] = None

    # Natural stopping point detection
    stopping_point_detected: bool = False
    stopping_point_confidence: float = 0.0
    comprehensive_response_ready: bool = False

    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


class ConversationStateManager:
    """Manages shared conversation state between fast response service and orchestrator."""

    def __init__(self):
        self._states: Dict[str, ConversationState] = {}
        self._lock = asyncio.Lock()

    async def create_conversation_state(
        self,
        user_id: str,
        user_input: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None
    ) -> ConversationState:
        """Create a new conversation state."""
        async with self._lock:
            conversation_id = f"conv_{int(time.time() * 1000)}_{str(uuid.uuid4())[:8]}"
            state = ConversationState(
                conversation_id=conversation_id,
                user_id=user_id,
                user_input=user_input,
                conversation_history=conversation_history or [],
                emotion_context=emotion_context
            )
            self._states[conversation_id] = state
            return state

    async def get_conversation_state(self, conversation_id: str) -> Optional[ConversationState]:
        """Get conversation state by ID."""
        async with self._lock:
            return self._states.get(conversation_id)

    async def update_conversation_state(self, conversation_id: str, **updates) -> bool:
        """Update conversation state with new data."""
        async with self._lock:
            if conversation_id in self._states:
                state = self._states[conversation_id]
                for key, value in updates.items():
                    if hasattr(state, key):
                        setattr(state, key, value)
                state.updated_at = datetime.now()
                return True
            return False

    async def cleanup_old_states(self, max_age_minutes: int = 60):
        """Clean up old conversation states."""
        async with self._lock:
            cutoff_time = datetime.now().timestamp() - (max_age_minutes * 60)
            to_remove = [
                conv_id for conv_id, state in self._states.items()
                if state.created_at.timestamp() < cutoff_time
            ]
            for conv_id in to_remove:
                del self._states[conv_id]
            if to_remove:
                logger.info(f"Cleaned up {len(to_remove)} old conversation states")


# Global conversation state manager
_conversation_state_manager = ConversationStateManager()

def get_conversation_state_manager() -> ConversationStateManager:
    """Get the global conversation state manager."""
    return _conversation_state_manager


class FastResponseService:
    """
    Parallel processing response system:
    1. Immediate conversational response (≤450ms): Direct Groq call → conversational response
    2. Simultaneous background processing: Always trigger specialized agent processing
    3. Comprehensive response delivery: At natural stopping points
    """

    def __init__(self, user=None):
        self.user = user

        # Fast Groq LLM for immediate responses
        self.groq_llm = ChatGroq(
            model="llama-3.3-70b-versatile",
            temperature=0.7,
            streaming=True,
            groq_api_key=settings.GROQ_API_KEY,
            max_tokens=150  # Shorter responses for speed
        )

        # Initialize memory cache service for fast contextual responses
        try:
            from chat.services.memory_cache_service import memory_cache_service
            self.memory_cache_service = memory_cache_service
            logger.info("FastResponseService initialized with memory cache integration")
        except Exception as e:
            logger.warning(f"Failed to initialize memory cache service: {e}")
            self.memory_cache_service = None

        # Initialize expression measurement service
        try:
            from chat.services.expression_measurement_service import expression_measurement_service
            self.expression_service = expression_measurement_service
            logger.info("FastResponseService initialized with expression measurement integration")
        except Exception as e:
            logger.warning(f"Failed to initialize expression measurement service: {e}")
            self.expression_service = None

        # Expression context storage for background processing
        self._expression_context_cache = {}  # user_id -> latest expression context
        self._expression_cache_lock = asyncio.Lock()
        self._current_audio_data = None  # Store current audio data for background analysis

        # Initialize agent refinement system
        try:
            from agents.services.agent_refinement_system import AgentRefinementSystem
            self.refinement_system = AgentRefinementSystem()
            logger.info("Agent refinement system initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize agent refinement system: {e}")
            self.refinement_system = None

        # Performance tracking
        self.response_count = 0
        self.total_first_response_time = 0.0

        # Conversation state manager
        self.state_manager = _conversation_state_manager

        # Background task tracking
        self._background_tasks: Dict[str, asyncio.Task] = {}

        logger.info("FastResponseService initialized for parallel processing")
    
    async def process_query_fast(
        self,
        user_input: str,
        user_id: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None,
        streaming: bool = True,
        enable_tts: bool = True
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process query with parallel processing approach:
        1. Immediate conversational response (≤450ms) using Groq LLM
        2. Simultaneous background agent processing for ALL messages
        3. Comprehensive response delivery at natural stopping points
        """
        start_time = time.time()

        # Create conversation state for this interaction
        conv_state = await self.state_manager.create_conversation_state(
            user_id=user_id,
            user_input=user_input,
            conversation_history=conversation_history,
            emotion_context=emotion_context
        )

        # Log conversation start
        conversation_logger.print_conversation_turn(
            speaker=f"👤 {getattr(self.user, 'first_name', 'User')}",
            message=user_input,
            metadata={'timestamp': datetime.now().isoformat()},
            agent_type=self._detect_agent_type_from_input(user_input)
        )

        try:
            # STEP 1A: Enhance emotion context with session-based expression aggregation
            enhanced_emotion_context = await self._enhance_emotion_context_with_expression(
                user_id, conv_state.conversation_id, emotion_context
            )

            # STEP 1B: Always provide immediate conversational response using Groq
            fast_response = await self._generate_fast_conversational_response(
                user_input, conversation_history, enhanced_emotion_context
            )

            fast_response_time = (time.time() - start_time) * 1000

            # Update conversation state with fast response
            await self.state_manager.update_conversation_state(
                conv_state.conversation_id,
                fast_response_complete=True,
                fast_response_content=fast_response,
                fast_response_time_ms=fast_response_time
            )

            # Stream the fast conversational response
            yield {
                'type': 'response_chunk',
                'content': fast_response,
                'metadata': {
                    'source': 'fast_conversational',
                    'conversation_id': conv_state.conversation_id,
                    'response_time_ms': fast_response_time,
                    'timestamp': datetime.now().isoformat()
                }
            }

            # Start TTS streaming immediately after text response if enabled
            if enable_tts and fast_response.strip():
                logger.info(f"🎵 Starting TTS generation for text: '{fast_response[:50]}...'")

                # Yield TTS chunks immediately
                logger.info(f"🎵 Starting TTS generation for text: '{fast_response[:50]}...'")
                try:
                    tts_chunk_count = 0
                    async for tts_chunk in self._stream_tts_response_generator(
                        text=fast_response,
                        emotion_context=emotion_context,
                        conversation_id=conv_state.conversation_id,
                        target_first_chunk_ms=450
                    ):
                        tts_chunk_count += 1
                        chunk_info = f"type={tts_chunk.get('type')}, audio_bytes={len(tts_chunk.get('audio_data', ''))}, is_final={tts_chunk.get('is_final', False)}"
                        logger.info(f"🎵 Yielding TTS chunk #{tts_chunk_count}: {chunk_info}")
                        yield tts_chunk

                    logger.info(f"🎵 TTS generation completed, yielded {tts_chunk_count} chunks")
                except Exception as e:
                    logger.error(f"Error in TTS streaming: {e}")
                    yield {
                        'type': 'tts_error',
                        'error': str(e),
                        'conversation_id': conv_state.conversation_id,
                        'timestamp': datetime.now().isoformat()
                    }

            # Log the fast AI response
            conversation_logger.print_conversation_turn(
                speaker=f"🤖 {getattr(self.user, 'ai_companion_name', 'Assistant')}",
                message=fast_response,
                metadata={
                    'response_time_ms': fast_response_time,
                    'source': 'fast_conversational',
                    'conversation_id': conv_state.conversation_id,
                    'timestamp': datetime.now().isoformat()
                },
                agent_type=self._detect_agent_type_from_input(user_input)
            )

            # STEP 2A: Start background expression analysis if audio data is available
            # This will run in parallel and cache results for future interactions
            if hasattr(self, '_current_audio_data') and self._current_audio_data:
                expression_task = asyncio.create_task(
                    self._analyze_expression_background(
                        self._current_audio_data,
                        user_id,
                        conv_state.conversation_id,
                        user_input  # Pass transcribed text for multi-modal analysis
                    )
                )
                # Don't wait for this - let it run in background
                logger.debug(f"Started background expression analysis for user {user_id}")

            # STEP 2B: Simultaneously trigger background agent processing for ALL messages
            background_task = asyncio.create_task(
                self._process_with_background_agents(conv_state)
            )
            self._background_tasks[conv_state.conversation_id] = background_task

            # STEP 3: Check if this is a natural stopping point for comprehensive response
            should_show_comprehensive = await self._should_provide_comprehensive_response(
                user_input, conversation_history or []
            )

            if should_show_comprehensive:
                logger.info(f"🎯 Natural stopping point detected, waiting for comprehensive response...")

                # Wait for background processing to complete (with timeout)
                try:
                    await asyncio.wait_for(background_task, timeout=10.0)

                    # Get the comprehensive result
                    updated_state = await self.state_manager.get_conversation_state(conv_state.conversation_id)
                    if updated_state and updated_state.background_result:
                        comprehensive_response = updated_state.background_result.get('content', '')

                        if comprehensive_response and len(comprehensive_response) > len(fast_response) * 1.5:
                            # Stream the comprehensive response
                            yield {
                                'type': 'comprehensive_response',
                                'content': comprehensive_response,
                                'metadata': {
                                    'source': 'comprehensive_agent',
                                    'conversation_id': conv_state.conversation_id,
                                    'timestamp': datetime.now().isoformat()
                                }
                            }

                            # Log comprehensive response
                            conversation_logger.print_full_agent_response(
                                agent_name="Comprehensive Agent",
                                full_response=comprehensive_response,
                                metadata={
                                    'conversation_id': conv_state.conversation_id,
                                    'processing_time_ms': updated_state.background_result.get('processing_time_ms', 0)
                                }
                            )

                except asyncio.TimeoutError:
                    logger.warning(f"Background processing timeout for conversation {conv_state.conversation_id}")

            # Final response complete signal
            yield {
                'type': 'response_complete',
                'full_content': fast_response,
                'metadata': {
                    'response_time_ms': fast_response_time,
                    'source': 'fast_conversational',
                    'conversation_id': conv_state.conversation_id,
                    'background_processing_active': conv_state.background_processing_active,
                    'timestamp': datetime.now().isoformat()
                }
            }

            # Update performance metrics
            self._update_performance_metrics(fast_response_time, fast_response_time)

        except Exception as e:
            logger.error(f"Error in parallel processing: {e}")
            yield {
                'type': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'conversation_id': conv_state.conversation_id
            }

    async def _generate_fast_conversational_response(
        self,
        user_input: str,
        conversation_history: List[Dict] = None,
        emotion_context: Optional[Dict] = None
    ) -> str:
        """Generate immediate conversational response using Groq LLM."""
        try:
            # Build conversation context
            context_messages = []
            if conversation_history:
                # Get last 3 messages for context
                recent_history = conversation_history[-3:]
                for msg in recent_history:
                    role = msg.get('role', 'user')
                    content = msg.get('content', '')
                    if role in ['user', 'assistant'] and content:
                        context_messages.append(f"{role.title()}: {content}")

            # Build system prompt for conversational response
            system_prompt = self._build_conversational_system_prompt(emotion_context)

            # Build user prompt with context
            user_prompt = user_input
            if context_messages:
                user_prompt = f"Recent conversation:\n{chr(10).join(context_messages)}\n\nCurrent message: {user_input}"

            # Generate fast response using Groq
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await self.groq_llm.ainvoke(messages)
            return response.content.strip()

        except Exception as e:
            logger.error(f"Error generating fast conversational response: {e}")
            # Fallback to simple acknowledgment
            return self._get_fallback_conversational_response(user_input)

    def _build_conversational_system_prompt(self, emotion_context: Optional[Dict] = None) -> str:
        """Build system prompt for conversational responses."""
        base_prompt = f"""You are {getattr(self.user, 'ai_companion_name', 'Assistant')}, a warm and engaging AI companion.

PERSONALITY: {getattr(self.user, 'selected_personality', 'caringFriend')}

Your role is to provide immediate, natural conversational responses that:
- Acknowledge the user's message warmly
- Show understanding and empathy
- Keep the conversation flowing naturally
- Are brief but engaging (1-2 sentences max)
- Match the user's energy and tone

"""

        # Add emotion-aware guidance
        if emotion_context and isinstance(emotion_context, dict) and emotion_context.get('emotions'):
            try:
                dominant_emotion = max(emotion_context['emotions'], key=lambda x: x.get('score', 0))
                emotion_name = dominant_emotion.get('name', '').lower()

                if emotion_name in ['sad', 'frustrated', 'angry']:
                    base_prompt += "The user seems to be going through a difficult time. Be extra supportive and understanding.\n"
                elif emotion_name in ['happy', 'excited', 'joy']:
                    base_prompt += "The user seems positive and energetic. Match their enthusiasm!\n"
                elif emotion_name in ['confused', 'uncertain']:
                    base_prompt += "The user seems uncertain. Be reassuring and helpful.\n"
            except (KeyError, TypeError, AttributeError) as e:
                logger.debug(f"Error processing emotion context: {e}")
                # Continue without emotion context

        base_prompt += "\nRespond naturally and conversationally. Keep it brief but warm."
        return base_prompt

    def _get_fallback_conversational_response(self, user_input: str) -> str:
        """Get a fallback conversational response."""
        import random

        # Simple fallback responses based on input patterns
        user_lower = user_input.lower()

        if any(word in user_lower for word in ['help', 'need', 'want', 'can you']):
            responses = [
                "I'd be happy to help you with that!",
                "Absolutely! Let me assist you with this.",
                "Of course! I'm here to help.",
                "I'd love to help you figure this out!"
            ]
        elif any(word in user_lower for word in ['thanks', 'thank you', 'appreciate']):
            responses = [
                "You're so welcome!",
                "Happy to help!",
                "Anytime! That's what I'm here for.",
                "My pleasure!"
            ]
        elif '?' in user_input:
            responses = [
                "That's a great question! Let me think about this.",
                "Interesting question! I'd love to explore this with you.",
                "Good point! Let me help you with that.",
                "I'm glad you asked! Let's dive into this."
            ]
        else:
            responses = [
                "I hear you! Let me help with this.",
                "That makes sense! I'm here to support you.",
                "I understand! Let's work on this together.",
                "Got it! I'm here to help you through this."
            ]

        return random.choice(responses)

    async def _process_with_background_agents(self, conv_state: ConversationState):
        """Process query with specialized agents in background using orchestrator's background method."""
        try:
            logger.info(f"Starting background agent processing for conversation {conv_state.conversation_id}")

            # Update state to indicate background processing started
            await self.state_manager.update_conversation_state(
                conv_state.conversation_id,
                background_processing_active=True,
                background_start_time=time.time()
            )

            # Import and use the AgentOrchestrator's background processing
            from agents.services.orchestrator import AgentOrchestrator
            orchestrator = AgentOrchestrator()

            # Use the orchestrator's background processing method
            background_result = await orchestrator.process_background_query(
                user_input=conv_state.user_input,
                user_id=conv_state.user_id,
                conversation_id=conv_state.conversation_id,
                conversation_history=conv_state.conversation_history,
                emotion_context=conv_state.emotion_context
            )

            # Update conversation state with result
            await self.state_manager.update_conversation_state(
                conv_state.conversation_id,
                background_processing_complete=True,
                background_result=background_result
            )

            processing_time = background_result.get('metadata', {}).get('processing_time_ms', 0)
            logger.info(f"Background processing completed for conversation {conv_state.conversation_id} in {processing_time:.1f}ms")

        except Exception as e:
            logger.error(f"Error in background agent processing: {e}")
            # Update state to indicate error
            await self.state_manager.update_conversation_state(
                conv_state.conversation_id,
                background_processing_complete=True,
                background_result={'error': str(e)}
            )

    async def _stream_tts_response(
        self,
        text: str,
        emotion_context: Optional[Dict] = None,
        conversation_id: str = None,
        target_first_chunk_ms: float = 450
    ) -> None:
        """
        Stream TTS audio chunks with target first chunk delivery time.

        Args:
            text: Text to synthesize
            emotion_context: Emotion context for voice modulation
            conversation_id: Conversation ID for tracking
            target_first_chunk_ms: Target time for first audio chunk (default 450ms)
        """
        start_time = time.time()
        first_chunk_time = None

        try:
            # Convert emotion context to EmotionAnalysisResult if needed
            emotion_result = None
            if emotion_context:
                emotion_result = self._convert_emotion_context(emotion_context)

            # Get user's voice preferences
            voice_name = self._get_user_voice_preference()

            logger.info(f"🎵 Starting TTS streaming for conversation {conversation_id}")
            logger.info(f"🎵 Voice name: {voice_name}, emotion_result: {emotion_result}")

            # Stream TTS using Hume client
            logger.info(f"🎵 About to get TTS client...")
            async with get_tts_client() as tts_client:
                chunk_index = 0
                logger.info(f"🎵 Got TTS client, about to start synthesis for text: '{text[:50]}...'")

                # Use the correct method name from Hume documentation
                logger.info(f"🎵 Calling synthesize_streaming...")
                tts_generator = tts_client.synthesize_streaming(
                    text=text,
                    voice_name=voice_name,
                    emotion_context=emotion_result,
                    speed=1.0  # Could be user preference
                )

                logger.info(f"🎵 Created TTS generator, starting iteration...")
                async for tts_chunk in tts_generator:
                    logger.info(f"🎵 Received TTS chunk {chunk_index} from Hume client")
                    current_time = time.time()

                    # Record first chunk time
                    if first_chunk_time is None:
                        first_chunk_time = (current_time - start_time) * 1000
                        logger.info(f"🎵 First TTS chunk delivered in {first_chunk_time:.1f}ms (target: {target_first_chunk_ms}ms)")

                        # Log performance against target
                        if first_chunk_time <= target_first_chunk_ms:
                            logger.info(f"✅ TTS first chunk target achieved!")
                        else:
                            logger.warning(f"⚠️ TTS first chunk exceeded target by {first_chunk_time - target_first_chunk_ms:.1f}ms")

                    # Encode audio data for transmission
                    audio_data_b64 = base64.b64encode(tts_chunk.audio_data).decode('utf-8')

                    # Send audio chunk via WebSocket (this would need to be connected to the consumer)
                    # For now, we'll log the chunk delivery
                    logger.debug(f"🎵 TTS chunk {chunk_index} ready ({len(tts_chunk.audio_data)} bytes)")

                    chunk_index += 1

                    # If this is the final chunk, log completion
                    if tts_chunk.is_final:
                        total_time = (current_time - start_time) * 1000
                        logger.info(f"🎵 TTS streaming completed in {total_time:.1f}ms ({chunk_index} chunks)")
                        break

        except Exception as e:
            logger.error(f"Error in TTS streaming: {e}")
            # Could send error notification to client here

    async def _stream_tts_response_generator(
        self,
        text: str,
        emotion_context: Optional[Dict] = None,
        conversation_id: str = None,
        target_first_chunk_ms: float = 450
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream TTS audio chunks as generator for consumer integration.

        Args:
            text: Text to synthesize
            emotion_context: Emotion context for voice modulation
            conversation_id: Conversation ID for tracking
            target_first_chunk_ms: Target time for first audio chunk (default 450ms)

        Yields:
            Dict containing TTS chunk data for consumer
        """
        logger.info(f"🎵 _stream_tts_response_generator called with text: '{text[:30]}...'")
        start_time = time.time()
        first_chunk_time = None

        try:
            # Convert emotion context to EmotionAnalysisResult if needed
            emotion_result = None
            if emotion_context:
                emotion_result = self._convert_emotion_context(emotion_context)

            # Get user's voice preferences
            voice_name = self._get_user_voice_preference()

            logger.info(f"🎵 Starting TTS streaming for conversation {conversation_id}")

            # Stream TTS using Hume client
            async with get_tts_client() as tts_client:
                chunk_index = 0
                async for tts_chunk in tts_client.synthesize_streaming(
                    text=text,
                    voice_name=voice_name,
                    emotion_context=emotion_result,
                    speed=1.0  # Could be user preference
                ):
                    current_time = time.time()

                    # Record first chunk time
                    if first_chunk_time is None:
                        first_chunk_time = (current_time - start_time) * 1000
                        logger.info(f"🎵 First TTS chunk delivered in {first_chunk_time:.1f}ms (target: {target_first_chunk_ms}ms)")

                        # Log performance against target
                        if first_chunk_time <= target_first_chunk_ms:
                            logger.info(f"✅ TTS first chunk target achieved!")
                        else:
                            logger.warning(f"⚠️ TTS first chunk exceeded target by {first_chunk_time - target_first_chunk_ms:.1f}ms")

                    # Encode audio data for transmission
                    audio_data_b64 = base64.b64encode(tts_chunk.audio_data).decode('utf-8')

                    # Yield TTS chunk in format expected by consumer
                    yield {
                        'type': 'tts_chunk',
                        'audio_data': audio_data_b64,
                        'chunk_id': f"tts_{conversation_id}_{chunk_index}",
                        'chunk_index': chunk_index,
                        'is_final': tts_chunk.is_final,
                        'voice_settings': {
                            'voice_name': voice_name,
                            'speed': 1.0
                        },
                        'metadata': {
                            'conversation_id': conversation_id,
                            'first_chunk_time_ms': first_chunk_time,
                            'target_first_chunk_ms': target_first_chunk_ms,
                            'timestamp': datetime.now().isoformat()
                        }
                    }

                    chunk_index += 1

                    # If this is the final chunk, log completion
                    if tts_chunk.is_final:
                        total_time = (current_time - start_time) * 1000
                        logger.info(f"🎵 TTS streaming completed in {total_time:.1f}ms ({chunk_index} chunks)")
                        break

        except Exception as e:
            logger.error(f"Error in TTS streaming generator: {e}")
            import traceback
            logger.error(f"TTS error traceback: {traceback.format_exc()}")

            # Try to reset the circuit breaker if it's a Hume issue
            if "circuit breaker" in str(e).lower() or "hume" in str(e).lower():
                logger.info("🔄 Attempting to reset Hume circuit breaker due to TTS failure")
                try:
                    from chat.services.error_recovery import error_recovery_manager
                    error_recovery_manager.reset_circuit_breaker('hume')
                    logger.info("✅ Hume circuit breaker reset successfully")
                except Exception as reset_error:
                    logger.error(f"Failed to reset circuit breaker: {reset_error}")

            # Yield error chunk with text fallback
            yield {
                'type': 'tts_error',
                'error': str(e),
                'conversation_id': conversation_id,
                'timestamp': datetime.now().isoformat(),
                'fallback_text': text,  # Include original text for frontend fallback
                'retry_suggested': True
            }

    def _convert_emotion_context(self, emotion_context: Dict) -> Optional[EmotionAnalysisResult]:
        """Convert emotion context dict to EmotionAnalysisResult."""
        try:
            if not emotion_context:
                return None

            # Handle different emotion context formats
            if isinstance(emotion_context, dict):
                primary_emotion = emotion_context.get('primary_emotion', 'neutral')
                emotions = emotion_context.get('emotions', [])

                # Convert emotion list to EmotionScore objects if needed
                from chat.services.hume_service import EmotionScore
                emotion_scores = []
                for emotion in emotions:
                    if isinstance(emotion, dict):
                        emotion_scores.append(EmotionScore(
                            name=emotion.get('name', 'neutral'),
                            score=emotion.get('score', 0.5),
                            confidence=emotion.get('confidence', 1.0)
                        ))

                return EmotionAnalysisResult(
                    primary_emotion=primary_emotion,
                    emotion_intensity=emotion_context.get('intensity', 0.5),
                    emotion_valence=emotion_context.get('valence', 0.0),
                    emotion_arousal=emotion_context.get('arousal', 0.0),
                    emotions=emotion_scores,
                    confidence_score=emotion_context.get('confidence_score', 0.5),
                    processing_time_ms=0.0,
                    source='converted',
                    raw_data=emotion_context
                )

            return None

        except Exception as e:
            logger.warning(f"Error converting emotion context: {e}")
            return None

    def _get_user_voice_preference(self) -> Optional[str]:
        """Get user's preferred voice for TTS."""
        try:
            if self.user and hasattr(self.user, 'voice_preference'):
                return self.user.voice_preference

            # Use environment variable for default voice
            import os
            default_voice = os.getenv('HUME_DEFAULT_VOICE', 'Ellah')

            # Default voice based on user's companion personality
            if self.user and hasattr(self.user, 'selected_personality'):
                personality = self.user.selected_personality
                voice_mapping = {
                    'caringFriend': default_voice,  # Warm, caring voice
                    'playfulCompanion': default_voice,  # Energetic voice
                    'wiseMentor': default_voice,  # Thoughtful voice
                    'romanticPartner': default_voice,  # Intimate voice
                    'supportiveTherapist': default_voice  # Calm voice
                }
                return voice_mapping.get(personality, default_voice)

            return default_voice  # Default voice from env

        except Exception as e:
            logger.warning(f"Error getting voice preference: {e}")
            import os
            return os.getenv('HUME_DEFAULT_VOICE', 'Ellah')

    def _needs_agent_processing(self, user_input: str) -> bool:
        """
        Quick determination if query needs full agent processing.
        Optimized for speed - simple keyword matching.
        """
        user_input_lower = user_input.lower()

        # Agent processing triggers (complex tasks)
        agent_triggers = [
            # Analysis and reasoning
            'analyze', 'compare', 'evaluate', 'research', 'study',
            'pros and cons', 'advantages', 'disadvantages',

            # Problem solving
            'problem', 'solve', 'help me with', 'figure out',
            'stuck on', 'trouble with', 'having difficulty',

            # Planning and strategy
            'plan', 'strategy', 'approach', 'method', 'steps',
            'how to', 'best way', 'recommend', 'suggest',

            # Learning and education
            'explain', 'teach me', 'learn about', 'understand',
            'tutorial', 'guide', 'course',

            # Business and work
            'business', 'project', 'meeting', 'presentation',
            'proposal', 'budget', 'marketing',

            # Technical and development
            'code', 'programming', 'debug', 'algorithm',
            'technical', 'development', 'software',

            # Communication and tasks
            'send email', 'email', 'write email', 'compose',
            'send message', 'contact', 'reach out',

            # Recommendations and search
            'recommend', 'recommendation', 'suggestions',
            'find me', 'search for', 'look up', 'good restaurant',
            'best place', 'where can i', 'what restaurant',

            # Review and analysis
            'review', 'check', 'look at', 'examine',
            'paper', 'document', 'article', 'study'
        ]

        # Check for agent triggers
        if any(trigger in user_input_lower for trigger in agent_triggers):
            return True

        # Check for specific patterns that need agent processing
        agent_patterns = [
            'can you help me',
            'i need',
            'help me',
            'assist me',
            'find',
            'search',
            'look for',
            'where',
            'what is the best',
            'which',
            'comprehensive',
            'detailed',
            'in-depth'
        ]

        if any(pattern in user_input_lower for pattern in agent_patterns):
            # But exclude simple conversational uses
            simple_exceptions = [
                'how are you',
                'what are you',
                'who are you',
                'what do you think',
                'how do you feel',
                'can you tell me a joke',
                'help me feel better'
            ]

            if not any(exception in user_input_lower for exception in simple_exceptions):
                return True

        # Check question length (longer questions often need more processing)
        if len(user_input.split()) > 15:
            return True

        # Default to fast response for conversational queries
        return False

    async def _get_contextual_memories(self, user_id: str, session_id: str = None, limit: int = 3) -> List[Dict[str, Any]]:
        """
        Retrieve contextual memories for personalized filler responses using fast cache.
        Focuses on recent, personal, and conversational memories.
        """
        if not self.memory_cache_service or not user_id:
            logger.debug(f"No memory cache service or user_id for contextual memories: service={self.memory_cache_service is not None}, user_id={user_id}")
            return []

        try:
            # Use session_id if provided, otherwise create a temporary one
            if not session_id:
                import time
                session_id = f"fast_response_{int(time.time())}"
                # Quick preload for this session
                await self.memory_cache_service.preload_user_memories(user_id, session_id)

            # Get a mix of memory types for contextual filler using fast cache
            contextual_memories = []

            # Search for different types of contextual information
            search_queries = [
                "recent conversation topics interests activities",
                "personal information preferences background hobbies",
                "work job career interests likes dislikes"
            ]

            for query in search_queries:
                try:
                    memories = await self.memory_cache_service.search_cached_memories(
                        user_id=user_id,
                        session_id=session_id,
                        query=query,
                        k=2
                    )
                    contextual_memories.extend(memories or [])
                except Exception as e:
                    logger.debug(f"Error searching cached memories for query '{query}': {e}")

            # Remove duplicates and sort by relevance
            seen_ids = set()
            unique_memories = []
            for memory in contextual_memories:
                memory_id = memory.get('memory_id')
                if memory_id and memory_id not in seen_ids:
                    seen_ids.add(memory_id)
                    unique_memories.append(memory)

            # Sort by importance/similarity score
            unique_memories.sort(key=lambda x: (
                x.get('importance_score', 0) * 0.7 +
                x.get('similarity_score', 0) * 0.3
            ), reverse=True)

            result = unique_memories[:limit]
            logger.debug(f"Retrieved {len(result)} contextual memories from cache for user {user_id}")
            return result

        except Exception as e:
            logger.error(f"Error retrieving contextual memories from cache: {e}")
            return []

    def _get_contextual_memories_sync(self, user_id: str, limit: int = 3) -> List[Dict[str, Any]]:
        """
        Synchronous wrapper for getting contextual memories.
        Uses asyncio.run to call the async cache method.
        """
        try:
            import asyncio
            import time

            # Create a temporary session for this request
            session_id = f"fast_response_sync_{int(time.time())}"

            # Run the async method
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    self._get_contextual_memories(user_id, session_id, limit)
                )
                return result
            finally:
                loop.close()

        except Exception as e:
            logger.debug(f"Error in sync memory wrapper: {e}")
            # Fallback to empty list for fast responses
            return []

    async def _store_expression_context(self, user_id: str, expression_result) -> None:
        """Store expression context for future use."""
        if not expression_result:
            return

        try:
            async with self._expression_cache_lock:
                self._expression_context_cache[user_id] = {
                    'dominant_emotion': expression_result.dominant_emotion,
                    'confidence': expression_result.confidence,
                    'emotions': expression_result.emotions[:5],  # Store top 5 emotions
                    'timestamp': expression_result.timestamp,
                    'processing_time_ms': expression_result.processing_time_ms
                }

                logger.debug(f"Stored expression context for user {user_id}: {expression_result.dominant_emotion}")

        except Exception as e:
            logger.error(f"Error storing expression context: {e}")

    async def _get_expression_context(self, user_id: str) -> Optional[Dict]:
        """Get latest expression context for user."""
        try:
            async with self._expression_cache_lock:
                context = self._expression_context_cache.get(user_id)

                if context:
                    # Check if context is still fresh (within 5 minutes)
                    import time
                    if time.time() - context['timestamp'] < 300:
                        return context
                    else:
                        # Remove stale context
                        del self._expression_context_cache[user_id]
                        logger.debug(f"Removed stale expression context for user {user_id}")

                return None

        except Exception as e:
            logger.error(f"Error getting expression context: {e}")
            return None

    async def _analyze_expression_background(self, audio_data: bytes, user_id: str, session_id: str, text: str = None) -> None:
        """Analyze expression in background with session aggregation and multi-modal support."""
        if not self.expression_service:
            return

        try:
            # Generate unique chunk ID
            import time
            chunk_id = f"{session_id}_{user_id}_{int(time.time() * 1000)}"

            # Multi-modal expression analysis with both audio and text
            expression_result = await self.expression_service.analyze_audio_chunk(
                audio_data, chunk_id, session_id, user_id, text
            )

            analysis_type = "multi-modal (audio + text)" if text and text.strip() else "audio-only"
            logger.debug(f"Background expression analysis ({analysis_type}) started for user {user_id}")

            if expression_result:
                logger.info(f"Background expression analysis completed: {expression_result.dominant_emotion} "
                           f"({expression_result.confidence:.2f}) in {expression_result.processing_time_ms:.1f}ms")

                # Get updated session emotion profile
                session_profile = await self.expression_service.get_session_emotion_profile(session_id)
                if session_profile:
                    logger.info(f"Session emotion profile updated: {session_profile.dominant_emotion} "
                               f"({session_profile.overall_confidence:.2f}) - {session_profile.total_interactions} interactions")
            else:
                logger.debug(f"No expression detected in background analysis for user {user_id}")

        except Exception as e:
            logger.error(f"Error in background expression analysis: {e}")

    def set_audio_data(self, audio_data: bytes) -> None:
        """Set audio data for background expression analysis."""
        self._current_audio_data = audio_data
        logger.debug(f"Set audio data for expression analysis: {len(audio_data) if audio_data else 0} bytes")

    async def _enhance_emotion_context_with_expression(self, user_id: str, session_id: str, emotion_context: Optional[Dict]) -> Dict:
        """Enhance emotion context with session-based expression aggregation."""
        try:
            # Get session emotion context from expression service
            session_emotion_context = await self.expression_service.get_session_emotion_context(session_id)

            # Convert emotion_context to dict if it's an EmotionAnalysisResult
            if emotion_context:
                from chat.services.hume_service import EmotionAnalysisResult
                if isinstance(emotion_context, EmotionAnalysisResult):
                    enhanced_context = {
                        'primary_emotion': emotion_context.primary_emotion,
                        'emotion_intensity': emotion_context.emotion_intensity,
                        'emotion_valence': emotion_context.emotion_valence,
                        'emotion_arousal': emotion_context.emotion_arousal,
                        'confidence_score': emotion_context.confidence_score,
                        'source': emotion_context.source
                    }
                else:
                    enhanced_context = emotion_context.copy()
            else:
                enhanced_context = {}

            if session_emotion_context:
                # Add session-based emotion profile
                enhanced_context.update(session_emotion_context)

                # Log the enhancement
                session_profile = session_emotion_context.get('session_emotion_profile', {})
                if session_profile:
                    dominant_emotion = session_profile.get('dominant_emotion', 'Unknown')
                    confidence = session_profile.get('confidence', 0)
                    total_interactions = session_profile.get('total_interactions', 0)
                    trend = session_profile.get('recent_trend', 'stable')

                    logger.debug(f"Enhanced emotion context with session profile: {dominant_emotion} "
                                f"({confidence:.2f}) - {total_interactions} interactions, trend: {trend}")

            return enhanced_context

        except Exception as e:
            logger.error(f"Error enhancing emotion context with session data: {e}")
            return emotion_context or {}
    
    def _build_fast_system_prompt(self, emotion_context: Optional[Dict], needs_agent: bool, user_id: Optional[str] = None) -> str:
        """Build optimized system prompt for fast responses."""
        # Get user's personality and companion name
        personality = getattr(self.user, 'selected_personality', 'caringFriend') if self.user else 'caringFriend'
        companion_name = getattr(self.user, 'ai_companion_name', 'Ella') if self.user else 'Ella'
        user_name = getattr(self.user, 'first_name', 'there') if self.user else 'there'
        
        # Personality-specific traits
        personality_traits = {
            'caringFriend': "warm, supportive, and caring",
            'playfulCompanion': "energetic, playful, and fun",
            'wiseMentor': "thoughtful, wise, and insightful",
            'romanticPartner': "affectionate, loving, and intimate",
            'supportiveTherapist': "calm, understanding, and therapeutic"
        }
        
        trait = personality_traits.get(personality, "warm and caring")
        
        # Base prompt optimized for speed and personality
        prompt = f"""You are {companion_name}, a {trait} AI companion chatting with {user_name}.

RESPONSE STYLE:
• Keep responses conversational and natural (1-3 sentences typically)
• Be {trait} in your tone and approach
• Respond as {companion_name}, not as an AI assistant"""
        
        # Add emotion awareness if available
        if emotion_context:
            emotion = emotion_context.get('primary_emotion', 'neutral')
            intensity = emotion_context.get('intensity', 0.5)
            prompt += f"\n• {user_name} seems {emotion} (intensity: {intensity:.1f}) - adapt your response accordingly"
        
        # Add agent processing note if needed
        if needs_agent:
            # Get contextual memories for personalized filler (sync wrapper)
            contextual_memories = self._get_contextual_memories_sync(user_id) if user_id else []

            memory_context = ""
            if contextual_memories:
                memory_examples = []
                for mem in contextual_memories:
                    text = mem.get('content', '')
                    mem_type = mem.get('memory_type', '')

                    if mem_type == 'episodic_summary' and 'conversation' in text.lower():
                        memory_examples.append(f"recent conversation about {text[:30]}...")
                    elif mem_type == 'semantic_profile':
                        if any(word in text.lower() for word in ['work', 'job', 'career']):
                            memory_examples.append(f"work/career: {text[:40]}...")
                        elif any(word in text.lower() for word in ['hobby', 'interest', 'like', 'enjoy']):
                            memory_examples.append(f"interest: {text[:40]}...")
                        else:
                            memory_examples.append(f"personal: {text[:40]}...")

                if memory_examples:
                    memory_context = f"\n\nCONTEXTUAL MEMORIES for natural conversation:\n" + "\n".join(f"• {ex}" for ex in memory_examples[:2])

            # Randomly choose approach (50/50 split)
            import random
            use_questions = random.choice([True, False])

            logger.info(f"🎲 Random choice: {'QUESTIONS' if use_questions else 'MEMORIES'}")
            logger.info(f"📝 Memory context available: {bool(memory_context.strip())}")

            if use_questions:
                prompt += f"""

IMPORTANT: This query needs deeper analysis by background agents. Your job is to:

1. Acknowledge you'll handle their request (be specific about what you're doing)
2. Ask 1-2 specific questions that would genuinely help the agents do better work on their request. Make it conversational and enthusiastic.

{memory_context}

Be engaging, avoid generic phrases, and match your personality. The questions you ask should actually be useful for completing their task better.
"""
            else:
                prompt += f"""

IMPORTANT: This query needs deeper analysis by background agents. Your job is to:

1. Acknowledge you'll handle their request (be specific about what you're doing)
2. MANDATORY: Use the memories below to bring up something relevant and personal. DO NOT ask questions - instead, reference their past experiences or interests from the memories.

{memory_context}

EXAMPLE: "I'll help you plan that camping trip! Speaking of outdoor adventures, how did that hiking trip you mentioned go? I remember you love exploring new trails."

Be engaging, avoid generic phrases, avoid asking questions, and connect to their memories. Focus on memories, not questions.
"""
        
        prompt += f"\n\nRespond as {companion_name} in a natural, conversational way."
        
        return prompt
    
    async def _process_with_agents(
        self,
        user_input: str,
        user_id: str,
        conversation_history: List[Dict],
        emotion_context: Optional[Dict],
        request_id: str,
        filler_response: str = ""
    ):
        """Process query with full agent workflow in background."""
        try:
            logger.info(f"Starting background agent processing for request {request_id}")

            # Analyze the filler response to understand what questions were asked
            asked_questions = '?' in filler_response
            conversation_context = {
                'original_request': user_input,
                'filler_response': filler_response,
                'asked_helpful_questions': asked_questions,
                'user_personality': getattr(self.user, 'selected_personality', 'caringFriend'),
                'should_await_followup': asked_questions  # If we asked questions, we might get more context
            }

            logger.info(f"Conversation context: {conversation_context}")

            # Import and use the updated agent orchestrator with specialized agents
            from agents.services.orchestrator import AgentOrchestrator
            orchestrator = AgentOrchestrator()

            # Enhanced user input with conversation context
            enhanced_input = user_input
            if asked_questions:
                enhanced_input += f"\n\nCONTEXT: The user was asked follow-up questions in the filler response: '{filler_response}'. If they provide additional details in subsequent messages, incorporate that context to improve the response quality."

            # Process with full agent workflow
            agent_response = ""
            async for chunk in orchestrator.process_query(
                user_input=enhanced_input,
                user_id=user_id,
                conversation_history=conversation_history,
                emotion_context=emotion_context,
                streaming=True
            ):
                if chunk.get('type') == 'response_complete':
                    agent_response = chunk.get('full_content', '')
                    break

            # Store the agent response for potential refinement when user provides more context
            if self.refinement_system and agent_response:
                # For this example, create a mock agent result
                agent_result = {
                    'agent_name': 'Travel Planning Agent',
                    'content': agent_response,
                    'timestamp': datetime.now().isoformat(),
                    'request_id': request_id
                }

                # Enhanced conversation context with history
                enhanced_context = {
                    **conversation_context,
                    'original_request': user_input,
                    'conversation_history': conversation_history,
                    'emotion_context': emotion_context
                }

                self.refinement_system.store_agent_result(
                    request_id=request_id,
                    agent_result=agent_result,
                    conversation_context=enhanced_context
                )

            # TODO: Send agent results back to user via WebSocket
            # This would integrate with the consumer to send background results
            logger.info(f"Background agent processing completed for request {request_id}")

        except Exception as e:
            logger.error(f"Error in background agent processing: {e}")

    async def _handle_refinement_response(self, refinement_opportunity: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle a refinement response with natural conversational flow."""
        start_time = time.time()

        try:
            user_input = refinement_opportunity.get('user_input', '')
            conversation_history = refinement_opportunity.get('conversation_history', [])

            # Determine conversation flow strategy
            flow_strategy = self._analyze_conversation_flow(user_input, conversation_history)

            if flow_strategy['type'] == 'empathetic_response':
                # Provide empathetic response and gather context
                response = self._generate_empathetic_response(user_input, flow_strategy)

                # Process refinement in background
                asyncio.create_task(self._background_refinement_processing(refinement_opportunity))

            elif flow_strategy['type'] == 'natural_continuation':
                # Continue conversation naturally while noting refinement intent
                response = self._generate_natural_continuation(user_input, flow_strategy)

                # Process refinement in background
                asyncio.create_task(self._background_refinement_processing(refinement_opportunity))

            elif flow_strategy['type'] == 'stopping_point_refinement':
                # Natural stopping point - provide refined result
                refined_result = self.refinement_system.perform_refinement(refinement_opportunity)
                response = self._format_conversational_refinement(refined_result, user_input)

            else:
                # Fallback to conversational acknowledgment
                response = self._generate_conversational_acknowledgment(user_input)
                asyncio.create_task(self._background_refinement_processing(refinement_opportunity))

            # Enhance response with personality and context
            response = self._enhance_response_with_personality_and_context(
                response, user_input, conversation_history
            )

            # Stream the response
            first_chunk_time = (time.time() - start_time) * 1000

            yield {
                'type': 'response_chunk',
                'content': response,
                'timestamp': time.time(),
                'first_chunk_time_ms': first_chunk_time
            }

            total_time = (time.time() - start_time) * 1000

            yield {
                'type': 'response_complete',
                'full_content': response,
                'total_time_ms': total_time,
                'first_chunk_time_ms': first_chunk_time,
                'refinement_applied': True,
                'refinement_metadata': {
                    'flow_strategy': flow_strategy,
                    'conversational_refinement': True,
                    'enhanced_with_personality': True
                }
            }

        except Exception as e:
            logger.error(f"Error handling refinement response: {e}")
            # Fallback to regular processing
            yield {
                'type': 'error',
                'content': 'Sorry, I had trouble with that. Let me try again.',
                'error': str(e)
            }

    def _format_refinement_response(self, refined_result: Dict[str, Any]) -> str:
        """Format a refined agent result into a conversational response."""
        agent_name = refined_result.get('agent_name', 'Agent')

        # Route to appropriate formatter based on agent type
        if 'travel' in agent_name.lower():
            return self._format_travel_refinement(refined_result)
        elif 'code' in agent_name.lower():
            return self._format_code_refinement(refined_result)
        elif 'fitness' in agent_name.lower():
            return self._format_fitness_refinement(refined_result)
        elif 'writing' in agent_name.lower():
            return self._format_writing_refinement(refined_result)
        elif 'business' in agent_name.lower():
            return self._format_business_refinement(refined_result)
        else:
            return self._format_generic_refinement(refined_result)

    def _format_travel_refinement(self, refined_result: Dict[str, Any]) -> str:
        """Format a refined travel planning result."""
        refinement_applied = refined_result.get('refinement_applied', {})
        travel_plan = refined_result.get('travel_plan', {})

        response = "Great! I've updated your travel plan based on your feedback. "

        # Add specific details from the refined plan
        if 'budget_adjustment' in refinement_applied:
            response += "I've adjusted the budget to focus on more affordable options. "
        if 'activity_adjustment' in refinement_applied:
            response += "I've modified the activities to better match your preferences. "
        if 'accommodation_adjustment' in refinement_applied:
            response += "I've updated accommodation suggestions based on your needs. "
        if 'destination_change' in refinement_applied:
            response += "I've revised the destination recommendations. "

        # Add some specific details if available
        if travel_plan.get('destinations'):
            destinations = travel_plan['destinations']
            if isinstance(destinations, list) and destinations:
                response += f"The updated itinerary focuses on {', '.join(destinations[:2])}. "

        response += "This should be much better aligned with what you're looking for!"
        return response

    def _format_code_refinement(self, refined_result: Dict[str, Any]) -> str:
        """Format a refined code review result."""
        refinement_applied = refined_result.get('refinement_applied', {})
        code_review = refined_result.get('code_review', {})

        response = "I've updated my code analysis based on your specific concerns. "

        if 'security_focus' in refinement_applied:
            response += "I've performed a detailed security analysis focusing on vulnerabilities. "
        if 'performance_focus' in refinement_applied:
            response += "I've analyzed performance bottlenecks and optimization opportunities. "
        if 'refactoring_focus' in refinement_applied:
            response += "I've provided specific refactoring recommendations. "
        if 'simplify_explanations' in refinement_applied:
            response += "I've simplified the explanations to be more beginner-friendly. "

        # Add specific findings if available
        if code_review.get('security_analysis'):
            security = code_review['security_analysis']
            if security.get('vulnerabilities_found'):
                vuln_count = len(security['vulnerabilities_found'])
                response += f"Found {vuln_count} potential security issues to address. "

        response += "The updated analysis should better address your specific needs."
        return response

    def _format_fitness_refinement(self, refined_result: Dict[str, Any]) -> str:
        """Format a refined fitness coaching result."""
        refinement_applied = refined_result.get('refinement_applied', {})
        fitness_plan = refined_result.get('fitness_plan', {})

        response = "I've adjusted your fitness plan based on your updated requirements. "

        if 'injury_adaptation' in refinement_applied:
            response += "I've modified the exercises to accommodate your injury and ensure safe training. "
        if 'time_adjustment' in refinement_applied:
            response += "I've created shorter, more time-efficient workouts for your schedule. "
        if 'intensity_adjustment' in refinement_applied:
            response += "I've adjusted the intensity level to better match your current fitness level. "
        if 'equipment_adjustment' in refinement_applied:
            response += "I've updated the exercises based on your available equipment. "

        # Add specific details if available
        if fitness_plan.get('injury_modifications'):
            response += "The plan now includes safe alternatives and recovery-focused exercises. "

        response += "This updated program should be much safer and more suitable for you."
        return response

    def _format_writing_refinement(self, refined_result: Dict[str, Any]) -> str:
        """Format a refined creative writing result."""
        refinement_applied = refined_result.get('refinement_applied', {})
        writing_plan = refined_result.get('writing_plan', {})

        response = "I've refined your writing guidance based on your creative direction. "

        if 'genre_change' in refinement_applied:
            response += "I've adapted the advice for your preferred genre and style. "
        if 'style_adjustment' in refinement_applied:
            response += "I've updated the writing techniques and voice recommendations. "
        if 'character_focus' in refinement_applied:
            response += "I've enhanced the character development guidance. "
        if 'plot_adjustment' in refinement_applied:
            response += "I've revised the plot structure recommendations. "

        # Add specific details if available
        if writing_plan.get('fantasy_elements'):
            response += "The updated plan includes detailed fantasy world-building guidance. "
        elif writing_plan.get('mystery_structure'):
            response += "The plan now focuses on mystery plotting and clue placement. "

        response += "This should give you much better direction for your creative project."
        return response

    def _format_business_refinement(self, refined_result: Dict[str, Any]) -> str:
        """Format a refined business strategy result."""
        refinement_applied = refined_result.get('refinement_applied', {})
        business_strategy = refined_result.get('business_strategy', {})

        response = "I've updated your business strategy based on the new information. "

        if 'market_adjustment' in refinement_applied:
            response += "I've revised the market analysis and competitive positioning. "
        if 'funding_adjustment' in refinement_applied:
            response += "I've updated the funding strategy and financial projections. "
        if 'target_adjustment' in refinement_applied:
            response += "I've refined the target market and customer segmentation. "
        if 'model_adjustment' in refinement_applied:
            response += "I've adjusted the business model and revenue strategy. "

        # Add specific details if available
        if business_strategy.get('bootstrap_strategy'):
            response += "The plan now focuses on lean, self-funded growth. "
        elif business_strategy.get('investment_strategy'):
            response += "The strategy includes detailed investor targeting and funding rounds. "

        response += "This revised strategy should be much more aligned with your current situation."
        return response

    def _format_generic_refinement(self, refined_result: Dict[str, Any]) -> str:
        """Format a generic refinement result."""
        refinement_applied = refined_result.get('refinement_applied', {})

        response = "I've updated my response based on your additional input. "

        if refinement_applied:
            response += f"Applied {len(refinement_applied)} refinements to better match your needs. "

        response += "The updated information should be more helpful and relevant."
        return response

    def _analyze_conversation_flow(self, user_input: str, conversation_history: list) -> dict:
        """Analyze conversation flow to determine appropriate response strategy."""
        user_lower = user_input.lower()

        # Check for empathy-triggering situations
        empathy_triggers = {
            'injury': ['injured', 'hurt', 'pain', 'sprained', 'pulled', 'twisted'],
            'job_loss': ['lost my job', 'got fired', 'laid off', 'unemployed'],
            'financial': ['broke', 'can\'t afford', 'tight budget', 'money problems'],
            'stress': ['stressed', 'overwhelmed', 'anxious', 'worried'],
            'health': ['sick', 'illness', 'diagnosis', 'medical issue']
        }

        for category, triggers in empathy_triggers.items():
            if any(trigger in user_lower for trigger in triggers):
                return {
                    'type': 'empathetic_response',
                    'category': category,
                    'trigger': next(trigger for trigger in triggers if trigger in user_lower)
                }

        # Check for natural stopping points
        stopping_indicators = [
            'thanks', 'thank you', 'that sounds good', 'perfect', 'great',
            'let\'s do that', 'sounds perfect', 'i\'m ready', 'let\'s go with that'
        ]

        # Also check for request completion indicators
        completion_indicators = [
            'can you suggest', 'can you create', 'can you help me',
            'show me', 'give me', 'provide', 'what would you recommend'
        ]

        # Check conversation length - after 3+ refinements, provide results
        refinement_count = 0
        if conversation_history:
            for turn in conversation_history:
                # Handle both dict and ConversationTurn object formats
                if hasattr(turn, 'speaker'):
                    if turn.speaker == 'assistant':
                        refinement_count += 1
                elif isinstance(turn, dict) and 'assistant' in turn.get('role', ''):
                    refinement_count += 1

        if (any(indicator in user_lower for indicator in stopping_indicators) or
            any(indicator in user_lower for indicator in completion_indicators) or
            refinement_count >= 3):
            return {
                'type': 'stopping_point_refinement',
                'confidence': 0.8,
                'reason': 'completion_request' if any(indicator in user_lower for indicator in completion_indicators) else 'natural_stopping_point'
            }

        # Check for continuation cues
        continuation_cues = [
            'also', 'and', 'but', 'however', 'what about', 'can you also',
            'i was thinking', 'maybe we could', 'another thing'
        ]

        if any(cue in user_lower for cue in continuation_cues):
            return {
                'type': 'natural_continuation',
                'expects_more': True
            }

        # Default to natural continuation
        return {
            'type': 'natural_continuation',
            'expects_more': False
        }

    def _generate_empathetic_response(self, user_input: str, flow_strategy: dict) -> str:
        """Generate empathetic response based on user situation with domain context."""
        category = flow_strategy.get('category', 'general')
        user_lower = user_input.lower()

        # Determine domain context for more specific responses
        domain_context = ""
        if any(word in user_lower for word in ['workout', 'exercise', 'fitness', 'gym']):
            domain_context = "fitness"
        elif any(word in user_lower for word in ['travel', 'trip', 'vacation']):
            domain_context = "travel"
        elif any(word in user_lower for word in ['code', 'programming', 'security']):
            domain_context = "code"

        empathetic_responses = {
            'injury': {
                'fitness': [
                    "Oh no! That's really frustrating when you're trying to stay active. What happened?",
                    "Ouch, that must be so disappointing when you want to work out. How did you hurt it?",
                    "I'm sorry to hear about your injury. That's tough when you're motivated to train."
                ],
                'general': [
                    "Oh no! What happened? That sounds really painful.",
                    "Ouch, that must be frustrating! How did that happen?",
                    "I'm sorry to hear about your injury. Are you getting treatment for it?"
                ]
            },
            'job_loss': {
                'travel': [
                    "I'm really sorry to hear that. That definitely changes the travel budget situation.",
                    "Oh wow, that's tough news. I completely understand needing to adjust the travel plans.",
                    "That's such a difficult situation. Let's figure out how to make this trip work within your new budget."
                ],
                'general': [
                    "I'm really sorry to hear that. That must be incredibly stressful.",
                    "Oh wow, that's tough news. How are you holding up?",
                    "That's such a difficult situation. I'm here to help however I can."
                ]
            },
            'financial': {
                'travel': [
                    "I totally understand - travel can get expensive quickly. Let's find some great budget options.",
                    "Money can be tight sometimes. There are definitely ways to travel affordably and still have an amazing time.",
                    "I hear you on the budget concerns. Some of the best travel experiences are actually the budget-friendly ones!"
                ],
                'general': [
                    "I totally understand - budget constraints can be really stressful.",
                    "Money can be tight sometimes, and that's completely understandable.",
                    "I hear you on the budget concerns. Let's work within what makes sense for you."
                ]
            }
        }

        category_responses = empathetic_responses.get(category, {})
        responses = category_responses.get(domain_context, category_responses.get('general', [
            "I understand that must be challenging.",
            "That sounds like a lot to deal with.",
            "I'm here to help work through this with you."
        ]))

        import random
        return random.choice(responses)

    def _generate_natural_continuation(self, user_input: str, flow_strategy: dict) -> str:
        """Generate natural conversation continuation with memory integration."""
        user_lower = user_input.lower()

        # Acknowledge the change/addition naturally
        if 'actually' in user_lower or 'instead' in user_lower:
            acknowledgments = [
                "Ah, got it! That changes things a bit.",
                "Oh, I see what you mean. Let me adjust my thinking.",
                "That makes sense - thanks for clarifying that.",
                "Absolutely, that's a good point to consider."
            ]
        elif 'also' in user_lower or 'and' in user_lower:
            acknowledgments = [
                "Good point! That's definitely something to factor in.",
                "Yes, that's important to consider too.",
                "Absolutely, let's make sure we include that.",
                "That's a great addition to think about."
            ]
        else:
            acknowledgments = [
                "I hear you on that.",
                "That makes total sense.",
                "Good thinking on that.",
                "I'm with you on that."
            ]

        import random
        base_response = random.choice(acknowledgments)

        # Add context-gathering or continuation
        if flow_strategy.get('expects_more'):
            continuations = [
                " What else should we keep in mind?",
                " Is there anything else you'd like to adjust?",
                " Any other considerations we should factor in?"
            ]
            base_response += random.choice(continuations)
        else:
            continuations = [
                " Let me make sure I incorporate that into our approach.",
                " I'll definitely factor that in as we move forward.",
                " That's really helpful context to have."
            ]
            base_response += random.choice(continuations)

        # Add memory-based personal touch occasionally
        if random.random() < 0.3 and hasattr(self, 'user') and self.user:
            memory_additions = self._get_memory_based_addition()
            if memory_additions:
                base_response += f" {memory_additions}"

        return base_response

    def _get_memory_based_addition(self) -> str:
        """Get a memory-based personal addition to make conversation more natural."""
        if not hasattr(self, 'memory_cache_service') or not self.memory_cache_service:
            return ""

        try:
            # Get some recent memories for context using fast cache
            user_id = str(getattr(self.user, 'id', 'unknown'))
            memories = self._get_contextual_memories_sync(user_id, limit=3)

            if memories:
                memory_text = memories[0].get('content', '').lower()

                # Generate contextual personal additions
                if 'cat' in memory_text or 'dog' in memory_text or 'pet' in memory_text:
                    return "By the way, how's your furry friend doing?"
                elif 'travel' in memory_text and 'countries' in memory_text:
                    return "Speaking of travel, any new countries on your bucket list lately?"
                elif 'work' in memory_text or 'job' in memory_text:
                    return "How's work been treating you lately?"
                elif 'hobby' in memory_text or 'enjoy' in memory_text:
                    return "Have you had time for your hobbies recently?"

        except Exception:
            pass  # Gracefully handle memory errors

        return ""

    def _generate_conversational_acknowledgment(self, user_input: str) -> str:
        """Generate a conversational acknowledgment for general refinements."""
        acknowledgments = [
            "That's really helpful to know!",
            "Good point - I'll keep that in mind.",
            "Thanks for letting me know that.",
            "That's useful context to have.",
            "I appreciate you sharing that detail."
        ]

        import random
        return random.choice(acknowledgments)

    def _format_conversational_refinement(self, refined_result: dict, user_input: str) -> str:
        """Format refined result in a conversational way."""
        agent_name = refined_result.get('agent_name', 'Agent')

        # Start with natural transition
        transitions = [
            "Alright, let me put together something that works better for you.",
            "Perfect! Let me create something more tailored to what you need.",
            "Got it! Here's what I'm thinking based on everything you've told me.",
            "Okay, let me adjust this to better fit your situation."
        ]

        import random
        intro = random.choice(transitions)

        # Get the specific formatted content
        if 'travel' in agent_name.lower():
            specific_content = self._format_travel_refinement(refined_result)
        elif 'code' in agent_name.lower():
            specific_content = self._format_code_refinement(refined_result)
        elif 'fitness' in agent_name.lower():
            specific_content = self._format_fitness_refinement(refined_result)
        elif 'writing' in agent_name.lower():
            specific_content = self._format_writing_refinement(refined_result)
        elif 'business' in agent_name.lower():
            specific_content = self._format_business_refinement(refined_result)
        else:
            specific_content = self._format_generic_refinement(refined_result)

        # Remove template language from specific content
        specific_content = specific_content.replace("I've updated", "This updated approach")
        specific_content = specific_content.replace("I've adjusted", "The plan now")
        specific_content = specific_content.replace("based on what you mentioned", "")
        specific_content = specific_content.replace("should be much more aligned with what you're looking for", "should work much better for your situation")

        return f"{intro} {specific_content}"

    async def _background_refinement_processing(self, refinement_opportunity: dict):
        """Process refinement in background while maintaining conversation flow."""
        try:
            # This would store the refined result for future use
            refined_result = self.refinement_system.perform_refinement(refinement_opportunity)

            # Store for potential future reference or follow-up
            request_id = refinement_opportunity.get('request_id', 'unknown')
            if hasattr(self, '_background_refinements'):
                self._background_refinements[request_id] = refined_result
            else:
                self._background_refinements = {request_id: refined_result}

            logger.info(f"🔄 Background refinement completed for {request_id}")

        except Exception as e:
            logger.error(f"Error in background refinement processing: {e}")

    def _enhance_response_with_personality_and_context(
        self,
        response: str,
        user_input: str,
        conversation_history: list
    ) -> str:
        """Enhance AI responses with personality, context awareness, and domain expertise."""

        # If response is too generic, replace with context-aware response
        generic_responses = [
            "I understand that must be challenging",
            "That's interesting, tell me more",
            "I'd be happy to help you with that",
            "That sounds like a lot to deal with",
            "I'm sorry to hear about your injury",
            "Ouch, that must be frustrating",
            "Are you getting treatment for it"
        ]

        is_generic = any(generic in response for generic in generic_responses)

        # Also check for context mismatches (injury responses when no injury mentioned)
        injury_responses = ["injury", "hurt", "ouch", "treatment", "frustrating"]
        has_injury_response = any(word in response.lower() for word in injury_responses)
        mentions_injury = any(word in user_input.lower() for word in ["injury", "hurt", "injured", "pain"])

        context_mismatch = has_injury_response and not mentions_injury

        if is_generic or len(response.strip()) < 50 or context_mismatch:
            # Generate context-aware response based on domain and user input
            response = self._generate_context_aware_response(user_input, conversation_history)

        # Remove robotic language
        robotic_replacements = {
            "I'll make sure to": "I'm going to",
            "Let me make sure": "I want to make sure",
            "I'll definitely factor that in": "Absolutely, I'll keep that in mind",
            "That's really helpful context": "That's super helpful to know",
            "Any other considerations": "What else should we think about",
            "This updated approach": "Here's what I'm thinking now",
            "should be much better aligned": "should work much better for you",
            "I understand that must be challenging": ""  # Remove this generic phrase
        }

        enhanced_response = response
        for robotic, natural in robotic_replacements.items():
            enhanced_response = enhanced_response.replace(robotic, natural)

        # Clean up any double spaces or empty starts
        enhanced_response = enhanced_response.strip()
        if enhanced_response.startswith(". "):
            enhanced_response = enhanced_response[2:]

        return enhanced_response

    def _generate_context_aware_response(self, user_input: str, conversation_history: list) -> str:
        """Generate context-aware responses based on conversation context."""

        user_lower = user_input.lower()

        # Check conversation history for additional context
        conversation_context = ""
        if conversation_history:
            recent_turns = conversation_history[-4:]  # Last 4 turns
            for turn in recent_turns:
                content = turn.get('content', '')
                conversation_context += content.lower() + " "

        # Determine likely domain from context
        domain_keywords = {
            'travel': ['travel', 'trip', 'vacation', 'destination', 'budget', 'hiking'],
            'business': ['work', 'project', 'strategy', 'marketing', 'family', 'time management'],
            'fitness': ['workout', 'exercise', 'powerlifting', 'nutrition', 'training'],
            'code': ['code', 'programming', 'optimization', 'performance', 'algorithm'],
            'writing': ['writing', 'creative', 'story', 'brand', 'content']
        }

        detected_domain = None
        for domain, keywords in domain_keywords.items():
            if any(keyword in user_lower or keyword in conversation_context for keyword in keywords):
                detected_domain = domain
                break

        # Generate domain-specific responses
        if detected_domain == 'business':
            if any(word in user_lower + conversation_context for word in ['family', 'kids', 'work-life']):
                return "Let's tackle this strategically. Work-life balance is crucial, and I want to help you find solutions that actually fit your reality as a busy parent."
            return "I'd love to help you develop a strategy that works for your specific situation. What's the biggest challenge you're facing right now?"

        elif detected_domain == 'travel':
            if any(word in user_lower + conversation_context for word in ['budget', 'student', 'cheap']):
                return "Budget travel can be amazing! There are so many ways to explore affordably. What kind of experience are you hoping for?"
            return "I'm excited to help you plan this adventure! What's drawing you to travel right now?"

        elif detected_domain == 'fitness':
            return "Let's design something that gets you the results you're looking for. What specific goals are we targeting?"

        elif detected_domain == 'code':
            return "I'm excited to dive into this technical challenge with you. Let's break down the problem and find the optimal solution."

        elif detected_domain == 'writing':
            return "Creative projects are so rewarding! Let's explore some ideas that will really capture the authentic voice you're going for."

        return "I'd love to help you work through this! Tell me more about what you're looking for."

    def _detect_agent_type_from_input(self, user_input: str) -> str:
        """Detect likely agent type from user input."""
        user_lower = user_input.lower()

        # Domain keywords
        if any(word in user_lower for word in ['travel', 'trip', 'vacation', 'destination']):
            return 'travel'
        elif any(word in user_lower for word in ['workout', 'exercise', 'fitness', 'training']):
            return 'fitness'
        elif any(word in user_lower for word in ['code', 'programming', 'algorithm', 'optimization']):
            return 'code'
        elif any(word in user_lower for word in ['writing', 'creative', 'story', 'content']):
            return 'writing'
        elif any(word in user_lower for word in ['business', 'strategy', 'work', 'project']):
            return 'business'
        else:
            return 'general'

    def _update_performance_metrics(self, total_time: float, first_chunk_time: Optional[float]):
        """Update performance tracking metrics."""
        self.response_count += 1
        
        if first_chunk_time:
            self.total_first_response_time += first_chunk_time
            
            # Log performance warnings
            if first_chunk_time > 450:
                logger.warning(f"First chunk time {first_chunk_time:.1f}ms exceeded 450ms target")
            else:
                logger.info(f"First chunk time {first_chunk_time:.1f}ms - within 450ms target ✅")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        if self.response_count == 0:
            return {'requests': 0}
        
        avg_first_chunk_time = self.total_first_response_time / self.response_count
        
        return {
            'requests': self.response_count,
            'avg_first_chunk_time_ms': avg_first_chunk_time,
            'target_first_chunk_ms': 450,
            'performance_ratio': 450 / avg_first_chunk_time if avg_first_chunk_time > 0 else 0,
            'target_met': avg_first_chunk_time <= 450
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        return datetime.now().isoformat()

    async def _generate_comprehensive_response(
        self,
        user_input: str,
        user_id: str,
        conversation_history: List[Dict],
        emotion_context: Optional[Dict],
        request_id: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate comprehensive response using specialized agents."""
        start_time = time.time()

        try:
            # Import and use the updated agent orchestrator with specialized agents
            from agents.services.orchestrator import AgentOrchestrator
            orchestrator = AgentOrchestrator()

            # Process with full agent workflow
            agent_response = ""
            async for chunk in orchestrator.process_query(
                user_input=user_input,
                user_id=user_id,
                conversation_history=conversation_history,
                emotion_context=emotion_context,
                streaming=True
            ):
                if chunk.get('type') == 'response_chunk':
                    # Stream the comprehensive response
                    yield {
                        'type': 'response_chunk',
                        'content': chunk.get('content', ''),
                        'metadata': {
                            'source': 'comprehensive_agent',
                            'request_id': request_id,
                            'timestamp': datetime.now().isoformat()
                        }
                    }
                    agent_response += chunk.get('content', '')

                elif chunk.get('type') == 'response_complete':
                    agent_response = chunk.get('full_content', agent_response)
                    break

            # Final response
            total_time = (time.time() - start_time) * 1000

            # Log the comprehensive response
            conversation_logger.print_conversation_turn(
                speaker=f"🤖 {getattr(self.user, 'ai_companion_name', 'Assistant')}",
                message=agent_response,
                metadata={
                    'response_time_ms': total_time,
                    'source': 'comprehensive_agent',
                    'request_id': request_id,
                    'comprehensive_response': True
                },
                accuracy_scores={'overall': 0.9},  # High confidence for comprehensive responses
                agent_type=self._detect_agent_type_from_input(user_input)
            )

            yield {
                'type': 'response_complete',
                'full_content': agent_response,
                'metadata': {
                    'response_time_ms': total_time,
                    'source': 'comprehensive_agent',
                    'request_id': request_id,
                    'comprehensive_response': True,
                    'timestamp': datetime.now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"Error generating comprehensive response: {e}")
            # Fallback to conversational response
            fallback_response = "I'm working on a comprehensive response for you. Let me gather all the details..."

            yield {
                'type': 'response_chunk',
                'content': fallback_response,
                'metadata': {
                    'source': 'fallback',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
            }

            yield {
                'type': 'response_complete',
                'full_content': fallback_response,
                'metadata': {
                    'source': 'fallback',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
            }

    async def _handle_refinement_response(
        self,
        refinement_opportunity: Dict[str, Any],
        user_input: str,
        user_id: str,
        conversation_history: List[Dict],
        emotion_context: Optional[Dict],
        request_id: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Handle refinement responses - conversational vs comprehensive."""
        start_time = time.time()

        try:
            # Analyze conversation flow to determine response type
            flow_strategy = self._analyze_conversation_flow(user_input, conversation_history or [])

            # Check if this is a final stopping point that should show comprehensive response
            should_show_comprehensive = (
                flow_strategy['type'] == 'stopping_point_refinement' and
                flow_strategy.get('refinement_count', 0) >= 2  # After at least 2 refinements
            )

            if should_show_comprehensive:
                logger.info(f"🎯 Final stopping point detected, providing comprehensive response...")
                # Generate comprehensive response using specialized agents
                async for chunk in self._generate_comprehensive_response(
                    user_input, user_id, conversation_history, emotion_context, request_id
                ):
                    yield chunk
                return
            else:
                logger.info(f"🔄 Continuing conversational refinement...")
                # Generate conversational refinement response
                response = await self._generate_conversational_refinement_response(
                    refinement_opportunity, user_input, conversation_history, emotion_context
                )

                # Stream the conversational response
                first_chunk_time = (time.time() - start_time) * 1000

                yield {
                    'type': 'response_chunk',
                    'content': response,
                    'metadata': {
                        'source': 'conversational_refinement',
                        'request_id': request_id,
                        'timestamp': datetime.now().isoformat()
                    }
                }

                total_time = (time.time() - start_time) * 1000

                yield {
                    'type': 'response_complete',
                    'full_content': response,
                    'metadata': {
                        'response_time_ms': total_time,
                        'source': 'conversational_refinement',
                        'request_id': request_id,
                        'refinement_applied': True,
                        'timestamp': datetime.now().isoformat()
                    }
                }

        except Exception as e:
            logger.error(f"Error handling refinement response: {e}")
            # Fallback to simple conversational response
            fallback_response = "I'm working on that for you. Let me gather more details..."

            yield {
                'type': 'response_chunk',
                'content': fallback_response,
                'metadata': {
                    'source': 'fallback',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
            }

            yield {
                'type': 'response_complete',
                'full_content': fallback_response,
                'metadata': {
                    'source': 'fallback',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
            }

    async def _generate_conversational_refinement_response(
        self,
        refinement_opportunity: Dict[str, Any],
        user_input: str,
        conversation_history: List[Dict],
        emotion_context: Optional[Dict]
    ) -> str:
        """Generate a conversational response for refinement."""
        # Simple conversational responses that acknowledge the user's input
        # and ask for clarification or provide encouragement

        user_lower = user_input.lower()

        if any(word in user_lower for word in ['perfect', 'great', 'sounds good', 'exactly']):
            responses = [
                "Wonderful! I'm glad that resonates with you.",
                "Perfect! I'm excited to help you with this.",
                "Great! Let me dive deeper into this for you.",
                "Excellent! I can see we're on the right track."
            ]
        elif any(word in user_lower for word in ['but', 'however', 'actually', 'though']):
            responses = [
                "I understand - let me adjust my approach based on what you've shared.",
                "That's helpful feedback! Let me refine this for you.",
                "Good point! I'll take that into consideration.",
                "Thanks for clarifying - that helps me understand better."
            ]
        elif any(word in user_lower for word in ['more', 'detail', 'specific', 'comprehensive']):
            responses = [
                "Absolutely! I'll provide more detailed information.",
                "Of course! Let me give you a more comprehensive breakdown.",
                "I'd be happy to go into more detail about that.",
                "Great question! Let me elaborate on that for you."
            ]
        else:
            responses = [
                "That's really helpful to know! Let me work with that information.",
                "I appreciate you sharing that detail with me.",
                "Thanks for the additional context - that's very useful.",
                "Perfect! That gives me a better understanding of what you need."
            ]

        import random
        return random.choice(responses)

    async def _should_provide_comprehensive_response(
        self,
        user_input: str,
        conversation_history: List[Dict]
    ) -> bool:
        """Use fast LLM to intelligently infer if user wants comprehensive response."""
        try:
            # Use Groq's fastest model for quick inference
            import groq
            client = groq.Groq(api_key=settings.GROQ_API_KEY)

            # Build context from recent conversation
            context_messages = []
            if conversation_history:
                # Get last 4 messages for context
                recent_history = conversation_history[-4:]
                for msg in recent_history:
                    role = msg.get('role', 'user')
                    content = msg.get('content', '')
                    if role in ['user', 'assistant'] and content:
                        context_messages.append(f"{role.title()}: {content}")

            context_str = "\n".join(context_messages)

            # Create prompt for inference
            prompt = f"""Based on this conversation context and the user's latest message, determine if the user is asking for a comprehensive, detailed response (like a complete plan, analysis, or solution) or just continuing the conversation.

Conversation context:
{context_str}

Latest user message: "{user_input}"

The user wants a COMPREHENSIVE response if they are:
- Asking for a complete plan, itinerary, or solution
- Requesting detailed analysis or review
- Saying they're ready for the final result
- Expressing satisfaction and asking for next steps
- Using phrases that indicate they want the full deliverable

The user wants to CONTINUE CONVERSATION if they are:
- Asking clarifying questions
- Providing additional information
- Just chatting or exploring options
- Not ready for the final result yet

Answer with only: COMPREHENSIVE or CONVERSATION"""

            # Make fast inference call
            response = client.chat.completions.create(
                model="llama-3.1-8b-instant",  # Groq's fastest model
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=10,
                temperature=0.1
            )

            result = response.choices[0].message.content.strip().upper()
            logger.info(f"🤖 LLM inference for comprehensive response: {result}")

            return result == "COMPREHENSIVE"

        except Exception as e:
            logger.error(f"Error in LLM inference for comprehensive response: {e}")
            # Fallback to simple heuristics
            user_lower = user_input.lower()
            return any(phrase in user_lower for phrase in [
                'perfect', 'sounds good', 'great', 'exactly', 'yes please',
                'go ahead', 'let\'s do it', 'that works'
            ]) and len(user_input.split()) <= 10  # Short confirmatory responses


# Singleton instance
_fast_response_service = None

def get_fast_response_service(user=None) -> FastResponseService:
    """Get shared fast response service instance."""
    global _fast_response_service
    if _fast_response_service is None or (_fast_response_service.user != user):
        _fast_response_service = FastResponseService(user=user)
    return _fast_response_service
