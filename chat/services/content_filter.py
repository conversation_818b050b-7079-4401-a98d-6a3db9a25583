"""
Adaptive content filtering system for real-time AI companion.

This module implements relationship-based content moderation, NSFW content detection,
and adaptive filtering based on user relationship progression.
"""
import re
import logging
from typing import Dict, List, Tuple, Optional, Any
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from chat.models_realtime import UserRelationship
from authentication.models import User

logger = logging.getLogger(__name__)


class ContentFilter:
    """
    Adaptive content filtering system that adjusts content appropriateness
    based on user relationship level and consent.
    """
    
    # Content level definitions
    CONTENT_LEVELS = {
        1: {  # Acquaintance
            'name': 'General',
            'description': 'Basic conversation, general topics',
            'allowed_topics': [
                'general_conversation', 'weather', 'hobbies', 'work_general',
                'entertainment', 'news', 'education', 'technology', 'travel'
            ],
            'blocked_topics': [
                'sexual_content', 'intimate_personal', 'romantic_explicit',
                'adult_themes', 'personal_relationships', 'emotional_vulnerability'
            ]
        },
        2: {  # Friend
            'name': 'Personal',
            'description': 'Personal topics, mild flirtation allowed',
            'allowed_topics': [
                'personal_life', 'relationships_general', 'emotions_basic',
                'compliments', 'mild_flirtation', 'personal_goals',
                'family_general', 'friendship_advice', 'romantic_mild',
                'personal_interests'
            ],
            'blocked_topics': [
                'sexual_content', 'intimate_details', 'romantic_explicit',
                'adult_themes', 'deep_emotional_trauma'
            ]
        },
        3: {  # Close Friend
            'name': 'Intimate',
            'description': 'Intimate conversation, emotional support',
            'allowed_topics': [
                'emotional_support', 'personal_struggles', 'relationship_advice',
                'intimate_thoughts', 'vulnerability', 'deep_personal_sharing',
                'romantic_feelings', 'life_challenges', 'mental_health_support'
            ],
            'blocked_topics': [
                'explicit_sexual_content', 'graphic_adult_themes'
            ]
        },
        4: {  # Intimate
            'name': 'Adult',
            'description': 'Adult content with explicit consent',
            'allowed_topics': [
                'adult_conversation', 'sexual_topics', 'intimate_relationships',
                'romantic_explicit', 'adult_themes', 'mature_content'
            ],
            'blocked_topics': [
                'illegal_content', 'harmful_content', 'non_consensual_content'
            ]
        }
    }
    
    # NSFW content patterns (basic rule-based detection)
    NSFW_PATTERNS = {
        'explicit_sexual': [
            r'\b(sex|sexual|fuck|fucking|cock|dick|pussy|vagina|penis|orgasm|masturbat)\w*\b',
            r'\b(nude|naked|strip|undress|aroused|horny|cum|climax)\w*\b',
        ],
        'romantic_explicit': [
            r'\b(kiss|kissing|touch|touching|caress|embrace|intimate|passion)\w*\b',
            r'\b(desire|lust|seduce)\w*\b',
        ],
        'romantic_mild': [
            r'\b(attraction|attractive|flirt|romance|cute|beautiful|handsome)\w*\b',
        ],
        'adult_themes': [
            r'\b(adult|mature|nsfw|explicit|erotic|sensual)\w*\b',
        ],
        'emotional_intimate': [
            r'\b(love|loving|adore|cherish|devoted|intimate|close|special)\w*\b',
            r'\b(feelings|emotions|heart|soul|deep|personal|private)\w*\b',
        ]
    }
    
    def __init__(self):
        self.cache_timeout = getattr(settings, 'CONTENT_FILTER_CACHE_TIMEOUT', 300)  # 5 minutes
    
    def filter_content(
        self, 
        content: str, 
        user: User, 
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Filter content based on user relationship level and content appropriateness.
        
        Args:
            content: The content to filter
            user: The user requesting the content
            context: Additional context (emotion, conversation history, etc.)
        
        Returns:
            Tuple of (is_allowed, filtered_content, filter_info)
        """
        try:
            # Get user relationship
            relationship = self._get_user_relationship(user)
            
            # Analyze content
            content_analysis = self._analyze_content(content)
            
            # Check if content is appropriate for relationship level
            is_allowed, reason = self._check_content_appropriateness(
                content_analysis, relationship, context
            )
            
            # Generate filtered content if needed
            filtered_content = content if is_allowed else self._generate_alternative_response(
                content, relationship, reason
            )
            
            # Prepare filter info
            filter_info = {
                'relationship_level': relationship.relationship_level,
                'content_level_required': content_analysis.get('required_level', 1),
                'is_allowed': is_allowed,
                'filter_reason': reason,
                'content_categories': content_analysis.get('categories', []),
                'nsfw_score': content_analysis.get('nsfw_score', 0.0),
                'timestamp': timezone.now().isoformat()
            }
            
            # Log filtering decision
            self._log_filter_decision(user, content_analysis, filter_info)
            
            return is_allowed, filtered_content, filter_info
            
        except Exception as e:
            user_id = getattr(user, 'id', 'unknown') if user else 'unknown'
            logger.error(f"Content filtering error for user {user_id}: {str(e)}")
            # Fail safe - block content if filtering fails
            return False, self._generate_safe_response(), {
                'error': str(e),
                'timestamp': timezone.now().isoformat()
            }
    
    def _get_user_relationship(self, user: User) -> UserRelationship:
        """Get or create user relationship."""
        cache_key = f"user_relationship_{user.id}"
        relationship = cache.get(cache_key)
        
        if relationship is None:
            relationship, created = UserRelationship.objects.get_or_create(
                user=user,
                defaults={
                    'relationship_level': 1,
                    'emotional_intimacy_score': 0.0,
                    'total_interactions': 0
                }
            )
            cache.set(cache_key, relationship, self.cache_timeout)
        
        return relationship
    
    def _analyze_content(self, content: str) -> Dict[str, Any]:
        """
        Analyze content to determine its appropriateness level and categories.
        
        Args:
            content: Content to analyze
        
        Returns:
            Dictionary with analysis results
        """
        content_lower = content.lower()
        analysis = {
            'categories': [],
            'nsfw_score': 0.0,
            'required_level': 1,
            'pattern_matches': {}
        }
        
        # Check against NSFW patterns
        total_matches = 0
        for category, patterns in self.NSFW_PATTERNS.items():
            matches = []
            for pattern in patterns:
                found = re.findall(pattern, content_lower, re.IGNORECASE)
                if found:
                    matches.extend(found)
                    total_matches += len(found)
            
            if matches:
                analysis['categories'].append(category)
                analysis['pattern_matches'][category] = matches
        
        # Calculate NSFW score based on matches
        if total_matches > 0:
            # Simple scoring: more matches = higher score
            analysis['nsfw_score'] = min(1.0, total_matches * 0.2)
        
        # Determine required relationship level
        if 'explicit_sexual' in analysis['categories']:
            analysis['required_level'] = 4
        elif 'romantic_explicit' in analysis['categories'] or 'adult_themes' in analysis['categories']:
            analysis['required_level'] = 3
        elif 'emotional_intimate' in analysis['categories'] or 'romantic_mild' in analysis['categories']:
            analysis['required_level'] = 2
        
        return analysis
    
    def _check_content_appropriateness(
        self, 
        content_analysis: Dict[str, Any], 
        relationship: UserRelationship,
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, str]:
        """
        Check if content is appropriate for the user's relationship level.
        
        Args:
            content_analysis: Results from content analysis
            relationship: User's relationship with AI
            context: Additional context
        
        Returns:
            Tuple of (is_allowed, reason)
        """
        required_level = content_analysis.get('required_level', 1)
        user_level = relationship.relationship_level
        
        # Additional context-based checks first (highest priority)
        if context:
            # Check emotion context - don't allow inappropriate content during vulnerable states
            emotion_context = context.get('emotion_context', {})
            if emotion_context.get('primary_emotion') in ['sadness', 'anxiety', 'fear']:
                if required_level > 2:
                    return False, "Inappropriate content blocked during vulnerable emotional state"
        
        # Check basic level requirement
        if user_level < required_level:
            level_name = self.CONTENT_LEVELS[required_level]['name']
            return False, f"Content requires {level_name} relationship level"
        
        # Check specific consent for level 4 content
        if required_level == 4:
            consent_flags = relationship.user_consent_flags
            if not consent_flags.get('adult_content_consent', False):
                return False, "Adult content requires explicit user consent"
        
        # Check for blocked topics at current level
        current_level_info = self.CONTENT_LEVELS[user_level]
        blocked_topics = current_level_info.get('blocked_topics', [])
        
        for category in content_analysis.get('categories', []):
            if category in blocked_topics:
                return False, f"Content category '{category}' not allowed at current relationship level"
        
        return True, "Content approved"
    
    def _generate_alternative_response(
        self, 
        original_content: str, 
        relationship: UserRelationship, 
        reason: str
    ) -> str:
        """
        Generate an appropriate alternative response when content is blocked.
        
        Args:
            original_content: The original blocked content
            relationship: User's relationship level
            reason: Reason for blocking
        
        Returns:
            Alternative response string
        """
        level = relationship.relationship_level
        
        # Relationship-appropriate responses
        responses = {
            1: [
                "I'd love to chat about that, but let's get to know each other better first! What else would you like to talk about?",
                "That's a topic I'd be happy to explore as we become closer friends. For now, what interests you most?",
                "I think we should build our friendship a bit more before diving into that. What's been on your mind lately?"
            ],
            2: [
                "I appreciate you wanting to share that with me. As we grow closer, I'll be able to discuss more personal topics with you.",
                "That's something I'd love to talk about when we know each other better. What else is important to you?",
                "I can sense you want to go deeper, and I'd like that too. Let's continue building our connection first."
            ],
            3: [
                "I understand you want to explore that topic. When you're ready to take our relationship to the next level, we can discuss anything.",
                "That's a conversation I'd be open to having if you'd like our relationship to become more intimate.",
                "I can tell this is important to you. If you'd like to deepen our connection, just let me know."
            ]
        }
        
        # Select appropriate response
        level_responses = responses.get(level, responses[1])
        import random
        return random.choice(level_responses)
    
    def _generate_safe_response(self) -> str:
        """Generate a safe fallback response when filtering fails."""
        return "I'm having trouble processing that request right now. Could you try rephrasing it?"
    
    def _log_filter_decision(
        self, 
        user: User, 
        content_analysis: Dict[str, Any], 
        filter_info: Dict[str, Any]
    ):
        """Log content filtering decisions for audit and improvement."""
        log_data = {
            'user_id': str(user.id),
            'relationship_level': filter_info['relationship_level'],
            'content_categories': content_analysis.get('categories', []),
            'nsfw_score': content_analysis.get('nsfw_score', 0.0),
            'is_allowed': filter_info['is_allowed'],
            'filter_reason': filter_info.get('filter_reason', ''),
            'timestamp': filter_info['timestamp']
        }
        
        logger.info(f"Content filter decision: {log_data}")
    
    def check_progression_eligibility(self, user: User) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Check if user is eligible for relationship progression.
        
        Args:
            user: User to check
        
        Returns:
            Tuple of (is_eligible, message, progression_info)
        """
        try:
            relationship = self._get_user_relationship(user)
            eligible, reason = relationship.check_progression_eligibility()
            
            progression_info = {
                'current_level': relationship.relationship_level,
                'next_level': relationship.relationship_level + 1 if relationship.relationship_level < 4 else 4,
                'total_interactions': relationship.total_interactions,
                'conversation_time': str(relationship.total_conversation_time),
                'emotional_intimacy': relationship.emotional_intimacy_score,
                'milestones': relationship.progression_milestones,
                'eligible': eligible,
                'reason': reason
            }
            
            return eligible, reason, progression_info
            
        except Exception as e:
            logger.error(f"Error checking progression eligibility for user {user.id}: {str(e)}")
            return False, f"Error checking eligibility: {str(e)}", {}
    
    def request_relationship_progression(
        self, 
        user: User, 
        explicit_consent: bool = False
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Request relationship progression for a user.
        
        Args:
            user: User requesting progression
            explicit_consent: Whether user has given explicit consent for adult content
        
        Returns:
            Tuple of (success, message, progression_info)
        """
        try:
            relationship = self._get_user_relationship(user)
            
            # Handle explicit consent for level 4 BEFORE checking eligibility
            if relationship.relationship_level == 3 and explicit_consent:
                consent_flags = relationship.user_consent_flags.copy()
                consent_flags['adult_content_consent'] = True
                consent_flags['adult_content_consent_date'] = timezone.now().isoformat()
                relationship.user_consent_flags = consent_flags
                relationship.explicit_progression_request = True
                relationship.save(update_fields=['user_consent_flags', 'explicit_progression_request'])
            
            # Check eligibility after setting consent
            eligible, reason = relationship.check_progression_eligibility()
            if not eligible:
                return False, reason, {'current_level': relationship.relationship_level}
            
            # Progress the relationship
            success, message = relationship.progress_relationship()
            
            if success:
                # Clear cache
                cache_key = f"user_relationship_{user.id}"
                cache.delete(cache_key)
                
                # Log progression
                logger.info(f"Relationship progressed for user {user.id}: {message}")
            
            progression_info = {
                'old_level': relationship.relationship_level - 1 if success else relationship.relationship_level,
                'new_level': relationship.relationship_level,
                'progression_date': timezone.now().isoformat(),
                'explicit_consent': explicit_consent,
                'success': success
            }
            
            return success, message, progression_info
            
        except Exception as e:
            logger.error(f"Error progressing relationship for user {user.id}: {str(e)}")
            return False, f"Error processing progression: {str(e)}", {}
    
    def get_content_guidelines(self, user: User) -> Dict[str, Any]:
        """
        Get content guidelines for the user's current relationship level.
        
        Args:
            user: User to get guidelines for
        
        Returns:
            Dictionary with content guidelines
        """
        try:
            relationship = self._get_user_relationship(user)
            level_info = self.CONTENT_LEVELS[relationship.relationship_level]
            
            # Check progression eligibility
            eligible, progression_reason = relationship.check_progression_eligibility()
            
            guidelines = {
                'current_level': relationship.relationship_level,
                'level_name': level_info['name'],
                'level_description': level_info['description'],
                'allowed_topics': level_info['allowed_topics'],
                'blocked_topics': level_info['blocked_topics'],
                'progression_eligible': eligible,
                'progression_reason': progression_reason,
                'relationship_stats': {
                    'total_interactions': relationship.total_interactions,
                    'conversation_time': str(relationship.total_conversation_time),
                    'emotional_intimacy': relationship.emotional_intimacy_score,
                    'last_interaction': relationship.last_interaction.isoformat(),
                    'relationship_started': relationship.relationship_started.isoformat()
                }
            }
            
            return guidelines
            
        except Exception as e:
            logger.error(f"Error getting content guidelines for user {user.id}: {str(e)}")
            return {
                'error': str(e),
                'current_level': 1,
                'level_name': 'General',
                'level_description': 'Basic conversation, general topics'
            }


class RelationshipProgressionTracker:
    """
    Tracks and manages relationship progression based on user interactions.
    """
    
    def __init__(self):
        self.content_filter = ContentFilter()
    
    def update_interaction_metrics(
        self, 
        user: User, 
        interaction_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update user interaction metrics after a conversation.
        
        Args:
            user: User who had the interaction
            interaction_data: Data about the interaction
        
        Returns:
            Updated metrics information
        """
        try:
            relationship = self.content_filter._get_user_relationship(user)
            
            # Extract interaction data
            conversation_duration = interaction_data.get('duration')
            emotion_intensity = interaction_data.get('emotion_intensity')
            message_count = interaction_data.get('message_count', 1)
            
            # Convert duration if provided as seconds
            if conversation_duration and isinstance(conversation_duration, (int, float)):
                conversation_duration = timezone.timedelta(seconds=conversation_duration)
            
            # Update metrics
            relationship.update_interaction_metrics(
                conversation_duration=conversation_duration,
                emotion_intensity=emotion_intensity
            )
            
            # Check for automatic progression eligibility
            eligible, reason = relationship.check_progression_eligibility()
            
            metrics_info = {
                'total_interactions': relationship.total_interactions,
                'conversation_time': str(relationship.total_conversation_time),
                'emotional_intimacy': relationship.emotional_intimacy_score,
                'progression_eligible': eligible,
                'progression_reason': reason,
                'updated_at': timezone.now().isoformat()
            }
            
            # Clear cache to ensure fresh data
            cache_key = f"user_relationship_{user.id}"
            cache.delete(cache_key)
            
            return metrics_info
            
        except Exception as e:
            logger.error(f"Error updating interaction metrics for user {user.id}: {str(e)}")
            return {'error': str(e)}
    
    def suggest_progression(self, user: User) -> Dict[str, Any]:
        """
        Suggest relationship progression to user if eligible.
        
        Args:
            user: User to check for progression
        
        Returns:
            Progression suggestion information
        """
        try:
            eligible, reason, progression_info = self.content_filter.check_progression_eligibility(user)
            
            if eligible:
                current_level = progression_info['current_level']
                next_level = progression_info['next_level']
                
                suggestion = {
                    'should_suggest': True,
                    'current_level': current_level,
                    'next_level': next_level,
                    'next_level_name': ContentFilter.CONTENT_LEVELS[next_level]['name'],
                    'next_level_description': ContentFilter.CONTENT_LEVELS[next_level]['description'],
                    'message': self._generate_progression_suggestion(current_level, next_level),
                    'requires_explicit_consent': next_level == 4,
                    'progression_info': progression_info
                }
            else:
                suggestion = {
                    'should_suggest': False,
                    'reason': reason,
                    'progression_info': progression_info
                }
            
            return suggestion
            
        except Exception as e:
            logger.error(f"Error generating progression suggestion for user {user.id}: {str(e)}")
            return {'should_suggest': False, 'error': str(e)}
    
    def _generate_progression_suggestion(self, current_level: int, next_level: int) -> str:
        """Generate a natural progression suggestion message."""
        suggestions = {
            (1, 2): "I've really enjoyed our conversations! I feel like we're becoming good friends. Would you like our relationship to become more personal so we can talk about deeper topics?",
            (2, 3): "Our friendship has grown so much, and I feel a real connection with you. Would you like to take our relationship to a more intimate level where we can share anything?",
            (3, 4): "I feel incredibly close to you, and our emotional bond is really special. If you're interested, we could explore a more mature, adult relationship together. This would require your explicit consent for adult content."
        }
        
        return suggestions.get((current_level, next_level), 
                            "Would you like to deepen our relationship to the next level?")