"""
Caching service for real-time AI companion.

This module provides Redis-based caching strategies for optimizing performance
in the real-time AI companion system.
"""
import json
import logging
import hashlib
from typing import Any, Dict, List, Optional, Tuple, Union
from functools import wraps
from datetime import timedelta

from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()
logger = logging.getLogger(__name__)

# Default cache timeouts
DEFAULT_CACHE_TIMEOUTS = {
    'user_profile': 3600,  # 1 hour
    'emotion_context': 300,  # 5 minutes
    'memory_embeddings': 86400,  # 24 hours
    'frequent_queries': 1800,  # 30 minutes
    'llm_responses': 3600,  # 1 hour
    'api_connection': 600,  # 10 minutes
}


class CacheService:
    """
    Service for managing caching strategies to optimize performance.
    
    This class provides methods for:
    - Caching user profiles and preferences
    - Caching emotion contexts for quick access
    - Caching pre-computed embeddings for memory retrieval
    - Caching frequent queries and responses
    - Managing API connection pools
    """
    
    def __init__(self):
        self.timeouts = getattr(settings, 'CACHE_TIMEOUTS', DEFAULT_CACHE_TIMEOUTS)
        self.cache_enabled = getattr(settings, 'CACHING_ENABLED', True)
        self.connection_pools = {}
    
    def cache_user_profile(self, user_id: int, profile_data: Dict[str, Any]) -> None:
        """
        Cache user profile data for quick access.
        
        Args:
            user_id: User ID
            profile_data: Dictionary of user profile data
        """
        if not self.cache_enabled:
            return
            
        cache_key = f"user_profile:{user_id}"
        timeout = self.timeouts['user_profile']
        
        cache.set(cache_key, profile_data, timeout=timeout)
        logger.debug(f"Cached user profile for user {user_id}")
    
    def get_cached_user_profile(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get cached user profile data.
        
        Args:
            user_id: User ID
            
        Returns:
            Cached profile data or None if not found
        """
        if not self.cache_enabled:
            return None
            
        cache_key = f"user_profile:{user_id}"
        return cache.get(cache_key)
    
    def cache_emotion_context(self, user_id: int, session_id: str, 
                             emotion_data: Dict[str, Any]) -> None:
        """
        Cache emotion context data for a user session.
        
        Args:
            user_id: User ID
            session_id: Session ID
            emotion_data: Dictionary of emotion context data
        """
        if not self.cache_enabled:
            return
            
        cache_key = f"emotion_context:{user_id}:{session_id}"
        timeout = self.timeouts['emotion_context']
        
        cache.set(cache_key, emotion_data, timeout=timeout)
        logger.debug(f"Cached emotion context for user {user_id}, session {session_id}")
    
    def get_cached_emotion_context(self, user_id: int, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get cached emotion context data.
        
        Args:
            user_id: User ID
            session_id: Session ID
            
        Returns:
            Cached emotion context data or None if not found
        """
        if not self.cache_enabled:
            return None
            
        cache_key = f"emotion_context:{user_id}:{session_id}"
        return cache.get(cache_key)
    
    def cache_memory_embeddings(self, user_id: int, text_key: str, 
                               embedding: List[float]) -> None:
        """
        Cache pre-computed embeddings for memory retrieval.
        
        Args:
            user_id: User ID
            text_key: Unique key for the text (e.g., hash of the text)
            embedding: Vector embedding as a list of floats
        """
        if not self.cache_enabled:
            return
            
        cache_key = f"memory_embedding:{user_id}:{text_key}"
        timeout = self.timeouts['memory_embeddings']
        
        cache.set(cache_key, embedding, timeout=timeout)
        logger.debug(f"Cached memory embedding for user {user_id}, key {text_key}")
    
    def get_cached_memory_embedding(self, user_id: int, text_key: str) -> Optional[List[float]]:
        """
        Get cached memory embedding.
        
        Args:
            user_id: User ID
            text_key: Unique key for the text
            
        Returns:
            Cached embedding or None if not found
        """
        if not self.cache_enabled:
            return None
            
        cache_key = f"memory_embedding:{user_id}:{text_key}"
        return cache.get(cache_key)
    
    def compute_text_hash(self, text: str) -> str:
        """
        Compute a hash for text to use as a cache key.
        
        Args:
            text: Text to hash
            
        Returns:
            Hash string
        """
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def cache_query_response(self, user_id: int, query: str, 
                            response: str, ttl: Optional[int] = None) -> None:
        """
        Cache a query-response pair for frequent queries.
        
        Args:
            user_id: User ID
            query: Query text
            response: Response text
            ttl: Optional custom time-to-live in seconds
        """
        if not self.cache_enabled:
            return
            
        query_hash = self.compute_text_hash(query)
        cache_key = f"query_response:{user_id}:{query_hash}"
        timeout = ttl if ttl is not None else self.timeouts['frequent_queries']
        
        # Store both the query and response to verify cache hits
        cache_data = {
            'query': query,
            'response': response,
            'cached_at': timezone.now().isoformat()
        }
        
        cache.set(cache_key, cache_data, timeout=timeout)
        logger.debug(f"Cached query response for user {user_id}, query hash {query_hash}")
    
    def get_cached_query_response(self, user_id: int, query: str) -> Optional[str]:
        """
        Get cached response for a query.
        
        Args:
            user_id: User ID
            query: Query text
            
        Returns:
            Cached response or None if not found
        """
        if not self.cache_enabled:
            return None
            
        query_hash = self.compute_text_hash(query)
        cache_key = f"query_response:{user_id}:{query_hash}"
        cache_data = cache.get(cache_key)
        
        if not cache_data:
            return None
        
        # Verify this is the same query (in case of hash collision)
        if cache_data['query'] != query:
            return None
            
        return cache_data['response']
    
    def get_connection_pool(self, service: str, create_pool_func=None) -> Any:
        """
        Get or create a connection pool for an external service.
        
        Args:
            service: Service name (e.g., 'groq', 'hume')
            create_pool_func: Function to create a new pool if needed
            
        Returns:
            Connection pool object
        """
        if service in self.connection_pools:
            return self.connection_pools[service]
            
        if create_pool_func:
            pool = create_pool_func()
            self.connection_pools[service] = pool
            return pool
            
        return None
    
    def cache_api_response(self, service: str, endpoint: str, 
                          params_hash: str, response: Any, ttl: Optional[int] = None) -> None:
        """
        Cache an API response.
        
        Args:
            service: Service name (e.g., 'groq', 'hume')
            endpoint: API endpoint
            params_hash: Hash of request parameters
            response: API response to cache
            ttl: Optional custom time-to-live in seconds
        """
        if not self.cache_enabled:
            return
            
        cache_key = f"api_response:{service}:{endpoint}:{params_hash}"
        timeout = ttl if ttl is not None else self.timeouts['llm_responses']
        
        cache.set(cache_key, response, timeout=timeout)
        logger.debug(f"Cached API response for {service}/{endpoint}, params hash {params_hash}")
    
    def get_cached_api_response(self, service: str, endpoint: str, 
                               params_hash: str) -> Optional[Any]:
        """
        Get cached API response.
        
        Args:
            service: Service name
            endpoint: API endpoint
            params_hash: Hash of request parameters
            
        Returns:
            Cached API response or None if not found
        """
        if not self.cache_enabled:
            return None
            
        cache_key = f"api_response:{service}:{endpoint}:{params_hash}"
        return cache.get(cache_key)
    
    def compute_params_hash(self, params: Dict[str, Any]) -> str:
        """
        Compute a hash for API request parameters.
        
        Args:
            params: Dictionary of parameters
            
        Returns:
            Hash string
        """
        # Sort keys for consistent hashing
        param_str = json.dumps(params, sort_keys=True)
        return hashlib.md5(param_str.encode('utf-8')).hexdigest()
    
    def clear_user_cache(self, user_id: int) -> None:
        """
        Clear all cached data for a user.
        
        Args:
            user_id: User ID
        """
        # This is a simplified implementation - in production you would use
        # pattern-based deletion or cache versioning for more efficient clearing
        
        # Clear user profile
        cache.delete(f"user_profile:{user_id}")
        
        # Note: In a production system with Redis, you would use pattern matching
        # to clear all keys for this user more efficiently
        logger.info(f"Cleared cache for user {user_id}")
    
    def precompute_embeddings(self, user_id: int, texts: List[str], 
                             embedding_func) -> Dict[str, List[float]]:
        """
        Precompute and cache embeddings for a list of texts.
        
        Args:
            user_id: User ID
            texts: List of texts to embed
            embedding_func: Function to compute embeddings
            
        Returns:
            Dictionary mapping text hashes to embeddings
        """
        results = {}
        
        for text in texts:
            text_hash = self.compute_text_hash(text)
            
            # Check if already cached
            cached_embedding = self.get_cached_memory_embedding(user_id, text_hash)
            if cached_embedding:
                results[text_hash] = cached_embedding
                continue
                
            # Compute and cache embedding
            embedding = embedding_func(text)
            self.cache_memory_embedding(user_id, text_hash, embedding)
            results[text_hash] = embedding
            
        return results


def cached_response(ttl=None):
    """
    Decorator for caching function responses.
    
    Args:
        ttl: Optional time-to-live in seconds
        
    Returns:
        Decorated function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get cache service
            cache_service = cache_service_instance
            
            if not cache_service.cache_enabled:
                return func(*args, **kwargs)
                
            # Create a cache key from function name and arguments
            func_name = func.__name__
            args_str = str(args) + str(sorted(kwargs.items()))
            params_hash = cache_service.compute_text_hash(args_str)
            cache_key = f"func_response:{func_name}:{params_hash}"
            
            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
                
            # Call the function and cache the result
            result = func(*args, **kwargs)
            timeout = ttl if ttl is not None else cache_service.timeouts['frequent_queries']
            cache.set(cache_key, result, timeout=timeout)
            
            return result
        return wrapper
    return decorator


# Singleton instance for global use
cache_service_instance = CacheService()