"""
Backblaze B2 storage backend for Django.
"""
from django.conf import settings
from django.core.files.storage import Storage
from storages.backends.s3boto3 import S3Boto3Storage
import logging
import mimetypes
import os

logger = logging.getLogger(__name__)

class BackblazeB2Storage(S3Boto3Storage):
    """
    Backblaze B2 storage backend for Django using django-storages.
    
    This uses the S3Boto3Storage backend since Backblaze B2 provides an S3-compatible API.
    """
    access_key = settings.B2_ACCESS_KEY
    secret_key = settings.B2_SECRET_KEY
    bucket_name = settings.B2_BUCKET_NAME
    endpoint_url = f"https://s3.{settings.B2_REGION}.backblazeb2.com"
    region_name = settings.B2_REGION
    custom_domain = settings.B2_CUSTOM_DOMAIN if hasattr(settings, 'B2_CUSTOM_DOMAIN') else None
    file_overwrite = False
    default_acl = 'private'  # Default to private for security
    
    def get_available_name(self, name, max_length=None):
        """
        Returns a filename that's free on the target storage system.
        
        This adds a timestamp to ensure uniqueness and avoid overwriting files.
        """
        from datetime import datetime
        import uuid
        
        # Get the file name and extension
        dir_name, file_name = os.path.split(name)
        file_root, file_ext = os.path.splitext(file_name)
        
        # Add a timestamp and UUID for uniqueness
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        
        # Construct the new filename
        new_name = f"{file_root}_{timestamp}_{unique_id}{file_ext}"
        
        # Join with the directory name if it exists
        if dir_name:
            new_name = os.path.join(dir_name, new_name)
            
        return super().get_available_name(new_name, max_length)
    
    def url(self, name):
        """
        Return the URL where the file can be accessed.
        """
        url = super().url(name)
        
        # If we're using a custom domain, return the URL as is
        if self.custom_domain:
            return url
            
        # Otherwise, return the URL with the correct endpoint
        return url
    
    def get_content_type(self, name):
        """
        Get the content type of the file based on its extension.
        """
        content_type, _ = mimetypes.guess_type(name)
        return content_type or 'application/octet-stream'