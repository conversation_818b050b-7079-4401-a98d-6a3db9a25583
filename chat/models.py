from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid
import json

# Import real-time models
from .models_realtime import (
    UserRelationship,
    EmotionContext,
    StreamingSession,
    PerformanceMetrics
)


class Relationship(models.Model):
    """Tracks relationship levels between user and AI companion."""
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='relationship')
    level = models.IntegerField(default=1)
    xp = models.IntegerField(default=0)
    xp_to_next_level = models.IntegerField(default=100)
    last_interaction = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'chat_relationship'
        verbose_name = 'Relationship'
        verbose_name_plural = 'Relationships'
    
    def __str__(self):
        return f"{self.user.email} - Level {self.level}"
    
    def add_xp(self, amount):
        """Add XP and handle level ups"""
        self.xp += amount
        
        # Check for level up
        while self.xp >= self.xp_to_next_level:
            self.xp -= self.xp_to_next_level
            self.level += 1
            self.xp_to_next_level = self._calculate_xp_for_next_level()
        
        self.save()
        return self.level
    
    def _calculate_xp_for_next_level(self):
        """Calculate XP required for next level"""
        return 100 * (self.level + 1)  # Linear scaling


class Conversation(models.Model):
    """Represents a conversation thread between user and AI companion."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='conversations')
    title = models.CharField(max_length=200, blank=True)
    
    # Conversation metadata
    is_active = models.BooleanField(default=True)
    is_archived = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_message_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'chat_conversations'
        ordering = ['-last_message_at']
        indexes = [
            models.Index(fields=['user', '-last_message_at']),
            models.Index(fields=['user', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.title or 'Untitled'}"
    
    def update_last_message_time(self):
        """Update the last message timestamp."""
        self.last_message_at = timezone.now()
        self.save(update_fields=['last_message_at'])
    
    def get_message_count(self):
        """Get total message count in this conversation."""
        return self.messages.count()
    
    def get_recent_messages(self, limit=10):
        """Get recent messages from this conversation."""
        return self.messages.order_by('-created_at')[:limit]


class Message(models.Model):
    """Represents a single message in a conversation."""
    
    MESSAGE_TYPES = [
        ('text', 'Text'),
        ('audio', 'Audio'),
        ('voice', 'Voice'),
        ('image', 'Image'),
        ('system', 'System'),
        ('typing', 'Typing'),
    ]
    
    SENDER_TYPES = [
        ('user', 'User'),
        ('assistant', 'AI Assistant'),
        ('system', 'System'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    
    # Message content
    sender_type = models.CharField(max_length=20, choices=SENDER_TYPES)
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='text')
    content = models.TextField()
    
    # Media fields
    media_url = models.URLField(null=True, blank=True)
    audio_file = models.FileField(upload_to='audio/messages/', null=True, blank=True)
    audio_duration = models.FloatField(null=True, blank=True)  # in seconds
    transcription = models.TextField(blank=True)  # For audio messages
    
    # Emotion data
    emotion = models.CharField(max_length=50, null=True, blank=True)
    
    # AI processing metadata
    processing_time = models.FloatField(null=True, blank=True)  # in seconds
    model_used = models.CharField(max_length=100, blank=True)  # e.g., 'gpt-4', 'whisper-1'
    tokens_used = models.IntegerField(null=True, blank=True)
    metadata = models.JSONField(default=dict)
    
    # Message status
    is_edited = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'chat_messages'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
            models.Index(fields=['conversation', 'sender_type']),
        ]
    
    def __str__(self):
        return f"{self.sender_type}: {self.content[:50]}..."
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Update conversation's last message time
        self.conversation.update_last_message_time()


class MessageReaction(models.Model):
    """User reactions to AI messages (like, dislike, etc.)."""
    
    REACTION_TYPES = [
        ('like', 'Like'),
        ('dislike', 'Dislike'),
        ('love', 'Love'),
        ('helpful', 'Helpful'),
        ('not_helpful', 'Not Helpful'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='reactions')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    reaction_type = models.CharField(max_length=20, choices=REACTION_TYPES)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'chat_message_reactions'
        unique_together = ['message', 'user']  # One reaction per user per message
    
    def __str__(self):
        return f"{self.user.email} - {self.reaction_type} - {self.message.id}"


class ChatSession(models.Model):
    """Represents an active chat session (WebSocket connection)."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='chat_sessions')
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='sessions', null=True, blank=True)
    
    # Session metadata
    channel_name = models.CharField(max_length=255, unique=True)  # WebSocket channel name
    is_active = models.BooleanField(default=True)
    
    # Connection info
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    # Timestamps
    connected_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    disconnected_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'chat_sessions'
        ordering = ['-connected_at']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['channel_name']),
        ]
    
    def __str__(self):
        return f"{self.user.email} - {self.channel_name}"
    
    def disconnect(self):
        """Mark session as disconnected."""
        self.is_active = False
        self.disconnected_at = timezone.now()
        self.save(update_fields=['is_active', 'disconnected_at'])


class VoiceSettings(models.Model):
    """User-specific voice settings for TTS."""
    
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='voice_settings')
    
    # TTS Settings
    voice_model = models.CharField(max_length=50, default='tts-1')
    voice_name = models.CharField(max_length=50, default='nova')
    speech_speed = models.FloatField(default=1.0)  # 0.25 to 4.0
    
    # STT Settings
    language = models.CharField(max_length=10, default='en')
    auto_transcribe = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'chat_voice_settings'
    
    def __str__(self):
        return f"{self.user.email} - {self.voice_name}"
