# Groq Whisper Integration

## Overview

The EllaHAI backend now uses **Groq's Whisper large-v3-turbo** model for real-time speech-to-text transcription, replacing the previous mock implementation.

## Complete Audio Pipeline

```
📱 Audio Input (WebSocket)
    ↓
🎤 Groq Whisper large-v3-turbo (Speech-to-Text)
    ↓
😊 Hume AI Expression Measurement (Emotion Detection)
    ↓
🤖 Agent Orchestration (LLM + Emotion Context)
    ↓
🔊 Hume AI TTS (Emotion-Aware Voice Response)
    ↓
📱 Audio Response (WebSocket)
```

## Features

### Groq Whisper Integration
- **Model**: `whisper-large-v3-turbo`
- **Response Format**: `verbose_json` (for confidence scores)
- **Language**: English (configurable)
- **Temperature**: 0.0 (for consistent results)
- **Audio Format**: Automatic WAV conversion from raw PCM
- **Error Handling**: Graceful fallback with error messages

### Audio Processing
- **Real-time streaming**: Process audio chunks as they arrive
- **Quality assessment**: Skip low-quality audio automatically
- **Parallel processing**: Transcription and emotion detection in parallel
- **Performance monitoring**: Track processing times and metrics

## Configuration

### Environment Variables
```bash
# Required
GROQ_API_KEY=your-groq-api-key-here

# Optional (with defaults)
GROQ_BASE_URL=https://api.groq.com
GROQ_TIMEOUT=30.0
GROQ_MAX_RETRIES=3
```

### Audio Settings
```bash
# Audio processing limits
AUDIO_MAX_FILE_SIZE=10485760  # 10MB
AUDIO_PROCESSING_TIMEOUT=30
```

## Usage

### WebSocket Message Format
Send audio chunks to the WebSocket endpoint:

```json
{
  "type": "audio_chunk",
  "data": "base64_encoded_audio_data",
  "chunk_id": "unique_chunk_id",
  "is_final": true,
  "timestamp": 1234567890
}
```

### Response Format
Receive real-time transcription results:

```json
{
  "type": "transcription_partial",
  "text": "Hello, how are you today?",
  "confidence": 0.95,
  "chunk_id": "unique_chunk_id",
  "is_partial": false,
  "processing_time_ms": 150,
  "timestamp": 1234567890,
  "request_id": "request_uuid"
}
```

## Performance

### Target Metrics
- **Transcription Time**: < 200ms for typical audio chunks
- **Total Response Time**: < 450ms (including emotion detection and LLM)
- **First Token Time**: < 200ms for LLM response
- **TTS First Chunk**: < 100ms for audio synthesis

### Quality Features
- **Audio Quality Assessment**: Automatic quality scoring
- **Confidence Adjustment**: Confidence scores adjusted based on audio quality
- **Error Recovery**: Fallback responses when transcription fails
- **Circuit Breaker**: Prevents cascade failures

## Testing

Run the Groq Whisper integration tests:

```bash
DJANGO_SETTINGS_MODULE=ellahai_backend.test_settings python -m pytest tests/test_audio_service.py::TestGroqWhisperIntegration -v
```

## Dependencies

### Required Packages
```
groq>=0.29.0,<1.0
hume==0.6.0
numpy==1.24.3
wave (built-in)
```

### Installation
```bash
pip install "groq>=0.29.0,<1.0" hume==0.6.0
```

## Error Handling

### Graceful Degradation
1. **API Errors**: Returns fallback message with low confidence
2. **Network Issues**: Retries with exponential backoff
3. **Audio Format Issues**: Automatic format conversion
4. **Quality Issues**: Skips processing for very low-quality audio

### Monitoring
- Performance metrics tracked for all operations
- Error rates monitored with circuit breaker pattern
- Audio quality assessment for each chunk
- Processing time optimization

## Integration Points

### With Hume AI
- Emotion detection runs in parallel with transcription
- Emotion context passed to LLM for response generation
- TTS synthesis uses emotion context for voice modulation

### With Agent System
- Transcribed text sent to agent coordinator
- Conversation history maintained
- Memory context retrieved and used
- Streaming responses generated

### With WebSocket Consumer
- Real-time chunk processing
- Streaming results sent immediately
- Connection management and heartbeat
- Error recovery and reconnection
