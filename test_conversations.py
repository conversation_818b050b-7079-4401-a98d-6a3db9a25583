#!/usr/bin/env python3
"""
Test script to validate conversations endpoints.
"""

import requests
import json

def test_endpoint(url, description):
    """Test a single API endpoint."""
    print(f"\n🔍 Testing {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                data = response.json()
                print(f"Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            except json.JSONDecodeError:
                print("Response is not JSON")
        elif response.status_code == 401:
            print("🔒 Authentication required (expected)")
        elif response.status_code == 404:
            print("❌ Not Found - endpoint may not exist")
        else:
            print(f"⚠️ Unexpected status: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the server running?")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Test conversations endpoints."""
    base_url = "http://127.0.0.1:8000"
    
    print("🚀 Testing Conversations Endpoints")
    print("=" * 40)
    
    # Test conversations endpoints
    endpoints = [
        ("/api/conversations/", "Conversations List (alias)"),
        ("/api/chat/conversations/", "Conversations List (original)"),
        ("/api/conversations/test-id/", "Conversation Detail (alias)"),
        ("/api/chat/conversations/test-id/", "Conversation Detail (original)"),
        ("/api/conversations/test-id/messages/", "Conversation Messages (alias)"),
        ("/api/chat/conversations/test-id/messages/", "Conversation Messages (original)"),
    ]
    
    for endpoint, description in endpoints:
        test_endpoint(f"{base_url}{endpoint}", description)
    
    print("\n" + "=" * 40)
    print("✅ Testing complete!")
    print("\nNote: 401 (Unauthorized) responses are expected for protected endpoints.")
    print("404 (Not Found) responses indicate missing endpoints that need to be fixed.")

if __name__ == "__main__":
    main()
