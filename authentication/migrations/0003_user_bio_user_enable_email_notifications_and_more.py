# Generated by Django 4.2.7 on 2025-07-19 04:39

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("authentication", "0002_user_ai_companion_name"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="bio",
            field=models.TextField(blank=True, default=""),
        ),
        migrations.AddField(
            model_name="user",
            name="enable_email_notifications",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="user",
            name="enable_push_notifications",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="user",
            name="enable_voice_responses",
            field=models.<PERSON>oleanField(default=True),
        ),
    ]
