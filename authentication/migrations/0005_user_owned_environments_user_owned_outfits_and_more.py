# Generated by Django 4.2.7 on 2025-07-21 20:32

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("authentication", "0004_user_personality_traits"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="user",
            name="owned_environments",
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name="user",
            name="owned_outfits",
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name="user",
            name="owned_pets",
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name="user",
            name="preferences",
            field=models.<PERSON><PERSON><PERSON>ield(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name="user",
            name="selected_environment",
            field=models.Char<PERSON>ield(default="cozy_room", max_length=100),
        ),
        migrations.AddField(
            model_name="user",
            name="selected_personality",
            field=models.<PERSON><PERSON><PERSON><PERSON>(
                choices=[
                    ("caring<PERSON>riend", "Caring Friend"),
                    ("playfulCompanion", "Playful Companion"),
                    ("wise<PERSON><PERSON><PERSON>", "Wise Mentor"),
                    ("romantic<PERSON>artner", "Romantic Partner"),
                    ("supportiveTherapist", "Supportive Therapist"),
                ],
                default="caringFriend",
                max_length=50,
            ),
        ),
    ]
