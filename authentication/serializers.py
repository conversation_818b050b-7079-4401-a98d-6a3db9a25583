from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken
from .models import User, UserSession


class UserSerializer(serializers.ModelSerializer):
    """Comprehensive serializer for User model with all fields."""
    
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name', 
            'date_of_birth', 'user_timezone', 'preferred_language', 
            'ai_companion_name', 'bio', 'enable_email_notifications',
            'enable_push_notifications', 'enable_voice_responses',
            'selected_personality', 'selected_environment', 
            'owned_environments', 'owned_outfits', 'owned_pets',
            'preferences', 'companion_personality', 'personality_traits',
            'preferred_voice', 'allow_data_collection', 
            'allow_personalization', 'allow_proactive_messages',
            'created_at', 'updated_at', 'last_active'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'last_active']
        extra_kwargs = {
            'password': {'write_only': True}
        }


class UserRegistrationSerializer(serializers.ModelSerializer):
    """Serializer for user registration."""
    
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'email', 'username', 'password', 'password_confirm',
            'first_name', 'last_name', 'date_of_birth', 'user_timezone',
            'preferred_language', 'selected_personality', 'preferred_voice',
            'ai_companion_name'
        ]
        extra_kwargs = {
            'email': {'required': True},
            'username': {'required': True},
        }
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError({"password_confirm": "Passwords don't match."})
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user


class UserLoginSerializer(serializers.Serializer):
    """Serializer for user login."""
    
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(username=email, password=password)
            if not user:
                raise serializers.ValidationError({'non_field_errors': ['Invalid credentials.']})
            if not user.is_active:
                raise serializers.ValidationError({'non_field_errors': ['User account is disabled.']})
            attrs['user'] = user
        else:
            raise serializers.ValidationError({'non_field_errors': ['Must include email and password.']})
        
        return attrs


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """Custom JWT token serializer that includes user data."""
    
    def validate(self, attrs):
        data = super().validate(attrs)
        
        # Add user data to response
        user = self.user
        user_serializer = UserSerializer(user)
        data['user'] = user_serializer.data
        
        # Ensure standard JWT response format
        return {
            'access': data['access'],
            'refresh': data['refresh'],
            'user': data['user']
        }


class TokenRefreshResponseSerializer(serializers.Serializer):
    """Response serializer for token refresh."""
    access = serializers.CharField()
    refresh = serializers.CharField()


class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer for user profile information."""
    
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name', 'bio',
            'date_of_birth', 'user_timezone', 'preferred_language',
            'ai_companion_name', 'selected_personality', 'preferred_voice',
            'allow_data_collection', 'allow_personalization', 'allow_proactive_messages',
            'created_at', 'last_active', 'enable_email_notifications',
            'enable_push_notifications', 'enable_voice_responses',
            'selected_environment', 'personality_traits'
        ]
        read_only_fields = ['id', 'email', 'created_at', 'last_active']


class UserPreferencesSerializer(serializers.ModelSerializer):
    """Serializer for updating user preferences only."""
    
    class Meta:
        model = User
        fields = [
            'selected_personality', 'preferred_voice', 'user_timezone',
            'preferred_language', 'allow_data_collection',
            'allow_personalization', 'allow_proactive_messages',
            'enable_email_notifications', 'enable_push_notifications',
            'enable_voice_responses', 'ai_companion_name',
            'selected_environment', 'personality_traits'
        ]


class UserSessionSerializer(serializers.ModelSerializer):
    """Serializer for user session information."""
    
    class Meta:
        model = UserSession
        fields = [
            'id', 'session_key', 'device_type', 'ip_address',
            'created_at', 'last_activity', 'is_active'
        ]
        read_only_fields = ['id', 'session_key', 'created_at', 'last_activity']


class PasswordChangeSerializer(serializers.Serializer):
    """Serializer for password change."""
    
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    new_password_confirm = serializers.CharField(write_only=True)
    
    def validate_old_password(self, value):
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError('Old password is incorrect.')
        return value
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError({"new_password_confirm": "New passwords don't match."})
        return attrs
    
    def save(self):
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user


class SocialAuthSerializer(serializers.Serializer):
    """Base serializer for social authentication."""
    
    def get_tokens_for_user(self, user):
        """Generate JWT tokens for a user."""
        refresh = RefreshToken.for_user(user)
        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }


class GoogleAuthSerializer(SocialAuthSerializer):
    """Serializer for Google OAuth authentication."""
    id_token = serializers.CharField(required=True)


class AppleAuthSerializer(SocialAuthSerializer):
    """Serializer for Apple Sign In authentication."""
    identity_token = serializers.CharField(required=True)
    authorization_code = serializers.CharField(required=True)
    email = serializers.EmailField(required=False, allow_blank=True)
    first_name = serializers.CharField(required=False, allow_blank=True)
    last_name = serializers.CharField(required=False, allow_blank=True)
