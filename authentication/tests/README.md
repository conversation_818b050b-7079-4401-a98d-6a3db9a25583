# Authentication Tests

This directory contains tests for the authentication app.

## Running Tests

To run all authentication tests:

```bash
python manage.py test authentication
```

To run specific test modules:

```bash
# Run serializer tests
python manage.py test authentication.tests.test_serializers

# Run model tests
python manage.py test authentication.tests.test_models

# Run URL tests
python manage.py test authentication.tests.test_urls

# Run view tests
python manage.py test authentication.tests.test_views
```

## Test Coverage

The tests cover the following components:

### Models
- User model
- UserSession model

### Serializers
- UserSerializer
- UserRegistrationSerializer
- UserLoginSerializer
- UserProfileSerializer
- UserPreferencesSerializer

### Views
- UserRegistrationView
- Login view
- Logout view
- Token refresh
- User profile view

### URLs
- All URL patterns in the authentication app

## Adding New Tests

When adding new tests, follow these guidelines:

1. Place tests in the appropriate module based on what they're testing
2. Use descriptive test method names that explain what's being tested
3. Include docstrings for each test method
4. Follow the AAA pattern (Arrange, Act, Assert)
5. Use setUp methods for common test setup

## Test Data

The tests use test data that is created within the tests themselves. No external data sources are required.