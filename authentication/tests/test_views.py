from django.test import TestCase
from django.test import TestCase
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
import json

User = get_user_model()


class UserRegistrationViewTests(TestCase):
    """Test the user registration endpoint."""
    
    def setUp(self):
        self.client = APIClient()
        self.register_url = reverse('authentication:register')
        self.valid_payload = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'password_confirm': 'securepassword123',
            'first_name': 'New',
            'last_name': 'User',
        }
    
    def test_valid_registration(self):
        """Test registration with valid data."""
        response = self.client.post(
            self.register_url,
            data=json.dumps(self.valid_payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('user', response.data)
        self.assertIn('tokens', response.data)
        self.assertIn('refresh', response.data['tokens'])
        self.assertIn('access', response.data['tokens'])
        self.assertEqual(User.objects.count(), 1)
        self.assertEqual(User.objects.get().email, '<EMAIL>')
    
    def test_invalid_registration_password_mismatch(self):
        """Test registration with password mismatch."""
        invalid_payload = self.valid_payload.copy()
        invalid_payload['password_confirm'] = 'differentpassword'
        response = self.client.post(
            self.register_url,
            data=json.dumps(invalid_payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(User.objects.count(), 0)
    
    def test_invalid_registration_missing_fields(self):
        """Test registration with missing required fields."""
        invalid_payload = {
            'username': 'newuser',
            'email': '<EMAIL>',
        }
        response = self.client.post(
            self.register_url,
            data=json.dumps(invalid_payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(User.objects.count(), 0)


class UserLoginViewTests(TestCase):
    """Test the user login endpoint."""
    
    def setUp(self):
        self.client = APIClient()
        self.login_url = reverse('authentication:login')
        self.user = User.objects.create_user(
            username='loginuser',
            email='<EMAIL>',
            password='securepassword123'
        )
    
    def test_valid_login(self):
        """Test login with valid credentials."""
        payload = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }
        response = self.client.post(
            self.login_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('user', response.data)
        self.assertIn('tokens', response.data)
        self.assertIn('refresh', response.data['tokens'])
        self.assertIn('access', response.data['tokens'])
    
    def test_invalid_login_wrong_password(self):
        """Test login with wrong password."""
        payload = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        response = self.client.post(
            self.login_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_invalid_login_nonexistent_user(self):
        """Test login with nonexistent user."""
        payload = {
            'email': '<EMAIL>',
            'password': 'securepassword123'
        }
        response = self.client.post(
            self.login_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class LogoutViewTests(TestCase):
    """Test the logout endpoint."""
    
    def setUp(self):
        self.client = APIClient()
        self.logout_url = reverse('authentication:logout')
        self.user = User.objects.create_user(
            username='logoutuser',
            email='<EMAIL>',
            password='securepassword123'
        )
        self.refresh_token = RefreshToken.for_user(self.user)
        self.access_token = self.refresh_token.access_token
    
    def test_logout_success(self):
        """Test successful logout."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {'refresh': str(self.refresh_token)}
        response = self.client.post(
            self.logout_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)
    
    def test_logout_no_token(self):
        """Test logout without token."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        response = self.client.post(
            self.logout_url,
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_logout_invalid_token(self):
        """Test logout with invalid token."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {'refresh': 'invalid-token'}
        response = self.client.post(
            self.logout_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class TokenRefreshViewTests(TestCase):
    """Test the token refresh endpoint."""
    
    def setUp(self):
        self.client = APIClient()
        self.token_refresh_url = reverse('authentication:token_refresh')
        self.user = User.objects.create_user(
            username='refreshuser',
            email='<EMAIL>',
            password='securepassword123'
        )
        self.refresh_token = RefreshToken.for_user(self.user)
    
    def test_token_refresh_success(self):
        """Test successful token refresh."""
        payload = {'refresh': str(self.refresh_token)}
        response = self.client.post(
            self.token_refresh_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
    
    def test_token_refresh_invalid_token(self):
        """Test token refresh with invalid token."""
        payload = {'refresh': 'invalid-token'}
        response = self.client.post(
            self.token_refresh_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class UserProfileViewTests(TestCase):
    """Test the user profile endpoint."""
    
    def setUp(self):
        self.client = APIClient()
        self.profile_url = reverse('authentication:profile')
        self.user = User.objects.create_user(
            username='profileuser',
            email='<EMAIL>',
            password='securepassword123',
            first_name='Profile',
            last_name='User'
        )
        self.access_token = str(RefreshToken.for_user(self.user).access_token)
    
    def test_get_profile(self):
        """Test getting user profile."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        response = self.client.get(self.profile_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['profile']['email'], self.user.email)
        self.assertEqual(response.data['profile']['first_name'], self.user.first_name)
        self.assertEqual(response.data['profile']['last_name'], self.user.last_name)
    
    def test_update_profile(self):
        """Test updating user profile."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'bio': 'This is my updated bio'
        }
        response = self.client.patch(
            self.profile_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.user.refresh_from_db()
        self.assertEqual(self.user.first_name, payload['first_name'])
        self.assertEqual(self.user.last_name, payload['last_name'])
        self.assertEqual(self.user.bio, payload['bio'])
    
    def test_profile_unauthorized(self):
        """Test accessing profile without authentication."""
        response = self.client.get(self.profile_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class UserPreferencesViewTests(TestCase):
    """Test the user preferences endpoint."""
    
    def setUp(self):
        self.client = APIClient()
        self.preferences_url = reverse('authentication:preferences')
        self.user = User.objects.create_user(
            username='prefsuser',
            email='<EMAIL>',
            password='securepassword123',
            preferred_language='en',
            user_timezone='UTC',
            selected_personality='caringFriend',
            preferred_voice='nova'
        )
        self.access_token = str(RefreshToken.for_user(self.user).access_token)
    
    def test_get_preferences(self):
        """Test getting user preferences."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        response = self.client.get(self.preferences_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['preferences']['preferred_language'], self.user.preferred_language)
        self.assertEqual(response.data['preferences']['user_timezone'], self.user.user_timezone)
        self.assertEqual(response.data['preferences']['selected_personality'], self.user.selected_personality)
        self.assertEqual(response.data['preferences']['preferred_voice'], self.user.preferred_voice)
    
    def test_update_preferences(self):
        """Test updating user preferences."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {
            'preferred_language': 'fr',
            'user_timezone': 'Europe/Paris',
            'selected_personality': 'playfulCompanion',
            'preferred_voice': 'alloy',
            'enable_email_notifications': False
        }
        response = self.client.patch(
            self.preferences_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.user.refresh_from_db()
        self.assertEqual(self.user.preferred_language, payload['preferred_language'])
        self.assertEqual(self.user.user_timezone, payload['user_timezone'])
        self.assertEqual(self.user.selected_personality, payload['selected_personality'])
        self.assertEqual(self.user.preferred_voice, payload['preferred_voice'])
        self.assertEqual(self.user.enable_email_notifications, payload['enable_email_notifications'])
    
    def test_preferences_unauthorized(self):
        """Test accessing preferences without authentication."""
        response = self.client.get(self.preferences_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

class UserProgressViewTests(TestCase):
    """Test the user progress endpoint."""
    
    def setUp(self):
        self.client = APIClient()
        self.progress_url = reverse('authentication:progress')
        self.user = User.objects.create_user(
            username='progressuser',
            email='<EMAIL>',
            password='securepassword123'
        )
        self.access_token = str(RefreshToken.for_user(self.user).access_token)
        
        # Create user progress
        from gamification.models import UserProgress, Wallet
        self.progress = UserProgress.objects.create(
            user=self.user,
            xp=50,
            level=2,
            hearts=10,
            messages_count=25
        )
        self.wallet = Wallet.objects.create(
            user=self.user,
            balance=100,
            lifetime_earnings=150
        )
    
    def test_get_progress(self):
        """Test getting user progress."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        response = self.client.get(self.progress_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['progress']['xp'], self.progress.xp)
        self.assertEqual(response.data['progress']['level'], self.progress.level)
        self.assertEqual(response.data['progress']['hearts'], self.progress.hearts)
        self.assertEqual(response.data['progress']['messages_count'], self.progress.messages_count)
        self.assertEqual(response.data['wallet']['balance'], self.wallet.balance)
        self.assertEqual(response.data['wallet']['lifetime_earnings'], self.wallet.lifetime_earnings)
    
    def test_update_progress(self):
        """Test updating user progress."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {
            'total_time_spent': 3600  # 1 hour in seconds
        }
        response = self.client.patch(
            self.progress_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.progress.refresh_from_db()
        self.assertEqual(self.progress.total_time_spent, payload['total_time_spent'])
    
    def test_progress_unauthorized(self):
        """Test accessing progress without authentication."""
        response = self.client.get(self.progress_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_get_progress_auto_create(self):
        """Test that progress is auto-created if it doesn't exist."""
        # Delete existing progress
        self.progress.delete()
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        response = self.client.get(self.progress_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        
        # Check that a new progress object was created
        from gamification.models import UserProgress
        self.assertEqual(UserProgress.objects.filter(user=self.user).count(), 1)


class UserProgressUpdateViewTests(TestCase):
    """Test the user progress update endpoint."""
    
    def setUp(self):
        self.client = APIClient()
        self.progress_update_url = reverse('authentication:progress-update')
        self.user = User.objects.create_user(
            username='progressupdateuser',
            email='<EMAIL>',
            password='securepassword123'
        )
        self.access_token = str(RefreshToken.for_user(self.user).access_token)
        
        # Create user progress
        from gamification.models import UserProgress
        self.progress = UserProgress.objects.create(
            user=self.user,
            xp=50,
            level=2,
            hearts=10,
            messages_count=25
        )
    
    def test_add_xp(self):
        """Test adding XP to user progress."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {
            'action': 'add_xp',
            'amount': 60
        }
        response = self.client.post(
            self.progress_update_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['xp_added'], 60)
        self.progress.refresh_from_db()
        self.assertEqual(self.progress.level, 3)  # Level up from 2 to 3
    
    def test_add_hearts(self):
        """Test adding hearts to user progress."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {
            'action': 'add_hearts',
            'amount': 5
        }
        response = self.client.post(
            self.progress_update_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['hearts_added'], 5)
        self.progress.refresh_from_db()
        self.assertEqual(self.progress.hearts, 15)  # 10 + 5 = 15
    
    def test_increment_message(self):
        """Test incrementing message count."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {
            'action': 'increment_message',
            'is_voice': False
        }
        response = self.client.post(
            self.progress_update_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.progress.refresh_from_db()
        self.assertEqual(self.progress.messages_count, 26)  # 25 + 1 = 26
        self.assertEqual(self.progress.voice_messages_count, 0)  # Unchanged
        
        # Test voice message
        payload['is_voice'] = True
        response = self.client.post(
            self.progress_update_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.progress.refresh_from_db()
        self.assertEqual(self.progress.messages_count, 27)  # 26 + 1 = 27
        self.assertEqual(self.progress.voice_messages_count, 1)  # 0 + 1 = 1
    
    def test_add_time_spent(self):
        """Test adding time spent."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {
            'action': 'add_time_spent',
            'amount': 300  # 5 minutes in seconds
        }
        response = self.client.post(
            self.progress_update_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertEqual(response.data['seconds_added'], 300)
        self.progress.refresh_from_db()
        self.assertEqual(self.progress.total_time_spent, 300)
    
    def test_invalid_action(self):
        """Test invalid action type."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {
            'action': 'invalid_action',
            'amount': 10
        }
        response = self.client.post(
            self.progress_update_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['status'], 'error')
    
    def test_invalid_amount(self):
        """Test invalid amount."""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        payload = {
            'action': 'add_xp',
            'amount': -10
        }
        response = self.client.post(
            self.progress_update_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['status'], 'error')
    
    def test_progress_update_unauthorized(self):
        """Test accessing progress update without authentication."""
        payload = {
            'action': 'add_xp',
            'amount': 10
        }
        response = self.client.post(
            self.progress_update_url,
            data=json.dumps(payload),
            content_type='application/json'
        )
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)