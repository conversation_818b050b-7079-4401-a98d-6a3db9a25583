from django.test import TestCase
from django.test import TestCase
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from django.conf import settings

class CORSConfigurationTests(TestCase):
    """Test CORS configuration"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.token_url = reverse('authentication:token_obtain_pair')
    
    def test_cors_headers_present(self):
        """Test that CORS headers are present in the response"""
        # Make an OPTIONS request to simulate CORS preflight
        response = self.client.options(
            self.token_url,
            HTTP_ORIGIN='http://localhost:3000'
        )
        
        # Check that the response has CORS headers
        self.assertEqual(response.status_code, 200)
        self.assertIn('Access-Control-Allow-Origin', response)
        self.assertEqual(response['Access-Control-Allow-Origin'], 'http://localhost:3000')
        self.assertIn('Access-Control-Allow-Methods', response)
        self.assertIn('Access-Control-Allow-Headers', response)
        
        # Check that the allowed methods include the ones we configured
        allowed_methods = response['Access-Control-Allow-Methods']
        for method in ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']:
            self.assertIn(method, allowed_methods)
        
        # Check that the allowed headers include the ones we configured
        allowed_headers = response['Access-Control-Allow-Headers']
        for header in ['authorization', 'content-type']:
            self.assertIn(header.lower(), allowed_headers.lower())
    
    def test_cors_disallowed_origin(self):
        """Test that CORS headers are not present for disallowed origins"""
        # Make an OPTIONS request with a disallowed origin
        response = self.client.options(
            self.token_url,
            HTTP_ORIGIN='http://evil-site.com'
        )
        
        # Check that the response does not have the Access-Control-Allow-Origin header
        self.assertEqual(response.status_code, 200)
        self.assertNotIn('Access-Control-Allow-Origin', response)