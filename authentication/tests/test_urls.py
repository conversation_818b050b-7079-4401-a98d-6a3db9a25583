from django.test import TestCase
from django.test import TestCase
from django.test import TestCase
from django.urls import reverse, resolve
from authentication import views
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView


class AuthenticationUrlsTests(TestCase):
    """Test the authentication app URLs."""
    
    def test_register_url_resolves(self):
        """Test that the register URL resolves to the correct view."""
        url = reverse('authentication:register')
        self.assertEqual(resolve(url).func.view_class, views.UserRegistrationView)
    
    def test_login_url_resolves(self):
        """Test that the login URL resolves to the correct view."""
        url = reverse('authentication:login')
        self.assertEqual(resolve(url).func, views.login_view)
    
    def test_logout_url_resolves(self):
        """Test that the logout URL resolves to the correct view."""
        url = reverse('authentication:logout')
        self.assertEqual(resolve(url).func.view_class, views.LogoutView)
    
    def test_google_auth_url_resolves(self):
        """Test that the Google auth URL resolves to the correct view."""
        url = reverse('authentication:google_auth')
        self.assertEqual(resolve(url).func.view_class, views.GoogleAuthView)
    
    def test_apple_auth_url_resolves(self):
        """Test that the Apple auth URL resolves to the correct view."""
        url = reverse('authentication:apple_auth')
        self.assertEqual(resolve(url).func.view_class, views.AppleAuthView)
    
    def test_token_url_resolves(self):
        """Test that the token URL resolves to the correct view."""
        url = reverse('authentication:token_obtain_pair')
        self.assertEqual(resolve(url).func.view_class, views.CustomTokenObtainPairView)
    
    def test_token_refresh_url_resolves(self):
        """Test that the token refresh URL resolves to the correct view."""
        url = reverse('authentication:token_refresh')
        self.assertEqual(resolve(url).func.view_class, TokenRefreshView)
    
    def test_token_verify_url_resolves(self):
        """Test that the token verify URL resolves to the correct view."""
        url = reverse('authentication:token_verify')
        self.assertEqual(resolve(url).func.view_class, TokenVerifyView)
    
    def test_profile_url_resolves(self):
        """Test that the profile URL resolves to the correct view."""
        url = reverse('authentication:profile')
        self.assertEqual(resolve(url).func.view_class, views.UserProfileView)
    
    def test_preferences_url_resolves(self):
        """Test that the preferences URL resolves to the correct view."""
        url = reverse('authentication:preferences')
        self.assertEqual(resolve(url).func.view_class, views.UserPreferencesView)
    
    def test_change_password_url_resolves(self):
        """Test that the change password URL resolves to the correct view."""
        url = reverse('authentication:change-password')
        self.assertEqual(resolve(url).func.view_class, views.PasswordChangeView)
    
    def test_sessions_url_resolves(self):
        """Test that the sessions URL resolves to the correct view."""
        url = reverse('authentication:sessions')
        self.assertEqual(resolve(url).func.view_class, views.UserSessionListView)
    
    def test_stats_url_resolves(self):
        """Test that the stats URL resolves to the correct view."""
        url = reverse('authentication:stats')
        self.assertEqual(resolve(url).func, views.user_stats_view)
    
    def test_progress_url_resolves(self):
        """Test that the progress URL resolves to the correct view."""
        url = reverse('authentication:progress')
        self.assertEqual(resolve(url).func.view_class, views.UserProgressView)
    
    def test_progress_update_url_resolves(self):
        """Test that the progress update URL resolves to the correct view."""
        url = reverse('authentication:progress-update')
        self.assertEqual(resolve(url).func.view_class, views.UserProgressUpdateView)