from django.test import TestCase
from django.test import TestCase
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
import json

class CustomExceptionHandlerTests(TestCase):
    """Test custom exception handler for consistent error responses"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.token_url = reverse('authentication:token_obtain_pair')
        self.nonexistent_url = '/api/nonexistent-endpoint/'
    
    def test_authentication_error_format(self):
        """Test that authentication errors follow the custom format"""
        response = self.client.post(
            self.token_url,
            data={
                'username': 'nonexistentuser',
                'password': 'wrongpassword'
            },
            format='json'
        )
        
        # The response might be 400 Bad Request instead of 401 Unauthorized
        # depending on how DRF SimpleJWT is configured
        self.assertIn(response.status_code, [status.HTTP_400_BAD_REQUEST, status.HTTP_401_UNAUTHORIZED])
        
        # Check that the response follows our custom error format
        response_data = json.loads(response.content)
        self.assertIn('error', response_data)
        self.assertIn('code', response_data['error'])
        self.assertIn('message', response_data['error'])
        
        # Check that the error code is appropriate
        self.assertIn(response_data['error']['code'], ['authentication_failed', 'validation_error', 'bad_request'])
    
    def test_not_found_error_format(self):
        """Test that 404 errors follow the custom format"""
        response = self.client.get(self.nonexistent_url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # For 404 errors, Django might return HTML instead of JSON
        # Let's check if the response is JSON before trying to parse it
        if 'application/json' in response.get('Content-Type', ''):
            # Check that the response follows our custom error format
            response_data = json.loads(response.content)
            self.assertIn('error', response_data)
            self.assertIn('code', response_data['error'])
            self.assertIn('message', response_data['error'])
            
            # Check that the error code is as expected
            self.assertEqual(response_data['error']['code'], 'resource_not_found')
        else:
            # If not JSON, just check that we got a 404 status code
            self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_validation_error_format(self):
        """Test that validation errors follow the custom format"""
        # Send an incomplete request to trigger validation error
        response = self.client.post(
            self.token_url,
            data={},  # Missing required fields
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Check that the response follows our custom error format
        response_data = json.loads(response.content)
        self.assertIn('error', response_data)
        self.assertIn('code', response_data['error'])
        self.assertIn('message', response_data['error'])
        self.assertIn('details', response_data['error'])
        
        # Check that the error code is as expected
        self.assertEqual(response_data['error']['code'], 'validation_error')