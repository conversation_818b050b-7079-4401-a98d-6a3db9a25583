from django.test import TestCase
from django.test import TestCase
from django.test import TestCase
from django.contrib.auth import get_user_model
from authentication.serializers import (
    UserSerializer, UserRegistrationSerializer, UserLoginSerializer,
    UserProfileSerializer, UserPreferencesSerializer
)

User = get_user_model()


class UserSerializerTests(TestCase):
    """Test the UserSerializer."""
    
    def setUp(self):
        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'first_name': 'Test',
            'last_name': 'User',
            'date_of_birth': '1990-01-01',
            'user_timezone': 'UTC',
            'preferred_language': 'en',
            'ai_companion_name': 'TestBot',
            'selected_personality': 'caringFriend',
        }
        self.user = User.objects.create_user(**self.user_data)
    
    def test_user_serializer_contains_expected_fields(self):
        """Test that UserSerializer contains the expected fields."""
        serializer = UserSerializer(instance=self.user)
        expected_fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'date_of_birth', 'user_timezone', 'preferred_language',
            'ai_companion_name', 'bio', 'enable_email_notifications',
            'enable_push_notifications', 'enable_voice_responses',
            'selected_personality', 'selected_environment',
            'owned_environments', 'owned_outfits', 'owned_pets',
            'preferences', 'companion_personality', 'personality_traits',
            'preferred_voice', 'allow_data_collection',
            'allow_personalization', 'allow_proactive_messages',
            'created_at', 'updated_at', 'last_active'
        ]
        self.assertEqual(set(serializer.data.keys()), set(expected_fields))


class UserRegistrationSerializerTests(TestCase):
    """Test the UserRegistrationSerializer."""
    
    def setUp(self):
        self.valid_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'password_confirm': 'securepassword123',
            'first_name': 'New',
            'last_name': 'User',
            'date_of_birth': '1990-01-01',
            'user_timezone': 'UTC',
            'preferred_language': 'en',
            'selected_personality': 'caringFriend',
            'ai_companion_name': 'NewBot',
        }
        self.invalid_data = self.valid_data.copy()
        self.invalid_data['password_confirm'] = 'differentpassword'
    
    def test_valid_registration_data(self):
        """Test that valid registration data passes validation."""
        serializer = UserRegistrationSerializer(data=self.valid_data)
        self.assertTrue(serializer.is_valid())
    
    def test_password_mismatch(self):
        """Test that password mismatch fails validation."""
        serializer = UserRegistrationSerializer(data=self.invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('password_confirm', serializer.errors)
    
    def test_create_user(self):
        """Test that the serializer creates a user correctly."""
        serializer = UserRegistrationSerializer(data=self.valid_data)
        self.assertTrue(serializer.is_valid())
        user = serializer.save()
        self.assertEqual(user.username, self.valid_data['username'])
        self.assertEqual(user.email, self.valid_data['email'])
        self.assertTrue(user.check_password(self.valid_data['password']))


class UserLoginSerializerTests(TestCase):
    """Test the UserLoginSerializer."""
    
    def setUp(self):
        self.user_data = {
            'username': 'loginuser',
            'email': '<EMAIL>',
            'password': 'securepassword123',
        }
        self.user = User.objects.create_user(**self.user_data)
        self.valid_data = {
            'email': '<EMAIL>',
            'password': 'securepassword123',
        }
        self.invalid_data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword',
        }
    
    def test_valid_login_data(self):
        """Test that valid login data passes validation."""
        serializer = UserLoginSerializer(data=self.valid_data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['user'], self.user)
    
    def test_invalid_login_data(self):
        """Test that invalid login data fails validation."""
        serializer = UserLoginSerializer(data=self.invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('non_field_errors', serializer.errors)


class UserProfileSerializerTests(TestCase):
    """Test the UserProfileSerializer."""
    
    def setUp(self):
        self.user_data = {
            'username': 'profileuser',
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'first_name': 'Profile',
            'last_name': 'User',
        }
        self.user = User.objects.create_user(**self.user_data)
    
    def test_profile_serializer_contains_expected_fields(self):
        """Test that UserProfileSerializer contains the expected fields."""
        serializer = UserProfileSerializer(instance=self.user)
        expected_fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name', 'bio',
            'date_of_birth', 'user_timezone', 'preferred_language',
            'ai_companion_name', 'selected_personality', 'preferred_voice',
            'allow_data_collection', 'allow_personalization', 'allow_proactive_messages',
            'created_at', 'last_active', 'enable_email_notifications',
            'enable_push_notifications', 'enable_voice_responses',
            'selected_environment', 'personality_traits'
        ]
        self.assertEqual(set(serializer.data.keys()), set(expected_fields))
    
    def test_full_name_field(self):
        """Test that the full_name field is correctly generated."""
        serializer = UserProfileSerializer(instance=self.user)
        self.assertEqual(serializer.data['full_name'], 'Profile User')


class UserPreferencesSerializerTests(TestCase):
    """Test the UserPreferencesSerializer."""
    
    def setUp(self):
        self.user_data = {
            'username': 'prefuser',
            'email': '<EMAIL>',
            'password': 'securepassword123',
        }
        self.user = User.objects.create_user(**self.user_data)
        self.valid_data = {
            'selected_personality': 'playfulCompanion',
            'preferred_voice': 'nova',
            'user_timezone': 'America/New_York',
            'preferred_language': 'es',
            'enable_email_notifications': False,
        }
    
    def test_preferences_serializer_contains_expected_fields(self):
        """Test that UserPreferencesSerializer contains the expected fields."""
        serializer = UserPreferencesSerializer(instance=self.user)
        expected_fields = [
            'selected_personality', 'preferred_voice', 'user_timezone',
            'preferred_language', 'allow_data_collection',
            'allow_personalization', 'allow_proactive_messages',
            'enable_email_notifications', 'enable_push_notifications',
            'enable_voice_responses', 'ai_companion_name',
            'selected_environment', 'personality_traits'
        ]
        self.assertEqual(set(serializer.data.keys()), set(expected_fields))
    
    def test_update_preferences(self):
        """Test that the serializer updates preferences correctly."""
        serializer = UserPreferencesSerializer(instance=self.user, data=self.valid_data, partial=True)
        self.assertTrue(serializer.is_valid())
        updated_user = serializer.save()
        self.assertEqual(updated_user.selected_personality, self.valid_data['selected_personality'])
        self.assertEqual(updated_user.preferred_voice, self.valid_data['preferred_voice'])
        self.assertEqual(updated_user.user_timezone, self.valid_data['user_timezone'])
        self.assertEqual(updated_user.preferred_language, self.valid_data['preferred_language'])
        self.assertEqual(updated_user.enable_email_notifications, self.valid_data['enable_email_notifications'])