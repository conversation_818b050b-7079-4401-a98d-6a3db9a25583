from django.test import TestCase
from django.test import TestCase
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
import json

class JWTSimpleTests(TestCase):
    """Simple tests for JWT authentication without relying on the User model"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.token_url = reverse('authentication:token_obtain_pair')
        self.token_refresh_url = reverse('authentication:token_refresh')
        self.token_verify_url = reverse('authentication:token_verify')
    
    def test_jwt_token_verify_invalid(self):
        """Test verifying invalid JWT token fails"""
        response = self.client.post(
            self.token_verify_url,
            data={'token': 'invalid.token.here'},
            format='json'
        )
        
        # Should be 401 Unauthorized or 400 Bad Request
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_400_BAD_REQUEST])
        
        # Check that the response is JSON
        self.assertEqual(response['Content-Type'], 'application/json')
        
        # Parse the response
        response_data = json.loads(response.content)
        
        # Check that the response contains an error
        self.assertTrue('error' in response_data or 'detail' in response_data or 'code' in response_data)