from django.test import TestCase
from django.test import TestCase
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
import json

class CustomExceptionHandlerSimpleTests(TestCase):
    """Simple tests for custom exception handler without relying on the User model"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.token_url = reverse('authentication:token_obtain_pair')
        self.nonexistent_url = '/api/nonexistent-endpoint/'
    
    def test_validation_error_format(self):
        """Test that validation errors follow the custom format"""
        # Send an incomplete request to trigger validation error
        response = self.client.post(
            self.token_url,
            data={},  # Missing required fields
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Check that the response is JSON
        self.assertEqual(response['Content-Type'], 'application/json')
        
        # Parse the response
        response_data = json.loads(response.content)
        
        # Check that the response contains an error
        self.assertTrue('error' in response_data or 'detail' in response_data)