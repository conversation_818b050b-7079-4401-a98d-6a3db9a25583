from django.test import TestCase
from django.test import TestCase
import pytest
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.authtoken.models import Token
from authentication.models import UserSession

User = get_user_model()


@pytest.mark.django_db
class TestUserRegistration:
    """Test user registration functionality"""
    
    def test_register_user_success(self, api_client):
        """Test successful user registration"""
        url = reverse('authentication:register')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'newpass123',
            'password_confirm': 'newpass123',
            'first_name': 'New',
            'last_name': 'User'
        }
        
        response = api_client.post(url, data)
        assert response.status_code == status.HTTP_201_CREATED
        assert 'token' in response.data
        assert User.objects.filter(username='newuser').exists()
    
    def test_register_user_password_mismatch(self, api_client):
        """Test registration with password mismatch"""
        url = reverse('authentication:register')
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password': 'newpass123',
            'password_confirm': 'differentpass',
            'first_name': 'New',
            'last_name': 'User'
        }
        
        response = api_client.post(url, data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_register_duplicate_username(self, api_client, user):
        """Test registration with duplicate username"""
        url = reverse('authentication:register')
        data = {
            'username': user.username,
            'email': '<EMAIL>',
            'password': 'newpass123',
            'password_confirm': 'newpass123'
        }
        
        response = api_client.post(url, data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.django_db
class TestUserLogin:
    """Test user login functionality"""
    
    def test_login_success(self, api_client, user):
        """Test successful login"""
        url = reverse('authentication:login')
        data = {
            'email': user.email,
            'password': 'testpass123'
        }
        
        response = api_client.post(url, data)
        assert response.status_code == status.HTTP_200_OK
        assert 'token' in response.data
        assert 'user' in response.data
    
    def test_login_invalid_credentials(self, api_client, user):
        """Test login with invalid credentials"""
        url = reverse('authentication:login')
        data = {
            'email': user.email,
            'password': 'wrongpassword'
        }
        
        response = api_client.post(url, data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_login_creates_session(self, api_client, user):
        """Test that login creates a user session"""
        url = reverse('authentication:login')
        data = {
            'email': user.email,
            'password': 'testpass123'
        }
        
        response = api_client.post(url, data)
        assert response.status_code == status.HTTP_200_OK
        assert UserSession.objects.filter(user=user).exists()


@pytest.mark.django_db
class TestUserProfile:
    """Test user profile functionality"""
    
    def test_get_profile(self, authenticated_client, user):
        """Test getting user profile"""
        url = reverse('authentication:profile')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['username'] == user.username
        assert response.data['email'] == user.email
    
    def test_update_profile(self, authenticated_client, user):
        """Test updating user profile"""
        url = reverse('authentication:profile')
        data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'bio': 'Updated bio'
        }
        
        response = authenticated_client.patch(url, data)
        assert response.status_code == status.HTTP_200_OK
        
        user.refresh_from_db()
        assert user.first_name == 'Updated'
        assert user.last_name == 'Name'
        assert user.bio == 'Updated bio'
    
    def test_profile_requires_authentication(self, api_client):
        """Test that profile endpoint requires authentication"""
        url = reverse('authentication:profile')
        response = api_client.get(url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.django_db
class TestUserPreferences:
    """Test user preferences functionality"""
    
    def test_get_preferences(self, authenticated_client, user):
        """Test getting user preferences"""
        url = reverse('authentication:preferences')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'companion_personality' in response.data
        assert 'preferred_voice' in response.data
        assert 'timezone' in response.data
        assert 'preferred_language' in response.data
        assert 'allow_data_collection' in response.data
        assert 'allow_personalization' in response.data
        assert 'allow_proactive_messages' in response.data
    
    def test_update_preferences(self, authenticated_client, user):
        """Test updating user preferences"""
        url = reverse('authentication:preferences')
        data = {
            'ai_companion_name': 'Ella',
            'preferred_voice': 'alloy',
            'enable_voice_responses': True
        }
        
        response = authenticated_client.patch(url, data)
        assert response.status_code == status.HTTP_200_OK
        
        user.refresh_from_db()
        assert user.ai_companion_name == 'Ella'
        assert user.preferred_voice == 'alloy'
        assert user.enable_voice_responses is True


@pytest.mark.django_db
class TestPasswordChange:
    """Test password change functionality"""
    
    def test_change_password_success(self, authenticated_client, user):
        """Test successful password change"""
        url = reverse('authentication:change-password')
        data = {
            'old_password': 'testpass123',
            'new_password': 'newpass456',
            'new_password_confirm': 'newpass456'
        }
        
        response = authenticated_client.post(url, data)
        assert response.status_code == status.HTTP_200_OK
        
        # Verify password was changed
        user.refresh_from_db()
        assert user.check_password('newpass456')
    
    def test_change_password_wrong_old_password(self, authenticated_client, user):
        """Test password change with wrong old password"""
        url = reverse('authentication:change-password')
        data = {
            'old_password': 'wrongpass',
            'new_password': 'newpass456',
            'new_password_confirm': 'newpass456'
        }
        
        response = authenticated_client.post(url, data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
    
    def test_change_password_mismatch(self, authenticated_client, user):
        """Test password change with password mismatch"""
        url = reverse('authentication:change-password')
        data = {
            'old_password': 'testpass123',
            'new_password': 'newpass456',
            'new_password_confirm': 'differentpass'
        }
        
        response = authenticated_client.post(url, data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.django_db
class TestUserSessions:
    """Test user session tracking"""
    
    def test_list_sessions(self, authenticated_client, user):
        """Test listing user sessions"""
        # Create a session
        UserSession.objects.create(
            user=user,
            ip_address='127.0.0.1',
            user_agent='Test Agent'
        )
        
        url = reverse('authentication:sessions')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) >= 1
    
    def test_user_stats(self, authenticated_client, user):
        """Test getting user statistics"""
        url = reverse('authentication:stats')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'total_sessions' in response.data
        assert 'account_age_days' in response.data
