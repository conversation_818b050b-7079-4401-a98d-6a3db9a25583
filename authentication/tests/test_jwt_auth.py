from django.test import TestCase
from django.test import TestCase
import json
from django.urls import reverse
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

User = get_user_model()

class JWTAuthenticationTests(TestCase):
    """Test JWT authentication functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        self.register_url = reverse('authentication:register')
        self.token_url = reverse('authentication:token_obtain_pair')
        self.token_refresh_url = reverse('authentication:token_refresh')
        self.token_verify_url = reverse('authentication:token_verify')
        
        # Create test user
        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'TestPassword123!',
            'password_confirm': 'TestPassword123!',
            'first_name': 'Test',
            'last_name': 'User'
        }
        
        # Create a user for token tests
        self.user = User.objects.create_user(
            username='existinguser',
            email='<EMAIL>',
            password='ExistingPassword123!',
            # Add default values for required fields
            selected_personality='caringFriend',
            selected_environment='cozy_room',
            owned_environments=[],
            owned_outfits=[],
            owned_pets=[],
            preferences={},
            personality_traits={}
        )
    
    def test_user_registration(self):
        """Test user registration creates a new user"""
        response = self.client.post(
            self.register_url,
            data=json.dumps(self.user_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(User.objects.filter(username='testuser').exists())
    
    def test_jwt_token_obtain(self):
        """Test obtaining JWT token with valid credentials"""
        response = self.client.post(
            self.token_url,
            data={
                'email': '<EMAIL>',
                'password': 'ExistingPassword123!'
            },
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)
    
    def test_jwt_token_obtain_invalid_credentials(self):
        """Test obtaining JWT token with invalid credentials fails"""
        response = self.client.post(
            self.token_url,
            data={
                'email': '<EMAIL>',
                'password': 'WrongPassword123!'
            },
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_jwt_token_refresh(self):
        """Test refreshing JWT token"""
        # First obtain tokens
        response = self.client.post(
            self.token_url,
            data={
                'email': '<EMAIL>',
                'password': 'ExistingPassword123!'
            },
            format='json'
        )
        
        refresh_token = response.data['refresh']
        
        # Then use refresh token to get new access token
        response = self.client.post(
            self.token_refresh_url,
            data={'refresh': refresh_token},
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
    
    def test_jwt_token_verify(self):
        """Test verifying JWT token"""
        # First obtain tokens
        response = self.client.post(
            self.token_url,
            data={
                'email': '<EMAIL>',
                'password': 'ExistingPassword123!'
            },
            format='json'
        )
        
        access_token = response.data['access']
        
        # Then verify the token
        response = self.client.post(
            self.token_verify_url,
            data={'token': access_token},
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_jwt_token_verify_invalid(self):
        """Test verifying invalid JWT token fails"""
        response = self.client.post(
            self.token_verify_url,
            data={'token': 'invalid.token.here'},
            format='json'
        )
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_protected_endpoint_with_jwt(self):
        """Test accessing protected endpoint with JWT token"""
        # Create a protected endpoint for testing
        profile_url = reverse('user:profile')
        
        # First obtain tokens
        response = self.client.post(
            self.token_url,
            data={
                'email': '<EMAIL>',
                'password': 'ExistingPassword123!'
            },
            format='json'
        )
        
        access_token = response.data['access']
        
        # Access protected endpoint without token
        response = self.client.get(profile_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Access protected endpoint with token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        response = self.client.get(profile_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)