from django.test import TestCase
from django.test import TestCase
from django.test import TestCase
from django.contrib.auth import get_user_model
from authentication.models import UserSession
from django.utils import timezone
import datetime

User = get_user_model()


class UserModelTests(TestCase):
    """Test the User model."""
    
    def setUp(self):
        self.user_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password': 'securepassword123',
            'first_name': 'Test',
            'last_name': 'User',
        }
        self.user = User.objects.create_user(**self.user_data)
    
    def test_create_user(self):
        """Test creating a user."""
        self.assertEqual(self.user.username, self.user_data['username'])
        self.assertEqual(self.user.email, self.user_data['email'])
        self.assertTrue(self.user.check_password(self.user_data['password']))
        self.assertTrue(self.user.is_active)
        self.assertFalse(self.user.is_staff)
        self.assertFalse(self.user.is_superuser)
    
    def test_get_full_name(self):
        """Test the get_full_name method."""
        self.assertEqual(self.user.get_full_name(), 'Test User')
        
        # Test with empty first and last name
        user = User.objects.create_user(
            username='noname',
            email='<EMAIL>',
            password='securepassword123',
            first_name='',
            last_name=''
        )
        self.assertEqual(user.get_full_name(), 'noname')
    
    def test_update_last_active(self):
        """Test the update_last_active method."""
        old_last_active = self.user.last_active
        # Ensure some time passes
        self.user.last_active = timezone.now() - datetime.timedelta(hours=1)
        self.user.save()
        
        self.user.update_last_active()
        self.assertGreater(self.user.last_active, old_last_active)
    
    def test_default_values(self):
        """Test the default values for User model fields."""
        self.assertEqual(self.user.user_timezone, 'UTC')
        self.assertEqual(self.user.preferred_language, 'en')
        self.assertEqual(self.user.ai_companion_name, 'Ella')
        self.assertEqual(self.user.selected_personality, 'caringFriend')
        self.assertEqual(self.user.selected_environment, 'cozy_room')
        self.assertTrue(self.user.enable_email_notifications)
        self.assertTrue(self.user.enable_push_notifications)
        self.assertTrue(self.user.enable_voice_responses)
        self.assertTrue(self.user.allow_data_collection)
        self.assertTrue(self.user.allow_personalization)
        self.assertTrue(self.user.allow_proactive_messages)


class UserSessionModelTests(TestCase):
    """Test the UserSession model."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='sessionuser',
            email='<EMAIL>',
            password='securepassword123'
        )
        self.session = UserSession.objects.create(
            user=self.user,
            session_key='test-session-key',
            ip_address='127.0.0.1',
            user_agent='Test User Agent',
            device_type='desktop'
        )
    
    def test_create_session(self):
        """Test creating a user session."""
        self.assertEqual(self.session.user, self.user)
        self.assertEqual(self.session.session_key, 'test-session-key')
        self.assertEqual(self.session.ip_address, '127.0.0.1')
        self.assertEqual(self.session.user_agent, 'Test User Agent')
        self.assertEqual(self.session.device_type, 'desktop')
        self.assertTrue(self.session.is_active)
    
    def test_session_string_representation(self):
        """Test the string representation of a user session."""
        expected_string = f"{self.user.email} - desktop - {self.session.created_at}"
        self.assertEqual(str(self.session), expected_string)
    
    def test_session_ordering(self):
        """Test that sessions are ordered by last_activity in descending order."""
        # Create a second session
        second_session = UserSession.objects.create(
            user=self.user,
            session_key='second-session-key',
            ip_address='127.0.0.1',
            user_agent='Test User Agent',
            device_type='mobile'
        )
        
        # Explicitly set the last_activity times with a clear difference
        now = timezone.now()
        one_hour_ago = now - datetime.timedelta(hours=1)
        
        # Set the first session to be older
        self.session.last_activity = one_hour_ago
        self.session.save()
        
        # Set the second session to be newer
        second_session.last_activity = now
        second_session.save()
        
        # Get all sessions ordered by last_activity (descending)
        sessions = list(UserSession.objects.all().order_by('-last_activity'))
        
        # Verify the order - newer session should be first
        self.assertEqual(sessions[0].session_key, second_session.session_key)
        self.assertEqual(sessions[1].session_key, self.session.session_key)
        
        # Verify the timestamps
        self.assertGreater(sessions[0].last_activity, sessions[1].last_activity)