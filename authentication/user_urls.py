from django.urls import path
from . import views

app_name = 'user'

urlpatterns = [
    # Profile management
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('preferences/', views.UserPreferencesView.as_view(), name='preferences'),
    path('change-password/', views.PasswordChangeView.as_view(), name='change-password'),

    # Settings endpoints (aliases for frontend compatibility)
    path('settings/', views.UserPreferencesView.as_view(), name='settings'),

    # User progress management
    path('progress/', views.UserProgressView.as_view(), name='progress'),
    path('progress/update/', views.UserProgressUpdateView.as_view(), name='progress-update'),

    # Gamification aliases (for frontend compatibility)
    path('level/', views.user_level_alias, name='level'),
    path('achievements/', views.user_achievements_alias, name='achievements'),
    path('inventory/', views.user_inventory_alias, name='inventory'),

    # Wallet management (aliases to gamification endpoints)
    path('wallet/', views.UserWalletView.as_view(), name='wallet'),
    path('wallet/add-currency/', views.add_currency_alias, name='wallet-add-currency'),

    # Session management
    path('sessions/', views.UserSessionListView.as_view(), name='sessions'),
    path('stats/', views.user_stats_view, name='stats'),
]
