from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from django.utils.timezone import now
import uuid


class User(AbstractUser):
    """Extended user model for the AI companion app."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=30, blank=True)
    last_name = models.CharField(max_length=30, blank=True)
    
    # Profile information
    date_of_birth = models.DateField(null=True, blank=True)
    user_timezone = models.CharField(max_length=50, default='UTC')
    timezone = property(lambda self: self.user_timezone, 
                       lambda self, value: setattr(self, 'user_timezone', value))
    preferred_language = models.CharField(max_length=10, default='en')
    ai_companion_name = models.CharField(max_length=50, default='Ella')
    bio = models.TextField(blank=True, default='')
    
    # Notification preferences
    enable_email_notifications = models.BooleanField(default=True)
    enable_push_notifications = models.BooleanField(default=True)
    enable_voice_responses = models.BooleanField(default=True)
    
    # AI companion preferences - Updated to match Flutter model
    selected_personality = models.CharField(
        max_length=50, 
        choices=[
            ('caringFriend', 'Caring Friend'),
            ('playfulCompanion', 'Playful Companion'),
            ('wiseMentor', 'Wise Mentor'),
            ('romanticPartner', 'Romantic Partner'),
            ('supportiveTherapist', 'Supportive Therapist'),
        ],
        default='caringFriend'
    )
    selected_environment = models.CharField(max_length=100, default='cozy_room')
    owned_environments = models.JSONField(default=list, blank=True)
    owned_outfits = models.JSONField(default=list, blank=True)
    owned_pets = models.JSONField(default=list, blank=True)
    preferences = models.JSONField(default=dict, blank=True)
    
    # Legacy field for backward compatibility
    companion_personality = models.CharField(
        max_length=50, 
        choices=[
            ('supportive', 'Sweet & Supportive'),
            ('confident', 'Confident & Assertive'),
            ('playful', 'Playful & Fun'),
            ('intellectual', 'Intellectual & Thoughtful'),
            ('caring', 'Caring & Empathetic'),
        ],
        default='supportive'
    )
    personality_traits = models.JSONField(default=dict, blank=True, help_text="Key-value pairs of personality traits")
    
    # Voice preferences
    preferred_voice = models.CharField(
        max_length=50,
        choices=[
            ('alloy', 'Alloy'),
            ('echo', 'Echo'),
            ('fable', 'Fable'),
            ('onyx', 'Onyx'),
            ('nova', 'Nova'),
            ('shimmer', 'Shimmer'),
        ],
        default='nova'
    )
    
    # Privacy settings
    allow_data_collection = models.BooleanField(default=True)
    allow_personalization = models.BooleanField(default=True)
    allow_proactive_messages = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_active = models.DateTimeField(default=now)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']
    
    class Meta:
        db_table = 'auth_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
    
    def __str__(self):
        return self.email
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip() or self.username
    
    def update_last_active(self):
        """Update the last active timestamp."""
        self.last_active = timezone.now()
        self.save(update_fields=['last_active'])


class UserSession(models.Model):
    """Track user sessions for analytics and security."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    device_type = models.CharField(
        max_length=20,
        choices=[
            ('mobile', 'Mobile'),
            ('tablet', 'Tablet'),
            ('desktop', 'Desktop'),
            ('unknown', 'Unknown'),
        ],
        default='unknown'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'user_sessions'
        ordering = ['-last_activity']
    
    def __str__(self):
        return f"{self.user.email} - {self.device_type} - {self.created_at}"
