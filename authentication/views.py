from rest_framework import status, generics, permissions, views
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken, TokenError
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.contrib.auth import login, logout
from django.utils import timezone
from django.contrib.sessions.models import Session
from django.conf import settings
import jwt
import requests
import json
import logging
from .models import User, UserSession
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer,
    UserPreferencesSerializer, UserSessionSerializer, PasswordChangeSerializer,
    CustomTokenObtainPairSerializer, UserSerializer, GoogleAuthSerializer,
    AppleAuthSerializer
)
from gamification.models import UserProgress, Wallet
from gamification.serializers import UserProgressSerializer, WalletSerializer

logger = logging.getLogger(__name__)


class UserRegistrationView(generics.CreateAPIView):
    """User registration endpoint."""
    
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        
        # Create user session
        self._create_user_session(request, user)
        
        logger.info(f"New user registered: {user.email}")
        
        return Response({
            'user': UserProfileSerializer(user).data,
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            },
            'message': 'Registration successful'
        }, status=status.HTTP_201_CREATED)
    
    def _create_user_session(self, request, user):
        """Create a user session record."""
        try:
            UserSession.objects.create(
                user=user,
                session_key=request.session.session_key or '',
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                device_type=self._detect_device_type(request.META.get('HTTP_USER_AGENT', ''))
            )
        except Exception as e:
            logger.error(f"Failed to create user session: {e}")
    
    def _get_client_ip(self, request):
        """Get client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _detect_device_type(self, user_agent):
        """Simple device type detection."""
        user_agent = user_agent.lower()
        if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
            return 'mobile'
        elif 'tablet' in user_agent or 'ipad' in user_agent:
            return 'tablet'
        elif 'desktop' in user_agent or 'windows' in user_agent or 'mac' in user_agent:
            return 'desktop'
        return 'unknown'


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom token obtain pair view that uses our custom serializer."""
    serializer_class = CustomTokenObtainPairSerializer


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def login_view(request):
    """User login endpoint using JWT."""
    
    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']
        
        # Update last active timestamp
        user.update_last_active()
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        
        # Create user session
        try:
            UserSession.objects.create(
                user=user,
                session_key=request.session.session_key or '',
                ip_address=_get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                device_type=_detect_device_type(request.META.get('HTTP_USER_AGENT', ''))
            )
        except Exception as e:
            logger.error(f"Failed to create user session: {e}")
        
        logger.info(f"User logged in: {user.email}")
        
        return Response({
            'user': UserProfileSerializer(user).data,
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            },
            'message': 'Login successful'
        }, status=status.HTTP_200_OK)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(views.APIView):
    """User logout endpoint that blacklists the refresh token."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        try:
            # Get the refresh token from request
            refresh_token = request.data.get('refresh')
            if not refresh_token:
                return Response({"error": "Refresh token is required"}, status=status.HTTP_400_BAD_REQUEST)
            
            # Blacklist the token
            token = RefreshToken(refresh_token)
            token.blacklist()
            
            # Mark user sessions as inactive
            UserSession.objects.filter(
                user=request.user,
                is_active=True
            ).update(is_active=False)
            
            logger.info(f"User logged out: {request.user.email}")
            
            return Response({
                'message': 'Logout successful'
            }, status=status.HTTP_200_OK)
        
        except TokenError as e:
            logger.error(f"Logout error: {e}")
            return Response({
                'error': 'Invalid token'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return Response({
                'error': 'Logout failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile view and update endpoint.
    
    This endpoint allows users to retrieve and update their profile information.
    
    GET: Retrieve the user's profile
    PUT/PATCH: Update the user's profile
    """
    
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        return self.request.user
    
    def retrieve(self, request, *args, **kwargs):
        """Retrieve user profile with additional information."""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        # Log profile access
        logger.info(f"User profile accessed: {instance.email}")
        
        return Response({
            'status': 'success',
            'message': 'Profile retrieved successfully',
            'profile': serializer.data
        })
    
    def update(self, request, *args, **kwargs):
        """Update user profile with validation."""
        partial = kwargs.pop('partial', True)  # Allow partial updates by default
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        # Log profile update
        logger.info(f"User profile updated: {instance.email}")
        
        return Response({
            'status': 'success',
            'message': 'Profile updated successfully',
            'profile': serializer.data
        })


class UserPreferencesView(generics.RetrieveUpdateAPIView):
    """User preferences retrieve and update endpoint.
    
    This endpoint allows users to retrieve and update their preferences.
    
    GET: Retrieve the user's preferences
    PUT/PATCH: Update the user's preferences
    """
    
    serializer_class = UserPreferencesSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        return self.request.user
    
    def retrieve(self, request, *args, **kwargs):
        """Retrieve user preferences."""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        # Log preferences access
        logger.info(f"User preferences accessed: {instance.email}")
        
        return Response({
            'status': 'success',
            'message': 'Preferences retrieved successfully',
            'preferences': serializer.data
        })
    
    def update(self, request, *args, **kwargs):
        """Update user preferences with validation."""
        partial = kwargs.pop('partial', True)  # Allow partial updates
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        # Log preferences update
        logger.info(f"User preferences updated: {instance.email}")
        
        return Response({
            'status': 'success',
            'message': 'Preferences updated successfully',
            'preferences': serializer.data
        })


class PasswordChangeView(generics.UpdateAPIView):
    """Password change endpoint.
    
    Supports both PATCH and POST methods for backward compatibility.
    """
    
    serializer_class = PasswordChangeSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, *args, **kwargs):
        """Handle POST requests for password change."""
        return self.update(request, *args, **kwargs)
    
    def get_object(self):
        return self.request.user
    
    def update(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        
        # Invalidate all existing tokens
        Token.objects.filter(user=request.user).delete()
        
        # Create new token
        token = Token.objects.create(user=request.user)
        
        logger.info(f"Password changed for user: {request.user.email}")
        
        return Response({
            'message': 'Password changed successfully',
            'token': token.key
        })


class UserSessionListView(generics.ListAPIView):
    """List user sessions endpoint."""
    
    serializer_class = UserSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserSession.objects.filter(user=self.request.user)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_stats_view(request):
    """Get user statistics."""
    
    user = request.user
    
    # Calculate days since registration
    days_since_registration = (timezone.now() - user.created_at).days
    
    # Get session count
    session_count = UserSession.objects.filter(user=user).count()
    
    # Get active sessions
    active_sessions = UserSession.objects.filter(user=user, is_active=True).count()
    
    stats = {
        'days_since_registration': days_since_registration,
        'account_age_days': days_since_registration,  # Alias for backward compatibility
        'total_sessions': session_count,
        'active_sessions': active_sessions,
        'last_active': user.last_active,
        'companion_personality': user.companion_personality,
        'preferred_voice': user.preferred_voice,
    }
    
    return Response(stats)


class GoogleAuthView(views.APIView):
    """Google OAuth authentication endpoint."""
    
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = GoogleAuthSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        id_token = serializer.validated_data['id_token']
        
        try:
            # Verify the token with Google
            google_response = requests.get(
                f'https://oauth2.googleapis.com/tokeninfo?id_token={id_token}'
            )
            
            if google_response.status_code != 200:
                return Response({
                    'error': 'Invalid Google token'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            google_data = google_response.json()
            
            # Get or create user
            try:
                user = User.objects.get(email=google_data['email'])
                # Update user info if needed
                if not user.first_name and 'given_name' in google_data:
                    user.first_name = google_data['given_name']
                if not user.last_name and 'family_name' in google_data:
                    user.last_name = google_data['family_name']
                user.save(update_fields=['first_name', 'last_name'])
            except User.DoesNotExist:
                # Create new user
                username = google_data.get('email').split('@')[0]
                # Ensure username is unique
                base_username = username
                counter = 1
                while User.objects.filter(username=username).exists():
                    username = f"{base_username}{counter}"
                    counter += 1
                
                user = User.objects.create_user(
                    username=username,
                    email=google_data['email'],
                    first_name=google_data.get('given_name', ''),
                    last_name=google_data.get('family_name', ''),
                    # Set a random password as the user will login via Google
                    password=User.objects.make_random_password()
                )
            
            # Update last active timestamp
            user.update_last_active()
            
            # Generate JWT tokens
            tokens = serializer.get_tokens_for_user(user)
            
            # Create user session
            try:
                UserSession.objects.create(
                    user=user,
                    session_key=request.session.session_key or '',
                    ip_address=_get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    device_type=_detect_device_type(request.META.get('HTTP_USER_AGENT', ''))
                )
            except Exception as e:
                logger.error(f"Failed to create user session: {e}")
            
            logger.info(f"User logged in via Google: {user.email}")
            
            return Response({
                'user': UserProfileSerializer(user).data,
                'tokens': tokens,
                'message': 'Google authentication successful'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Google auth error: {e}")
            return Response({
                'error': 'Google authentication failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AppleAuthView(views.APIView):
    """Apple Sign In authentication endpoint."""
    
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = AppleAuthSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        identity_token = serializer.validated_data['identity_token']
        authorization_code = serializer.validated_data['authorization_code']
        email = serializer.validated_data.get('email', '')
        first_name = serializer.validated_data.get('first_name', '')
        last_name = serializer.validated_data.get('last_name', '')
        
        try:
            # Decode the token (in a real implementation, you would verify with Apple)
            # This is a simplified version
            try:
                # Decode without verification for demo purposes
                # In production, you should verify with Apple's public key
                decoded_token = jwt.decode(identity_token, options={"verify_signature": False})

                # Extract email from token or use provided email
                token_email = decoded_token.get('email')
                user_email = email or token_email
                if not user_email:
                    return Response({
                        'error': 'Email not found in Apple token'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Get or create user
                try:
                    user = User.objects.get(email=user_email)
                    # Update user info if provided and not already set
                    if first_name and not user.first_name:
                        user.first_name = first_name
                    if last_name and not user.last_name:
                        user.last_name = last_name
                    user.save(update_fields=['first_name', 'last_name'])
                except User.DoesNotExist:
                    # Create new user
                    username = user_email.split('@')[0]
                    # Ensure username is unique
                    base_username = username
                    counter = 1
                    while User.objects.filter(username=username).exists():
                        username = f"{base_username}{counter}"
                        counter += 1
                    
                    user = User.objects.create_user(
                        username=username,
                        email=user_email,
                        first_name=first_name,
                        last_name=last_name,
                        # Set a random password as the user will login via Apple
                        password=User.objects.make_random_password()
                    )
                
                # Update last active timestamp
                user.update_last_active()
                
                # Generate JWT tokens
                tokens = serializer.get_tokens_for_user(user)
                
                # Create user session
                try:
                    UserSession.objects.create(
                        user=user,
                        session_key=request.session.session_key or '',
                        ip_address=_get_client_ip(request),
                        user_agent=request.META.get('HTTP_USER_AGENT', ''),
                        device_type=_detect_device_type(request.META.get('HTTP_USER_AGENT', ''))
                    )
                except Exception as e:
                    logger.error(f"Failed to create user session: {e}")
                
                logger.info(f"User logged in via Apple: {user.email}")
                
                return Response({
                    'user': UserProfileSerializer(user).data,
                    'tokens': tokens,
                    'message': 'Apple authentication successful'
                }, status=status.HTTP_200_OK)
                
            except jwt.PyJWTError as e:
                logger.error(f"Apple token decode error: {e}")
                return Response({
                    'error': 'Invalid Apple token'
                }, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            logger.error(f"Apple auth error: {e}")
            return Response({
                'error': 'Apple authentication failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Helper functions
def _get_client_ip(request):
    """Get client IP address."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def _detect_device_type(user_agent):
    """Simple device type detection."""
    user_agent = user_agent.lower()
    if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
        return 'mobile'
    elif 'tablet' in user_agent or 'ipad' in user_agent:
        return 'tablet'
    elif 'desktop' in user_agent or 'windows' in user_agent or 'mac' in user_agent:
        return 'desktop'
    return 'unknown'


class UserProgressView(generics.RetrieveUpdateAPIView):
    """User progress view and update endpoint.
    
    This endpoint allows users to retrieve and update their progress information.
    
    GET: Retrieve the user's progress
    PUT/PATCH: Update the user's progress
    """
    
    serializer_class = UserProgressSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        """Get or create user progress."""
        user = self.request.user
        progress, created = UserProgress.objects.get_or_create(user=user)
        return progress
    
    def retrieve(self, request, *args, **kwargs):
        """Retrieve user progress with additional information."""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        # Get wallet information if available
        wallet_data = None
        try:
            wallet = Wallet.objects.get(user=request.user)
            wallet_data = WalletSerializer(wallet).data
        except Wallet.DoesNotExist:
            pass
        
        # Log progress access
        logger.info(f"User progress accessed: {request.user.email}")
        
        return Response({
            'status': 'success',
            'message': 'Progress retrieved successfully',
            'progress': serializer.data,
            'wallet': wallet_data
        })
    
    def update(self, request, *args, **kwargs):
        """Update user progress with validation.
        
        Only certain fields can be updated directly by the user.
        """
        partial = kwargs.pop('partial', True)  # Allow partial updates
        instance = self.get_object()
        
        # Only allow updating certain fields
        allowed_fields = ['total_time_spent']
        data = {k: v for k, v in request.data.items() if k in allowed_fields}
        
        serializer = self.get_serializer(instance, data=data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        # Log progress update
        logger.info(f"User progress updated: {request.user.email}")
        
        return Response({
            'status': 'success',
            'message': 'Progress updated successfully',
            'progress': serializer.data
        })


class UserProgressUpdateView(views.APIView):
    """API endpoint for updating specific user progress metrics.
    
    This endpoint allows controlled updates to user progress metrics like XP and hearts.
    """
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """Update user progress based on action type."""
        action = request.data.get('action')
        amount = request.data.get('amount', 0)
        
        try:
            amount = int(amount)
        except (TypeError, ValueError):
            return Response({
                'status': 'error',
                'message': 'Amount must be a valid integer'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get or create user progress
        progress, created = UserProgress.objects.get_or_create(user=request.user)
        
        # Process different action types
        if action == 'add_xp':
            if amount <= 0:
                return Response({
                    'status': 'error',
                    'message': 'XP amount must be positive'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            new_level = progress.add_xp(amount)
            level_up = new_level > progress.level
            
            return Response({
                'status': 'success',
                'message': 'XP added successfully',
                'xp_added': amount,
                'current_xp': progress.xp,
                'level': new_level,
                'level_up': level_up,
                'xp_to_next_level': progress.xp_to_next_level
            })
            
        elif action == 'add_hearts':
            if amount <= 0:
                return Response({
                    'status': 'error',
                    'message': 'Hearts amount must be positive'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            progress.hearts += amount
            progress.save(update_fields=['hearts', 'updated_at'])
            
            return Response({
                'status': 'success',
                'message': 'Hearts added successfully',
                'hearts_added': amount,
                'current_hearts': progress.hearts
            })
            
        elif action == 'increment_message':
            is_voice = request.data.get('is_voice', False)
            progress.increment_message_count(is_voice=is_voice)
            
            return Response({
                'status': 'success',
                'message': 'Message count incremented',
                'messages_count': progress.messages_count,
                'voice_messages_count': progress.voice_messages_count
            })
            
        elif action == 'add_time_spent':
            if amount <= 0:
                return Response({
                    'status': 'error',
                    'message': 'Time amount must be positive'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            progress.add_time_spent(amount)
            
            return Response({
                'status': 'success',
                'message': 'Time spent updated',
                'seconds_added': amount,
                'total_time_spent': progress.total_time_spent
            })
            
        else:
            return Response({
                'status': 'error',
                'message': 'Invalid action type'
            }, status=status.HTTP_400_BAD_REQUEST)


# Wallet views (aliases to gamification endpoints for frontend compatibility)
class UserWalletView(generics.RetrieveAPIView):
    """Get user's wallet information (alias to gamification wallet)"""
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, *args, **kwargs):
        from gamification.models import Wallet
        from gamification.serializers import WalletSerializer

        wallet, created = Wallet.objects.get_or_create(user=request.user)
        serializer = WalletSerializer(wallet)

        return Response({
            'status': 'success',
            'wallet': serializer.data
        })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def add_currency_alias(request):
    """Add currency to user's wallet (alias to gamification endpoint)"""
    from gamification.views import add_currency
    return add_currency(request)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_level_alias(request):
    """Get user level (alias to gamification endpoint)"""
    from gamification.views import UserLevelView
    view = UserLevelView.as_view()
    return view(request)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_achievements_alias(request):
    """Get user achievements (alias to gamification endpoint)"""
    from gamification.views import UserAchievementListView
    view = UserAchievementListView.as_view()
    return view(request)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_inventory_alias(request):
    """Get user inventory (alias to gamification endpoint)"""
    from gamification.views import UserInventoryListView
    view = UserInventoryListView.as_view()
    return view(request)
