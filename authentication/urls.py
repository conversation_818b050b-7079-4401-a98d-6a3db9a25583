from django.urls import path
from . import views
from rest_framework_simplejwt.views import (
    TokenRefreshView,
    TokenVerifyView,
)

app_name = 'authentication'

urlpatterns = [
    # Authentication endpoints
    path('register/', views.UserRegistrationView.as_view(), name='register'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.LogoutView.as_view(), name='logout'),
    
    # Social authentication endpoints
    path('google/', views.GoogleAuthView.as_view(), name='google_auth'),
    path('apple/', views.AppleAuthView.as_view(), name='apple_auth'),
    
    # JWT Token endpoints
    path('token/', views.CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/verify/', TokenVerifyView.as_view(), name='token_verify'),

    # Frontend-compatible token refresh endpoint
    path('refresh/', TokenRefreshView.as_view(), name='refresh'),
]
