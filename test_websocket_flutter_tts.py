#!/usr/bin/env python3
"""
WebSocket TTS Test for Flutter Integration
Tests the exact WebSocket flow that <PERSON>lut<PERSON> will use.
"""
import os
import sys
import django
import asyncio
import json
import time
import uuid
from channels.testing import WebsocketCommunicator
from channels.routing import URLRouter
from django.urls import re_path

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.consumers import ChatConsumer
from authentication.models import User
from asgiref.sync import sync_to_async


async def test_flutter_websocket_tts():
    """Test WebSocket TTS integration exactly as <PERSON>lut<PERSON> will use it."""
    print("🧪 Testing WebSocket TTS Integration for Flutter")
    print("=" * 60)
    
    # Create test user
    try:
        user = await sync_to_async(User.objects.filter(email__icontains='test').first)()
        if not user:
            print("❌ No test user found. Creating one...")
            user = await sync_to_async(User.objects.create_user)(
                username=f"fluttertest_{uuid.uuid4().hex[:8]}",
                email="<EMAIL>",
                password="testpass123"
            )
        print(f"👤 Using user: {user.email}")
    except Exception as e:
        print(f"❌ Error with user: {e}")
        return
    
    # Create WebSocket application
    application = URLRouter([
        re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
    ])
    
    # Test cases that Flutter will send
    flutter_test_cases = [
        {
            'name': 'Quick Greeting',
            'message': 'Hello!',
            'expected_tts_chunks': 7,
            'max_wait_seconds': 20,  # Increased timeout
            'expected_text_ms': 500,
            'expected_audio_ms': 1500
        },
        {
            'name': 'Question',
            'message': 'How are you today?',
            'expected_tts_chunks': 11,
            'max_wait_seconds': 25,  # Increased timeout
            'expected_text_ms': 500,
            'expected_audio_ms': 1500
        },
        {
            'name': 'Longer Request',
            'message': 'Can you help me plan a trip to Japan?',
            'expected_tts_chunks': 12,
            'max_wait_seconds': 35,  # Much longer timeout for complex responses
            'expected_text_ms': 500,
            'expected_audio_ms': 2500  # Allow more time for longer TTS
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(flutter_test_cases, 1):
        print(f"\n🧪 Flutter Test {i}: {test_case['name']}")
        print(f"📱 Message: '{test_case['message']}'")
        
        # Create fresh communicator for each test
        communicator = WebsocketCommunicator(application, "ws/chat/")
        communicator.scope["user"] = user
        
        try:
            # Connect (Flutter app connecting)
            connected, subprotocol = await communicator.connect()
            if not connected:
                print("❌ Failed to connect WebSocket")
                continue
            
            # Receive connection confirmation
            response = await communicator.receive_json_from()
            if response.get('type') != 'connection_established':
                print(f"❌ Unexpected connection response: {response}")
                continue
            
            print("✅ WebSocket connected")
            
            # Send message (Flutter sending user input)
            start_time = time.time()
            conversation_id = str(uuid.uuid4())  # Use proper UUID format
            
            # Use unique timestamp to bypass cache
            import time as time_module
            unique_message = f"{test_case['message']} at {int(time_module.time() * 1000)}"

            await communicator.send_json_to({
                'type': 'text_message',
                'content': unique_message,
                'conversation_id': conversation_id
            })
            
            # Track metrics Flutter cares about
            first_text_time = None
            first_audio_time = None
            text_chunks = 0
            audio_chunks = 0
            total_audio_size = 0
            response_complete = False
            
            # Listen for responses (Flutter receiving)
            max_wait = test_case['max_wait_seconds']
            while not response_complete and (time.time() - start_time) < max_wait:
                try:
                    # Longer individual timeout for complex responses
                    individual_timeout = 12.0 if 'Longer' in test_case['name'] else 8.0
                    response = await asyncio.wait_for(
                        communicator.receive_json_from(),
                        timeout=individual_timeout
                    )
                    
                    elapsed_ms = (time.time() - start_time) * 1000
                    response_type = response.get('type')
                    
                    # Handle text responses (Flutter will display these)
                    if response_type in ['ai_message_chunk', 'llm_response_chunk']:
                        if first_text_time is None:
                            first_text_time = elapsed_ms
                            print(f"📝 First text: {first_text_time:.0f}ms")
                        text_chunks += 1
                        content = response.get('content', '')
                        if text_chunks == 1:
                            print(f"   Text: '{content[:50]}{'...' if len(content) > 50 else ''}'")

                    # Handle audio responses (Flutter will play these)
                    elif response_type == 'audio_chunk':
                        if first_audio_time is None:
                            first_audio_time = elapsed_ms
                            print(f"🎵 First audio: {first_audio_time:.0f}ms")
                        audio_chunks += 1
                        audio_data = response.get('data', '')
                        total_audio_size += len(audio_data)
                        is_final = response.get('is_final', False)
                        
                        if audio_chunks <= 2 or is_final:
                            print(f"   Audio chunk {audio_chunks}: {len(audio_data)} bytes (final: {is_final})")
                        
                        if is_final:
                            print(f"🎵 Audio complete: {audio_chunks} chunks, {total_audio_size:,} bytes")
                    
                    # Handle completion
                    elif response_type == 'ai_message_complete':
                        print("✅ Response complete")
                        response_complete = True
                        break
                    
                    # Handle errors (Flutter needs to handle these)
                    elif response_type == 'tts_error':
                        print(f"❌ TTS Error: {response.get('error')}")
                    
                    # Skip non-essential responses
                    elif response_type in ['ai_typing', 'message_received']:
                        continue

                    else:
                        # Log all other response types for debugging
                        print(f"📦 Other: {response_type}")
                
                except asyncio.TimeoutError:
                    print("⏰ Response timeout")
                    break
                except Exception as e:
                    print(f"❌ Error receiving: {e}")
                    break
            
            # Calculate results
            total_time = (time.time() - start_time) * 1000
            
            # Store results for Flutter performance analysis
            result = {
                'test_name': test_case['name'],
                'message': test_case['message'],
                'total_time_ms': total_time,
                'first_text_ms': first_text_time,
                'first_audio_ms': first_audio_time,
                'text_chunks': text_chunks,
                'audio_chunks': audio_chunks,
                'total_audio_bytes': total_audio_size,
                'success': audio_chunks > 0 and text_chunks > 0
            }
            results.append(result)
            
            # Print Flutter-relevant results
            print(f"\n📊 Flutter Results:")
            print(f"   Total time: {total_time:.0f}ms")
            print(f"   Text chunks: {text_chunks}")
            print(f"   Audio chunks: {audio_chunks}")
            print(f"   Audio size: {total_audio_size:,} bytes")
            
            # Flutter performance targets
            expected_text = test_case.get('expected_text_ms', 500)
            expected_audio = test_case.get('expected_audio_ms', 2000)

            if first_text_time:
                text_status = "✅" if first_text_time <= expected_text else "⚠️"
                print(f"   {text_status} Text latency: {first_text_time:.0f}ms (target: ≤{expected_text}ms)")

            if first_audio_time:
                audio_status = "✅" if first_audio_time <= expected_audio else "⚠️"
                print(f"   {audio_status} Audio latency: {first_audio_time:.0f}ms (target: ≤{expected_audio}ms)")

            success_status = "✅" if result['success'] else "❌"
            print(f"   {success_status} Overall: {'SUCCESS' if result['success'] else 'FAILED'}")
        
        except Exception as e:
            print(f"❌ Test failed: {e}")
            results.append({
                'test_name': test_case['name'],
                'success': False,
                'error': str(e)
            })
        
        finally:
            try:
                await communicator.disconnect()
            except:
                pass  # Ignore disconnect errors
        
        # Wait between tests
        await asyncio.sleep(1)
    
    # Print Flutter Integration Summary
    print(f"\n🚀 FLUTTER INTEGRATION SUMMARY")
    print("=" * 60)
    
    successful_tests = [r for r in results if r.get('success', False)]
    success_rate = len(successful_tests) / len(results) * 100
    
    print(f"✅ Success Rate: {success_rate:.0f}% ({len(successful_tests)}/{len(results)})")
    
    if successful_tests:
        avg_text = sum(r['first_text_ms'] for r in successful_tests if r['first_text_ms']) / len([r for r in successful_tests if r['first_text_ms']])
        avg_audio = sum(r['first_audio_ms'] for r in successful_tests if r['first_audio_ms']) / len([r for r in successful_tests if r['first_audio_ms']])
        
        print(f"📝 Average Text Latency: {avg_text:.0f}ms")
        print(f"🎵 Average Audio Latency: {avg_audio:.0f}ms")
        
        total_audio = sum(r['total_audio_bytes'] for r in successful_tests)
        print(f"🎵 Total Audio Generated: {total_audio:,} bytes")
    
    print(f"\n🎯 FLUTTER READINESS:")
    if success_rate >= 80:
        print("✅ WebSocket TTS integration is READY for Flutter!")
        print("✅ Audio streaming will work smoothly in the mobile app")
        print("✅ Text responses are fast enough for real-time chat")
    else:
        print("⚠️ WebSocket integration needs optimization before Flutter deployment")
    
    return results


if __name__ == '__main__':
    asyncio.run(test_flutter_websocket_tts())
