#!/usr/bin/env python3
"""
ElevenLabs Conversation Audio Library Creator
Creates realistic conversation scenarios for session-based emotion testing.
"""
import os
import requests
import json
import time
from typing import Dict, List, Tuple
from pathlib import Path

# ElevenLabs Configuration
ELEVENLABS_API_KEY = "***************************************************"
VOICE_ID = "5l5f8iK3YPeGga21rQIX"
ELEVENLABS_URL = "https://api.elevenlabs.io/v1/text-to-speech"

# Create conversation library directory
CONVERSATION_LIBRARY_DIR = Path("conversation_audio_library")
CONVERSATION_LIBRARY_DIR.mkdir(exist_ok=True)


def create_conversation_audio(text: str, filename: str, voice_settings: Dict = None) -> bool:
    """Create conversation audio using ElevenLabs v3."""
    
    if voice_settings is None:
        voice_settings = {
            "stability": 0.5,
            "similarity_boost": 0.8,
            "style": 0.7,
            "use_speaker_boost": True
        }
    
    url = f"{ELEVENLABS_URL}/{VOICE_ID}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVENLABS_API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_turbo_v2_5",
        "voice_settings": voice_settings
    }
    
    try:
        print(f"🎭 Creating: '{text[:50]}...'")
        
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        if response.status_code == 200:
            filepath = CONVERSATION_LIBRARY_DIR / f"{filename}.mp3"
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"   ✅ Saved: {filepath}")
            return True
        else:
            print(f"   ❌ Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False


def create_extended_scenarios():
    """Create extended conversation scenarios for memory-enhanced testing."""
    print("🎭 CREATING EXTENDED SCENARIO AUDIO LIBRARY")
    print("=" * 60)

    # Load scenario metadata
    metadata_file = Path("conversation_audio_library/conversation_metadata.json")

    if not metadata_file.exists():
        print(f"❌ Metadata file not found: {metadata_file}")
        return

    with open(metadata_file, 'r') as f:
        metadata = json.load(f)

    scenarios = metadata['scenarios']

    # Filter for new extended scenarios
    new_scenarios = [
        s for s in scenarios
        if s['scenario'] in ['creative_breakthrough_journey', 'family_reconciliation_journey']
    ]

    print(f"🎯 Generating audio for {len(new_scenarios)} new extended scenarios")

    total_generated = 0

    for scenario in new_scenarios:
        scenario_name = scenario["scenario"]
        interactions = scenario["interactions"]

        print(f"\n🎭 Generating scenario: {scenario_name}")
        print(f"   Total interactions: {len(interactions)}")
        print(f"   Emotional arc: {scenario.get('emotional_arc', 'N/A')}")

        # Voice settings based on scenario type
        if scenario_name == "creative_breakthrough_journey":
            base_voice_settings = {
                "stability": 0.6,
                "similarity_boost": 0.85,
                "style": 0.8,
                "use_speaker_boost": True
            }
        else:  # family_reconciliation_journey
            base_voice_settings = {
                "stability": 0.7,
                "similarity_boost": 0.8,
                "style": 0.7,
                "use_speaker_boost": True
            }

        scenario_generated = 0

        for i, interaction in enumerate(interactions):
            stage = interaction["stage"]
            text = interaction["text"]
            expected_emotion = interaction["expected_emotion"]

            # Enhance text with emotional context
            enhanced_text = enhance_text_for_emotion(text, expected_emotion)

            # Adjust voice settings based on emotion
            voice_settings = adjust_voice_for_emotion(base_voice_settings.copy(), expected_emotion)

            # Generate filename
            filename = f"{scenario_name}_{i+1:02d}_{stage}"

            print(f"   {i+1:2d}. {stage} ({expected_emotion})")

            # Generate audio
            success = create_conversation_audio(enhanced_text, filename, voice_settings)

            if success:
                scenario_generated += 1
                total_generated += 1

            # Rate limiting
            time.sleep(1.0)

        print(f"   📊 Generated {scenario_generated}/{len(interactions)} files for {scenario_name}")

    print(f"\n✨ Extended scenario audio generation completed!")
    print(f"📊 Total files generated: {total_generated}")

    return total_generated


def enhance_text_for_emotion(text: str, emotion: str) -> str:
    """Enhance text with emotional context for better TTS."""

    # Emotional prefixes to guide TTS
    emotion_prefixes = {
        "frustration": "Ugh, ",
        "despair": "I just... ",
        "disappointment": "I guess... ",
        "excitement": "Oh wow, ",
        "joy": "I'm so happy that ",
        "triumph": "Yes! ",
        "pride": "I'm really proud that ",
        "gratitude": "I'm so grateful that ",
        "anger": "I'm really angry that ",
        "sadness": "It makes me sad that ",
        "empathy": "I understand that ",
        "love": "I love that ",
        "peace": "I feel so peaceful knowing that ",
        "relief": "I'm so relieved that ",
        "hope": "I'm hopeful that "
    }

    prefix = emotion_prefixes.get(emotion, "")

    # Add emotional emphasis to key words
    emotion_keywords = {
        "frustration": ["frustrated", "stuck", "blocked", "annoying"],
        "excitement": ["excited", "amazing", "incredible", "wonderful"],
        "joy": ["happy", "joyful", "delighted", "love"],
        "sadness": ["sad", "hurt", "painful", "lonely"],
        "anger": ["angry", "mad", "furious", "upset"],
        "gratitude": ["grateful", "thankful", "appreciate"],
        "pride": ["proud", "accomplished", "achieved"]
    }

    enhanced_text = prefix + text

    # Add natural emphasis
    keywords = emotion_keywords.get(emotion, [])
    for keyword in keywords:
        if keyword in enhanced_text.lower():
            enhanced_text = enhanced_text.replace(keyword, f"{keyword}")

    return enhanced_text


def adjust_voice_for_emotion(base_settings: Dict, emotion: str) -> Dict:
    """Adjust voice settings based on emotion."""

    settings = base_settings.copy()

    # Emotion-specific adjustments
    emotion_adjustments = {
        "frustration": {"stability": -0.2, "style": +0.2},
        "despair": {"stability": -0.3, "style": -0.2},
        "excitement": {"stability": -0.1, "style": +0.3},
        "joy": {"stability": +0.1, "style": +0.2},
        "anger": {"stability": -0.3, "style": +0.3},
        "sadness": {"stability": -0.2, "style": -0.1},
        "calmness": {"stability": +0.2, "style": -0.1},
        "peace": {"stability": +0.3, "style": -0.2},
        "gratitude": {"stability": +0.1, "style": +0.1},
        "love": {"stability": +0.2, "style": +0.1}
    }

    adjustments = emotion_adjustments.get(emotion, {})

    for key, adjustment in adjustments.items():
        if key in settings:
            settings[key] = max(0.1, min(1.0, settings[key] + adjustment))

    return settings


def create_conversation_scenarios():
    """Create realistic conversation scenarios for session testing."""
    print("🎭 CREATING CONVERSATION AUDIO LIBRARY")
    print("=" * 60)

    # Realistic conversation scenarios with emotional progression
    conversation_scenarios = [
        
        # SCENARIO 1: Work Stress to Relief (5 interactions)
        {
            "scenario": "work_stress_relief",
            "description": "User starts stressed about work, gradually finds relief",
            "interactions": [
                {
                    "stage": "initial_stress",
                    "text": "I'm so overwhelmed with this project deadline.",
                    "expected_emotion": "anxiety",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "building_pressure", 
                    "text": "My boss keeps adding more requirements.",
                    "expected_emotion": "frustration",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "seeking_help",
                    "text": "Can you help me figure out how to prioritize this?",
                    "expected_emotion": "anxiety",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "finding_solution",
                    "text": "That's actually a really good approach.",
                    "expected_emotion": "relief",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "feeling_better",
                    "text": "I feel so much better about this now.",
                    "expected_emotion": "relief",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}
                }
            ]
        },
        
        # SCENARIO 2: Sad to Happy Journey (6 interactions)
        {
            "scenario": "sad_to_happy",
            "description": "User starts sad, gradually becomes happier through conversation",
            "interactions": [
                {
                    "stage": "initial_sadness",
                    "text": "I've been feeling really down lately.",
                    "expected_emotion": "sadness",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "sharing_problems",
                    "text": "Everything just seems to be going wrong.",
                    "expected_emotion": "distress",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "opening_up",
                    "text": "Maybe talking about it will help.",
                    "expected_emotion": "contemplation",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "small_improvement",
                    "text": "You know what, you're right about that.",
                    "expected_emotion": "interest",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "feeling_hopeful",
                    "text": "I'm starting to feel more optimistic.",
                    "expected_emotion": "contentment",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "much_better",
                    "text": "Thank you, I'm feeling so much better!",
                    "expected_emotion": "gratitude",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.8}
                }
            ]
        },
        
        # SCENARIO 3: Excitement Building (4 interactions)
        {
            "scenario": "excitement_building",
            "description": "User gets increasingly excited about good news",
            "interactions": [
                {
                    "stage": "good_news",
                    "text": "I just got some really good news!",
                    "expected_emotion": "excitement",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "sharing_details",
                    "text": "I got the job I've been hoping for!",
                    "expected_emotion": "joy",
                    "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "overwhelming_joy",
                    "text": "I can't believe this is actually happening!",
                    "expected_emotion": "excitement",
                    "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "grateful_excitement",
                    "text": "This is the best day ever!",
                    "expected_emotion": "joy",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}
                }
            ]
        },
        
        # SCENARIO 4: Anger to Calm (5 interactions)
        {
            "scenario": "anger_to_calm",
            "description": "User starts angry, gradually calms down",
            "interactions": [
                {
                    "stage": "initial_anger",
                    "text": "I'm so angry about what happened today!",
                    "expected_emotion": "anger",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "venting_frustration",
                    "text": "This is completely unfair and ridiculous!",
                    "expected_emotion": "anger",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "starting_to_process",
                    "text": "I guess I need to think about this differently.",
                    "expected_emotion": "contemplation",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "finding_perspective",
                    "text": "Maybe there's another way to handle this.",
                    "expected_emotion": "determination",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "feeling_calmer",
                    "text": "I feel much calmer about it now.",
                    "expected_emotion": "calmness",
                    "voice_settings": {"stability": 0.8, "similarity_boost": 0.9, "style": 0.6}
                }
            ]
        },
        
        # SCENARIO 5: Anxiety Spike and Recovery (4 interactions)
        {
            "scenario": "anxiety_recovery",
            "description": "User has anxiety spike, then recovers",
            "interactions": [
                {
                    "stage": "anxiety_spike",
                    "text": "I'm really worried about tomorrow's presentation.",
                    "expected_emotion": "anxiety",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "panic_thoughts",
                    "text": "What if everything goes wrong?",
                    "expected_emotion": "fear",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.7, "style": 0.9}
                },
                {
                    "stage": "getting_support",
                    "text": "Your advice is really helping me.",
                    "expected_emotion": "gratitude",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "feeling_confident",
                    "text": "I think I can actually do this well.",
                    "expected_emotion": "determination",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}
                }
            ]
        },

        # SCENARIO 6: Disappointment to Acceptance (5 interactions)
        {
            "scenario": "disappointment_acceptance",
            "description": "User deals with disappointment and finds acceptance",
            "interactions": [
                {
                    "stage": "bad_news",
                    "text": "I didn't get the promotion I was hoping for.",
                    "expected_emotion": "disappointment",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "processing_sadness",
                    "text": "I'm really sad about this outcome.",
                    "expected_emotion": "sadness",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "reflecting",
                    "text": "Maybe this is a chance to learn something.",
                    "expected_emotion": "contemplation",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "finding_silver_lining",
                    "text": "Actually, this gives me time to improve my skills.",
                    "expected_emotion": "realization",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "acceptance",
                    "text": "I'm okay with how things turned out.",
                    "expected_emotion": "contentment",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}
                }
            ]
        },

        # SCENARIO 7: Surprise and Joy (3 interactions)
        {
            "scenario": "surprise_joy",
            "description": "User receives unexpected good news",
            "interactions": [
                {
                    "stage": "unexpected_news",
                    "text": "Wait, what? I can't believe this!",
                    "expected_emotion": "surprise",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "processing_joy",
                    "text": "This is absolutely amazing!",
                    "expected_emotion": "joy",
                    "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "overwhelming_gratitude",
                    "text": "I'm so grateful for this opportunity!",
                    "expected_emotion": "gratitude",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.9, "style": 0.8}
                }
            ]
        },

        # SCENARIO 8: Embarrassment and Recovery (4 interactions)
        {
            "scenario": "embarrassment_recovery",
            "description": "User feels embarrassed but recovers confidence",
            "interactions": [
                {
                    "stage": "embarrassing_moment",
                    "text": "I made such a fool of myself in that meeting.",
                    "expected_emotion": "embarrassment",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "feeling_awkward",
                    "text": "Everyone was staring at me.",
                    "expected_emotion": "awkwardness",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "getting_perspective",
                    "text": "Maybe it wasn't as bad as I thought.",
                    "expected_emotion": "contemplation",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "moving_forward",
                    "text": "I'll do better next time.",
                    "expected_emotion": "determination",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}
                }
            ]
        },

        # SCENARIO 9: Fear to Courage (5 interactions)
        {
            "scenario": "fear_to_courage",
            "description": "User overcomes fear with growing courage",
            "interactions": [
                {
                    "stage": "initial_fear",
                    "text": "I'm terrified of giving this speech.",
                    "expected_emotion": "fear",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "anxiety_building",
                    "text": "My heart is racing just thinking about it.",
                    "expected_emotion": "anxiety",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "seeking_courage",
                    "text": "I need to find the courage to do this.",
                    "expected_emotion": "determination",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "building_confidence",
                    "text": "I've prepared well, I can handle this.",
                    "expected_emotion": "confidence",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "feeling_brave",
                    "text": "I'm ready to face this challenge.",
                    "expected_emotion": "determination",
                    "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.8}
                }
            ]
        },

        # SCENARIO 10: Boredom to Interest (4 interactions)
        {
            "scenario": "boredom_to_interest",
            "description": "User goes from bored to engaged",
            "interactions": [
                {
                    "stage": "feeling_bored",
                    "text": "This is so boring, I can't focus.",
                    "expected_emotion": "boredom",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.5}
                },
                {
                    "stage": "losing_attention",
                    "text": "I'm having trouble staying awake.",
                    "expected_emotion": "tiredness",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.4}
                },
                {
                    "stage": "finding_interest",
                    "text": "Wait, this part is actually interesting.",
                    "expected_emotion": "interest",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "getting_engaged",
                    "text": "Now I'm really curious to learn more.",
                    "expected_emotion": "curiosity",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}
                }
            ]
        },

        # SCENARIO 11: Pride and Achievement (3 interactions)
        {
            "scenario": "pride_achievement",
            "description": "User feels proud of accomplishment",
            "interactions": [
                {
                    "stage": "completing_task",
                    "text": "I finally finished this difficult project.",
                    "expected_emotion": "relief",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "feeling_proud",
                    "text": "I'm really proud of what I accomplished.",
                    "expected_emotion": "pride",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "celebrating_success",
                    "text": "This feels like a real triumph!",
                    "expected_emotion": "triumph",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.9}
                }
            ]
        },

        # SCENARIO 12: Confusion to Understanding (4 interactions)
        {
            "scenario": "confusion_understanding",
            "description": "User works through confusion to clarity",
            "interactions": [
                {
                    "stage": "feeling_confused",
                    "text": "I don't understand what's happening here.",
                    "expected_emotion": "confusion",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "seeking_clarity",
                    "text": "Let me think about this more carefully.",
                    "expected_emotion": "concentration",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "starting_to_understand",
                    "text": "Oh wait, I think I'm starting to get it.",
                    "expected_emotion": "realization",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "full_understanding",
                    "text": "Now it all makes perfect sense!",
                    "expected_emotion": "satisfaction",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}
                }
            ]
        },

        # SCENARIO 13: Envy to Acceptance (4 interactions)
        {
            "scenario": "envy_acceptance",
            "description": "User deals with envy and finds peace",
            "interactions": [
                {
                    "stage": "feeling_envious",
                    "text": "I wish I had what they have.",
                    "expected_emotion": "envy",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "comparing_self",
                    "text": "Why can't I be as successful as them?",
                    "expected_emotion": "disappointment",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "self_reflection",
                    "text": "Maybe I should focus on my own journey.",
                    "expected_emotion": "contemplation",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "finding_peace",
                    "text": "I'm content with my own progress.",
                    "expected_emotion": "contentment",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}
                }
            ]
        },

        # SCENARIO 14: Nostalgia and Warmth (3 interactions)
        {
            "scenario": "nostalgia_warmth",
            "description": "User experiences nostalgic feelings",
            "interactions": [
                {
                    "stage": "remembering_past",
                    "text": "This reminds me of when I was a child.",
                    "expected_emotion": "nostalgia",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "warm_feelings",
                    "text": "Those were such wonderful times.",
                    "expected_emotion": "love",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.9, "style": 0.8}
                },
                {
                    "stage": "appreciating_memories",
                    "text": "I'm grateful for those beautiful memories.",
                    "expected_emotion": "gratitude",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.8}
                }
            ]
        },

        # SCENARIO 15: Disgust to Tolerance (3 interactions)
        {
            "scenario": "disgust_tolerance",
            "description": "User overcomes initial disgust",
            "interactions": [
                {
                    "stage": "initial_disgust",
                    "text": "That's absolutely disgusting!",
                    "expected_emotion": "disgust",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "trying_to_cope",
                    "text": "I guess I have to deal with this.",
                    "expected_emotion": "resignation",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "finding_tolerance",
                    "text": "It's not as bad as I first thought.",
                    "expected_emotion": "acceptance",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.6}
                }
            ]
        },

        # SCENARIO 16: Job Interview Journey (15 interactions) - EXTENDED
        {
            "scenario": "job_interview_journey",
            "description": "Complete job interview process from anxiety to confidence to outcome",
            "interactions": [
                {
                    "stage": "initial_anxiety",
                    "text": "I have a really important job interview tomorrow and I'm so nervous.",
                    "expected_emotion": "anxiety",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "building_worry",
                    "text": "What if I mess up and say something stupid?",
                    "expected_emotion": "fear",
                    "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "seeking_help",
                    "text": "I need to prepare properly for this interview.",
                    "expected_emotion": "determination",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "preparation_focus",
                    "text": "Let me research the company and practice my answers.",
                    "expected_emotion": "concentration",
                    "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "building_confidence",
                    "text": "I've prepared well and I know my strengths.",
                    "expected_emotion": "satisfaction",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "morning_nerves",
                    "text": "It's the morning of the interview and my heart is racing.",
                    "expected_emotion": "anxiety",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "arrival_stress",
                    "text": "I'm here at the office and I feel like I might panic.",
                    "expected_emotion": "panic",
                    "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "calming_down",
                    "text": "Take deep breaths, you've got this, stay calm.",
                    "expected_emotion": "calmness",
                    "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "interview_performance",
                    "text": "I'm focusing on giving clear, thoughtful answers.",
                    "expected_emotion": "concentration",
                    "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "post_interview_relief",
                    "text": "The interview is over and I think it went pretty well.",
                    "expected_emotion": "relief",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "waiting_anxiety",
                    "text": "Now I have to wait for their decision and it's killing me.",
                    "expected_emotion": "anticipation",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "rejection_news",
                    "text": "They called and said they went with someone else.",
                    "expected_emotion": "disappointment",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "processing_sadness",
                    "text": "I'm really sad about this, I wanted that job so much.",
                    "expected_emotion": "sadness",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "finding_resolve",
                    "text": "But I'm not giving up, I'll keep applying and improving.",
                    "expected_emotion": "determination",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.8}
                },
                {
                    "stage": "future_optimism",
                    "text": "The right opportunity will come along when it's meant to.",
                    "expected_emotion": "hope",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
                }
            ]
        },

        # SCENARIO 17: Relationship Conflict Resolution (12 interactions) - EXTENDED
        {
            "scenario": "relationship_conflict_resolution",
            "description": "Working through a serious relationship conflict from anger to understanding",
            "interactions": [
                {
                    "stage": "initial_anger",
                    "text": "I can't believe they did this to me, I'm so angry right now!",
                    "expected_emotion": "anger",
                    "voice_settings": {"stability": 0.2, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "venting_frustration",
                    "text": "This is so frustrating, why can't they understand how this affects me?",
                    "expected_emotion": "frustration",
                    "voice_settings": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.9}
                },
                {
                    "stage": "feeling_hurt",
                    "text": "It really hurts that someone I care about would do this.",
                    "expected_emotion": "sadness",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "questioning_relationship",
                    "text": "I don't know what to think about our relationship anymore.",
                    "expected_emotion": "confusion",
                    "voice_settings": {"stability": 0.5, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "deep_sadness",
                    "text": "This whole situation is making me feel so lost and sad.",
                    "expected_emotion": "distress",
                    "voice_settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.8}
                },
                {
                    "stage": "starting_reflection",
                    "text": "Maybe I need to try to understand their perspective too.",
                    "expected_emotion": "contemplation",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.6}
                },
                {
                    "stage": "seeking_understanding",
                    "text": "I wonder what was going through their mind when this happened.",
                    "expected_emotion": "curiosity",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "finding_empathy",
                    "text": "I can see how they might have felt pressured in that situation.",
                    "expected_emotion": "empathy",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "choosing_forgiveness",
                    "text": "I think I'm ready to forgive them and move forward.",
                    "expected_emotion": "relief",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.8, "style": 0.7}
                },
                {
                    "stage": "expressing_gratitude",
                    "text": "I'm grateful we can work through difficult things like this together.",
                    "expected_emotion": "gratitude",
                    "voice_settings": {"stability": 0.6, "similarity_boost": 0.9, "style": 0.8}
                },
                {
                    "stage": "renewed_love",
                    "text": "This experience has actually made me love them even more.",
                    "expected_emotion": "love",
                    "voice_settings": {"stability": 0.7, "similarity_boost": 0.9, "style": 0.8}
                },
                {
                    "stage": "peaceful_resolution",
                    "text": "I feel so much peace now that we've worked through this.",
                    "expected_emotion": "contentment",
                    "voice_settings": {"stability": 0.8, "similarity_boost": 0.8, "style": 0.6}
                }
            ]
        }
    ]
    
    # Create audio files for each scenario
    successful_creations = 0
    total_files = sum(len(scenario['interactions']) for scenario in conversation_scenarios)
    
    for scenario in conversation_scenarios:
        scenario_name = scenario['scenario']
        print(f"\n📁 Creating scenario: {scenario_name}")
        print(f"   Description: {scenario['description']}")
        
        for i, interaction in enumerate(scenario['interactions']):
            filename = f"{scenario_name}_{i+1:02d}_{interaction['stage']}"
            audio_path = CONVERSATION_LIBRARY_DIR / f"{filename}.mp3"

            # Skip if file already exists
            if audio_path.exists():
                print(f"   ⏭️  Skipping: {filename}.mp3 (already exists)")
                successful_creations += 1  # Count as successful since it exists
                continue

            success = create_conversation_audio(
                text=interaction['text'],
                filename=filename,
                voice_settings=interaction.get('voice_settings')
            )

            if success:
                successful_creations += 1

            # Rate limiting
            time.sleep(1)
    
    # Create metadata file
    metadata = {
        "library_info": {
            "total_scenarios": len(conversation_scenarios),
            "total_files": total_files,
            "successful_creations": successful_creations,
            "voice_id": VOICE_ID,
            "model": "eleven_turbo_v2_5",
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S")
        },
        "scenarios": conversation_scenarios
    }
    
    metadata_file = CONVERSATION_LIBRARY_DIR / "conversation_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"\n🏆 CONVERSATION LIBRARY CREATION COMPLETE")
    print(f"=" * 60)
    print(f"✅ Successfully created: {successful_creations}/{total_files} audio files")
    print(f"📁 Library location: {CONVERSATION_LIBRARY_DIR}")
    print(f"📄 Metadata saved: {metadata_file}")
    
    return successful_creations, total_files


def main():
    """Main function to create audio libraries."""
    import sys

    print("🎭 ELEVENLABS CONVERSATION AUDIO LIBRARY CREATOR")
    print("=" * 70)

    if len(sys.argv) > 1 and sys.argv[1] == "extended":
        # Generate extended scenarios
        print("🎯 Generating EXTENDED SCENARIOS for memory-enhanced testing")
        total_generated = create_extended_scenarios()
        print(f"\n🚀 Ready for memory-enhanced emotion testing!")
        print(f"Use these {total_generated} extended scenario audio files for testing.")
    else:
        # Generate original scenarios
        print("🎯 Generating ORIGINAL SCENARIOS for session testing")
        successful, total = create_conversation_scenarios()
        print(f"\n🚀 Ready for session-based emotion testing!")
        print(f"Use these {successful} conversation audio files for realistic session testing.")


if __name__ == "__main__":
    main()
