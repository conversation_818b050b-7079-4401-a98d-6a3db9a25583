#!/usr/bin/env python3
"""
Test actual response generation to validate streaming and performance.
"""
import asyncio
import os
import sys
import time

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')

import django
django.setup()

from agents.services.langgraph_orchestrator import LangGraphOrchestrator


class MockUser:
    def __init__(self, personality, companion_name):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = "TestUser"
        self.id = f"test_{personality}"


async def test_response_generation():
    """Test actual response generation and streaming."""
    print("🎯 Response Generation Test")
    print("=" * 40)
    
    user = MockUser('caringFriend', 'Ella')
    orchestrator = LangGraphOrchestrator(user=user)
    
    test_cases = [
        ("Hello!", "simple"),
        ("What's 2+2?", "simple"),
        ("Analyze the benefits of AI", "complex")
    ]
    
    for i, (query, complexity) in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: '{query}' ({complexity})")
        
        start_time = time.time()
        chunks_received = 0
        response_content = ""
        
        try:
            async for chunk in orchestrator.process_query(
                user_input=query,
                user_id="test_user",
                streaming=True
            ):
                chunks_received += 1
                chunk_type = chunk.get('type', 'unknown')
                
                if chunk_type == 'domain_classification':
                    domain = chunk.get('domain', 'unknown')
                    print(f"   📂 Domain: {domain}")
                
                elif chunk_type == 'response_chunk':
                    content = chunk.get('content', '')
                    response_content += content
                    if chunks_received <= 3:  # Only show first few chunks
                        print(f"   📝 Chunk {chunks_received}: '{content[:30]}...'")
                
                elif chunk_type == 'response_complete':
                    full_content = chunk.get('full_content', '')
                    llm_used = chunk.get('llm_used', 'unknown')
                    total_time = (time.time() - start_time) * 1000
                    
                    print(f"   ✅ Complete response:")
                    print(f"      LLM used: {llm_used}")
                    print(f"      Total time: {total_time:.1f}ms")
                    print(f"      Response length: {len(full_content)} chars")
                    print(f"      Preview: '{full_content[:100]}...'")
                    break
                
                elif chunk_type == 'error':
                    error = chunk.get('error', 'Unknown error')
                    print(f"   ❌ Error: {error}")
                    break
            
            if chunks_received == 0:
                print(f"   ⚠️ No chunks received")
            else:
                print(f"   📊 Total chunks: {chunks_received}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 40)
    print("🎯 Response generation test complete!")


if __name__ == "__main__":
    asyncio.run(test_response_generation())
