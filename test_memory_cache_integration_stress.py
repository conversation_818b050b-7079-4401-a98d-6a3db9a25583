#!/usr/bin/env python3
"""
Comprehensive Memory Cache Integration & Stress Test
Blends with existing memory integration tests and adds stress testing with varying memory loads.
"""
import os
import sys
import django
import asyncio
import time
import random
from typing import Dict, List, Any, Tuple

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from channels.testing import WebsocketCommunicator
from channels.routing import URLRouter
from django.urls import re_path
from chat.consumers import ChatConsumer
from authentication.models import User
from asgiref.sync import sync_to_async

# Import memory services
from memory.services import MemoryManager
from chat.services.memory_cache_service import memory_cache_service, memory_dual_write_service
from agents.services.memory_manager import get_memory_manager


class MemoryCacheStressTest:
    """Comprehensive memory cache stress testing with varying loads."""
    
    def __init__(self):
        self.test_results = {
            'memory_creation': [],
            'cache_performance': [],
            'websocket_integration': [],
            'stress_test_results': {}
        }
        
        # Test configurations for different memory loads
        self.test_configs = [
            {'name': 'Light Load', 'memory_count': 10, 'concurrent_users': 1},
            {'name': 'Medium Load', 'memory_count': 50, 'concurrent_users': 3},
            {'name': 'Heavy Load', 'memory_count': 200, 'concurrent_users': 5},
            {'name': 'Stress Load', 'memory_count': 500, 'concurrent_users': 10}
        ]
    
    async def setup_test_user(self, email: str) -> User:
        """Setup test user."""
        try:
            user = await sync_to_async(User.objects.get)(email=email)
        except User.DoesNotExist:
            user = await sync_to_async(User.objects.create_user)(
                username=email,
                email=email,
                password="testpass123"
            )
        return user
    
    def generate_realistic_memories(self, count: int) -> List[Tuple[str, str, float]]:
        """Generate realistic memory data for testing."""
        memory_templates = [
            # Personal information
            ("My name is {name} and I work as a {job}", "personal", 0.9),
            ("I live in {city} and have been there for {years} years", "personal", 0.8),
            ("I have a {pet} named {pet_name}", "personal", 0.7),
            ("I graduated from {university} with a degree in {field}", "personal", 0.8),
            
            # Preferences
            ("I love {food} food, especially {dish}", "preference", 0.6),
            ("My favorite music genre is {genre}", "preference", 0.5),
            ("I enjoy {hobby} in my free time", "preference", 0.6),
            ("I prefer {season} weather", "preference", 0.4),
            
            # Goals and aspirations
            ("I want to learn {skill} this year", "goal", 0.7),
            ("My dream is to visit {destination}", "goal", 0.6),
            ("I'm planning to {action} next month", "goal", 0.8),
            
            # Health and lifestyle
            ("I'm allergic to {allergen}", "health", 0.9),
            ("I exercise by {exercise} regularly", "health", 0.6),
            ("I follow a {diet} diet", "health", 0.7),
            
            # Work and career
            ("I specialize in {specialization} at work", "work", 0.7),
            ("My biggest work challenge is {challenge}", "work", 0.6),
            ("I use {tool} for my daily work", "work", 0.5),
        ]
        
        # Sample data for filling templates
        sample_data = {
            'name': ['Alex', 'Jordan', 'Taylor', 'Casey', 'Morgan'],
            'job': ['software engineer', 'designer', 'teacher', 'doctor', 'artist'],
            'city': ['San Francisco', 'New York', 'Seattle', 'Austin', 'Portland'],
            'years': ['2', '5', '10', '15', '3'],
            'pet': ['cat', 'dog', 'bird', 'fish'],
            'pet_name': ['Max', 'Luna', 'Charlie', 'Bella', 'Rocky'],
            'university': ['Stanford', 'MIT', 'Berkeley', 'Harvard', 'UCLA'],
            'field': ['Computer Science', 'Engineering', 'Biology', 'Psychology'],
            'food': ['Italian', 'Japanese', 'Mexican', 'Indian', 'Thai'],
            'dish': ['pasta', 'sushi', 'tacos', 'curry', 'pad thai'],
            'genre': ['jazz', 'rock', 'classical', 'electronic', 'folk'],
            'hobby': ['reading', 'hiking', 'photography', 'cooking', 'gaming'],
            'season': ['spring', 'summer', 'fall', 'winter'],
            'skill': ['Spanish', 'guitar', 'painting', 'coding', 'yoga'],
            'destination': ['Japan', 'Italy', 'Iceland', 'New Zealand', 'Peru'],
            'action': ['start a business', 'move apartments', 'adopt a pet'],
            'allergen': ['peanuts', 'shellfish', 'dairy', 'gluten'],
            'exercise': ['running', 'swimming', 'cycling', 'weightlifting'],
            'diet': ['vegetarian', 'keto', 'Mediterranean', 'plant-based'],
            'specialization': ['machine learning', 'web development', 'data analysis'],
            'challenge': ['time management', 'team coordination', 'technical debt'],
            'tool': ['Python', 'Figma', 'Excel', 'Slack', 'Docker']
        }
        
        memories = []
        for i in range(count):
            template, memory_type, importance = random.choice(memory_templates)
            
            # Fill template with random data
            content = template
            for key, values in sample_data.items():
                if f'{{{key}}}' in content:
                    content = content.replace(f'{{{key}}}', random.choice(values))
            
            # Add some randomness to importance
            importance += random.uniform(-0.1, 0.1)
            importance = max(0.1, min(1.0, importance))
            
            memories.append((content, memory_type, importance))
        
        return memories
    
    async def create_memories_for_user(self, user: User, memory_count: int) -> Dict[str, Any]:
        """Create memories for a user and measure performance."""
        print(f"   📚 Creating {memory_count} memories for {user.email}...")
        
        start_time = time.time()
        memories = self.generate_realistic_memories(memory_count)
        
        # Use the actual memory manager
        memory_manager = MemoryManager(user=user)
        created_count = 0
        
        for content, memory_type, importance in memories:
            try:
                memory_id = await memory_manager.store_memory(
                    content=content,
                    memory_type=memory_type,
                    importance_score=importance
                )
                if memory_id:
                    created_count += 1
            except Exception as e:
                print(f"      ⚠️ Error creating memory: {e}")
        
        creation_time = (time.time() - start_time) * 1000
        
        result = {
            'user_email': user.email,
            'requested_count': memory_count,
            'created_count': created_count,
            'creation_time_ms': creation_time,
            'avg_time_per_memory_ms': creation_time / created_count if created_count > 0 else 0
        }
        
        print(f"      ✅ Created {created_count}/{memory_count} memories in {creation_time:.1f}ms")
        return result
    
    async def test_cache_performance_with_load(self, user: User, memory_count: int, 
                                             query_count: int = 20) -> Dict[str, Any]:
        """Test cache performance with specific memory load."""
        print(f"   ⚡ Testing cache with {memory_count} memories, {query_count} queries...")
        
        session_id = f"stress_test_{int(time.time())}"
        
        # Preload memories
        preload_start = time.time()
        preload_success = await memory_cache_service.preload_user_memories(
            user_id=str(user.id),
            session_id=session_id
        )
        preload_time = (time.time() - preload_start) * 1000
        
        if not preload_success:
            return {'error': 'Failed to preload memories'}
        
        # Test queries
        test_queries = [
            "What are my hobbies?", "Tell me about my work", "What foods do I like?",
            "Where do I live?", "What are my goals?", "Do I have any allergies?",
            "What's my educational background?", "What music do I enjoy?",
            "What are my health habits?", "What tools do I use for work?",
            "What pets do I have?", "What places do I want to visit?",
            "What skills am I learning?", "What's my diet like?",
            "What challenges do I face at work?", "What's my exercise routine?",
            "What's my favorite season?", "What's my specialization?",
            "What are my preferences?", "Tell me about my lifestyle?"
        ]
        
        query_times = []
        memories_found = []
        
        for i in range(query_count):
            query = random.choice(test_queries)
            
            start_time = time.time()
            memories = await memory_cache_service.search_cached_memories(
                user_id=str(user.id),
                session_id=session_id,
                query=query,
                k=5
            )
            query_time = (time.time() - start_time) * 1000
            
            query_times.append(query_time)
            memories_found.append(len(memories))
        
        # Cleanup
        await memory_cache_service.invalidate_user_cache(str(user.id), session_id)
        
        result = {
            'memory_count': memory_count,
            'query_count': query_count,
            'preload_time_ms': preload_time,
            'avg_query_time_ms': sum(query_times) / len(query_times),
            'min_query_time_ms': min(query_times),
            'max_query_time_ms': max(query_times),
            'avg_memories_found': sum(memories_found) / len(memories_found),
            'cache_performance_score': 10 - (sum(query_times) / len(query_times))  # Lower is better
        }
        
        print(f"      ⚡ Avg query: {result['avg_query_time_ms']:.1f}ms | Memories: {result['avg_memories_found']:.1f}")
        return result
    
    async def test_websocket_with_memory_load(self, user: User, memory_count: int) -> Dict[str, Any]:
        """Test WebSocket performance with specific memory load."""
        print(f"   🔌 Testing WebSocket with {memory_count} memories...")
        
        try:
            # Create WebSocket application
            application = URLRouter([
                re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
            ])
            
            # Create communicator
            communicator = WebsocketCommunicator(application, "/ws/chat/")
            communicator.scope["user"] = user
            
            # Connect
            connect_start = time.time()
            connected, _ = await communicator.connect()
            connect_time = (time.time() - connect_start) * 1000
            
            if not connected:
                return {'error': 'Failed to connect WebSocket'}
            
            # Test message
            test_query = f"Tell me about my preferences and background (test with {memory_count} memories)"
            
            message_start = time.time()
            
            # Send message
            import uuid
            await communicator.send_json_to({
                'type': 'text_message',
                'content': test_query,
                'conversation_id': str(uuid.uuid4())
            })
            
            # Collect responses
            first_text_time = None
            first_audio_time = None
            text_chunks = 0
            audio_chunks = 0
            
            timeout = 20  # 20 seconds
            while (time.time() - message_start) < timeout:
                try:
                    response = await asyncio.wait_for(
                        communicator.receive_json_from(),
                        timeout=3.0
                    )
                    
                    response_type = response.get('type')
                    elapsed_ms = (time.time() - message_start) * 1000
                    
                    if response_type == 'llm_response_chunk':
                        if first_text_time is None:
                            first_text_time = elapsed_ms
                        text_chunks += 1
                        if response.get('is_final'):
                            break
                    elif response_type == 'audio_chunk':
                        if first_audio_time is None:
                            first_audio_time = elapsed_ms
                        audio_chunks += 1
                    
                except asyncio.TimeoutError:
                    break
            
            total_time = (time.time() - message_start) * 1000
            
            # Disconnect
            await communicator.disconnect()
            
            result = {
                'memory_count': memory_count,
                'connect_time_ms': connect_time,
                'first_text_time_ms': first_text_time,
                'first_audio_time_ms': first_audio_time,
                'total_time_ms': total_time,
                'text_chunks': text_chunks,
                'audio_chunks': audio_chunks,
                'success': first_text_time is not None
            }
            
            print(f"      🔌 Connect: {connect_time:.1f}ms | Text: {first_text_time or 'N/A'}ms | Success: {result['success']}")
            return result
            
        except Exception as e:
            print(f"      ❌ WebSocket error: {e}")
            return {'error': str(e), 'memory_count': memory_count}
    
    async def run_stress_test_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Run stress test for a specific configuration."""
        print(f"\n🔥 {config['name']} Stress Test")
        print(f"   📊 {config['memory_count']} memories, {config['concurrent_users']} users")
        print("-" * 50)
        
        # Create test users
        users = []
        for i in range(config['concurrent_users']):
            user = await self.setup_test_user(f"stress_test_{config['name'].lower().replace(' ', '_')}_{i}@example.com")
            users.append(user)
        
        # Create memories for each user
        memory_creation_results = []
        for user in users:
            result = await self.create_memories_for_user(user, config['memory_count'])
            memory_creation_results.append(result)
        
        # Test cache performance for each user
        cache_performance_results = []
        for user in users:
            result = await self.test_cache_performance_with_load(user, config['memory_count'])
            cache_performance_results.append(result)
        
        # Test WebSocket performance for each user
        websocket_results = []
        for user in users:
            result = await self.test_websocket_with_memory_load(user, config['memory_count'])
            websocket_results.append(result)
        
        # Calculate aggregated results
        successful_cache_tests = [r for r in cache_performance_results if 'error' not in r]
        successful_websocket_tests = [r for r in websocket_results if 'error' not in r and r.get('success')]
        
        config_result = {
            'config': config,
            'memory_creation': {
                'total_memories_created': sum(r['created_count'] for r in memory_creation_results),
                'avg_creation_time_ms': sum(r['creation_time_ms'] for r in memory_creation_results) / len(memory_creation_results),
                'avg_time_per_memory_ms': sum(r['avg_time_per_memory_ms'] for r in memory_creation_results) / len(memory_creation_results)
            },
            'cache_performance': {
                'avg_query_time_ms': sum(r['avg_query_time_ms'] for r in successful_cache_tests) / len(successful_cache_tests) if successful_cache_tests else 0,
                'avg_preload_time_ms': sum(r['preload_time_ms'] for r in successful_cache_tests) / len(successful_cache_tests) if successful_cache_tests else 0,
                'success_rate': len(successful_cache_tests) / len(cache_performance_results) * 100
            },
            'websocket_performance': {
                'avg_first_text_time_ms': sum(r['first_text_time_ms'] for r in successful_websocket_tests if r['first_text_time_ms']) / len(successful_websocket_tests) if successful_websocket_tests else 0,
                'success_rate': len(successful_websocket_tests) / len(websocket_results) * 100,
                'avg_connect_time_ms': sum(r['connect_time_ms'] for r in successful_websocket_tests) / len(successful_websocket_tests) if successful_websocket_tests else 0
            }
        }
        
        print(f"   📊 Results Summary:")
        print(f"      Memory Creation: {config_result['memory_creation']['total_memories_created']} memories in {config_result['memory_creation']['avg_creation_time_ms']:.1f}ms avg")
        print(f"      Cache Performance: {config_result['cache_performance']['avg_query_time_ms']:.1f}ms avg query ({config_result['cache_performance']['success_rate']:.1f}% success)")
        print(f"      WebSocket Performance: {config_result['websocket_performance']['avg_first_text_time_ms']:.1f}ms avg text ({config_result['websocket_performance']['success_rate']:.1f}% success)")
        
        return config_result

    async def run_comprehensive_stress_test(self) -> Dict[str, Any]:
        """Run comprehensive stress test across all configurations."""
        print("🚀 COMPREHENSIVE MEMORY CACHE STRESS TEST")
        print("=" * 60)

        all_results = {}

        for config in self.test_configs:
            try:
                result = await self.run_stress_test_config(config)
                all_results[config['name']] = result
            except Exception as e:
                print(f"❌ Error in {config['name']} test: {e}")
                all_results[config['name']] = {'error': str(e)}

        return all_results

    def analyze_stress_test_results(self, results: Dict[str, Any]) -> None:
        """Analyze and display stress test results."""
        print("\n📊 STRESS TEST ANALYSIS")
        print("=" * 60)

        # Performance comparison table
        print("\n📈 Performance Comparison:")
        print(f"{'Load Level':<15} {'Memories':<10} {'Cache Query':<12} {'WebSocket Text':<15} {'Success Rate':<12}")
        print("-" * 70)

        for config_name, result in results.items():
            if 'error' in result:
                print(f"{config_name:<15} {'ERROR':<10} {'ERROR':<12} {'ERROR':<15} {'ERROR':<12}")
                continue

            memory_count = result['config']['memory_count']
            cache_query_ms = result['cache_performance']['avg_query_time_ms']
            websocket_text_ms = result['websocket_performance']['avg_first_text_time_ms']
            websocket_success = result['websocket_performance']['success_rate']

            print(f"{config_name:<15} {memory_count:<10} {cache_query_ms:<12.1f} {websocket_text_ms:<15.1f} {websocket_success:<12.1f}%")

        # Performance targets analysis
        print("\n🎯 Performance Target Analysis:")
        target_cache_ms = 10  # 10ms target for cache queries
        target_websocket_ms = 500  # 500ms target for WebSocket text

        for config_name, result in results.items():
            if 'error' in result:
                continue

            cache_ms = result['cache_performance']['avg_query_time_ms']
            websocket_ms = result['websocket_performance']['avg_first_text_time_ms']

            cache_status = "✅ PASS" if cache_ms <= target_cache_ms else "❌ FAIL"
            websocket_status = "✅ PASS" if websocket_ms <= target_websocket_ms else "❌ FAIL"

            print(f"   {config_name}:")
            print(f"      Cache Query: {cache_ms:.1f}ms (target: ≤{target_cache_ms}ms) {cache_status}")
            print(f"      WebSocket Text: {websocket_ms:.1f}ms (target: ≤{target_websocket_ms}ms) {websocket_status}")

        # Scalability analysis
        print("\n📈 Scalability Analysis:")
        memory_counts = []
        cache_times = []
        websocket_times = []

        for result in results.values():
            if 'error' not in result:
                memory_counts.append(result['config']['memory_count'])
                cache_times.append(result['cache_performance']['avg_query_time_ms'])
                websocket_times.append(result['websocket_performance']['avg_first_text_time_ms'])

        if len(memory_counts) >= 2:
            # Calculate performance degradation
            light_cache = cache_times[0] if cache_times else 0
            heavy_cache = cache_times[-1] if cache_times else 0
            cache_degradation = ((heavy_cache - light_cache) / light_cache * 100) if light_cache > 0 else 0

            light_websocket = websocket_times[0] if websocket_times else 0
            heavy_websocket = websocket_times[-1] if websocket_times else 0
            websocket_degradation = ((heavy_websocket - light_websocket) / light_websocket * 100) if light_websocket > 0 else 0

            print(f"   Cache Performance Degradation: {cache_degradation:.1f}% (from {memory_counts[0]} to {memory_counts[-1]} memories)")
            print(f"   WebSocket Performance Degradation: {websocket_degradation:.1f}% (from {memory_counts[0]} to {memory_counts[-1]} memories)")

            # Scalability verdict
            if cache_degradation < 50 and websocket_degradation < 50:
                print("   🎉 EXCELLENT scalability - performance degrades gracefully")
            elif cache_degradation < 100 and websocket_degradation < 100:
                print("   ✅ GOOD scalability - acceptable performance degradation")
            else:
                print("   ⚠️ POOR scalability - significant performance degradation")

        # Memory efficiency analysis
        print("\n💾 Memory Efficiency Analysis:")
        for config_name, result in results.items():
            if 'error' in result:
                continue

            memory_count = result['config']['memory_count']
            preload_time = result['cache_performance']['avg_preload_time_ms']

            efficiency_score = memory_count / preload_time if preload_time > 0 else 0
            print(f"   {config_name}: {efficiency_score:.2f} memories/ms preload efficiency")

        # Final recommendations
        print("\n🎯 RECOMMENDATIONS:")

        # Find best performing configuration
        best_config = None
        best_score = 0

        for config_name, result in results.items():
            if 'error' in result:
                continue

            # Calculate composite score (lower is better for times, higher for success rates)
            cache_score = max(0, 10 - result['cache_performance']['avg_query_time_ms'])
            websocket_score = max(0, 500 - result['websocket_performance']['avg_first_text_time_ms']) / 50
            success_score = result['websocket_performance']['success_rate'] / 10

            composite_score = cache_score + websocket_score + success_score

            if composite_score > best_score:
                best_score = composite_score
                best_config = config_name

        if best_config:
            print(f"   🏆 Best Overall Performance: {best_config}")

        # Check if system meets production requirements
        production_ready = True
        for result in results.values():
            if 'error' in result:
                production_ready = False
                break
            if (result['cache_performance']['avg_query_time_ms'] > target_cache_ms or
                result['websocket_performance']['avg_first_text_time_ms'] > target_websocket_ms or
                result['websocket_performance']['success_rate'] < 80):
                production_ready = False
                break

        if production_ready:
            print("   ✅ PRODUCTION READY: All configurations meet performance targets")
        else:
            print("   ⚠️ OPTIMIZATION NEEDED: Some configurations exceed performance targets")

        print("\n🎉 Stress test analysis completed!")


async def main():
    """Run the comprehensive memory cache stress test."""
    test = MemoryCacheStressTest()

    # Run the comprehensive stress test
    results = await test.run_comprehensive_stress_test()

    # Analyze and display results
    test.analyze_stress_test_results(results)

    return results


if __name__ == "__main__":
    asyncio.run(main())
