#!/usr/bin/env python3
"""
Expression Measurement Latency Test
Tests the latency of Hume AI expression measurement integration.
"""
import os
import sys
import django
import asyncio
import time
import base64
import json
import uuid
from typing import Dict, List, Any

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from channels.testing import WebsocketCommunicator
from channels.routing import URLRouter
from django.urls import re_path
from chat.consumers import ChatConsumer
from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service


class ExpressionMeasurementLatencyTest:
    """Test expression measurement latency and integration."""
    
    def __init__(self):
        self.test_results = []
    
    async def setup_test_user(self) -> User:
        """Setup test user."""
        try:
            user = await sync_to_async(User.objects.get)(email="<EMAIL>")
        except User.DoesNotExist:
            user = await sync_to_async(User.objects.create_user)(
                username="<EMAIL>",
                email="<EMAIL>",
                password="testpass123"
            )
        return user
    
    def generate_test_audio(self, duration_ms: int = 1000) -> bytes:
        """Generate test audio data (simple sine wave)."""
        import math
        
        sample_rate = 16000  # 16kHz
        samples = int(sample_rate * duration_ms / 1000)
        frequency = 440  # A4 note
        
        audio_data = bytearray()
        for i in range(samples):
            # Generate sine wave
            sample = int(32767 * math.sin(2 * math.pi * frequency * i / sample_rate))
            # Convert to 16-bit little-endian
            audio_data.extend(sample.to_bytes(2, byteorder='little', signed=True))
        
        return bytes(audio_data)
    
    async def test_expression_service_directly(self) -> Dict[str, Any]:
        """Test expression measurement service directly."""
        print("🔬 Testing Expression Measurement Service Directly...")
        
        # Generate test audio
        test_audio = self.generate_test_audio(1000)  # 1 second
        chunk_id = str(uuid.uuid4())
        
        # Test direct service call
        start_time = time.time()
        
        try:
            result = await expression_measurement_service.analyze_audio_chunk(test_audio, chunk_id)
            
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            if result:
                print(f"   ✅ Expression analysis successful")
                print(f"   ⏱️  Latency: {latency_ms:.1f}ms")
                print(f"   😊 Dominant emotion: {result.dominant_emotion} ({result.confidence:.2f})")
                print(f"   📊 Total emotions detected: {len(result.emotions)}")
                
                return {
                    'success': True,
                    'latency_ms': latency_ms,
                    'dominant_emotion': result.dominant_emotion,
                    'confidence': result.confidence,
                    'emotions_count': len(result.emotions),
                    'processing_time_ms': result.processing_time_ms
                }
            else:
                print(f"   ❌ Expression analysis failed")
                print(f"   ⏱️  Latency: {latency_ms:.1f}ms")
                
                return {
                    'success': False,
                    'latency_ms': latency_ms,
                    'error': 'No result returned'
                }
                
        except Exception as e:
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            print(f"   ❌ Expression analysis error: {e}")
            print(f"   ⏱️  Latency: {latency_ms:.1f}ms")
            
            return {
                'success': False,
                'latency_ms': latency_ms,
                'error': str(e)
            }
    
    async def test_websocket_integration(self, user: User) -> Dict[str, Any]:
        """Test expression measurement through WebSocket integration."""
        print("\n🔌 Testing WebSocket Integration with Expression Measurement...")
        
        try:
            # Create WebSocket application
            application = URLRouter([
                re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
            ])
            
            # Create communicator
            communicator = WebsocketCommunicator(application, "/ws/chat/")
            communicator.scope["user"] = user
            
            # Connect
            connect_start = time.time()
            connected, _ = await communicator.connect()
            connect_time = (time.time() - connect_start) * 1000
            
            if not connected:
                return {'error': 'Failed to connect WebSocket'}
            
            print(f"   ✅ WebSocket connected in {connect_time:.1f}ms")
            
            # Generate test audio
            test_audio = self.generate_test_audio(2000)  # 2 seconds
            audio_b64 = base64.b64encode(test_audio).decode('utf-8')
            chunk_id = str(uuid.uuid4())
            
            # Send audio chunk
            audio_start = time.time()
            
            await communicator.send_json_to({
                'type': 'audio_chunk',
                'data': audio_b64,
                'chunk_id': chunk_id,
                'is_final': True,
                'timestamp': time.time() * 1000
            })
            
            # Collect responses
            expression_detected = False
            transcription_received = False
            ai_response_received = False
            first_response_time = None
            
            timeout = 30  # 30 seconds
            while (time.time() - audio_start) < timeout:
                try:
                    response = await asyncio.wait_for(
                        communicator.receive_json_from(),
                        timeout=5.0
                    )
                    
                    response_type = response.get('type')
                    elapsed_ms = (time.time() - audio_start) * 1000
                    
                    if first_response_time is None:
                        first_response_time = elapsed_ms
                    
                    print(f"   📦 Received: {response_type} at {elapsed_ms:.1f}ms")
                    
                    if response_type == 'transcription_result':
                        transcription_received = True
                        print(f"      📝 Transcription: {response.get('text', '')[:50]}...")
                    
                    elif response_type == 'emotion_analysis':
                        expression_detected = True
                        emotions = response.get('emotions', [])
                        if emotions:
                            dominant = max(emotions, key=lambda x: x.get('score', 0))
                            print(f"      😊 Emotion: {dominant.get('name')} ({dominant.get('score', 0):.2f})")
                    
                    elif response_type == 'llm_response_chunk':
                        if not ai_response_received:
                            ai_response_received = True
                            print(f"      🤖 AI response started")
                        if response.get('is_final'):
                            break
                    
                except asyncio.TimeoutError:
                    print("   ⏰ Response timeout")
                    break
            
            total_time = (time.time() - audio_start) * 1000
            
            # Disconnect
            await communicator.disconnect()
            
            result = {
                'websocket_connected': True,
                'connect_time_ms': connect_time,
                'first_response_time_ms': first_response_time,
                'total_time_ms': total_time,
                'transcription_received': transcription_received,
                'expression_detected': expression_detected,
                'ai_response_received': ai_response_received,
                'success': transcription_received and ai_response_received
            }
            
            print(f"   📊 Results:")
            print(f"      Connect: {connect_time:.1f}ms")
            print(f"      First response: {first_response_time:.1f}ms")
            print(f"      Total time: {total_time:.1f}ms")
            print(f"      Expression detected: {'✅' if expression_detected else '❌'}")
            
            return result
            
        except Exception as e:
            print(f"   ❌ WebSocket error: {e}")
            return {'error': str(e)}
    
    async def test_performance_targets(self) -> Dict[str, Any]:
        """Test against performance targets."""
        print("\n🎯 Testing Performance Targets...")
        
        # Test multiple iterations for average
        iterations = 5
        latencies = []
        
        for i in range(iterations):
            print(f"   Iteration {i+1}/{iterations}...")
            
            test_audio = self.generate_test_audio(1000)
            chunk_id = f"perf_test_{i}"
            
            start_time = time.time()
            result = await expression_measurement_service.analyze_audio_chunk(test_audio, chunk_id)
            latency = (time.time() - start_time) * 1000
            
            latencies.append(latency)
            
            if result:
                print(f"      ✅ {latency:.1f}ms - {result.dominant_emotion}")
            else:
                print(f"      ❌ {latency:.1f}ms - Failed")
        
        # Calculate statistics
        avg_latency = sum(latencies) / len(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)
        
        # Performance targets (from settings)
        target_ms = 100  # 100ms target for emotion detection
        
        performance_result = {
            'avg_latency_ms': avg_latency,
            'min_latency_ms': min_latency,
            'max_latency_ms': max_latency,
            'target_ms': target_ms,
            'target_met': avg_latency <= target_ms,
            'iterations': iterations
        }
        
        print(f"   📊 Performance Results:")
        print(f"      Average: {avg_latency:.1f}ms")
        print(f"      Min: {min_latency:.1f}ms")
        print(f"      Max: {max_latency:.1f}ms")
        print(f"      Target: ≤{target_ms}ms")
        print(f"      Status: {'✅ PASS' if performance_result['target_met'] else '❌ FAIL'}")
        
        return performance_result
    
    async def run_comprehensive_test(self):
        """Run comprehensive expression measurement latency test."""
        print("🚀 EXPRESSION MEASUREMENT LATENCY TEST")
        print("=" * 60)
        
        # Setup
        user = await self.setup_test_user()
        
        # Test 1: Direct service test
        direct_result = await self.test_expression_service_directly()
        
        # Test 2: WebSocket integration test
        websocket_result = await self.test_websocket_integration(user)
        
        # Test 3: Performance targets test
        performance_result = await self.test_performance_targets()
        
        # Get service stats
        service_stats = expression_measurement_service.get_performance_stats()
        
        # Final analysis
        print("\n📊 FINAL ANALYSIS")
        print("=" * 60)
        
        print(f"🔬 Direct Service Test:")
        if direct_result.get('success'):
            print(f"   ✅ Success: {direct_result['latency_ms']:.1f}ms")
            print(f"   😊 Emotion: {direct_result['dominant_emotion']} ({direct_result['confidence']:.2f})")
        else:
            print(f"   ❌ Failed: {direct_result.get('error', 'Unknown error')}")
        
        print(f"\n🔌 WebSocket Integration:")
        if websocket_result.get('success'):
            print(f"   ✅ Success: {websocket_result['total_time_ms']:.1f}ms total")
            print(f"   📡 First response: {websocket_result['first_response_time_ms']:.1f}ms")
            print(f"   😊 Expression detected: {'✅' if websocket_result['expression_detected'] else '❌'}")
        else:
            print(f"   ❌ Failed: {websocket_result.get('error', 'Unknown error')}")
        
        print(f"\n🎯 Performance Targets:")
        print(f"   Average latency: {performance_result['avg_latency_ms']:.1f}ms")
        print(f"   Target (≤100ms): {'✅ PASS' if performance_result['target_met'] else '❌ FAIL'}")
        
        print(f"\n📈 Service Statistics:")
        print(f"   Total requests: {service_stats['total_requests']}")
        print(f"   Success rate: {service_stats['success_rate_percent']:.1f}%")
        print(f"   Average processing: {service_stats['avg_processing_time_ms']:.1f}ms")
        print(f"   API enabled: {'✅' if service_stats['api_enabled'] else '❌'}")
        
        # Overall verdict
        overall_success = (
            direct_result.get('success', False) and
            websocket_result.get('success', False) and
            performance_result.get('target_met', False)
        )
        
        print(f"\n🏆 OVERALL VERDICT:")
        if overall_success:
            print("   ✅ EXCELLENT - Expression measurement is working with good performance!")
        else:
            print("   ⚠️ NEEDS OPTIMIZATION - Some tests failed or exceeded targets")
        
        print("\n🎉 Expression measurement latency test completed!")


async def main():
    """Run the expression measurement latency test."""
    test = ExpressionMeasurementLatencyTest()
    await test.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())
