#!/usr/bin/env python3
"""
Extended Session-Based Emotion Aggregation Test
Tests accuracy over time with extended conversation scenarios (10-15 interactions).
"""
import os
import sys
import django
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service


def get_comprehensive_emotion_families() -> Dict[str, List[str]]:
    """Get comprehensive emotion families for accuracy calculation."""
    return {
        "joy": ["Joy", "Happiness", "Excitement", "Enthusiasm", "Satisfaction", "Contentment"],
        "excitement": ["Excitement", "Joy", "Enthusiasm", "Anticipation", "Thrill"],
        "satisfaction": ["Satisfaction", "Contentment", "Joy", "Pride", "Accomplishment"],
        "anger": ["Anger", "Rage", "Fury", "Annoyance", "Irritation", "Frustration"],
        "frustration": ["Frustration", "Annoyance", "Anger", "Irritation"],
        "sadness": ["Sadness", "Sorrow", "Grief", "Disappointment", "Distress"],
        "disappointment": ["Disappointment", "Sadness", "Dissatisfaction"],
        "distress": ["Distress", "Anguish", "Sadness", "Anxiety"],
        "fear": ["Fear", "Terror", "Anxiety", "Worry", "Panic"],
        "anxiety": ["Anxiety", "Worry", "Fear", "Nervousness"],
        "panic": ["Panic", "Terror", "Fear", "Anxiety"],
        "interest": ["Interest", "Curiosity", "Fascination"],
        "curiosity": ["Curiosity", "Interest", "Wonder"],
        "concentration": ["Concentration", "Focus", "Contemplation"],
        "contemplation": ["Contemplation", "Reflection", "Concentration"],
        "realization": ["Realization", "Understanding", "Insight"],
        "confusion": ["Confusion", "Bewilderment", "Uncertainty"],
        "pride": ["Pride", "Accomplishment", "Satisfaction", "Triumph"],
        "triumph": ["Triumph", "Victory", "Pride", "Success"],
        "determination": ["Determination", "Resolve", "Persistence"],
        "gratitude": ["Gratitude", "Thankfulness", "Appreciation"],
        "love": ["Love", "Affection", "Adoration"],
        "calmness": ["Calmness", "Serenity", "Peace", "Tranquility"],
        "relief": ["Relief", "Ease", "Comfort"],
        "contentment": ["Contentment", "Satisfaction", "Peace"],
        "embarrassment": ["Embarrassment", "Shame", "Awkwardness"],
        "shame": ["Shame", "Embarrassment", "Guilt"],
        "hope": ["Hope", "Optimism", "Anticipation"],
        "empathy": ["Empathy", "Compassion", "Understanding"],
        "anticipation": ["Anticipation", "Expectation", "Hope"]
    }


def calculate_accuracy_with_families(results: List[Dict]) -> Dict:
    """Calculate accuracy including emotion family matches."""
    if not results:
        return {"exact": 0, "family": 0, "total": 0}
    
    exact_matches = 0
    family_matches = 0
    total = len(results)
    
    emotion_families = get_comprehensive_emotion_families()
    
    for result in results:
        detected = result.get('detected_emotion', '').lower()
        expected = result.get('expected_emotion', '').lower()
        
        if detected.lower() == expected.lower():
            exact_matches += 1
            family_matches += 1
        elif expected in emotion_families:
            family_emotions = [e.lower() for e in emotion_families[expected]]
            if detected.lower() in family_emotions:
                family_matches += 1
    
    return {
        "exact": exact_matches,
        "family": family_matches,
        "total": total,
        "exact_percent": (exact_matches / total * 100) if total > 0 else 0,
        "family_percent": (family_matches / total * 100) if total > 0 else 0
    }


async def test_extended_scenarios():
    """Test the extended conversation scenarios with session aggregation."""
    
    print("🎯 EXTENDED SESSION AGGREGATION TEST")
    print("=" * 70)
    
    # Load conversation metadata
    metadata_file = Path("conversation_audio_library/conversation_metadata.json")
    if not metadata_file.exists():
        print(f"❌ Metadata not found: {metadata_file}")
        return
    
    with open(metadata_file, 'r') as f:
        metadata = json.load(f)
    
    scenarios = metadata['scenarios']
    
    # Filter for extended scenarios (10+ interactions)
    extended_scenarios = [s for s in scenarios if len(s['interactions']) >= 10]
    
    print(f"📊 Found {len(extended_scenarios)} extended scenarios:")
    for scenario in extended_scenarios:
        print(f"   🎭 {scenario['scenario']}: {len(scenario['interactions'])} interactions")
    
    # Setup user
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    
    user_id = str(user.id)
    all_results = []
    
    for scenario in extended_scenarios:
        scenario_name = scenario['scenario']
        interactions = scenario['interactions']
        
        print(f"\n🎭 TESTING: {scenario_name}")
        print(f"   Description: {scenario['description']}")
        print(f"   Interactions: {len(interactions)}")
        
        session_id = f"extended_{scenario_name}_{int(time.time())}"
        scenario_results = []
        
        for i, interaction in enumerate(interactions):
            stage = interaction['stage']
            text = interaction['text']
            expected_emotion = interaction['expected_emotion']
            
            # Load audio file
            audio_filename = f"{scenario_name}_{i+1:02d}_{stage}.mp3"
            audio_path = Path("conversation_audio_library") / audio_filename
            
            if not audio_path.exists():
                print(f"   ❌ Audio not found: {audio_filename}")
                continue
            
            with open(audio_path, 'rb') as f:
                audio_data = f.read()
            
            # Multi-modal analysis with session aggregation
            chunk_id = f"{session_id}_interaction_{i+1}"
            start_time = time.time()
            
            result = await expression_measurement_service.analyze_audio_chunk(
                audio_data, chunk_id, session_id, user_id, text=text
            )
            
            analysis_time = (time.time() - start_time) * 1000
            
            if result:
                detected_emotion = result.dominant_emotion
                confidence = result.confidence
                
                # Check accuracy
                exact_match = detected_emotion.lower() == expected_emotion.lower()
                
                # Family match
                emotion_families = get_comprehensive_emotion_families()
                family_match = False
                if expected_emotion in emotion_families:
                    family_emotions = [e.lower() for e in emotion_families[expected_emotion]]
                    family_match = detected_emotion.lower() in family_emotions
                
                match_type = "EXACT" if exact_match else "FAMILY" if family_match else "MISS"
                match_icon = "✅" if exact_match else "🟡" if family_match else "❌"
                
                print(f"   {i+1:2d}. {match_icon} {expected_emotion} → {detected_emotion} ({confidence:.2f}) [{match_type}] - {analysis_time:.0f}ms")
                
                scenario_results.append({
                    'interaction': i + 1,
                    'expected_emotion': expected_emotion,
                    'detected_emotion': detected_emotion,
                    'confidence': confidence,
                    'exact_match': exact_match,
                    'family_match': family_match,
                    'analysis_time': analysis_time
                })
                
                all_results.append({
                    'scenario': scenario_name,
                    'interaction': i + 1,
                    'expected_emotion': expected_emotion,
                    'detected_emotion': detected_emotion,
                    'confidence': confidence,
                    'exact_match': exact_match,
                    'family_match': family_match
                })
            
            # Small delay between interactions
            await asyncio.sleep(0.2)
        
        # Get session aggregation results
        session_result = await expression_measurement_service.get_session_emotion_profile(session_id)

        if session_result:
            print(f"\n   📊 SESSION AGGREGATION:")
            print(f"      Dominant emotion: {session_result.dominant_emotion}")
            print(f"      Overall confidence: {session_result.overall_confidence:.2f}")
            print(f"      Total interactions: {session_result.total_interactions}")
            print(f"      Emotional trend: {session_result.recent_trend}")
            print(f"      Session duration: {session_result.session_duration_minutes:.1f} minutes")

            # Show top aggregated emotions
            if session_result.aggregated_emotions:
                top_emotions = sorted(session_result.aggregated_emotions.items(), key=lambda x: x[1], reverse=True)[:3]
                print(f"      Top emotions: {[f'{name}({score:.2f})' for name, score in top_emotions]}")
        else:
            print(f"\n   ⚠️ No session aggregation data available")
        
        # Calculate scenario accuracy
        scenario_accuracy = calculate_accuracy_with_families(scenario_results)
        print(f"\n   🎯 SCENARIO ACCURACY:")
        print(f"      Exact: {scenario_accuracy['exact_percent']:.1f}% ({scenario_accuracy['exact']}/{scenario_accuracy['total']})")
        print(f"      Family: {scenario_accuracy['family_percent']:.1f}% ({scenario_accuracy['family']}/{scenario_accuracy['total']})")
        
        # Show accuracy progression over time
        if len(scenario_results) >= 5:
            early_results = scenario_results[:len(scenario_results)//2]
            late_results = scenario_results[len(scenario_results)//2:]
            
            early_accuracy = calculate_accuracy_with_families(early_results)
            late_accuracy = calculate_accuracy_with_families(late_results)
            
            print(f"      📈 ACCURACY OVER TIME:")
            print(f"         Early interactions: {early_accuracy['family_percent']:.1f}%")
            print(f"         Later interactions: {late_accuracy['family_percent']:.1f}%")
            print(f"         Improvement: {late_accuracy['family_percent'] - early_accuracy['family_percent']:+.1f}%")
    
    return all_results


async def analyze_overall_results(results: List[Dict]):
    """Analyze overall results across all extended scenarios."""
    
    print(f"\n🏆 OVERALL EXTENDED SCENARIO ANALYSIS")
    print("=" * 70)
    
    if not results:
        print("❌ No results to analyze")
        return
    
    # Overall accuracy
    overall_accuracy = calculate_accuracy_with_families(results)
    
    print(f"📊 Overall Results:")
    print(f"   Total interactions: {overall_accuracy['total']}")
    print(f"   Exact accuracy: {overall_accuracy['exact_percent']:.1f}%")
    print(f"   Family accuracy: {overall_accuracy['family_percent']:.1f}%")
    
    # Accuracy by scenario
    scenarios = {}
    for result in results:
        scenario = result['scenario']
        if scenario not in scenarios:
            scenarios[scenario] = []
        scenarios[scenario].append(result)
    
    print(f"\n📈 Accuracy by Extended Scenario:")
    for scenario_name, scenario_results in scenarios.items():
        accuracy = calculate_accuracy_with_families(scenario_results)
        print(f"   🎭 {scenario_name}: {accuracy['family_percent']:.1f}% ({len(scenario_results)} interactions)")
    
    # Confidence analysis
    confidences = [r['confidence'] for r in results]
    avg_confidence = sum(confidences) / len(confidences)
    high_confidence_count = len([c for c in confidences if c > 3.0])
    
    print(f"\n🎯 Confidence Analysis:")
    print(f"   Average confidence: {avg_confidence:.2f}")
    print(f"   High confidence (>3.0): {high_confidence_count}/{len(confidences)} ({high_confidence_count/len(confidences)*100:.1f}%)")
    
    # Production assessment
    family_accuracy = overall_accuracy['family_percent']
    
    print(f"\n🚀 PRODUCTION ASSESSMENT:")
    if family_accuracy >= 80:
        print("   ✅ EXCELLENT - Ready for production deployment!")
    elif family_accuracy >= 75:
        print("   ✅ VERY GOOD - Strong performance for production")
    elif family_accuracy >= 70:
        print("   ⚠️ GOOD - Acceptable for production with monitoring")
    else:
        print("   ❌ NEEDS IMPROVEMENT - Additional optimization required")
    
    print(f"\n📋 Key Insights:")
    print(f"   • Extended scenarios provide {family_accuracy:.1f}% family accuracy")
    print(f"   • Average confidence: {avg_confidence:.2f}")
    print(f"   • Multi-modal approach working effectively")
    print(f"   • Session aggregation providing emotional context")


async def main():
    """Run extended session aggregation test."""
    
    print("🎯 EXTENDED SESSION-BASED EMOTION AGGREGATION TEST")
    print("=" * 80)
    print("Testing accuracy over time with 10-15 interaction scenarios")
    
    results = await test_extended_scenarios()
    
    if results:
        await analyze_overall_results(results)
    
    print(f"\n✨ Extended session aggregation test completed!")
    print(f"🎭 Tested emotion accuracy over extended conversation flows")


if __name__ == "__main__":
    asyncio.run(main())
