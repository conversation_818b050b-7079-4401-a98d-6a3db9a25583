#!/usr/bin/env python
"""
Django test runner for ellahai-backend.
"""
import os
import sys
import argparse
import django
from django.conf import settings
from django.test.runner import DiscoverRunner
from django.core.management import call_command

class <PERSON><PERSON><PERSON><PERSON>estRunner(DiscoverRunner):
    """Custom test runner for EllaHai backend."""
    
    def __init__(self, pattern=None, top_level=None, verbosity=1,
                 interactive=True, failfast=False, keepdb=False,
                 reverse=False, debug_mode=False, debug_sql=False, parallel=0,
                 tags=None, exclude_tags=None, test_name_patterns=None,
                 **kwargs):
        
        super().__init__(
            pattern=pattern, top_level=top_level, verbosity=verbosity,
            interactive=interactive, failfast=failfast, keepdb=keepdb,
            reverse=reverse, debug_mode=debug_mode, debug_sql=debug_sql,
            parallel=parallel, tags=tags, exclude_tags=exclude_tags,
            test_name_patterns=test_name_patterns, **kwargs
        )

def run_tests(args):
    """Run tests with specified options."""
    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
    django.setup()
    
    # Create test runner options
    test_runner_kwargs = {
        'verbosity': 2 if args.verbose else 1,
        'failfast': args.failfast,
        'keepdb': args.keepdb,
        'parallel': args.parallel,
    }
    
    # Add tags if specified
    if args.tags:
        test_runner_kwargs['tags'] = args.tags
    
    # Add exclude tags
    exclude_tags = []
    if args.skip_slow:
        exclude_tags.append('slow')
    if args.skip_integration:
        exclude_tags.append('integration')
    if args.skip_api:
        exclude_tags.append('api')
    
    if exclude_tags:
        test_runner_kwargs['exclude_tags'] = exclude_tags
    
    # Create test runner
    test_runner = EllaHaiTestRunner(**test_runner_kwargs)
    
    # Determine which tests to run
    test_labels = [args.path] if args.path else None
    
    # Run the tests
    failures = test_runner.run_tests(test_labels)
    
    # Generate coverage report if requested
    if args.coverage:
        try:
            import coverage
            cov = coverage.Coverage()
            cov.load()
            cov.report()
            if args.html:
                cov.html_report(directory='htmlcov')
                print("\nHTML coverage report generated in htmlcov/index.html")
        except ImportError:
            print("Coverage package not installed. Run 'pip install coverage' to enable coverage reporting.")
    
    return failures

def main():
    """Parse arguments and run tests."""
    parser = argparse.ArgumentParser(description="Run ellahai-backend tests")
    
    parser.add_argument("path", nargs="?", help="Test path to run (app or app.TestCase)")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    parser.add_argument("--tags", nargs="+", help="Only run tests with these tags")
    parser.add_argument("--skip-slow", action="store_true", help="Skip slow tests")
    parser.add_argument("--skip-integration", action="store_true", help="Skip integration tests")
    parser.add_argument("--skip-api", action="store_true", help="Skip tests requiring API access")
    parser.add_argument("--coverage", action="store_true", help="Run with coverage")
    parser.add_argument("--html", action="store_true", help="Generate HTML coverage report")
    parser.add_argument("-x", "--failfast", action="store_true", help="Stop on first failure")
    parser.add_argument("--keepdb", action="store_true", help="Keep test database between runs")
    parser.add_argument("--parallel", type=int, default=0, help="Number of parallel test processes")
    
    args = parser.parse_args()
    
    # If coverage is requested, start coverage collection
    if args.coverage:
        try:
            import coverage
            cov = coverage.Coverage()
            cov.start()
        except ImportError:
            print("Coverage package not installed. Run 'pip install coverage' to enable coverage reporting.")
    
    failures = run_tests(args)
    
    # Stop coverage collection
    if args.coverage:
        try:
            cov.stop()
            cov.save()
        except:
            pass
    
    return failures

if __name__ == "__main__":
    sys.exit(main())