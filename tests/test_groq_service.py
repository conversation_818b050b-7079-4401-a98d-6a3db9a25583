"""
Tests for Groq streaming service.
"""
import pytest
import asyncio
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch
import os
from dataclasses import dataclass
from typing import AsyncGenerator

from chat.services.groq_service import (
    GroqStreamingClient,
    GroqResponse,
    GroqStreamChunk,
    CircuitBreaker,
    stream_groq_response,
    stream_with_fallback
)


class TestCircuitBreaker:
    """Test circuit breaker functionality."""
    
    def test_initial_state(self):
        """Test circuit breaker initial state."""
        cb = CircuitBreaker(failure_threshold=3, recovery_timeout=60)
        
        assert cb.state == 'closed'
        assert cb.failure_count == 0
        assert cb.can_execute() is True
    
    def test_failure_recording(self):
        """Test recording failures."""
        cb = CircuitBreaker(failure_threshold=2, recovery_timeout=60)
        
        # First failure
        cb.record_failure()
        assert cb.failure_count == 1
        assert cb.state == 'closed'
        assert cb.can_execute() is True
        
        # Second failure - should open circuit
        cb.record_failure()
        assert cb.failure_count == 2
        assert cb.state == 'open'
        assert cb.can_execute() is False
    
    def test_success_reset(self):
        """Test that success resets failure count."""
        cb = CircuitBreaker(failure_threshold=2, recovery_timeout=60)
        
        cb.record_failure()
        assert cb.failure_count == 1
        
        cb.record_success()
        assert cb.failure_count == 0
        assert cb.state == 'closed'
    
    def test_recovery_timeout(self):
        """Test recovery after timeout."""
        import time
        
        cb = CircuitBreaker(failure_threshold=1, recovery_timeout=0.1)  # 100ms timeout
        
        # Trigger circuit breaker
        cb.record_failure()
        assert cb.state == 'open'
        assert cb.can_execute() is False
        
        # Wait for recovery timeout
        time.sleep(0.2)
        
        # Should be in half-open state
        assert cb.can_execute() is True


@pytest.mark.asyncio
class TestGroqStreamingClient:
    """Test Groq streaming client."""
    
    @pytest.fixture
    def mock_groq_client(self):
        """Mock Groq client."""
        with patch('chat.services.groq_service.AsyncGroq') as mock:
            yield mock
    
    @pytest.fixture
    def client(self, mock_groq_client):
        """Create test client."""
        with patch.dict(os.environ, {'GROQ_API_KEY': 'test-key'}):
            return GroqStreamingClient()
    
    def test_client_initialization(self, mock_groq_client):
        """Test client initialization."""
        with patch.dict(os.environ, {'GROQ_API_KEY': 'test-key'}):
            client = GroqStreamingClient()
            
            assert client.api_key == 'test-key'
            assert client.base_url == "https://api.groq.com/openai/v1"
            assert client.max_retries == 3
            assert client.timeout == 30.0
    
    def test_client_initialization_no_api_key(self, mock_groq_client):
        """Test client initialization without API key."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="GROQ_API_KEY must be provided"):
                GroqStreamingClient()
    
    async def test_stream_chat_completion_success(self, client):
        """Test successful streaming chat completion."""
        # Mock response chunks
        mock_chunks = [
            MagicMock(choices=[MagicMock(delta=MagicMock(content="Hello"), finish_reason=None)]),
            MagicMock(choices=[MagicMock(delta=MagicMock(content=" world"), finish_reason=None)]),
            MagicMock(choices=[MagicMock(delta=MagicMock(content="!"), finish_reason="stop")])
        ]
        
        async def mock_stream():
            for chunk in mock_chunks:
                yield chunk
        
        # Mock the client's create method
        client.client.chat.completions.create = AsyncMock(return_value=mock_stream())
        
        messages = [{"role": "user", "content": "Hello"}]
        chunks = []
        
        async for chunk in client.stream_chat_completion(messages):
            chunks.append(chunk)
        
        assert len(chunks) == 3
        assert chunks[0].content == "Hello"
        assert chunks[1].content == " world"
        assert chunks[2].content == "!"
        assert chunks[2].is_final is True
    
    async def test_stream_chat_completion_circuit_breaker_open(self, client):
        """Test streaming when circuit breaker is open."""
        # Force circuit breaker open
        client.circuit_breaker.state = 'open'
        
        messages = [{"role": "user", "content": "Hello"}]
        
        with pytest.raises(Exception, match="Circuit breaker is open"):
            async for chunk in client.stream_chat_completion(messages):
                pass
    
    async def test_chat_completion_success(self, client):
        """Test successful non-streaming chat completion."""
        # Mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock(message=MagicMock(content="Hello world!"), finish_reason="stop")]
        mock_response.model = "llama-3.3-70b-versatile"
        mock_response.usage = MagicMock(total_tokens=10)
        mock_response.id = "test-id"
        
        client.client.chat.completions.create = AsyncMock(return_value=mock_response)
        
        messages = [{"role": "user", "content": "Hello"}]
        response = await client.chat_completion(messages)
        
        assert isinstance(response, GroqResponse)
        assert response.content == "Hello world!"
        assert response.model == "llama-3.3-70b-versatile"
        assert response.tokens_used == 10
        assert response.finish_reason == "stop"
        assert response.request_id == "test-id"
    
    async def test_health_check_success(self, client):
        """Test successful health check."""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock(message=MagicMock(content="Hi"), finish_reason="stop")]
        mock_response.model = "llama-3.3-70b-versatile"
        mock_response.usage = MagicMock(total_tokens=5)
        
        client.client.chat.completions.create = AsyncMock(return_value=mock_response)
        
        is_healthy = await client.health_check()
        assert is_healthy is True
    
    async def test_health_check_failure(self, client):
        """Test health check failure."""
        client.client.chat.completions.create = AsyncMock(side_effect=Exception("API Error"))
        
        is_healthy = await client.health_check()
        assert is_healthy is False
    
    def test_performance_stats(self, client):
        """Test performance statistics."""
        # Initially no stats
        stats = client.get_performance_stats()
        assert stats['total_requests'] == 0
        assert stats['average_response_time_ms'] == 0.0
        
        # Update metrics manually
        client._update_performance_metrics(200.0, 50.0)
        client._update_performance_metrics(300.0, 75.0)
        
        stats = client.get_performance_stats()
        assert stats['total_requests'] == 2
        assert stats['average_response_time_ms'] == 250.0  # (200 + 300) / 2
        assert stats['average_first_token_time_ms'] == 62.5  # (50 + 75) / 2


@pytest.mark.asyncio
class TestConvenienceFunctions:
    """Test convenience functions."""
    
    @patch('chat.services.groq_service.get_groq_client')
    async def test_stream_groq_response(self, mock_get_client):
        """Test stream_groq_response convenience function."""
        # Mock client and chunks
        mock_client = AsyncMock()
        mock_chunks = [
            GroqStreamChunk("Hello", False, 0, 1000.0),
            GroqStreamChunk(" world", False, 1, 1001.0),
            GroqStreamChunk("!", True, 2, 1002.0, "stop")
        ]
        
        async def mock_stream(*args, **kwargs):
            for chunk in mock_chunks:
                yield chunk
        
        mock_client.stream_chat_completion = mock_stream
        mock_get_client.return_value.__aenter__ = AsyncMock(return_value=mock_client)
        mock_get_client.return_value.__aexit__ = AsyncMock(return_value=None)
        
        messages = [{"role": "user", "content": "Hello"}]
        content_chunks = []
        
        async for content in stream_groq_response(messages):
            content_chunks.append(content)
        
        assert content_chunks == ["Hello", " world", "!"]
    
    @patch('chat.services.groq_service.stream_groq_response')
    @patch('chat.services.groq_service._fallback_service')
    async def test_stream_with_fallback_success(self, mock_fallback, mock_groq):
        """Test stream_with_fallback when Groq succeeds."""
        # Mock successful Groq response
        async def mock_groq_stream(*args, **kwargs):
            yield "Hello"
            yield " from"
            yield " Groq"
        
        mock_groq.return_value = mock_groq_stream()
        
        messages = [{"role": "user", "content": "Hello"}]
        chunks = []
        
        async for chunk in stream_with_fallback(messages):
            chunks.append(chunk)
        
        assert chunks == ["Hello", " from", " Groq"]
        # Fallback should not be called
        mock_fallback.stream_chat_completion.assert_not_called()
    
    @patch('chat.services.groq_service.stream_groq_response')
    @patch('chat.services.groq_service._fallback_service')
    async def test_stream_with_fallback_groq_fails(self, mock_fallback, mock_groq):
        """Test stream_with_fallback when Groq fails."""
        # Mock Groq failure
        async def mock_groq_stream(*args, **kwargs):
            raise Exception("Groq API Error")
        
        mock_groq.return_value = mock_groq_stream()
        
        # Mock successful fallback
        async def mock_fallback_stream(*args, **kwargs):
            yield "Hello"
            yield " from"
            yield " OpenAI"
        
        mock_fallback.stream_chat_completion = mock_fallback_stream
        
        messages = [{"role": "user", "content": "Hello"}]
        chunks = []
        
        async for chunk in stream_with_fallback(messages):
            chunks.append(chunk)
        
        assert chunks == ["Hello", " from", " OpenAI"]
    
    @patch('chat.services.groq_service.stream_groq_response')
    @patch('chat.services.groq_service._fallback_service')
    async def test_stream_with_fallback_both_fail(self, mock_fallback, mock_groq):
        """Test stream_with_fallback when both services fail."""
        # Mock both services failing
        async def mock_groq_stream(*args, **kwargs):
            raise Exception("Groq API Error")
        
        async def mock_fallback_stream(*args, **kwargs):
            raise Exception("OpenAI API Error")
        
        mock_groq.return_value = mock_groq_stream()
        mock_fallback.stream_chat_completion = mock_fallback_stream
        
        messages = [{"role": "user", "content": "Hello"}]
        chunks = []
        
        async for chunk in stream_with_fallback(messages):
            chunks.append(chunk)
        
        # Should get error message
        assert len(chunks) == 1
        assert "technical difficulties" in chunks[0]


@pytest.mark.integration
@pytest.mark.asyncio
class TestGroqIntegration:
    """Integration tests for Groq service (requires API key)."""
    
    @pytest.mark.skipif(
        not os.getenv('GROQ_API_KEY'),
        reason="GROQ_API_KEY not set"
    )
    async def test_real_groq_streaming(self):
        """Test real Groq API streaming (integration test)."""
        client = GroqStreamingClient()
        
        messages = [
            {"role": "user", "content": "Say hello in exactly 3 words"}
        ]
        
        chunks = []
        async for chunk in client.stream_chat_completion(
            messages,
            max_tokens=10,
            temperature=0.1
        ):
            chunks.append(chunk)
            if chunk.is_final:
                break
        
        # Should have received at least one chunk
        assert len(chunks) > 0
        
        # Combine content
        full_content = "".join(chunk.content for chunk in chunks)
        assert len(full_content) > 0
        
        # Check performance
        stats = client.get_performance_stats()
        assert stats['total_requests'] == 1
        assert stats['average_first_token_time_ms'] > 0
        assert stats['average_first_token_time_ms'] < 5000  # Should be under 5 seconds
    
    @pytest.mark.skipif(
        not os.getenv('GROQ_API_KEY'),
        reason="GROQ_API_KEY not set"
    )
    async def test_real_groq_health_check(self):
        """Test real Groq API health check."""
        client = GroqStreamingClient()
        
        is_healthy = await client.health_check()
        assert is_healthy is True
    
    @pytest.mark.skipif(
        not os.getenv('GROQ_API_KEY'),
        reason="GROQ_API_KEY not set"
    )
    async def test_performance_target(self):
        """Test that first token time meets performance target."""
        client = GroqStreamingClient()
        
        messages = [
            {"role": "user", "content": "Hi"}
        ]
        
        import time
        start_time = time.time()
        first_token_time = None
        
        async for chunk in client.stream_chat_completion(
            messages,
            max_tokens=5,
            temperature=0.0
        ):
            if first_token_time is None:
                first_token_time = (time.time() - start_time) * 1000
            
            if chunk.is_final:
                break
        
        # Should meet <200ms first token target (allowing some network latency)
        assert first_token_time is not None
        assert first_token_time < 1000  # 1 second max for integration test
        
        print(f"First token time: {first_token_time:.2f}ms")


if __name__ == "__main__":
    # Run basic tests
    pytest.main([__file__, "-v"])