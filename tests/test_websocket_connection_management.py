"""
Tests for WebSocket connection management functionality.
"""
import pytest
import json
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from channels.testing import WebsocketCommunicator
from django.contrib.auth import get_user_model
from django.test import TransactionTestCase

from chat.consumers import ChatConsumer
from chat.models import StreamingSession

User = get_user_model()


class TestWebSocketConnectionManagement(TransactionTestCase):
    """Test WebSocket connection management features."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    async def test_heartbeat_monitoring(self):
        """Test heartbeat monitoring functionality."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        connection_msg = await communicator.receive_json_from()
        assert connection_msg['type'] == 'connection_established'
        
        # Wait for heartbeat request (with a shorter timeout for testing)
        try:
            # Send a heartbeat to trigger the monitoring
            await communicator.send_json_to({
                'type': 'connection_heartbeat',
                'timestamp': time.time() * 1000
            })
            
            # Wait for heartbeat response
            heartbeat_msg = await asyncio.wait_for(
                communicator.receive_json_from(), 
                timeout=5  # Shorter timeout for response
            )
            assert heartbeat_msg['type'] == 'heartbeat_response'
            assert 'timestamp' in heartbeat_msg
            assert 'session_id' in heartbeat_msg
        except asyncio.TimeoutError:
            pytest.fail("Did not receive heartbeat response within expected time")
        
        await communicator.disconnect()
    
    async def test_heartbeat_response(self):
        """Test heartbeat response handling."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        await communicator.receive_json_from()
        
        # Send heartbeat
        await communicator.send_json_to({
            'type': 'connection_heartbeat',
            'timestamp': 1234567890000
        })
        
        # Should receive heartbeat response
        response = await communicator.receive_json_from()
        assert response['type'] == 'heartbeat_response'
        assert 'timestamp' in response
        assert 'session_id' in response
        assert 'connection_id' in response
        assert response['connection_state'] == 'active'
        
        await communicator.disconnect()
    
    async def test_reconnection_with_session_restore(self):
        """Test reconnection with session state restoration."""
        # First connection
        communicator1 = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator1.scope["user"] = self.user
        
        connected, _ = await communicator1.connect()
        assert connected
        
        # Get session info
        connection_msg = await communicator1.receive_json_from()
        session_id = connection_msg['session_id']
        connection_id = connection_msg['connection_id']
        
        # Disconnect
        await communicator1.disconnect()
        
        # Second connection (reconnection)
        communicator2 = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator2.scope["user"] = self.user
        
        connected, _ = await communicator2.connect()
        assert connected
        
        # Skip connection message
        await communicator2.receive_json_from()
        
        # Send reconnection request
        await communicator2.send_json_to({
            'type': 'reconnection_request',
            'previous_session_id': session_id,
            'previous_connection_id': connection_id,
            'reason': 'connection_lost'
        })
        
        # Should receive reconnection accepted
        response = await communicator2.receive_json_from()
        assert response['type'] == 'reconnection_accepted'
        assert 'backoff_delay_seconds' in response
        assert 'attempt' in response
        assert 'session_id' in response
        assert 'connection_id' in response
        
        await communicator2.disconnect()
    
    async def test_message_queuing_during_disconnection(self):
        """Test message queuing when connection is down."""
        consumer = ChatConsumer()
        consumer.user = self.user
        consumer.connection_state = 'disconnected'
        consumer.message_queue = []
        consumer.max_queue_size = 5
        
        # Test queuing messages
        test_message = {'type': 'test_message', 'content': 'test'}
        await consumer.queue_message(test_message)
        
        assert len(consumer.message_queue) == 1
        assert consumer.message_queue[0]['content'] == 'test'
        assert 'queued_at' in consumer.message_queue[0]
    
    async def test_queue_size_limit(self):
        """Test message queue size limiting."""
        consumer = ChatConsumer()
        consumer.user = self.user
        consumer.connection_state = 'disconnected'
        consumer.message_queue = []
        consumer.max_queue_size = 3
        
        # Add messages beyond limit
        for i in range(5):
            await consumer.queue_message({'type': 'test', 'id': i})
        
        # Should only keep last 3 messages
        assert len(consumer.message_queue) == 3
        assert consumer.message_queue[0]['id'] == 2  # Oldest kept message
        assert consumer.message_queue[-1]['id'] == 4  # Newest message
    
    async def test_session_state_saving(self):
        """Test session state saving functionality."""
        consumer = ChatConsumer()
        consumer.user = self.user
        consumer.session_id = 'test-session-123'
        consumer.connection_state = 'active'
        consumer.is_processing_audio = True
        consumer.current_audio_chunks = ['chunk1', 'chunk2']
        consumer.message_queue = [{'type': 'test'}]
        
        # Create mock streaming session
        consumer.streaming_session = Mock()
        consumer.streaming_session.performance_metrics = {}
        consumer.streaming_session.save = AsyncMock()
        
        # Save session state
        await consumer.save_session_state()
        
        # Verify state was saved
        saved_state = consumer.streaming_session.performance_metrics['session_state']
        assert saved_state['is_processing_audio'] is True
        assert saved_state['current_audio_chunks'] == 2
        assert saved_state['connection_state'] == 'active'
        assert saved_state['queued_messages'] == 1
    
    async def test_connection_timeout_handling(self):
        """Test connection timeout handling."""
        consumer = ChatConsumer()
        consumer.user = self.user
        consumer.session_id = 'test-session-123'
        consumer.connection_state = 'active'
        consumer.streaming_session = Mock()
        consumer.streaming_session.performance_metrics = {}
        consumer.streaming_session.save = AsyncMock()
        
        # Mock send and close methods
        consumer.send = AsyncMock()
        consumer.close = AsyncMock()
        consumer.save_session_state = AsyncMock()
        
        # Handle timeout
        await consumer.handle_connection_timeout()
        
        # Verify timeout handling
        consumer.save_session_state.assert_called_once()
        consumer.send.assert_called_once()
        consumer.close.assert_called_once()
        
        # Check timeout message
        call_args = consumer.send.call_args[1]
        message_data = json.loads(call_args['text_data'])
        assert message_data['type'] == 'connection_timeout'
        assert message_data['session_id'] == 'test-session-123'
    
    async def test_exponential_backoff_calculation(self):
        """Test exponential backoff for reconnection attempts."""
        consumer = ChatConsumer()
        consumer.user = self.user
        consumer.max_reconnection_attempts = 5
        consumer.send = AsyncMock()
        consumer.close = AsyncMock()
        consumer.reset_connection_state = AsyncMock()
        
        # Test multiple reconnection attempts
        for attempt in range(1, 6):
            consumer.reconnection_attempts = attempt - 1
            
            await consumer.handle_reconnection_request({
                'previous_session_id': 'test-session',
                'previous_connection_id': 'test-connection'
            })
            
            if attempt <= 5:
                # Should accept reconnection
                call_args = consumer.send.call_args[1]
                message_data = json.loads(call_args['text_data'])
                
                expected_delay = min(2 ** attempt, 30)
                assert message_data['backoff_delay_seconds'] == expected_delay
                assert message_data['attempt'] == attempt
        
        # Test exceeding max attempts
        consumer.reconnection_attempts = 5
        consumer.send_error = AsyncMock()
        
        await consumer.handle_reconnection_request({
            'previous_session_id': 'test-session'
        })
        
        consumer.send_error.assert_called_once()
        consumer.close.assert_called_once()
    
    async def test_send_with_queue_fallback(self):
        """Test sending messages with queue fallback."""
        consumer = ChatConsumer()
        consumer.user = self.user
        consumer.message_queue = []
        consumer.send = AsyncMock()
        consumer.queue_message = AsyncMock()
        
        test_message = {'type': 'test', 'content': 'hello'}
        
        # Test active connection
        consumer.connection_state = 'active'
        await consumer.send_with_queue_fallback(test_message)
        
        consumer.send.assert_called_once()
        consumer.queue_message.assert_not_called()
        
        # Reset mocks
        consumer.send.reset_mock()
        consumer.queue_message.reset_mock()
        
        # Test inactive connection
        consumer.connection_state = 'disconnected'
        await consumer.send_with_queue_fallback(test_message)
        
        consumer.send.assert_not_called()
        consumer.queue_message.assert_called_once_with(test_message)
    
    async def test_process_queued_messages(self):
        """Test processing of queued messages after reconnection."""
        consumer = ChatConsumer()
        consumer.user = self.user
        consumer.send = AsyncMock()
        
        # Add messages to queue
        consumer.message_queue = [
            {'type': 'message1', 'content': 'hello'},
            {'type': 'message2', 'content': 'world'}
        ]
        
        # Process queue
        await consumer.process_queued_messages()
        
        # Should have sent both messages
        assert consumer.send.call_count == 2
        
        # Queue should be empty
        assert len(consumer.message_queue) == 0
        
        # Check message format
        call_args = consumer.send.call_args_list
        for i, call in enumerate(call_args):
            message_data = json.loads(call[1]['text_data'])
            assert message_data['type'] == 'queued_message'
            assert 'delivered_at' in message_data


class TestConnectionStateManagement:
    """Test connection state management functionality."""
    
    def test_connection_state_transitions(self):
        """Test connection state transitions."""
        consumer = ChatConsumer()
        
        # Initial state
        assert consumer.connection_state == 'disconnected'
        
        # Test state transitions
        consumer.connection_state = 'connecting'
        assert consumer.connection_state == 'connecting'
        
        consumer.connection_state = 'active'
        assert consumer.connection_state == 'active'
        
        consumer.connection_state = 'disconnecting'
        assert consumer.connection_state == 'disconnecting'
        
        consumer.connection_state = 'disconnected'
        assert consumer.connection_state == 'disconnected'
    
    def test_heartbeat_timing(self):
        """Test heartbeat timing calculations."""
        consumer = ChatConsumer()
        consumer.heartbeat_interval = 30
        
        import time
        current_time = time.time()
        consumer.last_heartbeat = current_time
        
        # Should not timeout immediately
        time_since_heartbeat = current_time - consumer.last_heartbeat
        assert time_since_heartbeat < (consumer.heartbeat_interval * 2)
        
        # Should timeout after interval
        consumer.last_heartbeat = current_time - (consumer.heartbeat_interval * 3)
        time_since_heartbeat = current_time - consumer.last_heartbeat
        assert time_since_heartbeat > (consumer.heartbeat_interval * 2)


if __name__ == '__main__':
    pytest.main([__file__])