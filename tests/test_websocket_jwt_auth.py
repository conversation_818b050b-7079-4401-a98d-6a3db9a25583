import pytest
import json
from channels.testing import Websocket<PERSON>ommunicator
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from ellahai_backend.asgi import application

User = get_user_model()

@pytest.mark.asyncio
@pytest.mark.django_db
async def test_websocket_jwt_auth():
    """Test WebSocket authentication with JWT token."""
    # Create a test user
    user = User.objects.create_user(
        username='<EMAIL>',
        email='<EMAIL>',
        password='testpassword'
    )
    
    # Generate JWT token for the user
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    # Connect with token in query string
    communicator = WebsocketCommunicator(
        application, f"/ws/chat/?token={access_token}"
    )
    connected, _ = await communicator.connect()
    
    # Check that the connection was accepted
    assert connected
    
    # Send a message to check authentication
    await communicator.send_json_to({
        'type': 'connection_heartbeat'
    })
    
    # Receive response
    response = await communicator.receive_json_from()
    
    # Check that the response contains the expected data
    assert response['type'] == 'heartbeat_response'
    assert 'session_id' in response
    assert 'connection_id' in response
    
    # Close the connection
    await communicator.disconnect()

@pytest.mark.asyncio
@pytest.mark.django_db
async def test_websocket_jwt_auth_header():
    """Test WebSocket authentication with JWT token in header."""
    # Create a test user
    user = User.objects.create_user(
        username='<EMAIL>',
        email='<EMAIL>',
        password='testpassword'
    )
    
    # Generate JWT token for the user
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    # Connect with token in header
    communicator = WebsocketCommunicator(
        application, "/ws/chat/",
        headers=[(b'authorization', f'Bearer {access_token}'.encode())]
    )
    connected, _ = await communicator.connect()
    
    # Check that the connection was accepted
    assert connected
    
    # Close the connection
    await communicator.disconnect()

@pytest.mark.asyncio
@pytest.mark.django_db
async def test_websocket_invalid_token():
    """Test WebSocket authentication with invalid JWT token."""
    # Connect with invalid token
    communicator = WebsocketCommunicator(
        application, "/ws/chat/?token=invalid_token"
    )
    connected, _ = await communicator.connect()
    
    # Connection should be rejected
    assert not connected