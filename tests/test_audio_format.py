"""
Tests for audio format handling and validation.
"""
import base64
import io
import wave
import struct
import numpy as np
from unittest.mock import patch, MagicMock
from django.test import TestCase

from chat.services.audio_format import (
    AudioFormatHandler,
    AudioMetadata,
    AudioFormat,
    decode_and_validate_audio,
    prepare_audio_for_processing
)


class TestAudioFormat(TestCase):
    """Test AudioFormat enum."""
    
    def test_audio_format_values(self):
        """Test AudioFormat enum values."""
        self.assertEqual(AudioFormat.WAV.value, "wav")
        self.assertEqual(AudioFormat.MP3.value, "mp3")
        self.assertEqual(AudioFormat.WEBM.value, "webm")
        self.assertEqual(AudioFormat.OGG.value, "ogg")
        self.assertEqual(AudioFormat.FLAC.value, "flac")
        self.assertEqual(AudioFormat.PCM.value, "pcm")


class TestAudioMetadata(TestCase):
    """Test AudioMetadata dataclass."""
    
    def test_audio_metadata_creation(self):
        """Test creating AudioMetadata."""
        metadata = AudioMetadata(
            format=AudioFormat.WAV,
            sample_rate=48000,
            channels=1,
            duration_ms=1000.0,
            bit_depth=16,
            size_bytes=96000,
            is_valid=True,
            quality_score=0.8
        )
        
        self.assertEqual(metadata.format, AudioFormat.WAV)
        self.assertEqual(metadata.sample_rate, 48000)
        self.assertEqual(metadata.channels, 1)
        self.assertEqual(metadata.duration_ms, 1000.0)
        self.assertEqual(metadata.bit_depth, 16)
        self.assertEqual(metadata.size_bytes, 96000)
        self.assertTrue(metadata.is_valid)
        self.assertEqual(metadata.quality_score, 0.8)
        self.assertIsNone(metadata.error_message)
    
    def test_audio_metadata_with_error(self):
        """Test AudioMetadata with error."""
        metadata = AudioMetadata(
            format=AudioFormat.PCM,
            sample_rate=0,
            channels=0,
            duration_ms=0.0,
            bit_depth=0,
            size_bytes=0,
            is_valid=False,
            error_message="Test error"
        )
        
        self.assertFalse(metadata.is_valid)
        self.assertEqual(metadata.error_message, "Test error")


class TestAudioFormatHandler(TestCase):
    """Test AudioFormatHandler functionality."""
    
    def test_decode_base64_plain(self):
        """Test decoding plain base64 audio."""
        test_data = b"test_audio_data"
        base64_data = base64.b64encode(test_data).decode()
        
        audio_bytes, detected_format = AudioFormatHandler.decode_base64_audio(base64_data)
        
        self.assertEqual(audio_bytes, test_data)
        self.assertIsNone(detected_format)  # No format detected from plain data
    
    def test_decode_base64_data_uri(self):
        """Test decoding data URI format."""
        test_data = b"test_audio_data"
        base64_data = base64.b64encode(test_data).decode()
        data_uri = f"data:audio/wav;base64,{base64_data}"
        
        audio_bytes, detected_format = AudioFormatHandler.decode_base64_audio(data_uri)
        
        self.assertEqual(audio_bytes, test_data)
        self.assertEqual(detected_format, AudioFormat.WAV)
    
    def test_decode_base64_invalid(self):
        """Test decoding invalid base64 data."""
        with self.assertRaises(ValueError):
            AudioFormatHandler.decode_base64_audio("invalid_base64!")
    
    def test_detect_format_wav(self):
        """Test WAV format detection."""
        # Create minimal WAV header
        wav_header = b'RIFF' + b'\x00' * 4 + b'WAVE' + b'\x00' * 4
        
        detected_format = AudioFormatHandler._detect_format_from_bytes(wav_header)
        
        self.assertEqual(detected_format, AudioFormat.WAV)
    
    def test_detect_format_mp3(self):
        """Test MP3 format detection."""
        mp3_header = b'ID3' + b'\x00' * 10
        
        detected_format = AudioFormatHandler._detect_format_from_bytes(mp3_header)
        
        self.assertEqual(detected_format, AudioFormat.MP3)
    
    def test_detect_format_unknown(self):
        """Test unknown format detection."""
        unknown_data = b'UNKNOWN_FORMAT'
        
        detected_format = AudioFormatHandler._detect_format_from_bytes(unknown_data)
        
        self.assertIsNone(detected_format)
    
    def test_detect_format_too_short(self):
        """Test format detection with insufficient data."""
        short_data = b'ABC'
        
        detected_format = AudioFormatHandler._detect_format_from_bytes(short_data)
        
        self.assertIsNone(detected_format)
    
    def test_extract_wav_metadata(self):
        """Test extracting metadata from WAV file."""
        # Create a simple WAV file
        sample_rate = 44100
        duration = 0.1  # 100ms
        samples = int(sample_rate * duration)
        
        # Generate sine wave
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t)
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        # Create WAV file in memory
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        wav_data = wav_buffer.getvalue()
        
        # Extract metadata
        metadata = AudioFormatHandler.extract_metadata(wav_data, AudioFormat.WAV)
        
        self.assertTrue(metadata.is_valid)
        self.assertEqual(metadata.format, AudioFormat.WAV)
        self.assertEqual(metadata.sample_rate, sample_rate)
        self.assertEqual(metadata.channels, 1)
        self.assertEqual(metadata.bit_depth, 16)
        self.assertLess(abs(metadata.duration_ms - 100), 10)  # Allow small tolerance
        self.assertGreater(metadata.quality_score, 0.0)
    
    def test_extract_pcm_metadata(self):
        """Test extracting metadata from PCM data."""
        # Create PCM data (1000 samples, 16-bit)
        pcm_data = np.random.randint(-1000, 1000, 1000, dtype=np.int16).tobytes()
        
        metadata = AudioFormatHandler.extract_metadata(pcm_data)
        
        self.assertTrue(metadata.is_valid)
        self.assertEqual(metadata.format, AudioFormat.PCM)
        self.assertEqual(metadata.sample_rate, AudioFormatHandler.DEFAULT_SAMPLE_RATE)
        self.assertEqual(metadata.channels, AudioFormatHandler.DEFAULT_CHANNELS)
        self.assertEqual(metadata.bit_depth, AudioFormatHandler.DEFAULT_BIT_DEPTH)
        self.assertGreaterEqual(metadata.quality_score, 0.0)
    
    def test_assess_audio_quality_empty(self):
        """Test audio quality assessment with empty samples."""
        empty_samples = np.array([])
        
        quality_score = AudioFormatHandler._assess_audio_quality_from_samples(empty_samples)
        
        self.assertEqual(quality_score, 0.0)
    
    def test_assess_audio_quality_good_signal(self):
        """Test audio quality assessment with good signal."""
        # Generate clean sine wave
        samples = 4800  # 100ms at 48kHz
        t = np.linspace(0, 0.1, samples, False)
        signal = np.sin(2 * np.pi * 440 * t) * 0.5  # Moderate amplitude
        audio_int16 = (signal * 32767).astype(np.int16)
        
        quality_score = AudioFormatHandler._assess_audio_quality_from_samples(audio_int16)
        
        self.assertGreater(quality_score, 0.5)  # Should be good quality
        self.assertLessEqual(quality_score, 1.0)
    
    def test_assess_audio_quality_noisy_signal(self):
        """Test audio quality assessment with noisy signal."""
        # Generate noisy signal
        samples = 4800
        noise = np.random.randint(-1000, 1000, samples, dtype=np.int16)
        
        quality_score = AudioFormatHandler._assess_audio_quality_from_samples(noise)
        
        self.assertGreaterEqual(quality_score, 0.0)
        self.assertLessEqual(quality_score, 1.0)
        # Noise should generally have lower quality, but we don't enforce a specific threshold
    
    def test_validate_audio_valid(self):
        """Test audio validation with valid audio."""
        # Create valid WAV data
        sample_rate = 48000
        duration = 1.0  # 1 second
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t) * 0.5
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        wav_data = wav_buffer.getvalue()
        
        is_valid, results = AudioFormatHandler.validate_audio(
            wav_data,
            min_duration_ms=500,
            max_duration_ms=2000,
            min_quality_score=0.1
        )
        
        self.assertTrue(is_valid)
        self.assertTrue(results['valid'])
        self.assertEqual(len(results['errors']), 0)
        self.assertIn('metadata', results)
    
    def test_validate_audio_too_short(self):
        """Test audio validation with too short audio."""
        # Create very short audio (50ms)
        sample_rate = 48000
        duration = 0.05  # 50ms
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t)
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        wav_data = wav_buffer.getvalue()
        
        is_valid, results = AudioFormatHandler.validate_audio(
            wav_data,
            min_duration_ms=100,  # Require at least 100ms
            max_duration_ms=2000
        )
        
        self.assertFalse(is_valid)
        self.assertFalse(results['valid'])
        self.assertGreater(len(results['errors']), 0)
        self.assertIn("too short", results['errors'][0])
    
    def test_validate_audio_wrong_channels(self):
        """Test audio validation with wrong channel count."""
        # Create stereo audio
        sample_rate = 48000
        duration = 1.0
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t)
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(2)  # Stereo
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            # Duplicate for stereo
            stereo_data = np.column_stack((audio_int16, audio_int16)).flatten()
            wav_file.writeframes(stereo_data.tobytes())
        
        wav_data = wav_buffer.getvalue()
        
        is_valid, results = AudioFormatHandler.validate_audio(
            wav_data,
            required_channels=1  # Require mono
        )
        
        self.assertFalse(is_valid)
        self.assertFalse(results['valid'])
        self.assertGreater(len(results['errors']), 0)
        self.assertIn("channel count", results['errors'][0])
    
    def test_convert_to_optimal_format_with_pydub(self):
        """Test audio conversion when pydub is not available (fallback behavior)."""
        # Since pydub is not available in test environment, test fallback behavior
        # Create WAV data that needs conversion
        sample_rate = 44100
        duration = 1.0
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t)
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(2)  # Stereo
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            # Create stereo data
            stereo_data = np.column_stack((audio_int16, audio_int16)).flatten()
            wav_file.writeframes(stereo_data.tobytes())
        
        wav_data = wav_buffer.getvalue()
        
        # Should return original data since pydub is not available
        converted_bytes, metadata = AudioFormatHandler.convert_to_optimal_format(
            wav_data,
            AudioFormat.WAV,
            target_sample_rate=48000,
            target_channels=1,
            target_bit_depth=16
        )
        
        # Should return original data when pydub is not available
        self.assertEqual(converted_bytes, wav_data)
        self.assertEqual(metadata.sample_rate, sample_rate)
        self.assertEqual(metadata.channels, 2)  # Original channels
    
    def test_convert_to_optimal_format_no_pydub(self):
        """Test audio conversion without pydub."""
        # Create WAV data that's already in optimal format
        sample_rate = 48000
        duration = 1.0
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t)
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        wav_data = wav_buffer.getvalue()
        
        # Should return as-is since it's already optimal
        converted_bytes, metadata = AudioFormatHandler.convert_to_optimal_format(
            wav_data,
            AudioFormat.WAV,
            target_sample_rate=48000,
            target_channels=1,
            target_bit_depth=16
        )
        
        self.assertEqual(converted_bytes, wav_data)
        self.assertEqual(metadata.sample_rate, sample_rate)
        self.assertEqual(metadata.channels, 1)
    
    def test_preprocess_for_transcription(self):
        """Test audio preprocessing for transcription."""
        # Create test audio
        sample_rate = 48000
        duration = 1.0
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t) * 0.3  # Lower amplitude
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        wav_data = wav_buffer.getvalue()
        
        processed_bytes, preprocessing_info = AudioFormatHandler.preprocess_for_transcription(wav_data)
        
        self.assertGreater(len(processed_bytes), 0)
        self.assertIn('applied_filters', preprocessing_info)
        self.assertIn('original_quality', preprocessing_info)
        self.assertIn('final_quality', preprocessing_info)
        self.assertGreaterEqual(preprocessing_info['original_quality'], 0.0)
        self.assertGreaterEqual(preprocessing_info['final_quality'], 0.0)


class TestConvenienceFunctions(TestCase):
    """Test convenience functions."""
    
    def test_decode_and_validate_audio_valid(self):
        """Test decode and validate with valid audio."""
        # Create valid WAV data
        sample_rate = 48000
        duration = 1.0
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t) * 0.5
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        wav_data = wav_buffer.getvalue()
        base64_data = base64.b64encode(wav_data).decode()
        
        is_valid, audio_bytes, metadata, errors = decode_and_validate_audio(
            base64_data,
            min_duration_ms=500,
            max_duration_ms=2000
        )
        
        self.assertTrue(is_valid)
        self.assertEqual(audio_bytes, wav_data)
        self.assertIsNotNone(metadata)
        self.assertTrue(metadata.is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_decode_and_validate_audio_invalid(self):
        """Test decode and validate with invalid audio."""
        invalid_base64 = "invalid_base64_data!"
        
        is_valid, audio_bytes, metadata, errors = decode_and_validate_audio(invalid_base64)
        
        self.assertFalse(is_valid)
        self.assertEqual(audio_bytes, b'')
        self.assertIsNone(metadata)
        self.assertGreater(len(errors), 0)
    
    def test_prepare_audio_for_processing_success(self):
        """Test complete audio preparation pipeline."""
        # Create valid WAV data
        sample_rate = 48000
        duration = 1.0
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t) * 0.5
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        wav_data = wav_buffer.getvalue()
        base64_data = base64.b64encode(wav_data).decode()
        
        success, processed_bytes, processing_info = prepare_audio_for_processing(base64_data)
        
        self.assertTrue(success)
        self.assertGreater(len(processed_bytes), 0)
        self.assertTrue(processing_info['validation_passed'])
        self.assertIn('final_metadata', processing_info)
        self.assertEqual(len(processing_info['errors']), 0)
    
    def test_prepare_audio_for_processing_failure(self):
        """Test audio preparation with invalid input."""
        invalid_base64 = "invalid_base64_data!"
        
        success, processed_bytes, processing_info = prepare_audio_for_processing(invalid_base64)
        
        self.assertFalse(success)
        self.assertEqual(processed_bytes, b'')
        self.assertGreater(len(processing_info['errors']), 0)


# Tests can be run with: python manage.py test tests.test_audio_format