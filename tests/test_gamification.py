import pytest
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from gamification.models import (
    UserLevel, Achievement, UserAchievement, UserWallet,
    ShopItem, UserInventory, DailyReward, UserDailyStreak
)

User = get_user_model()


@pytest.mark.django_db
class TestUserLevel:
    """Test user level and XP functionality"""
    
    def test_create_user_level(self, user):
        """Test creating user level"""
        level = UserLevel.objects.create(user=user)
        assert level.current_level == 1
        assert level.current_xp == 0
        assert level.total_xp == 0
    
    def test_add_xp(self, user):
        """Test adding XP and level up"""
        level = UserLevel.objects.create(user=user)
        
        # Add XP that should trigger level up
        new_level = level.add_xp(150)
        
        assert new_level == 2
        assert level.current_level == 2
        assert level.total_xp == 150
        assert level.current_xp == 50  # 150 - 100 for level up
    
    def test_get_user_level_api(self, authenticated_client, user):
        """Test getting user level via API"""
        url = reverse('gamification:user-level')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['current_level'] == 1
        assert 'progress_percentage' in response.data


@pytest.mark.django_db
class TestAchievements:
    """Test achievement functionality"""
    
    @pytest.fixture
    def achievement(self):
        """Create a test achievement"""
        return Achievement.objects.create(
            name='First Message',
            description='Send your first message',
            category='chat',
            rarity='common',
            xp_reward=50,
            currency_reward=10,
            requirement_type='message_count',
            requirement_value=1,
            is_hidden=False,
            is_active=True
        )
    
    def test_create_achievement(self, achievement):
        """Test creating an achievement"""
        assert achievement.name == 'First Message'
        assert achievement.xp_reward == 50
        assert achievement.currency_reward == 10
    
    def test_user_achievement_progress(self, user, achievement):
        """Test user achievement progress tracking"""
        user_achievement = UserAchievement.objects.create(
            user=user,
            achievement=achievement,
            progress=0
        )
        
        # Update progress to complete achievement
        user_achievement.update_progress(1)
        
        assert user_achievement.is_completed
        assert user_achievement.progress == 1
    
    def test_list_achievements_api(self, authenticated_client, achievement):
        """Test listing achievements via API"""
        url = reverse('gamification:achievement-list')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 1
        # Find the achievement in the response data
        achievement_found = False
        for ach in response.data:
            if ach['name'] == achievement.name:
                achievement_found = True
                break
        assert achievement_found, f"Achievement '{achievement.name}' not found in response"


@pytest.mark.django_db
class TestUserWallet:
    """Test user wallet functionality"""
    
    def test_create_wallet(self, user):
        """Test creating user wallet"""
        wallet = UserWallet.objects.create(user=user)
        assert wallet.balance == 0
        assert wallet.total_earned == 0
        assert wallet.total_spent == 0
    
    def test_add_currency(self, user):
        """Test adding currency to wallet"""
        wallet = UserWallet.objects.create(user=user)
        wallet.add_currency(100)
        
        assert wallet.balance == 100
        assert wallet.total_earned == 100
    
    def test_spend_currency(self, user):
        """Test spending currency from wallet"""
        wallet = UserWallet.objects.create(user=user, balance=100)
        
        # Successful spend
        success = wallet.spend_currency(50)
        assert success is True
        assert wallet.balance == 50
        assert wallet.total_spent == 50
        
        # Insufficient funds
        success = wallet.spend_currency(100)
        assert success is False
        assert wallet.balance == 50  # Unchanged
    
    def test_get_wallet_api(self, authenticated_client, user):
        """Test getting wallet via API"""
        url = reverse('gamification:user-wallet')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'balance' in response.data
        assert 'total_earned' in response.data


@pytest.mark.django_db
class TestShopAndInventory:
    """Test shop and inventory functionality"""
    
    @pytest.fixture
    def shop_item(self):
        """Create a test shop item"""
        return ShopItem.objects.create(
            name='Cool Avatar',
            description='A cool avatar customization',
            item_type='avatar_customization',
            rarity='common',
            price=50,
            level_requirement=1
        )
    
    def test_create_shop_item(self, shop_item):
        """Test creating shop item"""
        assert shop_item.name == 'Cool Avatar'
        assert shop_item.price == 50
        assert shop_item.is_active is True
    
    def test_can_purchase_item(self, user, shop_item):
        """Test item purchase eligibility"""
        # Create wallet with sufficient funds
        UserWallet.objects.create(user=user, balance=100)
        UserLevel.objects.create(user=user, current_level=1)
        
        can_purchase, message = shop_item.can_purchase(user)
        assert can_purchase is True
        assert message == "Can purchase"
    
    def test_cannot_purchase_insufficient_funds(self, user, shop_item):
        """Test purchase with insufficient funds"""
        UserWallet.objects.create(user=user, balance=10)  # Less than item price
        UserLevel.objects.create(user=user, current_level=1)
        
        can_purchase, message = shop_item.can_purchase(user)
        assert can_purchase is False
        assert "Insufficient funds" in message
    
    def test_purchase_item_api(self, authenticated_client, user, shop_item):
        """Test purchasing item via API"""
        # Setup user with sufficient funds and level
        UserWallet.objects.create(user=user, balance=100)
        UserLevel.objects.create(user=user, current_level=1)
        
        url = reverse('gamification:purchase-item')
        data = {'item_id': str(shop_item.id)}
        
        response = authenticated_client.post(url, data)
        assert response.status_code == status.HTTP_200_OK
        assert 'message' in response.data
        
        # Verify item is in inventory
        assert UserInventory.objects.filter(user=user, item=shop_item).exists()
        
        # Verify wallet balance decreased
        wallet = UserWallet.objects.get(user=user)
        assert wallet.balance == 50  # 100 - 50
    
    def test_list_shop_items_api(self, authenticated_client, shop_item):
        """Test listing shop items via API"""
        url = reverse('gamification:shop-item-list')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 1
    
    def test_list_inventory_api(self, authenticated_client, user, shop_item):
        """Test listing user inventory via API"""
        # Add item to inventory
        UserInventory.objects.create(user=user, item=shop_item)
        
        url = reverse('gamification:user-inventory')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) >= 1
        # Find the inventory item in the response data
        inventory_found = False
        for inv_item in response.data:
            if inv_item['item']['name'] == shop_item.name:
                inventory_found = True
                break
        assert inventory_found, f"Inventory item '{shop_item.name}' not found in response"


@pytest.mark.django_db
class TestDailyStreaks:
    """Test daily streak functionality"""
    
    def test_create_daily_streak(self, user):
        """Test creating daily streak"""
        streak = UserDailyStreak.objects.create(user=user)
        assert streak.current_streak == 0
        assert streak.longest_streak == 0
        assert streak.total_logins == 0
    
    def test_update_streak_first_login(self, user):
        """Test updating streak on first login"""
        streak = UserDailyStreak.objects.create(user=user)
        current_streak = streak.update_streak()
        
        assert current_streak == 1
        assert streak.current_streak == 1
        assert streak.total_logins == 1
    
    def test_get_daily_streak_api(self, authenticated_client, user):
        """Test getting daily streak via API"""
        url = reverse('gamification:user-daily-streak')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'current_streak' in response.data
        assert 'longest_streak' in response.data
    
    @pytest.fixture
    def daily_reward(self):
        """Create a test daily reward"""
        return DailyReward.objects.create(
            day=1,
            xp_reward=25,
            currency_reward=10
        )
    
    def test_claim_daily_reward_api(self, authenticated_client, user, daily_reward):
        """Test claiming daily reward via API"""
        # Setup user level and wallet
        UserLevel.objects.create(user=user)
        UserWallet.objects.create(user=user)
        
        url = reverse('gamification:claim-daily-reward')
        response = authenticated_client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'message' in response.data
        assert 'reward' in response.data


@pytest.mark.django_db
class TestGamificationStats:
    """Test gamification statistics"""
    
    def test_gamification_stats_api(self, authenticated_client, user):
        """Test getting gamification stats via API"""
        # Create some test data
        UserLevel.objects.create(user=user, total_xp=500)
        UserWallet.objects.create(user=user, balance=200)
        
        url = reverse('gamification:gamification-stats')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'level_info' in response.data
        assert 'wallet_info' in response.data
        assert 'achievements_count' in response.data
        assert 'rank' in response.data
    
    def test_leaderboard_api(self, authenticated_client, user):
        """Test getting leaderboard via API"""
        # Create user level
        UserLevel.objects.create(user=user, total_xp=1000)
        
        url = reverse('gamification:leaderboard')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'leaderboard' in response.data
        assert 'total_users' in response.data
        assert len(response.data['leaderboard']) >= 1


@pytest.mark.django_db
class TestEquipItems:
    """Test item equipping functionality"""
    
    def test_equip_item_api(self, authenticated_client, user):
        """Test equipping item via API"""
        # Create shop item and add to inventory
        shop_item = ShopItem.objects.create(
            name='Test Item',
            description='Test item',
            item_type='avatar_customization',
            price=50
        )
        UserInventory.objects.create(user=user, item=shop_item)
        
        url = reverse('gamification:equip-item', kwargs={'item_id': shop_item.id})
        response = authenticated_client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'is_equipped' in response.data
        
        # Verify item is equipped
        inventory_item = UserInventory.objects.get(user=user, item=shop_item)
        assert inventory_item.is_equipped is True
