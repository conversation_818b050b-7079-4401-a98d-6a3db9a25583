import pytest
import os
from django.conf import settings
from django.test import override_settings
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token
from unittest.mock import patch, MagicMock

User = get_user_model()


@pytest.fixture(scope='session')
def django_db_setup(django_db_setup, django_db_blocker):
    """Configure test database settings and ensure migrations are applied"""
    # Use the in-memory SQLite database for tests for better performance
    settings.DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'ATOMIC_REQUESTS': True,
        'TEST': {
            'NAME': ':memory:',
        },
    }
    
    # Apply all migrations
    with django_db_blocker.unblock():
        from django.core.management import call_command
        call_command('migrate', '--noinput')


@pytest.fixture
def api_client():
    """Provide API client for testing"""
    return APIClient()


@pytest.fixture
def user(db):
    """Create a test user with a unique username and email"""
    # Use a counter to ensure unique usernames and emails
    user_count = User.objects.count()
    username = f'testuser{user_count}'
    email = f'test{user_count}@example.com'
    
    return User.objects.create_user(
        username=username,
        email=email,
        password='testpass123',
        first_name='Test',
        last_name='User',
        bio='Test bio',
        enable_voice_responses=True
    )


@pytest.fixture
def authenticated_client(api_client, user):
    """Provide authenticated API client"""
    # Delete any existing tokens for the user to avoid duplicates
    Token.objects.filter(user=user).delete()
    token = Token.objects.create(user=user)
    api_client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
    return api_client


@pytest.fixture
def admin_user():
    """Create an admin user"""
    return User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='adminpass123'
    )


@pytest.fixture
def mock_openai():
    """Mock OpenAI API calls"""
    with patch('openai.ChatCompletion.create') as mock_chat, \
         patch('openai.Embedding.create') as mock_embedding, \
         patch('openai.Audio.transcribe') as mock_transcribe:
        
        # Mock chat completion
        mock_chat.return_value = MagicMock()
        mock_chat.return_value.choices = [
            MagicMock(message=MagicMock(content="Test AI response"))
        ]
        
        # Mock embedding
        mock_embedding.return_value = MagicMock()
        mock_embedding.return_value.data = [
            MagicMock(embedding=[0.1] * 1536)
        ]
        
        # Mock transcription
        mock_transcribe.return_value = MagicMock()
        mock_transcribe.return_value.text = "Test transcription"
        
        yield {
            'chat': mock_chat,
            'embedding': mock_embedding,
            'transcribe': mock_transcribe
        }


@pytest.fixture
def mock_vector_store():
    """Mock ChromaDB vector store"""
    with patch('chromadb.Client') as mock_client:
        mock_collection = MagicMock()
        mock_collection.add.return_value = None
        mock_collection.query.return_value = {
            'documents': [['Test memory']],
            'metadatas': [[{'user_id': 'test', 'memory_type': 'test'}]],
            'distances': [[0.5]]
        }
        
        mock_client.return_value.get_or_create_collection.return_value = mock_collection
        yield mock_collection


@pytest.fixture
def test_settings():
    """Override settings for testing"""
    return override_settings(
        DEBUG=True,
        CELERY_TASK_ALWAYS_EAGER=True,
        CELERY_TASK_EAGER_PROPAGATES=True,
        OPENAI_API_KEY='test-key',
        VECTOR_STORE_PERSIST_DIRECTORY='/tmp/test_vector_store',
        AUDIO_UPLOAD_PATH='/tmp/test_audio',
        EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend'
    )


@pytest.fixture
def sample_audio_file():
    """Create a sample audio file for testing"""
    import tempfile
    import wave
    import numpy as np
    
    # Create a temporary WAV file
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as f:
        # Generate 1 second of sine wave at 440Hz
        sample_rate = 44100
        duration = 1.0
        frequency = 440.0
        
        t = np.linspace(0, duration, int(sample_rate * duration))
        audio_data = np.sin(2 * np.pi * frequency * t)
        audio_data = (audio_data * 32767).astype(np.int16)
        
        with wave.open(f.name, 'w') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_data.tobytes())
        
        yield f.name
        
        # Cleanup
        os.unlink(f.name)


@pytest.fixture
def create_memory():
    """Fixture to create a memory with a unique vector_id"""
    from memory.models import Memory
    import uuid
    
    def _create_memory(**kwargs):
        # Ensure required fields have defaults
        defaults = {
            'content': 'Test memory content',
            'memory_type': 'general',
            'importance_score': 0.5,
            'personalness_score': 0.5,
            'actionability_score': 0.5,
            'vector_id': str(uuid.uuid4()),
            'is_active': True,
            'is_verified': False
        }
        
        # Update with any provided kwargs
        defaults.update(kwargs)
        
        # Create and return the memory
        return Memory.objects.create(**defaults)
    
    return _create_memory
