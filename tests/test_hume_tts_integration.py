"""
Comprehensive Hume TTS integration tests.
Tests streaming TTS functionality, emotion modulation, and error handling.
"""

import asyncio
import base64
import json
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model

from chat.services.hume_service import HumeTT<PERSON>lient
from chat.models_realtime import EmotionContext

User = get_user_model()


class HumeTTSIntegrationTestSuite(TransactionTestCase):
    """Comprehensive test suite for Hume TTS integration."""
    
    def setUp(self):
        """Set up test environment."""
        self.user = User.objects.create_user(
            username='ttsuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Mock Hume TTS client
        self.mock_hume_tts = AsyncMock(spec=HumeTTSClient)
        
        # Sample emotion contexts for testing
        self.emotion_contexts = {
            'happy': {'joy': 0.8, 'excitement': 0.6, 'confidence': 0.7},
            'sad': {'sadness': 0.7, 'melancholy': 0.5, 'disappointment': 0.4},
            'angry': {'anger': 0.8, 'frustration': 0.6, 'irritation': 0.5},
            'calm': {'contentment': 0.6, 'peace': 0.7, 'relaxation': 0.8},
            'excited': {'excitement': 0.9, 'joy': 0.8, 'enthusiasm': 0.7}
        }
    
    @pytest.mark.asyncio
    async def test_basic_tts_streaming(self):
        """Test basic TTS streaming functionality."""
        # Mock streaming response
        async def mock_tts_stream():
            audio_chunks = [
                base64.b64encode(f'audio_chunk_{i}'.encode()).decode()
                for i in range(8)
            ]
            for chunk in audio_chunks:
                yield chunk
                await asyncio.sleep(0.01)  # Simulate streaming delay
        
        self.mock_hume_tts.synthesize_streaming.return_value = mock_tts_stream()
        
        # Test TTS synthesis
        text_to_synthesize = "Hello, this is a test of the TTS system."
        voice_settings = {'voice_id': 'default', 'speed': 1.0}
        
        audio_chunks = []
        async for chunk in self.mock_hume_tts.synthesize_streaming(
            text=text_to_synthesize,
            voice_settings=voice_settings,
            emotion_context=self.emotion_contexts['happy']
        ):
            audio_chunks.append(chunk)
        
        # Verify streaming worked
        self.assertGreater(len(audio_chunks), 0, "Should generate audio chunks")
        self.assertEqual(len(audio_chunks), 8, "Should generate expected number of chunks")
        
        # Verify chunks are valid base64
        for i, chunk in enumerate(audio_chunks):
            try:
                decoded = base64.b64decode(chunk)
                self.assertIsInstance(decoded, bytes)
                self.assertIn(f'audio_chunk_{i}'.encode(), decoded)
            except Exception as e:
                self.fail(f"Chunk {i} is not valid base64: {e}")
    
    @pytest.mark.asyncio
    async def test_emotion_modulated_tts(self):
        """Test TTS with different emotion modulations."""
        for emotion_name, emotion_context in self.emotion_contexts.items():
            with self.subTest(emotion=emotion_name):
                # Mock emotion-specific TTS response
                async def emotion_specific_stream():
                    # Simulate emotion affecting audio characteristics
                    chunk_prefix = f'{emotion_name}_modulated'
                    for i in range(5):
                        chunk_data = f'{chunk_prefix}_chunk_{i}'
                        yield base64.b64encode(chunk_data.encode()).decode()
                        await asyncio.sleep(0.02)
                
                self.mock_hume_tts.synthesize_streaming.return_value = emotion_specific_stream()
                
                # Test TTS with emotion context
                audio_chunks = []
                async for chunk in self.mock_hume_tts.synthesize_streaming(
                    text=f"This text should sound {emotion_name}.",
                    voice_settings={'voice_id': 'default'},
                    emotion_context=emotion_context
                ):
                    audio_chunks.append(chunk)
                
                # Verify emotion-modulated audio
                self.assertGreater(len(audio_chunks), 0)
                
                # Decode first chunk to verify emotion modulation
                first_chunk = base64.b64decode(audio_chunks[0]).decode()
                self.assertIn(emotion_name, first_chunk)
    
    @pytest.mark.asyncio
    async def test_tts_voice_settings_variations(self):
        """Test TTS with different voice settings."""
        voice_settings_variations = [
            {'voice_id': 'default', 'speed': 0.8, 'pitch': 0.9},
            {'voice_id': 'default', 'speed': 1.2, 'pitch': 1.1},
            {'voice_id': 'alternative', 'speed': 1.0, 'pitch': 1.0},
            {'voice_id': 'expressive', 'speed': 1.1, 'pitch': 1.05}
        ]
        
        for i, voice_settings in enumerate(voice_settings_variations):
            with self.subTest(settings=voice_settings):
                # Mock response that reflects voice settings
                async def settings_specific_stream():
                    settings_id = f"voice_{voice_settings['voice_id']}_speed_{voice_settings['speed']}"
                    for j in range(3):
                        chunk_data = f'{settings_id}_chunk_{j}'
                        yield base64.b64encode(chunk_data.encode()).decode()
                        await asyncio.sleep(0.01)
                
                self.mock_hume_tts.synthesize_streaming.return_value = settings_specific_stream()
                
                # Test TTS with specific voice settings
                audio_chunks = []
                async for chunk in self.mock_hume_tts.synthesize_streaming(
                    text="Testing voice settings variation.",
                    voice_settings=voice_settings,
                    emotion_context=self.emotion_contexts['calm']
                ):
                    audio_chunks.append(chunk)
                
                # Verify settings were applied
                self.assertEqual(len(audio_chunks), 3)
                
                # Verify voice settings in generated audio
                first_chunk = base64.b64decode(audio_chunks[0]).decode()
                self.assertIn(voice_settings['voice_id'], first_chunk)
    
    @pytest.mark.asyncio
    async def test_tts_error_handling(self):
        """Test TTS error handling and recovery."""
        error_scenarios = [
            Exception("Network timeout"),
            Exception("API rate limit exceeded"),
            Exception("Invalid voice settings"),
            Exception("Text too long"),
            asyncio.TimeoutError("Request timeout")
        ]
        
        for error in error_scenarios:
            with self.subTest(error=type(error).__name__):
                # Mock TTS to raise specific error
                self.mock_hume_tts.synthesize_streaming.side_effect = error
                
                # Test error handling
                with self.assertRaises(Exception):
                    audio_chunks = []
                    async for chunk in self.mock_hume_tts.synthesize_streaming(
                        text="This should trigger an error.",
                        voice_settings={'voice_id': 'default'},
                        emotion_context=self.emotion_contexts['happy']
                    ):
                        audio_chunks.append(chunk)
                
                # Reset for next test
                self.mock_hume_tts.synthesize_streaming.side_effect = None
    
    @pytest.mark.asyncio
    async def test_tts_streaming_performance(self):
        """Test TTS streaming performance characteristics."""
        import time
        
        # Mock high-performance streaming
        async def performance_stream():
            start_time = time.time()
            chunk_count = 20
            
            for i in range(chunk_count):
                chunk_data = f'performance_chunk_{i}'
                yield base64.b64encode(chunk_data.encode()).decode()
                
                # Simulate realistic streaming delay
                await asyncio.sleep(0.005)  # 5ms per chunk
        
        self.mock_hume_tts.synthesize_streaming.return_value = performance_stream()
        
        # Measure streaming performance
        start_time = time.time()
        chunk_count = 0
        first_chunk_time = None
        
        async for chunk in self.mock_hume_tts.synthesize_streaming(
            text="Performance test text for TTS streaming.",
            voice_settings={'voice_id': 'default'},
            emotion_context=self.emotion_contexts['excited']
        ):
            if first_chunk_time is None:
                first_chunk_time = time.time()
            chunk_count += 1
        
        total_time = time.time() - start_time
        time_to_first_chunk = first_chunk_time - start_time
        
        # Performance assertions
        self.assertEqual(chunk_count, 20, "Should receive all chunks")
        self.assertLess(
            time_to_first_chunk * 1000, 
            100,  # 100ms target for first chunk
            f"Time to first chunk {time_to_first_chunk*1000:.2f}ms exceeds 100ms target"
        )
        self.assertLess(
            total_time,
            0.5,  # 500ms total for 20 chunks
            f"Total streaming time {total_time:.3f}s exceeds 500ms target"
        )
    
    @pytest.mark.asyncio
    async def test_tts_chunk_size_optimization(self):
        """Test TTS chunk size optimization for streaming."""
        chunk_sizes = [64, 128, 256, 512, 1024]  # bytes
        
        for chunk_size in chunk_sizes:
            with self.subTest(chunk_size=chunk_size):
                # Mock streaming with specific chunk sizes
                async def sized_chunk_stream():
                    num_chunks = 1024 // chunk_size  # Total 1KB of audio
                    for i in range(num_chunks):
                        # Create chunk of specified size
                        chunk_data = 'x' * chunk_size
                        yield base64.b64encode(chunk_data.encode()).decode()
                        await asyncio.sleep(0.01)
                
                self.mock_hume_tts.synthesize_streaming.return_value = sized_chunk_stream()
                
                # Test streaming with different chunk sizes
                chunks = []
                async for chunk in self.mock_hume_tts.synthesize_streaming(
                    text="Testing chunk size optimization.",
                    voice_settings={'voice_id': 'default'},
                    emotion_context=self.emotion_contexts['calm']
                ):
                    chunks.append(chunk)
                    
                    # Verify chunk size
                    decoded_size = len(base64.b64decode(chunk))
                    self.assertEqual(
                        decoded_size, 
                        chunk_size,
                        f"Chunk size {decoded_size} doesn't match expected {chunk_size}"
                    )
                
                # Verify total chunks
                expected_chunks = 1024 // chunk_size
                self.assertEqual(len(chunks), expected_chunks)
    
    @pytest.mark.asyncio
    async def test_tts_concurrent_requests(self):
        """Test TTS handling of concurrent requests."""
        concurrent_requests = 5
        
        async def concurrent_stream(request_id):
            """Mock stream for concurrent testing."""
            for i in range(3):
                chunk_data = f'concurrent_req_{request_id}_chunk_{i}'
                yield base64.b64encode(chunk_data.encode()).decode()
                await asyncio.sleep(0.02)
        
        # Create multiple concurrent TTS requests
        tasks = []
        for req_id in range(concurrent_requests):
            # Each request gets its own mock
            mock_client = AsyncMock(spec=HumeTTSClient)
            mock_client.synthesize_streaming.return_value = concurrent_stream(req_id)
            
            task = asyncio.create_task(self._process_tts_request(mock_client, req_id))
            tasks.append(task)
        
        # Wait for all concurrent requests to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all requests completed successfully
        successful_requests = [r for r in results if not isinstance(r, Exception)]
        self.assertEqual(
            len(successful_requests),
            concurrent_requests,
            f"Only {len(successful_requests)}/{concurrent_requests} concurrent requests succeeded"
        )
        
        # Verify each request got correct chunks
        for i, result in enumerate(successful_requests):
            self.assertEqual(len(result), 3, f"Request {i} should have 3 chunks")
            
            # Verify chunk content
            first_chunk = base64.b64decode(result[0]).decode()
            self.assertIn(f'concurrent_req_{i}', first_chunk)
    
    async def _process_tts_request(self, tts_client, request_id):
        """Helper method to process a single TTS request."""
        chunks = []
        async for chunk in tts_client.synthesize_streaming(
            text=f"Concurrent request {request_id}",
            voice_settings={'voice_id': 'default'},
            emotion_context=self.emotion_contexts['happy']
        ):
            chunks.append(chunk)
        return chunks
    
    def test_emotion_context_storage_integration(self):
        """Test integration with emotion context storage."""
        # Create emotion context in database
        emotion_context = EmotionContext.objects.create(
            user=self.user,
            session_id='tts-test-session',
            audio_emotions=self.emotion_contexts['happy'],
            text_emotions={'positive': 0.8, 'enthusiasm': 0.7},
            context_emotions={'primary': 'joy', 'secondary': 'excitement'},
            confidence_score=0.85
        )
        
        # Verify emotion context can be retrieved for TTS
        retrieved_context = EmotionContext.objects.filter(
            user=self.user,
            session_id='tts-test-session'
        ).first()
        
        self.assertIsNotNone(retrieved_context)
        self.assertEqual(retrieved_context.context_emotions['primary'], 'joy')
        self.assertGreater(retrieved_context.confidence_score, 0.8)
        
        # Verify emotion context format is compatible with TTS
        tts_emotion_context = retrieved_context.audio_emotions
        self.assertIn('joy', tts_emotion_context)
        self.assertIsInstance(tts_emotion_context['joy'], (int, float))
        self.assertGreaterEqual(tts_emotion_context['joy'], 0.0)
        self.assertLessEqual(tts_emotion_context['joy'], 1.0)
    
    @pytest.mark.asyncio
    async def test_tts_fallback_mechanisms(self):
        """Test TTS fallback mechanisms when primary service fails."""
        # Mock primary TTS failure
        self.mock_hume_tts.synthesize_streaming.side_effect = Exception("Primary TTS unavailable")
        
        # Create a simple fallback implementation
        async def fallback_stream():
            fallback_chunks = [
                base64.b64encode(f'fallback_chunk_{i}'.encode()).decode()
                for i in range(4)
            ]
            for chunk in fallback_chunks:
                yield chunk
                await asyncio.sleep(0.01)
        
        # Test fallback directly
        chunks = []
        async for chunk in fallback_stream():
            chunks.append(chunk)
        
        # Verify fallback would work
        self.assertEqual(len(chunks), 4)
        
        # Verify fallback chunks
        first_chunk = base64.b64decode(chunks[0]).decode()
        self.assertIn('fallback_chunk_0', first_chunk)
    
    def test_tts_configuration_validation(self):
        """Test TTS configuration validation."""
        # Test valid configurations
        valid_configs = [
            {
                'voice_settings': {'voice_id': 'default', 'speed': 1.0, 'pitch': 1.0},
                'emotion_context': {'joy': 0.5},
                'should_pass': True
            },
            {
                'voice_settings': {'voice_id': 'expressive', 'speed': 0.5, 'pitch': 2.0},
                'emotion_context': {'anger': 0.8, 'frustration': 0.6},
                'should_pass': True
            }
        ]
        
        # Test invalid configurations
        invalid_configs = [
            {
                'voice_settings': {'voice_id': '', 'speed': 1.0},  # Empty voice_id
                'emotion_context': {'joy': 0.5},
                'should_pass': False
            },
            {
                'voice_settings': {'voice_id': 'default', 'speed': -1.0},  # Invalid speed
                'emotion_context': {'joy': 0.5},
                'should_pass': False
            },
            {
                'voice_settings': {'voice_id': 'default', 'speed': 1.0},
                'emotion_context': {'joy': 1.5},  # Invalid emotion score
                'should_pass': False
            }
        ]
        
        all_configs = valid_configs + invalid_configs
        
        for config in all_configs:
            with self.subTest(config=config):
                # Test configuration validation logic
                voice_settings = config['voice_settings']
                emotion_context = config['emotion_context']
                
                # Basic validation checks
                voice_id_valid = bool(voice_settings.get('voice_id', '').strip())
                speed_valid = 0.1 <= voice_settings.get('speed', 1.0) <= 3.0
                
                emotion_scores_valid = all(
                    0.0 <= score <= 1.0 
                    for score in emotion_context.values()
                    if isinstance(score, (int, float))
                )
                
                config_valid = voice_id_valid and speed_valid and emotion_scores_valid
                
                self.assertEqual(
                    config_valid,
                    config['should_pass'],
                    f"Configuration validation mismatch for {config}"
                )