import pytest
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from django.conf import settings

User = get_user_model()


def test_django_setup(test_settings):
    """Test that Django is properly configured"""
    with test_settings:  # This ensures our test settings are applied
        assert settings.DEBUG is True
        assert 'authentication' in settings.INSTALLED_APPS
        assert 'chat' in settings.INSTALLED_APPS
        assert 'memory' in settings.INSTALLED_APPS
        assert 'gamification' in settings.INSTALLED_APPS


class TestBasicDjangoSetup(TestCase):
    """Test basic Django setup and configuration"""
    
    def test_database_connection(self):
        """Test database connection"""
        # This will fail if database is not properly configured
        User.objects.count()
        assert True  # If we get here, database is working
    
    def test_user_model_creation(self):
        """Test creating a user with the extended model"""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        assert user.username == 'testuser'
        assert user.email == '<EMAIL>'
        assert user.user_timezone == 'UTC'  # Default value
        assert user.ai_companion_name == 'Ella'  # Default value
    
    def test_user_model_fields(self):
        """Test that all expected fields exist on User model"""
        user = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Test that all our custom fields exist
        assert hasattr(user, 'user_timezone')
        assert hasattr(user, 'ai_companion_name')
        assert hasattr(user, 'preferred_voice')
        assert hasattr(user, 'enable_voice_responses')
        assert hasattr(user, 'personality_traits')
        assert hasattr(user, 'last_active')


@pytest.mark.django_db
class TestAPIEndpoints:
    """Test basic API endpoint accessibility"""
    
    def test_admin_accessible(self):
        """Test that admin interface is accessible"""
        client = APIClient()
        response = client.get('/admin/')
        # Should redirect to login, not 404
        assert response.status_code in [200, 302]
    
    def test_api_endpoints_exist(self):
        """Test that our API endpoints are properly configured"""
        from django.urls import resolve
        
        # Test that our URL patterns are properly configured by checking specific endpoints
        try:
            # Test authentication endpoints
            resolve('/api/auth/register/')
            resolve('/api/auth/login/')
            
            # Test chat endpoints
            resolve('/api/chat/messages/')
            
            # Test memory endpoints
            resolve('/api/memory/memories/')
            
            # Test gamification endpoints
            resolve('/api/gamification/achievements/')
            
        except Exception as e:
            pytest.fail(f"URL resolution failed: {e}")


class TestModelsImport:
    """Test that all models can be imported without errors"""
    
    def test_authentication_models_import(self):
        """Test authentication models import"""
        from authentication.models import User, UserSession
        assert User is not None
        assert UserSession is not None
    
    def test_gamification_models_import(self):
        """Test gamification models import"""
        from gamification.models import (
            UserLevel, Achievement, UserAchievement, 
            UserWallet, ShopItem, UserInventory
        )
        assert UserLevel is not None
        assert Achievement is not None
        assert UserAchievement is not None
        assert UserWallet is not None
        assert ShopItem is not None
        assert UserInventory is not None
    
    def test_memory_models_import(self):
        """Test memory models import (without external dependencies)"""
        from memory.models import Memory, MemoryCluster, UserMemoryConfig
        assert Memory is not None
        assert MemoryCluster is not None
        assert UserMemoryConfig is not None
