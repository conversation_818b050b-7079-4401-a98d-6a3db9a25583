#!/usr/bin/env python
"""
Simple test script to verify Hume AI API connectivity.
"""
import os
import asyncio
from dotenv import load_dotenv
from hume import AsyncHumeClient
from hume.expression_measurement.stream import Config
from hume.expression_measurement.stream.socket_client import StreamConnectOptions
from hume.expression_measurement.stream import StreamLanguage

# Load environment variables
load_dotenv()

async def test_hume_api():
    """Test Hume AI API connectivity."""
    api_key = os.getenv('HUME_API_KEY')
    if not api_key:
        print("❌ HUME_API_KEY not found in environment")
        return False
    
    print(f"✓ Using API key: {api_key[:8]}...")
    
    try:
        client = AsyncHumeClient(api_key=api_key)
        
        print("Testing Hume AI text emotion analysis...")
        
        # Configure language model
        model_config = Config(language=StreamLanguage())
        stream_options = StreamConnectOptions(config=model_config)
        
        # Test text emotion analysis
        async with client.expression_measurement.stream.connect(options=stream_options) as socket:
            result = await socket.send_text("I am so happy today!")
            
            if hasattr(result, 'language') and result.language:
                language_data = result.language
                if language_data.predictions:
                    emotions = []
                    for prediction in language_data.predictions:
                        if hasattr(prediction, 'emotions'):
                            for emotion in prediction.emotions:
                                emotions.append(f"{emotion.name}: {emotion.score:.3f}")
                    
                    if emotions:
                        print(f"✅ Hume AI working! Detected emotions: {', '.join(emotions[:3])}")
                        return True
                    else:
                        print("❌ No emotions detected")
                        return False
                else:
                    print("❌ No predictions in response")
                    return False
            else:
                print("❌ No language data in response")
                return False
                
    except Exception as e:
        print(f"❌ Hume AI error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_hume_api())
    if success:
        print("\n🎉 Hume AI test passed!")
    else:
        print("\n💥 Hume AI test failed!")