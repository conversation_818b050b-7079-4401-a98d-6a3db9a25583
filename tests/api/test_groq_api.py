#!/usr/bin/env python
"""
Simple test script to verify Groq API connectivity.
"""
import os
import asyncio
from dotenv import load_dotenv
from groq import AsyncGroq

# Load environment variables
load_dotenv()

async def test_groq_api():
    """Test Groq API with correct base URL."""
    api_key = os.getenv('GROQ_API_KEY')
    if not api_key:
        print("❌ GROQ_API_KEY not found in environment")
        return False
    
    print(f"✓ Using API key: {api_key[:8]}...")
    
    # Test with correct base URL
    client = AsyncGroq(
        api_key=api_key,
        base_url="https://api.groq.com"
    )
    
    try:
        print("Testing Groq API connectivity...")
        response = await client.chat.completions.create(
            model="llama-3.3-70b-versatile",
            messages=[{"role": "user", "content": "Say hello"}],
            max_tokens=10
        )
        
        if response.choices and response.choices[0].message.content:
            print(f"✅ Groq API working! Response: {response.choices[0].message.content}")
            return True
        else:
            print("❌ Groq API returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ Groq API error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_groq_api())
    if success:
        print("\n🎉 Groq API test passed!")
    else:
        print("\n💥 Groq API test failed!")