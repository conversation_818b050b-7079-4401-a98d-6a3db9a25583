"""
Tests for Hume AI emotion detection and TTS service.
"""
import pytest
import asyncio
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch
import os
import base64
from dataclasses import dataclass

from chat.services.hume_service import (
    HumeEmotionClient,
    HumeTTSClient,
    EmotionScore,
    EmotionAnalysisResult,
    T<PERSON>Chunk,
    TTSResult,
    analyze_audio_emotions,
    analyze_text_emotions,
    synthesize_speech_streaming
)


class TestEmotionScore:
    """Test EmotionScore dataclass."""
    
    def test_emotion_score_creation(self):
        """Test creating emotion score."""
        score = EmotionScore(name="joy", score=0.8, confidence=0.9)
        
        assert score.name == "joy"
        assert score.score == 0.8
        assert score.confidence == 0.9
    
    def test_emotion_score_default_confidence(self):
        """Test default confidence value."""
        score = EmotionScore(name="sadness", score=0.6)
        
        assert score.confidence == 1.0


class TestEmotionAnalysisResult:
    """Test EmotionAnalysisResult dataclass."""
    
    def test_emotion_analysis_result_creation(self):
        """Test creating emotion analysis result."""
        emotions = [
            EmotionScore("joy", 0.8),
            EmotionScore("excitement", 0.6)
        ]
        
        result = EmotionAnalysisResult(
            primary_emotion="joy",
            emotion_intensity=0.8,
            emotion_valence=0.7,
            emotion_arousal=0.6,
            emotions=emotions,
            confidence_score=0.9,
            processing_time_ms=150.0,
            source="audio",
            raw_data={"test": "data"}
        )
        
        assert result.primary_emotion == "joy"
        assert result.emotion_intensity == 0.8
        assert result.emotion_valence == 0.7
        assert result.emotion_arousal == 0.6
        assert len(result.emotions) == 2
        assert result.confidence_score == 0.9
        assert result.processing_time_ms == 150.0
        assert result.source == "audio"
        assert result.raw_data == {"test": "data"}


@pytest.mark.asyncio
class TestHumeEmotionClient:
    """Test Hume emotion detection client."""
    
    @pytest.fixture
    def mock_hume_client(self):
        """Mock Hume client."""
        with patch('chat.services.hume_service.AsyncHumeClient') as mock:
            yield mock
    
    @pytest.fixture
    def client(self, mock_hume_client):
        """Create test client."""
        with patch.dict(os.environ, {'HUME_API_KEY': 'test-key'}):
            return HumeEmotionClient()
    
    def test_client_initialization(self, mock_hume_client):
        """Test client initialization."""
        with patch.dict(os.environ, {'HUME_API_KEY': 'test-key'}):
            client = HumeEmotionClient()
            
            assert client.api_key == 'test-key'
            assert client.timeout == 30.0
            assert client.request_count == 0
    
    def test_client_initialization_no_api_key(self, mock_hume_client):
        """Test client initialization without API key."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(ValueError, match="HUME_API_KEY must be provided"):
                HumeEmotionClient()
    
    async def test_analyze_audio_stream_success(self, client):
        """Test successful audio emotion analysis."""
        # Mock audio data
        audio_data = b"fake_audio_data"
        
        # Mock Hume response
        mock_emotion = MagicMock()
        mock_emotion.name = "joy"
        mock_emotion.score = 0.8
        
        mock_prediction = MagicMock()
        mock_prediction.emotions = [mock_emotion]
        
        mock_prosody = MagicMock()
        mock_prosody.predictions = [mock_prediction]
        
        mock_result = MagicMock()
        mock_result.prosody = mock_prosody
        
        # Mock socket connection
        mock_socket = AsyncMock()
        mock_socket.send_bytes = AsyncMock(return_value=mock_result)
        
        mock_stream = AsyncMock()
        mock_stream.__aenter__ = AsyncMock(return_value=mock_socket)
        mock_stream.__aexit__ = AsyncMock(return_value=None)
        
        client.client.expression_measurement.stream.connect = AsyncMock(return_value=mock_stream)
        
        # Test analysis
        result = await client.analyze_audio_stream(audio_data)
        
        assert isinstance(result, EmotionAnalysisResult)
        assert result.primary_emotion == "joy"
        assert result.emotion_intensity == 0.8
        assert result.source == "audio"
        assert len(result.emotions) == 1
        assert result.emotions[0].name == "joy"
        assert result.emotions[0].score == 0.8
    
    async def test_analyze_audio_stream_error(self, client):
        """Test audio analysis with error."""
        audio_data = b"fake_audio_data"
        
        # Mock error
        client.client.expression_measurement.stream.connect = AsyncMock(
            side_effect=Exception("API Error")
        )
        
        result = await client.analyze_audio_stream(audio_data)
        
        # Should return neutral result on error
        assert result.primary_emotion == "neutral"
        assert result.confidence_score == 0.0
        assert result.source == "audio"
        assert "error" in result.raw_data
    
    async def test_analyze_text_emotions_success(self, client):
        """Test successful text emotion analysis."""
        text = "I am so happy today!"
        
        # Mock Hume response
        mock_emotion = MagicMock()
        mock_emotion.name = "happiness"
        mock_emotion.score = 0.9
        
        mock_prediction = MagicMock()
        mock_prediction.emotions = [mock_emotion]
        
        mock_language = MagicMock()
        mock_language.predictions = [mock_prediction]
        
        mock_result = MagicMock()
        mock_result.language = mock_language
        
        # Mock socket connection
        mock_socket = AsyncMock()
        mock_socket.send_text = AsyncMock(return_value=mock_result)
        
        mock_stream = AsyncMock()
        mock_stream.__aenter__ = AsyncMock(return_value=mock_socket)
        mock_stream.__aexit__ = AsyncMock(return_value=None)
        
        client.client.expression_measurement.stream.connect = AsyncMock(return_value=mock_stream)
        
        # Test analysis
        result = await client.analyze_text_emotions(text)
        
        assert isinstance(result, EmotionAnalysisResult)
        assert result.primary_emotion == "happiness"
        assert result.emotion_intensity == 0.9
        assert result.source == "text"
        assert len(result.emotions) == 1
    
    def test_calculate_valence_arousal(self, client):
        """Test valence and arousal calculation."""
        emotions = [
            EmotionScore("joy", 0.8),
            EmotionScore("excitement", 0.6),
            EmotionScore("sadness", 0.2)
        ]
        
        valence, arousal = client._calculate_valence_arousal(emotions)
        
        # Should be positive valence due to joy and excitement
        assert valence > 0
        # Should have moderate to high arousal due to excitement
        assert arousal > 0.5
    
    def test_calculate_valence_arousal_empty(self, client):
        """Test valence and arousal with empty emotions."""
        valence, arousal = client._calculate_valence_arousal([])
        
        assert valence == 0.0
        assert arousal == 0.0
    
    def test_performance_stats(self, client):
        """Test performance statistics."""
        # Initially no stats
        stats = client.get_performance_stats()
        assert stats['total_requests'] == 0
        assert stats['average_processing_time_ms'] == 0.0
        
        # Update metrics
        client._update_performance_metrics(100.0)
        client._update_performance_metrics(200.0)
        
        stats = client.get_performance_stats()
        assert stats['total_requests'] == 2
        assert stats['average_processing_time_ms'] == 150.0


@pytest.mark.asyncio
class TestHumeTTSClient:
    """Test Hume TTS client."""
    
    @pytest.fixture
    def mock_hume_client(self):
        """Mock Hume client."""
        with patch('chat.services.hume_service.AsyncHumeClient') as mock:
            yield mock
    
    @pytest.fixture
    def client(self, mock_hume_client):
        """Create test TTS client."""
        with patch.dict(os.environ, {'HUME_API_KEY': 'test-key'}):
            return HumeTTSClient()
    
    @pytest.fixture
    def emotion_context(self):
        """Create test emotion context."""
        return EmotionAnalysisResult(
            primary_emotion="joy",
            emotion_intensity=0.8,
            emotion_valence=0.7,
            emotion_arousal=0.6,
            emotions=[EmotionScore("joy", 0.8)],
            confidence_score=0.9,
            processing_time_ms=100.0,
            source="audio",
            raw_data={}
        )
    
    def test_client_initialization(self, mock_hume_client):
        """Test TTS client initialization."""
        with patch.dict(os.environ, {'HUME_API_KEY': 'test-key'}):
            client = HumeTTSClient()
            
            assert client.api_key == 'test-key'
            assert client.timeout == 30.0
            assert client.request_count == 0
    
    async def test_synthesize_streaming_success(self, client):
        """Test successful streaming TTS synthesis."""
        text = "Hello world!"
        
        # Mock TTS response chunks
        mock_chunks = [
            MagicMock(audio=base64.b64encode(b"chunk1").decode()),
            MagicMock(audio=base64.b64encode(b"chunk2").decode()),
            MagicMock(audio=base64.b64encode(b"chunk3").decode())
        ]
        
        async def mock_stream(*args, **kwargs):
            for chunk in mock_chunks:
                yield chunk
        
        client.client.tts.synthesize_json_streaming = mock_stream
        
        # Test streaming
        chunks = []
        async for chunk in client.synthesize_streaming(text):
            chunks.append(chunk)
        
        # Should have 3 audio chunks + 1 final marker
        assert len(chunks) == 4
        
        # Check audio chunks
        for i in range(3):
            assert chunks[i].audio_data == f"chunk{i+1}".encode()
            assert chunks[i].chunk_index == i
            assert not chunks[i].is_final
        
        # Check final marker
        assert chunks[3].is_final
        assert chunks[3].audio_data == b''
    
    async def test_synthesize_streaming_with_emotion(self, client, emotion_context):
        """Test streaming TTS with emotion context."""
        text = "I'm so happy!"
        
        # Mock TTS response
        mock_chunks = [MagicMock(audio=base64.b64encode(b"happy_audio").decode())]
        
        async def mock_stream(*args, **kwargs):
            for chunk in mock_chunks:
                yield chunk
        
        client.client.tts.synthesize_json_streaming = mock_stream
        
        # Test with emotion context
        chunks = []
        async for chunk in client.synthesize_streaming(text, emotion_context=emotion_context):
            chunks.append(chunk)
        
        assert len(chunks) == 2  # 1 audio + 1 final
        assert chunks[0].audio_data == b"happy_audio"
    
    async def test_synthesize_complete(self, client):
        """Test complete TTS synthesis."""
        text = "Complete synthesis test"
        
        # Mock streaming chunks
        mock_chunks = [
            MagicMock(audio=base64.b64encode(b"part1").decode(), generation_id="gen123"),
            MagicMock(audio=base64.b64encode(b"part2").decode(), generation_id="gen123")
        ]
        
        async def mock_stream(*args, **kwargs):
            for chunk in mock_chunks:
                yield chunk
        
        client.client.tts.synthesize_json_streaming = mock_stream
        
        # Test complete synthesis
        result = await client.synthesize_complete(text, voice_name="test_voice")
        
        assert isinstance(result, TTSResult)
        assert result.audio_data == b"part1part2"
        assert result.generation_id == "gen123"
        assert result.voice_settings['voice_name'] == "test_voice"
        assert result.total_chunks == 3  # 2 audio + 1 final
    
    def test_emotion_to_acting_instructions(self, client, emotion_context):
        """Test converting emotion context to acting instructions."""
        instructions = client._emotion_to_acting_instructions(emotion_context)
        
        assert isinstance(instructions, str)
        assert len(instructions) > 0
        # Should contain joy-related instructions
        assert any(word in instructions.lower() for word in ['cheerful', 'upbeat', 'positive'])
    
    def test_emotion_to_acting_instructions_empty(self, client):
        """Test acting instructions with no emotion context."""
        instructions = client._emotion_to_acting_instructions(None)
        assert instructions == ""
    
    def test_performance_stats(self, client):
        """Test TTS performance statistics."""
        # Initially no stats
        stats = client.get_performance_stats()
        assert stats['total_requests'] == 0
        assert stats['average_processing_time_ms'] == 0.0
        assert stats['average_first_chunk_time_ms'] == 0.0
        
        # Update metrics
        client._update_performance_metrics(200.0, 50.0)
        client._update_performance_metrics(300.0, 75.0)
        
        stats = client.get_performance_stats()
        assert stats['total_requests'] == 2
        assert stats['average_processing_time_ms'] == 250.0
        assert stats['average_first_chunk_time_ms'] == 62.5


@pytest.mark.asyncio
class TestConvenienceFunctions:
    """Test convenience functions."""
    
    @patch('chat.services.hume_service.get_emotion_client')
    async def test_analyze_audio_emotions(self, mock_get_client):
        """Test audio emotion analysis convenience function."""
        # Mock client and result
        mock_client = AsyncMock()
        mock_result = EmotionAnalysisResult(
            primary_emotion="joy",
            emotion_intensity=0.8,
            emotion_valence=0.7,
            emotion_arousal=0.6,
            emotions=[EmotionScore("joy", 0.8)],
            confidence_score=0.9,
            processing_time_ms=100.0,
            source="audio",
            raw_data={}
        )
        
        mock_client.analyze_audio_stream = AsyncMock(return_value=mock_result)
        mock_get_client.return_value.__aenter__ = AsyncMock(return_value=mock_client)
        mock_get_client.return_value.__aexit__ = AsyncMock(return_value=None)
        
        # Test function
        result = await analyze_audio_emotions(b"test_audio")
        
        assert isinstance(result, EmotionAnalysisResult)
        assert result.primary_emotion == "joy"
        assert result.source == "audio"
    
    @patch('chat.services.hume_service.get_tts_client')
    async def test_synthesize_speech_streaming(self, mock_get_client):
        """Test streaming TTS convenience function."""
        # Mock client and chunks
        mock_client = AsyncMock()
        mock_chunks = [
            TTSChunk(b"chunk1", 0, False, 1000.0),
            TTSChunk(b"chunk2", 1, False, 1001.0),
            TTSChunk(b"", 2, True, 1002.0)
        ]
        
        async def mock_stream(*args, **kwargs):
            for chunk in mock_chunks:
                yield chunk
        
        mock_client.synthesize_streaming = mock_stream
        mock_get_client.return_value.__aenter__ = AsyncMock(return_value=mock_client)
        mock_get_client.return_value.__aexit__ = AsyncMock(return_value=None)
        
        # Test function
        chunks = []
        async for chunk in synthesize_speech_streaming("Hello world"):
            chunks.append(chunk)
        
        assert len(chunks) == 3
        assert chunks[0].audio_data == b"chunk1"
        assert chunks[1].audio_data == b"chunk2"
        assert chunks[2].is_final


@pytest.mark.integration
@pytest.mark.asyncio
class TestHumeIntegration:
    """Integration tests for Hume service (requires API key)."""
    
    @pytest.mark.skipif(
        not os.getenv('HUME_API_KEY'),
        reason="HUME_API_KEY not set"
    )
    async def test_real_emotion_analysis(self):
        """Test real emotion analysis (integration test)."""
        client = HumeEmotionClient()
        
        # Test text emotion analysis
        result = await client.analyze_text_emotions("I am so excited about this!")
        
        assert isinstance(result, EmotionAnalysisResult)
        assert result.primary_emotion != "neutral"  # Should detect some emotion
        assert result.confidence_score > 0
        assert len(result.emotions) > 0
        
        print(f"Detected emotion: {result.primary_emotion} (intensity: {result.emotion_intensity:.2f})")
    
    @pytest.mark.skipif(
        not os.getenv('HUME_API_KEY'),
        reason="HUME_API_KEY not set"
    )
    async def test_real_tts_synthesis(self):
        """Test real TTS synthesis (integration test)."""
        client = HumeTTSClient()
        
        # Test complete synthesis
        result = await client.synthesize_complete("Hello, this is a test!")
        
        assert isinstance(result, TTSResult)
        assert len(result.audio_data) > 0
        assert result.generation_id
        assert result.processing_time_ms > 0
        
        print(f"Generated {len(result.audio_data)} bytes of audio in {result.processing_time_ms:.2f}ms")


if __name__ == "__main__":
    # Run basic tests
    pytest.main([__file__, "-v"])