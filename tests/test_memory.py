import pytest
import json
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework import status
from unittest.mock import patch, MagicMock
from memory.models import Memory, MemoryRetrieval, MemoryCluster, MemoryConfiguration
from memory.services import MemoryManager

User = get_user_model()


@pytest.mark.django_db
class TestMemory:
    """Test memory functionality"""
    
    def test_create_memory(self, user, create_memory):
        """Test creating a memory"""
        memory = create_memory(
            user=user,
            content="User likes pizza",
            memory_type="preference",
            importance_score=0.8,
            personalness_score=0.9,
            actionability_score=0.3
        )
        assert memory.content == "User likes pizza"
        assert memory.user == user
        assert memory.memory_type == "preference"
        assert memory.importance_score == 0.8
    
    def test_memory_salience_score(self, user, create_memory):
        """Test memory salience score calculation"""
        memory = create_memory(
            user=user,
            content="Important task reminder",
            memory_type="task",
            importance_score=0.9,
            personalness_score=0.5,
            actionability_score=0.8
        )
        
        # Salience score should be weighted average
        # Using the default weights from the model: importance=0.5, personalness=0.3, actionability=0.2
        expected_score = (0.9 * 0.5) + (0.5 * 0.3) + (0.8 * 0.2)
        assert abs(memory.get_salience_score() - expected_score) < 0.0001, \
            f"Expected score ~= {expected_score}, got {memory.get_salience_score()}"
    
    def test_list_memories_api(self, authenticated_client, user, create_memory):
        """Test listing memories via API"""
        # Create test memories using the fixture
        create_memory(
            user=user,
            content="Memory 1",
            memory_type="general"
        )
        create_memory(
            user=user,
            content="Memory 2",
            memory_type="preference"
        )
        
        url = reverse('memory:memory-list')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2
        
        # Get the content of all returned memories
        memory_contents = [memory['content'] for memory in response.data['results']]
        assert "Memory 1" in memory_contents
        assert "Memory 2" in memory_contents
    
    def test_create_memory_api(self, authenticated_client, user, create_memory):
        """Test creating memory via API"""
        url = reverse('memory:memory-list')
        data = {
            'content': 'New memory content',
            'memory_type': 'general',  # Use a valid memory type from MemoryType.choices
            'importance_score': 0.7,
            'personalness_score': 0.8,
            'actionability_score': 0.6
        }
        
        response = authenticated_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['content'] == 'New memory content'
        assert response.data['memory_type'] == 'idea'
        assert response.data['importance_score'] == 0.7
        assert response.data['personalness_score'] == 0.8
        assert response.data['actionability_score'] == 0.6
        assert response.data['user'] == user.id


@pytest.mark.django_db
class TestMemoryRetrieval:
    """Test memory retrieval functionality"""
    
    def test_create_memory_retrieval(self, user, create_memory):
        """Test creating memory retrieval record"""
        # Create a test memory using the fixture
        memory = create_memory(
            user=user,
            content="Test memory",
            memory_type="general"
        )
        
        # Create memory retrieval record
        retrieval = MemoryRetrieval.objects.create(
            memory=memory,
            user=user,
            query="test query",
            similarity_score=0.85,
            rank=1
        )
        
        # Verify retrieval was created correctly
        assert retrieval.memory == memory
        assert retrieval.user == user
        assert retrieval.query == "test query"
        assert retrieval.similarity_score == 0.85
        assert retrieval.rank == 1
    
    def test_memory_retrieval_updates_access_count(self, user, create_memory):
        """Test that retrieval updates memory access count"""
        # Create a test memory using the fixture
        memory = create_memory(
            user=user,
            content="Test memory",
            memory_type="general"
        )
        
        # Get initial last_accessed time
        initial_last_accessed = memory.last_accessed
        
        # Create a retrieval record
        with patch('memory.models.timezone.now') as mock_now:
            # Set up mock time to be after the initial time
            mock_time = initial_last_accessed + timezone.timedelta(seconds=1)
            mock_now.return_value = mock_time
            
            MemoryRetrieval.objects.create(
                memory=memory,
                user=user,
                query="test query",
                similarity_score=0.9,
                rank=1
            )
        
        # Refresh memory from db
        memory.refresh_from_db()
        
        # Check if last_accessed was updated
        assert memory.last_accessed > initial_last_accessed, "last_accessed should be greater than the initial time"


@pytest.mark.django_db
class TestMemoryCluster:
    """Test memory clustering functionality"""
    
    def test_create_memory_cluster(self, user):
        """Test creating memory cluster"""
        cluster = MemoryCluster.objects.create(
            user=user,
            name="Food Preferences",
            description="Memories about food likes and dislikes"
        )
        
        assert cluster.name == "Food Preferences"
        assert cluster.user == user
    
    def test_add_memory_to_cluster(self, user, create_memory):
        """Test adding memory to cluster"""
        # Create a test cluster
        cluster = MemoryCluster.objects.create(
            user=user,
            name="Test Cluster",
            description="Test cluster description"
        )
        
        # Create a memory and add it to the cluster using the fixture
        memory = create_memory(
            user=user,
            content="Test memory for cluster",
            memory_type="general"
        )
        
        # Add memory to cluster
        cluster.memories.add(memory)
        
        # Verify the memory was added to the cluster
        memory.refresh_from_db()
        assert cluster.memories.count() == 1
        assert cluster.memories.first() == memory
        assert memory in cluster.memories.all()


@pytest.mark.django_db
class TestMemoryConfiguration:
    """Test user memory configuration"""
    
    def test_create_memory_config(self, user):
        """Test creating user memory config"""
        config = MemoryConfiguration.objects.create(
            user=user,
            max_memories_per_retrieval=10,
            min_importance_threshold=0.5,
            min_personalness_threshold=0.3,
            min_actionability_threshold=0.2,
            auto_cleanup_enabled=True
        )
        
        assert config.user == user
        assert config.max_memories_per_retrieval == 10
        assert config.min_importance_threshold == 0.5
        assert config.min_personalness_threshold == 0.3
        assert config.min_actionability_threshold == 0.2
        assert config.auto_cleanup_enabled is True
    
    def test_should_store_memory(self, user):
        """Test memory storage decision logic"""
        # Create a user memory config with specific thresholds
        config = MemoryConfiguration.objects.create(
            user=user,
            min_importance_threshold=0.6,
            min_personalness_threshold=0.4,
            min_actionability_threshold=0.3,
            auto_cleanup_enabled=True
        )
        
        # Test case 1: All scores above thresholds - should store
        assert config.should_store_memory(
            importance=0.7, 
            personalness=0.5, 
            actionability=0.4
        ) is True, "Should store memory when all scores are above thresholds"
        
        # Test case 2: Importance score below threshold - should not store
        assert config.should_store_memory(
            importance=0.5,  # Below threshold of 0.6
            personalness=0.5, 
            actionability=0.5
        ) is False, "Should not store memory when importance score is below threshold"
        
        # Test case 3: Personalness score below threshold - should not store
        assert config.should_store_memory(
            importance=0.7, 
            personalness=0.3,  # Below threshold of 0.4
            actionability=0.5
        ) is False, "Should not store memory when personalness score is below threshold"
        
        # Test case 4: Actionability score below threshold - should not store
        assert config.should_store_memory(
            importance=0.7, 
            personalness=0.5, 
            actionability=0.2  # Below threshold of 0.3
        ) is False, "Should not store memory when actionability score is below threshold"
        
        # Test case 5: Edge case - scores exactly at thresholds - should store
        assert config.should_store_memory(
            importance=0.6,  # At threshold
            personalness=0.4,  # At threshold
            actionability=0.3  # At threshold
        ) is True, "Should store memory when scores are exactly at thresholds"


@pytest.mark.django_db
class TestMemoryManager:
    """Test memory manager service"""
    
    @pytest.fixture
    def memory_manager(self, mock_vector_store):
        """Create memory manager instance with mocked vector store"""
        with patch('memory.services.MemoryManager._initialize_vector_store'):
            manager = MemoryManager()
            manager.collection = mock_vector_store
            return manager
    
    @patch('memory.services.MemorySalienceScorer.score_text')
    def test_store_memory(self, mock_score, memory_manager, user):
        """Test storing memory via memory manager"""
        # Mock salience scoring
        mock_score.return_value = {
            'importance_score': 0.8,
            'personalness_score': 0.7,
            'actionability_score': 0.5
        }
        
        # Create user config
        UserMemoryConfig.objects.create(
            user=user,
            min_importance_threshold=0.5
        )
        
        memory = memory_manager.store_memory(
            user=user,
            content="User loves Italian food",
            memory_type="preference"
        )
        
        assert memory is not None
        assert memory.content == "User loves Italian food"
        assert memory.user == user
        mock_score.assert_called_once()
    
    def test_retrieve_memories(self, memory_manager, user, create_memory):
        """Test retrieving memories via memory manager"""
        # Create test memories using the fixture
        memory1 = create_memory(
            user=user,
            content="Memory 1 - General information",
            memory_type="general",
            importance_score=0.8,
            personalness_score=0.6,
            actionability_score=0.4
        )
        
        memory2 = create_memory(
            user=user,
            content="Memory 2 - User preference",
            memory_type="preference",
            importance_score=0.9,
            personalness_score=0.8,
            actionability_score=0.5
        )
        
        # Mock the vector store query results
        mock_results = [
            {'id': str(memory1.vector_id), 'score': 0.95, 'metadata': {}},
            {'id': str(memory2.vector_id), 'score': 0.85, 'metadata': {}}
        ]
        memory_manager.collection.query.return_value = {'documents': [], 'metadatas': [], 'distances': [], 'ids': [[str(memory1.vector_id), str(memory2.vector_id)]]}
        
        # Test retrieval with a query
        query = "test query"
        memories = memory_manager.retrieve_memories(
            user=user,
            query=query,
            limit=5
        )
        
        # Verify the vector store was queried correctly
        memory_manager.collection.query.assert_called_once()
        
        # Verify the correct memories were returned
        assert len(memories) == 2, "Should return both memories"
        
        # Verify the memories are ordered by score (descending)
        memory_ids = [str(m.id) for m in memories]
        assert memory_ids == [str(memory1.id), str(memory2.id)], "Memories should be ordered by score"
        
        # Verify the memories contain the expected content
        memory_contents = [m.content for m in memories]
        assert memory1.content in memory_contents
        assert memory2.content in memory_contents
    
    def test_update_memory(self, memory_manager, user, create_memory):
        """Test updating memory via memory manager"""
        # Create a memory to update using the fixture
        memory = create_memory(
            user=user,
            content="Original content for testing updates",
            memory_type="general",
            importance_score=0.7,
            personalness_score=0.5,
            actionability_score=0.3
        )
        
        # Mock the vector store update
        memory_manager.collection.update.return_value = True
        
        # Update the memory
        updated_content = "This content has been updated"
        updated_memory_type = "preference"
        
        updated_memory = memory_manager.update_memory(
            memory_id=memory.id,
            content=updated_content,
            memory_type=updated_memory_type,
            importance_score=0.8,
            personalness_score=0.6,
            actionability_score=0.4
        )
        
        # Refresh the memory from the database
        updated_memory.refresh_from_db()
        
        # Verify the memory was updated correctly
        assert updated_memory is not None, "Updated memory should not be None"
        assert updated_memory.content == updated_content, "Content should be updated"
        assert updated_memory.memory_type == updated_memory_type, "Memory type should be updated"
        assert updated_memory.user == user, "User should remain the same"
        assert updated_memory.importance_score == 0.8, "Importance score should be updated"
        assert updated_memory.personalness_score == 0.6, "Personalness score should be updated"
        assert updated_memory.actionability_score == 0.4, "Actionability score should be updated"
        
        # Verify the vector store was updated
        memory_manager.collection.update.assert_called_once()
    
    def test_delete_memory(self, memory_manager, user, create_memory):
        """Test deleting memory via memory manager"""
        # Create a memory to delete using the fixture
        memory = create_memory(
            user=user,
            content="Memory to be deleted",
            memory_type="general",
            importance_score=0.7,
            personalness_score=0.5,
            actionability_score=0.3
        )
        
        # Get the memory ID and vector ID before deletion
        memory_id = memory.id
        vector_id = memory.vector_id
        
        # Verify the memory exists in the database
        assert Memory.objects.filter(id=memory_id).exists(), "Memory should exist before deletion"
        
        # Mock vector store delete to return success
        memory_manager.collection.delete.return_value = True
        
        # Delete the memory
        result = memory_manager.delete_memory(memory_id)
        
        # Verify the result is True (success)
        assert result is True, "Delete operation should return True on success"
        
        # Verify the memory no longer exists in the database
        assert not Memory.objects.filter(id=memory_id).exists(), "Memory should be deleted from the database"
        
        # Verify the vector store delete was called with the correct vector ID
        memory_manager.collection.delete.assert_called_once_with(ids=[str(vector_id)])

    def test_retrieve_memories_api(self, authenticated_client, user, create_memory):
        """Test retrieving memories via API"""
        # Create test memories using the fixture
        memory1 = create_memory(
            user=user,
            content="Test memory for retrieval 1",
            memory_type="general",
            importance_score=0.8
        )
        memory2 = create_memory(
            user=user,
            content="Another test memory for retrieval",
            memory_type="preference",
            importance_score=0.9
        )
        
        # Mock the vector store query response
        with patch('memory.services.MemoryManager') as mock_manager:
            mock_instance = mock_manager.return_value
            mock_instance.retrieve_memories.return_value = [memory1, memory2]
            
            # Make the API request
            url = reverse('memory:retrieve-memories')
            response = authenticated_client.get(url, {'query': 'test', 'limit': '5'})
            
            # Verify the response
            assert response.status_code == status.HTTP_200_OK, "API should return 200 OK"
            assert 'memories' in response.data, "Response should contain 'memories' key"
            assert len(response.data['memories']) == 2, "Should return both memories"
            
            # Verify the memory manager was called with the correct parameters
            mock_instance.retrieve_memories.assert_called_once_with(
                user=user,
                query='test',
                limit=5,
                memory_type=None,
                min_importance=None,
                min_salience=None
            )
    
    def test_memory_stats_api(self, authenticated_client, user, create_memory):
        """Test getting memory statistics via API"""
        # Create test memories with different types using the fixture
        general_memory = create_memory(
            user=user,
            content="General memory for stats",
            memory_type="general",
            importance_score=0.7
        )
        preference_memory = create_memory(
            user=user,
            content="Preference memory for stats",
            memory_type="preference",
            importance_score=0.9
        )
        
        # Create a memory for a different user that shouldn't be counted
        other_user = User.objects.create_user(
            username="other_user",
            email="<EMAIL>",
            password="testpass123"
        )
        create_memory(
            user=other_user,
            content="Other user's memory",
            memory_type="general",
            importance_score=0.5
        )
        
        # Make the API request
        url = reverse('memory:memory-stats')
        response = authenticated_client.get(url)
        
        # Verify the response
        assert response.status_code == status.HTTP_200_OK, "API should return 200 OK"
        
        # Check required fields in response
        assert 'total_memories' in response.data, "Response should contain total_memories"
        assert 'memory_types' in response.data, "Response should contain memory_types"
        assert 'average_importance' in response.data, "Response should contain average_importance"
        
        # Verify the statistics
        assert response.data['total_memories'] == 2, "Should only count memories for the authenticated user"
        
        # Check memory type distribution
        memory_types = [mt['memory_type'] for mt in response.data['memory_types']]
        assert 'general' in memory_types, "Should include 'general' memory type"
        assert 'preference' in memory_types, "Should include 'preference' memory type"
        assert response.data['memory_types']['general'] == 1, "Should have 1 general memory"
        assert response.data['memory_types']['preference'] == 1, "Should have 1 preference memory"
        
        # Verify average importance calculation
        expected_avg_importance = (0.7 + 0.9) / 2
        assert abs(response.data['average_importance'] - expected_avg_importance) < 0.01, "Average importance calculation is incorrect"
    
    def test_memory_config_api(self, authenticated_client, user):
        """Test getting and updating memory config via API"""
        url = reverse('memory:memory-config')
        
        # Test 1: Get config (should create default if not exists)
        response = authenticated_client.get(url)
        assert response.status_code == status.HTTP_200_OK, "API should return 200 OK"
        
        # Verify default config structure
        config_data = response.data
        assert 'max_memories_per_retrieval' in config_data, "Config should contain max_memories_per_retrieval"
        assert 'min_importance_threshold' in config_data, "Config should contain min_importance_threshold"
        assert 'auto_cleanup_enabled' in config_data, "Config should contain auto_cleanup_enabled"
        
        # Test 2: Update config with PATCH (partial update)
        update_data = {
            'max_memories': 2000,
            'min_importance_threshold': 0.7,
            'enable_clustering': False
        }
        response = authenticated_client.patch(
            url, 
            data=json.dumps(update_data),
            content_type='application/json'
        )
        
        # Verify update response
        assert response.status_code == status.HTTP_200_OK, "Update should succeed with 200 OK"
        
        # Verify updated values
        updated_config = response.data
        assert updated_config['max_memories'] == 2000, "max_memories should be updated"
        assert updated_config['min_importance_threshold'] == 0.7, "min_importance_threshold should be updated"
        assert updated_config['enable_clustering'] is False, "enable_clustering should be updated to False"
        
        # Test 3: Verify config was saved by fetching it again
        response = authenticated_client.get(url)
        assert response.status_code == status.HTTP_200_OK
        saved_config = response.data
        
        # Verify the saved values match what we set
        assert saved_config['max_memories'] == 2000, "max_memories should be saved"
        assert saved_config['min_importance_threshold'] == 0.7, "min_importance_threshold should be saved"
        assert saved_config['enable_clustering'] is False, "enable_clustering should be saved as False"
        
        # Test 4: Test validation
        invalid_data = {
            'max_memories': -100,  # Invalid value
            'min_importance_threshold': 1.5  # Out of range
        }
        response = authenticated_client.patch(
            url,
            data=json.dumps(invalid_data),
            content_type='application/json'
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST, "Should reject invalid data"
        assert 'max_memories' in response.data, "Should report error for max_memories"
        assert 'min_importance_threshold' in response.data, "Should report error for min_importance_threshold"
    
    def test_cleanup_memories_api(self, authenticated_client, user, create_memory):
        """Test memory cleanup via API"""
        # Create a user config that will cause low-importance memories to be cleaned up
        from memory.models import MemoryConfiguration
        MemoryConfiguration.objects.create(
            user=user,
            min_importance_threshold=0.5,  # Will clean up memories below this threshold
            auto_cleanup_enabled=True
        )
        
        # Create memories with different importance scores
        important_memory = create_memory(
            user=user,
            content="Important memory (should be kept)",
            memory_type="general",
            importance_score=0.8,  # Above threshold
            last_accessed=timezone.now() - timezone.timedelta(days=10)
        )
        
        old_low_importance_memory = create_memory(
            user=user,
            content="Old low importance memory (should be deleted)",
            memory_type="general",
            importance_score=0.3,  # Below threshold
            last_accessed=timezone.now() - timezone.timedelta(days=60)  # Old memory
        )
        
        # Create a memory for a different user that shouldn't be affected
        other_user = User.objects.create_user(
            username="other_user_cleanup",
            email="<EMAIL>",
            password="testpass123"
        )
        other_user_memory = create_memory(
            user=other_user,
            content="Other user's memory (should not be deleted)",
            memory_type="general",
            importance_score=0.1,  # Below threshold but different user
            last_accessed=timezone.now() - timezone.timedelta(days=60)
        )
        
        # Get initial counts
        initial_count = Memory.objects.count()
        initial_user_count = Memory.objects.filter(user=user).count()
        
        # Call the cleanup API
        url = reverse('memory:cleanup-memories')
        response = authenticated_client.post(url)
        
        # Verify the response
        assert response.status_code == status.HTTP_200_OK, "API should return 200 OK"
        assert 'deleted_count' in response.data, "Response should include deleted_count"
        assert response.data['deleted_count'] == 1, "Should delete 1 memory"
        
        # Verify the database state after cleanup
        assert Memory.objects.count() == initial_count - 1, "Total memory count should decrease by 1"
        assert Memory.objects.filter(user=user).count() == initial_user_count - 1, "User's memory count should decrease by 1"
        
        # Verify the correct memory was deleted
        assert not Memory.objects.filter(id=old_low_importance_memory.id).exists(), "Low importance memory should be deleted"
        
        # Verify other memories still exist
        assert Memory.objects.filter(id=important_memory.id).exists(), "Important memory should still exist"
        assert Memory.objects.filter(id=other_user_memory.id).exists(), "Other user's memory should still exist"
        
        # Verify the response includes the correct statistics
        assert 'remaining_memories' in response.data, "Response should include remaining_memories"
        assert response.data['remaining_memories'] == initial_user_count - 1, "Remaining memories count should be correct"


@pytest.mark.django_db
class TestMemorySearch:
    """Test memory search functionality"""
    
    def test_search_memories_api(self, authenticated_client, user, create_memory):
        """Test searching memories via API"""
        # Create test memories with different content and types
        memory1 = create_memory(
            user=user,
            content="Test memory about programming in Python",
            memory_type="knowledge",
            importance_score=0.8,
            personalness_score=0.6,
            actionability_score=0.7
        )
        
        memory2 = create_memory(
            user=user,
            content="User prefers dark theme for the application",
            memory_type="preference",
            importance_score=0.9,
            personalness_score=0.8,
            actionability_score=0.3
        )
        
        # Create a memory for a different user
        other_user = User.objects.create_user(
            username="search_test_user",
            email="<EMAIL>",
            password="testpass123"
        )
        create_memory(
            user=other_user,
            content="Other user's memory (should not appear in results)",
            memory_type="general",
            importance_score=0.7
        )
        
        # Mock the vector store search results
        with patch('memory.services.MemoryManager') as mock_manager:
            mock_instance = mock_manager.return_value
            # Return both memories for the search query
            mock_instance.search_memories.return_value = [memory1, memory2]
            
            # Test 1: Basic search
            url = reverse('memory:search-memories')
            response = authenticated_client.get(url, {'q': 'programming'})
            
            # Verify the response
            assert response.status_code == status.HTTP_200_OK, "Search API should return 200 OK"
            assert 'results' in response.data, "Response should contain 'results' key"
            assert len(response.data['results']) == 2, "Should return both matching memories"
            
            # Verify the memory manager was called with the correct parameters
            mock_instance.search_memories.assert_called_once()
            call_args = mock_instance.search_memories.call_args[1]
            assert call_args['user'] == user, "Should search only the authenticated user's memories"
            assert call_args['query'] == 'programming', "Should pass the search query"
            assert call_args.get('limit', 10) == 10, "Should use default limit if not specified"
            
            # Test 2: Search with filters
            response = authenticated_client.get(url, {
                'q': 'theme',
                'memory_type': 'preference',
                'min_importance': '0.5',
                'limit': '5'
            })
            
            # Verify the filtered search
            assert response.status_code == status.HTTP_200_OK
            assert len(response.data['results']) == 1, "Should return only the matching preference memory"
            assert response.data['results'][0]['id'] == memory2.id, "Should return the correct memory"
            
            # Test 3: Empty search query
            response = authenticated_client.get(url, {'q': ''})
            assert response.status_code == status.HTTP_400_BAD_REQUEST, "Should require a search query"
            
            # Test 4: Pagination
            response = authenticated_client.get(url, {'q': 'test', 'page': '1', 'page_size': '1'})
            assert response.status_code == status.HTTP_200_OK
            assert 'count' in response.data, "Should include total count in paginated results"
            assert 'next' in response.data, "Should include next page URL"
            assert 'previous' in response.data, "Should include previous page URL"
            assert 'results' in response.data, "Should include results in paginated response"


@pytest.mark.django_db
class TestMemoryPermissions:
    """Test memory access permissions"""
    
    def test_user_can_only_access_own_memories(self, api_client, create_memory):
        """Test that users can only access their own memories"""
        # Create two test users with unique email addresses
        user1 = User.objects.create_user(
            username='test_user1',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User1'
        )
        
        user2 = User.objects.create_user(
            username='test_user2',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User2'
        )
        
        # Create memories for both users using the fixture
        memory1 = create_memory(
            user=user1,
            content="User 1's private memory about Python programming",
            memory_type="knowledge",
            importance_score=0.8
        )
        
        memory2 = create_memory(
            user=user2,
            content="User 2's private memory about music preferences",
            memory_type="preference",
            importance_score=0.9
        )
        
        # Test 1: Unauthenticated user should not access any memories
        url = reverse('memory:memory-detail', args=[memory1.id])
        response = api_client.get(url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED, "Unauthenticated users should get 401"
        
        # Authenticate as user1
        api_client.force_authenticate(user=user1)
        
        # Test 2: User1 should be able to access their own memory
        response = api_client.get(url)
        assert response.status_code == status.HTTP_200_OK, "User should be able to access their own memory"
        assert response.data['id'] == memory1.id, "Should return the correct memory"
        assert response.data['content'] == memory1.content, "Memory content should match"
        
        # Test 3: User1 should not be able to access user2's memory (should get 404)
        url = reverse('memory:memory-detail', args=[memory2.id])
        response = api_client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND, "Should not find other user's memory"
        
        # Test 4: User1 should only see their own memories in list view
        list_url = reverse('memory:memory-list')
        response = api_client.get(list_url)
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1, "Should only see user's own memories"
        assert response.data[0]['id'] == memory1.id, "Should only see user's own memories"
        
        # Test 5: User1 should not be able to update user2's memory
        update_data = {'content': 'Updated content by user1'}
        response = api_client.patch(
            reverse('memory:memory-detail', args=[memory2.id]),
            data=update_data,
            format='json'
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND, "Should not be able to update other user's memory"
        
        # Verify the memory was not actually updated
        memory2.refresh_from_db()
        assert memory2.content != update_data['content'], "Memory content should not be updated"
        
        # Test 6: User1 should not be able to delete user2's memory
        response = api_client.delete(reverse('memory:memory-detail', args=[memory2.id]))
        assert response.status_code == status.HTTP_404_NOT_FOUND, "Should not be able to delete other user's memory"
        assert Memory.objects.filter(id=memory2.id).exists(), "Memory should still exist"
        
        # Test 7: User1 should be able to delete their own memory
        response = api_client.delete(reverse('memory:memory-detail', args=[memory1.id]))
        assert response.status_code == status.HTTP_204_NO_CONTENT, "Should be able to delete own memory"
        assert not Memory.objects.filter(id=memory1.id).exists(), "Memory should be deleted"
