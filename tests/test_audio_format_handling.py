"""
Tests for audio format handling and validation functionality.
"""
import pytest
import numpy as np
import wave
import io
from unittest.mock import Mock, patch

from chat.services.audio_service import (
    AudioChunk,
    AudioProcessingService,
)
from chat.services.audio_format import (
    AudioFormatHandler,
    decode_and_validate_audio,
    prepare_audio_for_processing
)


class TestAudioFormatHandler:
    """Test audio format detection and validation."""
    
    def test_detect_wav_format(self):
        """Test WAV format detection."""
        # Create a simple WAV header
        wav_data = b'RIFF\x24\x00\x00\x00WAVE'
        format_type = AudioFormatHandler.detect_format(wav_data)
        assert format_type == 'wav'
    
    def test_detect_mp3_format(self):
        """Test MP3 format detection."""
        # MP3 with ID3 tag
        mp3_data = b'ID3\x03\x00\x00\x00\x00\x00\x00'
        format_type = AudioFormatHandler.detect_format(mp3_data)
        assert format_type == 'mp3'
        
        # MP3 frame header
        mp3_frame = b'\xff\xfb\x90\x00'
        format_type = AudioFormatHandler.detect_format(mp3_frame)
        assert format_type == 'mp3'
    
    def test_detect_webm_format(self):
        """Test WebM format detection."""
        webm_data = b'\x1a\x45\xdf\xa3\x00\x00\x00\x20'
        format_type = AudioFormatHandler.detect_format(webm_data)
        assert format_type == 'webm'
    
    def test_detect_ogg_format(self):
        """Test OGG format detection."""
        ogg_data = b'OggS\x00\x02\x00\x00'
        format_type = AudioFormatHandler.detect_format(ogg_data)
        assert format_type == 'ogg'
    
    def test_detect_pcm_format(self):
        """Test PCM format detection (fallback)."""
        pcm_data = b'\x00\x01\x02\x03\x04\x05'
        format_type = AudioFormatHandler.detect_format(pcm_data)
        assert format_type == 'pcm'
    
    def test_detect_unknown_format(self):
        """Test unknown format detection."""
        unknown_data = b''
        format_type = AudioFormatHandler.detect_format(unknown_data)
        assert format_type == 'unknown'
    
    def test_validate_empty_audio(self):
        """Test validation of empty audio data."""
        result = AudioFormatHandler.validate_audio_format(b'')
        assert not result['valid']
        assert 'empty' in result['error'].lower()
    
    def test_validate_wav_format(self):
        """Test WAV format validation."""
        # Create a valid WAV file in memory
        buffer = io.BytesIO()
        with wave.open(buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(48000)
            
            # Generate 1 second of sine wave
            samples = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 48000))
            samples = (samples * 32767).astype(np.int16)
            wav_file.writeframes(samples.tobytes())
        
        wav_data = buffer.getvalue()
        result = AudioFormatHandler.validate_audio_format(wav_data)
        
        assert result['valid']
        assert result['format'] == 'wav'
        assert result['channels'] == 1
        assert result['sample_rate'] == 48000
        assert result['sample_width'] == 2
        assert result['duration_seconds'] == pytest.approx(1.0, rel=0.1)
    
    def test_validate_wav_invalid_channels(self):
        """Test WAV validation with invalid channel count."""
        # Create WAV with 8 channels (unsupported)
        buffer = io.BytesIO()
        with wave.open(buffer, 'wb') as wav_file:
            wav_file.setnchannels(8)  # Invalid
            wav_file.setsampwidth(2)
            wav_file.setframerate(48000)
            wav_file.writeframes(b'\x00\x00' * 100)
        
        wav_data = buffer.getvalue()
        result = AudioFormatHandler.validate_audio_format(wav_data)
        
        assert not result['valid']
        assert 'channel' in result['error'].lower()
    
    def test_validate_pcm_format(self):
        """Test PCM format validation."""
        # Generate 16-bit PCM data
        samples = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 48000))
        pcm_data = (samples * 32767).astype(np.int16).tobytes()
        
        result = AudioFormatHandler.validate_audio_format(pcm_data)
        
        assert result['valid']
        assert result['format'] == 'pcm'
        assert result['channels'] == 1
        assert result['sample_rate'] == 48000
        assert result['duration_seconds'] == pytest.approx(1.0, rel=0.1)
    
    def test_convert_wav_to_pcm(self):
        """Test WAV to PCM conversion."""
        # Create stereo WAV at 44.1kHz
        buffer = io.BytesIO()
        with wave.open(buffer, 'wb') as wav_file:
            wav_file.setnchannels(2)
            wav_file.setsampwidth(2)
            wav_file.setframerate(44100)
            
            # Generate stereo sine wave
            samples = np.sin(2 * np.pi * 440 * np.linspace(0, 0.5, 22050))
            stereo_samples = np.column_stack([samples, samples])
            wav_file.writeframes((stereo_samples * 32767).astype(np.int16).tobytes())
        
        wav_data = buffer.getvalue()
        format_info = AudioFormatHandler.validate_audio_format(wav_data)
        
        # Convert to target PCM format
        pcm_data = AudioFormatHandler.convert_to_pcm(wav_data, format_info)
        
        # Verify conversion
        assert len(pcm_data) > 0
        # Should be mono (half the samples) and resampled to 48kHz
        expected_samples = int(22050 * 48000 / 44100)  # Resampling calculation
        assert len(pcm_data) == expected_samples * 2  # 2 bytes per sample
    
    def test_convert_pcm_resampling(self):
        """Test PCM resampling."""
        # Generate 22kHz PCM data
        samples = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 22000))
        pcm_data = (samples * 32767).astype(np.int16).tobytes()
        
        format_info = {
            'valid': True,
            'format': 'pcm',
            'channels': 1,
            'sample_rate': 22000,
            'sample_width': 2
        }
        
        # Convert to 48kHz
        converted_data = AudioFormatHandler.convert_to_pcm(pcm_data, format_info)
        
        # Should be resampled to 48kHz
        expected_samples = int(22000 * 48000 / 22000)  # Should be 48000
        assert len(converted_data) == expected_samples * 2
    
    def test_simple_resample(self):
        """Test simple resampling algorithm."""
        # Create test signal
        original = np.sin(2 * np.pi * np.linspace(0, 1, 1000))
        
        # Upsample 2x
        upsampled = AudioFormatHandler._simple_resample(original, 1000, 2000)
        assert len(upsampled) == 2000
        
        # Downsample 2x
        downsampled = AudioFormatHandler._simple_resample(original, 1000, 500)
        assert len(downsampled) == 500
        
        # No resampling
        unchanged = AudioFormatHandler._simple_resample(original, 1000, 1000)
        np.testing.assert_array_equal(unchanged, original)


class TestAudioPreprocessor:
    """Test audio preprocessing functionality."""
    
    def test_noise_reduction_basic(self):
        """Test basic noise reduction functionality."""
        # Generate noisy audio (sine wave + noise)
        duration = 1.0
        sample_rate = 48000
        samples = int(duration * sample_rate)
        
        # Clean signal
        t = np.linspace(0, duration, samples)
        clean_signal = np.sin(2 * np.pi * 440 * t)
        
        # Add noise
        noise = np.random.normal(0, 0.1, samples)
        noisy_signal = clean_signal + noise
        
        # Convert to bytes
        noisy_bytes = (noisy_signal * 32767).astype(np.int16).tobytes()
        
        # Apply noise reduction
        processed_bytes = AudioPreprocessor.apply_noise_reduction(noisy_bytes, sample_rate)
        
        # Verify processing
        assert len(processed_bytes) == len(noisy_bytes)
        
        # Convert back to array for analysis
        processed_array = np.frombuffer(processed_bytes, dtype=np.int16).astype(np.float32) / 32768.0
        
        # Processed signal should have similar length
        assert len(processed_array) == len(noisy_signal)
    
    def test_high_pass_filter(self):
        """Test high-pass filter functionality."""
        # Generate signal with low and high frequency components
        sample_rate = 48000
        duration = 1.0
        samples = int(duration * sample_rate)
        t = np.linspace(0, duration, samples)
        
        # Low frequency (should be attenuated)
        low_freq = np.sin(2 * np.pi * 50 * t)
        # High frequency (should pass through)
        high_freq = np.sin(2 * np.pi * 1000 * t)
        
        combined_signal = low_freq + high_freq
        
        # Apply high-pass filter
        filtered = AudioPreprocessor._high_pass_filter(combined_signal, sample_rate, cutoff=200)
        
        # High frequency component should be preserved better than low frequency
        assert len(filtered) == len(combined_signal)
        
        # Check that filter reduces low frequencies
        low_power_original = np.mean(low_freq ** 2)
        low_power_filtered = np.mean(filtered[:len(low_freq)] ** 2)
        
        # Filtered signal should have less low-frequency power
        # (This is a simplified test - in practice you'd use FFT analysis)
        assert low_power_filtered <= low_power_original
    
    def test_noise_gate(self):
        """Test noise gate functionality."""
        # Create signal with quiet and loud sections
        sample_rate = 48000
        samples = 4800  # 0.1 seconds
        
        # Quiet section (should be attenuated)
        quiet_signal = np.random.normal(0, 0.005, samples)
        # Loud section (should pass through)
        loud_signal = np.sin(2 * np.pi * 440 * np.linspace(0, 0.1, samples)) * 0.5
        
        combined = np.concatenate([quiet_signal, loud_signal])
        
        # Apply noise gate
        gated = AudioPreprocessor._noise_gate(combined, threshold=0.01)
        
        assert len(gated) == len(combined)
        
        # Quiet section should be more attenuated than loud section
        quiet_power_original = np.mean(quiet_signal ** 2)
        quiet_power_gated = np.mean(gated[:samples] ** 2)
        
        loud_power_original = np.mean(loud_signal ** 2)
        loud_power_gated = np.mean(gated[samples:] ** 2)
        
        # Quiet section should be more attenuated
        quiet_attenuation = quiet_power_gated / quiet_power_original
        loud_attenuation = loud_power_gated / loud_power_original
        
        assert quiet_attenuation < loud_attenuation
    
    def test_normalize_audio(self):
        """Test audio normalization."""
        # Create signal with low amplitude
        low_amplitude = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 1000)) * 0.1
        
        # Normalize to 0.7 target level
        normalized = AudioPreprocessor._normalize_audio(low_amplitude, target_level=0.7)
        
        # Peak should be close to target level
        peak = np.max(np.abs(normalized))
        assert peak == pytest.approx(0.7, rel=0.01)
        
        # Test with empty array
        empty_normalized = AudioPreprocessor._normalize_audio(np.array([]))
        assert len(empty_normalized) == 0
        
        # Test with zero signal
        zero_signal = np.zeros(1000)
        zero_normalized = AudioPreprocessor._normalize_audio(zero_signal)
        np.testing.assert_array_equal(zero_normalized, zero_signal)


class TestAudioProcessingServiceEnhanced:
    """Test enhanced audio processing service with format handling."""
    
    @pytest.fixture
    def audio_service(self):
        """Create audio processing service for testing."""
        return AudioProcessingService()
    
    @pytest.fixture
    def sample_wav_chunk(self):
        """Create a sample WAV audio chunk."""
        # Create WAV data
        buffer = io.BytesIO()
        with wave.open(buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(48000)
            
            # Generate 0.1 second of sine wave
            samples = np.sin(2 * np.pi * 440 * np.linspace(0, 0.1, 4800))
            wav_file.writeframes((samples * 32767).astype(np.int16).tobytes())
        
        wav_data = buffer.getvalue()
        
        return AudioChunk(
            data=wav_data,
            chunk_id="test_wav_chunk",
            timestamp_ms=1000.0,
            sample_rate=48000,
            channels=1,
            is_final=False,
            user_id="test_user"
        )
    
    @pytest.fixture
    def sample_pcm_chunk(self):
        """Create a sample PCM audio chunk."""
        # Generate PCM data
        samples = np.sin(2 * np.pi * 440 * np.linspace(0, 0.1, 4800))
        pcm_data = (samples * 32767).astype(np.int16).tobytes()
        
        return AudioChunk(
            data=pcm_data,
            chunk_id="test_pcm_chunk",
            timestamp_ms=1000.0,
            sample_rate=48000,
            channels=1,
            is_final=False,
            user_id="test_user"
        )
    
    @pytest.mark.asyncio
    async def test_validate_and_preprocess_wav_chunk(self, audio_service, sample_wav_chunk):
        """Test validation and preprocessing of WAV chunk."""
        processed_chunk = await audio_service._validate_and_preprocess_chunk(
            sample_wav_chunk, enable_preprocessing=True
        )
        
        assert processed_chunk is not None
        assert processed_chunk.chunk_id == sample_wav_chunk.chunk_id
        assert processed_chunk.user_id == sample_wav_chunk.user_id
        # Data should be processed (converted from WAV to PCM)
        assert len(processed_chunk.data) > 0
    
    @pytest.mark.asyncio
    async def test_validate_and_preprocess_pcm_chunk(self, audio_service, sample_pcm_chunk):
        """Test validation and preprocessing of PCM chunk."""
        processed_chunk = await audio_service._validate_and_preprocess_chunk(
            sample_pcm_chunk, enable_preprocessing=True
        )
        
        assert processed_chunk is not None
        assert processed_chunk.chunk_id == sample_pcm_chunk.chunk_id
        # PCM data should be processed for noise reduction
        assert len(processed_chunk.data) > 0
    
    @pytest.mark.asyncio
    async def test_validate_invalid_chunk(self, audio_service):
        """Test validation of invalid audio chunk."""
        invalid_chunk = AudioChunk(
            data=b'invalid_audio_data',
            chunk_id="invalid_chunk",
            timestamp_ms=1000.0,
            sample_rate=48000,
            channels=1,
            is_final=False,
            user_id="test_user"
        )
        
        # Should handle invalid data gracefully
        processed_chunk = await audio_service._validate_and_preprocess_chunk(
            invalid_chunk, enable_preprocessing=True
        )
        
        # Should return None for invalid data
        assert processed_chunk is None
    
    @pytest.mark.asyncio
    async def test_process_audio_chunk_with_preprocessing(self, audio_service, sample_pcm_chunk):
        """Test audio chunk processing with preprocessing enabled."""
        results = []
        async for result in audio_service.process_audio_chunk(
            sample_pcm_chunk,
            enable_transcription=True,
            enable_emotion_detection=True,
            parallel_processing=True,
            enable_preprocessing=True
        ):
            results.append(result)
        
        # Should get results (may be empty if buffer not ready)
        # This tests that preprocessing doesn't break the pipeline
        assert isinstance(results, list)


class TestUtilityFunctions:
    """Test utility functions for audio format handling."""
    
    def test_validate_audio_format_function(self):
        """Test the validate_audio_format utility function."""
        # Generate PCM data
        samples = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 48000))
        pcm_data = (samples * 32767).astype(np.int16).tobytes()
        
        result = validate_audio_format(pcm_data)
        
        assert result['valid']
        assert result['format'] == 'pcm'
    
    def test_convert_audio_format_function(self):
        """Test the convert_audio_format utility function."""
        # Generate PCM data at 22kHz
        samples = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 22000))
        pcm_data = (samples * 32767).astype(np.int16).tobytes()
        
        # Convert to 48kHz
        converted_data = convert_audio_format(pcm_data, target_sample_rate=48000)
        
        assert len(converted_data) > 0
        # Should be resampled
        assert len(converted_data) != len(pcm_data)
    
    def test_convert_invalid_audio_format(self):
        """Test conversion of invalid audio format."""
        with pytest.raises(ValueError):
            convert_audio_format(b'')  # Empty data
    
    def test_preprocess_audio_function(self):
        """Test the preprocess_audio utility function."""
        # Generate noisy PCM data
        samples = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 48000))
        noise = np.random.normal(0, 0.1, 48000)
        noisy_samples = samples + noise
        pcm_data = (noisy_samples * 32767).astype(np.int16).tobytes()
        
        processed_data = preprocess_audio(pcm_data, sample_rate=48000)
        
        assert len(processed_data) == len(pcm_data)
        assert processed_data != pcm_data  # Should be different after processing


if __name__ == '__main__':
    pytest.main([__file__])