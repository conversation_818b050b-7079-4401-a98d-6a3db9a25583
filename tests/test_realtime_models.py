"""
Tests for real-time AI companion models.
"""
import pytest
from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import timedelta
import uuid

from chat.models_realtime import (
    UserRelationship,
    EmotionContext,
    StreamingSession,
    PerformanceMetrics
)
from chat.models import Conversation, Message

User = get_user_model()


class UserRelationshipTestCase(TestCase):
    """Test cases for UserRelationship model."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.relationship = UserRelationship.objects.create(user=self.user)
    
    def test_relationship_creation(self):
        """Test that relationship is created with default values."""
        self.assertEqual(self.relationship.relationship_level, 1)
        self.assertEqual(self.relationship.total_interactions, 0)
        self.assertEqual(self.relationship.emotional_intimacy_score, 0.0)
        self.assertFalse(self.relationship.explicit_progression_request)
    
    def test_can_access_content_level(self):
        """Test content level access control."""
        # Level 1 user can access level 1 content
        self.assertTrue(self.relationship.can_access_content_level(1))
        # But not level 2
        self.assertFalse(self.relationship.can_access_content_level(2))
        
        # Upgrade to level 2
        self.relationship.relationship_level = 2
        self.relationship.save()
        
        # Now can access level 2
        self.assertTrue(self.relationship.can_access_content_level(2))
        self.assertFalse(self.relationship.can_access_content_level(3))
    
    def test_update_interaction_metrics(self):
        """Test updating interaction metrics."""
        initial_interactions = self.relationship.total_interactions
        
        # Update with conversation duration and emotion intensity
        duration = timedelta(minutes=5)
        emotion_intensity = 0.7
        
        self.relationship.update_interaction_metrics(
            conversation_duration=duration,
            emotion_intensity=emotion_intensity
        )
        
        self.assertEqual(self.relationship.total_interactions, initial_interactions + 1)
        self.assertEqual(self.relationship.total_conversation_time, duration)
        self.assertEqual(self.relationship.average_emotion_intensity, emotion_intensity)
    
    def test_progression_eligibility_level_2(self):
        """Test progression eligibility for level 2."""
        # Initially not eligible
        eligible, reason = self.relationship.check_progression_eligibility()
        self.assertFalse(eligible)
        self.assertIn("interactions", reason)
        
        # Meet requirements for level 2
        self.relationship.total_interactions = 10
        self.relationship.total_conversation_time = timedelta(hours=2)
        self.relationship.emotional_intimacy_score = 0.3
        self.relationship.save()
        
        eligible, reason = self.relationship.check_progression_eligibility()
        self.assertTrue(eligible)
        self.assertEqual(reason, "Eligible for progression")
    
    def test_progression_eligibility_level_4(self):
        """Test progression eligibility for level 4 (requires explicit consent)."""
        # Set to level 3
        self.relationship.relationship_level = 3
        self.relationship.total_interactions = 100
        self.relationship.total_conversation_time = timedelta(hours=25)
        self.relationship.emotional_intimacy_score = 0.8
        self.relationship.save()
        
        # Should not be eligible without explicit consent
        eligible, reason = self.relationship.check_progression_eligibility()
        self.assertFalse(eligible)
        self.assertIn("consent", reason)
        
        # Add explicit consent
        self.relationship.explicit_progression_request = True
        self.relationship.user_consent_flags = {'adult_content_consent': True}
        self.relationship.save()
        
        eligible, reason = self.relationship.check_progression_eligibility()
        self.assertTrue(eligible)
    
    def test_progress_relationship(self):
        """Test relationship progression."""
        # Set up for level 2 progression
        self.relationship.total_interactions = 10
        self.relationship.total_conversation_time = timedelta(hours=2)
        self.relationship.emotional_intimacy_score = 0.3
        self.relationship.save()
        
        old_level = self.relationship.relationship_level
        success, message = self.relationship.progress_relationship()
        
        self.assertTrue(success)
        self.assertEqual(self.relationship.relationship_level, old_level + 1)
        self.assertIn("level_2_achieved", self.relationship.progression_milestones)
        self.assertIsNotNone(self.relationship.last_milestone_date)
    
    def test_max_level_progression(self):
        """Test that progression stops at max level."""
        self.relationship.relationship_level = 4
        self.relationship.save()
        
        success, message = self.relationship.progress_relationship()
        self.assertFalse(success)
        self.assertIn("maximum", message)


class EmotionContextTestCase(TestCase):
    """Test cases for EmotionContext model."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.session_id = str(uuid.uuid4())
        
        # Create a relationship for the user
        self.relationship = UserRelationship.objects.create(user=self.user)
    
    def test_emotion_context_creation(self):
        """Test creating emotion context."""
        emotion_context = EmotionContext.objects.create(
            user=self.user,
            session_id=self.session_id,
            primary_emotion='joy',
            emotion_intensity=0.8,
            emotion_valence=0.7,
            emotion_arousal=0.6,
            confidence_score=0.9,
            audio_emotions={'joy': 0.8, 'excitement': 0.6},
            text_emotions={'positive': 0.7, 'happy': 0.8}
        )
        
        self.assertEqual(emotion_context.user, self.user)
        self.assertEqual(emotion_context.primary_emotion, 'joy')
        self.assertEqual(emotion_context.emotion_intensity, 0.8)
        self.assertEqual(emotion_context.confidence_score, 0.9)
    
    def test_get_top_emotions(self):
        """Test getting top emotions from different sources."""
        emotion_context = EmotionContext.objects.create(
            user=self.user,
            session_id=self.session_id,
            primary_emotion='joy',
            audio_emotions={'joy': 0.8, 'excitement': 0.6, 'calm': 0.3},
            text_emotions={'positive': 0.7, 'happy': 0.8, 'neutral': 0.2}
        )
        
        # Test audio emotions
        top_audio = emotion_context.get_top_emotions(n=2, source='audio')
        self.assertEqual(len(top_audio), 2)
        self.assertEqual(top_audio[0][0], 'joy')  # Highest score
        self.assertEqual(top_audio[1][0], 'excitement')  # Second highest
        
        # Test text emotions
        top_text = emotion_context.get_top_emotions(n=2, source='text')
        self.assertEqual(len(top_text), 2)
        self.assertEqual(top_text[0][0], 'happy')  # Highest score
    
    def test_update_relationship_metrics(self):
        """Test updating relationship metrics based on emotion."""
        initial_intimacy = self.relationship.emotional_intimacy_score
        
        # Create high-intensity intimate emotion
        emotion_context = EmotionContext.objects.create(
            user=self.user,
            session_id=self.session_id,
            primary_emotion='love',
            emotion_intensity=0.9,
            confidence_score=0.8
        )
        
        emotion_context.update_relationship_metrics()
        
        # Refresh relationship from database
        self.relationship.refresh_from_db()
        
        # Should have increased intimacy score
        self.assertGreater(self.relationship.emotional_intimacy_score, initial_intimacy)


class StreamingSessionTestCase(TestCase):
    """Test cases for StreamingSession model."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.session = StreamingSession.objects.create(
            user=self.user,
            session_id=str(uuid.uuid4()),
            websocket_id='ws_123',
            session_type='voice'
        )
    
    def test_session_creation(self):
        """Test streaming session creation."""
        self.assertEqual(self.session.user, self.user)
        self.assertEqual(self.session.session_type, 'voice')
        self.assertEqual(self.session.status, 'connecting')
        self.assertTrue(self.session.is_active)
        self.assertEqual(self.session.error_count, 0)
    
    def test_update_activity(self):
        """Test updating session activity."""
        old_activity = self.session.last_activity
        
        # Wait a moment and update activity
        import time
        time.sleep(0.01)
        self.session.update_activity()
        
        self.assertGreater(self.session.last_activity, old_activity)
    
    def test_add_performance_metric(self):
        """Test adding performance metrics."""
        self.session.add_performance_metric('response_time', 250.5)
        
        metrics = self.session.performance_metrics.get('metrics', [])
        self.assertEqual(len(metrics), 1)
        self.assertEqual(metrics[0]['name'], 'response_time')
        self.assertEqual(metrics[0]['value'], 250.5)
    
    def test_get_average_response_time(self):
        """Test calculating average response time."""
        # Add multiple response time metrics
        self.session.add_performance_metric('total_response_time', 200.0)
        self.session.add_performance_metric('total_response_time', 300.0)
        self.session.add_performance_metric('total_response_time', 400.0)
        
        avg_time = self.session.get_average_response_time()
        self.assertEqual(avg_time, 300.0)  # (200 + 300 + 400) / 3
    
    def test_record_error(self):
        """Test recording session errors."""
        error_message = "Connection timeout"
        initial_count = self.session.error_count
        
        self.session.record_error(error_message)
        
        self.assertEqual(self.session.error_count, initial_count + 1)
        self.assertEqual(self.session.last_error, error_message)
        self.assertIsNotNone(self.session.last_error_at)
    
    def test_end_session(self):
        """Test ending a streaming session."""
        self.session.end_session(reason='user_disconnect')
        
        self.assertFalse(self.session.is_active)
        self.assertEqual(self.session.status, 'disconnected')
        self.assertIsNotNone(self.session.ended_at)
        self.assertEqual(
            self.session.performance_metrics['end_reason'], 
            'user_disconnect'
        )


class PerformanceMetricsTestCase(TestCase):
    """Test cases for PerformanceMetrics model."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.session = StreamingSession.objects.create(
            user=self.user,
            session_id=str(uuid.uuid4()),
            websocket_id='ws_123'
        )
        self.metrics = PerformanceMetrics.objects.create(
            session=self.session,
            user=self.user,
            request_id=str(uuid.uuid4()),
            audio_processing_time=50.0,
            emotion_detection_time=75.0,
            llm_first_token_time=150.0,
            tts_first_chunk_time=100.0,
            total_response_time=400.0
        )
    
    def test_metrics_creation(self):
        """Test performance metrics creation."""
        self.assertEqual(self.metrics.user, self.user)
        self.assertEqual(self.metrics.session, self.session)
        self.assertEqual(self.metrics.total_response_time, 400.0)
        self.assertFalse(self.metrics.had_errors)
    
    def test_meets_performance_target(self):
        """Test performance target checking."""
        # Should meet 450ms target
        self.assertTrue(self.metrics.meets_performance_target(450))
        
        # Should not meet 300ms target
        self.assertFalse(self.metrics.meets_performance_target(300))
    
    def test_get_performance_breakdown(self):
        """Test getting performance breakdown."""
        breakdown = self.metrics.get_performance_breakdown()
        
        expected_keys = [
            'audio_processing', 'emotion_detection', 
            'llm_first_token', 'tts_first_chunk'
        ]
        
        for key in expected_keys:
            self.assertIn(key, breakdown)
        
        self.assertEqual(breakdown['audio_processing'], 50.0)
        self.assertEqual(breakdown['emotion_detection'], 75.0)
    
    def test_get_average_metrics(self):
        """Test getting average metrics."""
        # Create additional metrics
        PerformanceMetrics.objects.create(
            session=self.session,
            user=self.user,
            request_id=str(uuid.uuid4()),
            total_response_time=300.0,
            llm_first_token_time=100.0
        )
        
        PerformanceMetrics.objects.create(
            session=self.session,
            user=self.user,
            request_id=str(uuid.uuid4()),
            total_response_time=500.0,
            llm_first_token_time=200.0
        )
        
        # Get averages for this user
        averages = PerformanceMetrics.get_average_metrics(user=self.user)
        
        # Should average 400, 300, 500 = 400ms
        self.assertEqual(averages['avg_total_response_time'], 400.0)
        
        # Should average 150, 100, 200 = 150ms
        self.assertEqual(averages['avg_llm_first_token_time'], 150.0)


@pytest.mark.django_db
class TestModelIntegration:
    """Integration tests for real-time models working together."""
    
    def test_complete_interaction_flow(self):
        """Test a complete interaction flow with all models."""
        # Create user and relationship
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        relationship = UserRelationship.objects.create(user=user)
        
        # Create streaming session
        session = StreamingSession.objects.create(
            user=user,
            session_id=str(uuid.uuid4()),
            websocket_id='ws_123',
            session_type='voice',
            audio_enabled=True
        )
        
        # Create emotion context
        emotion_context = EmotionContext.objects.create(
            user=user,
            session_id=session.session_id,
            primary_emotion='joy',
            emotion_intensity=0.8,
            confidence_score=0.9,
            audio_emotions={'joy': 0.8, 'excitement': 0.6}
        )
        
        # Create performance metrics
        metrics = PerformanceMetrics.objects.create(
            session=session,
            user=user,
            request_id=str(uuid.uuid4()),
            audio_processing_time=45.0,
            emotion_detection_time=60.0,
            llm_first_token_time=120.0,
            tts_first_chunk_time=80.0,
            total_response_time=350.0,
            user_satisfaction_score=4.5
        )
        
        # Verify relationships
        assert emotion_context.user == user
        assert metrics.session == session
        assert metrics.user == user
        assert session.user == user
        assert relationship.user == user
        
        # Test performance target
        assert metrics.meets_performance_target(450)
        
        # Update relationship metrics
        emotion_context.update_relationship_metrics()
        relationship.refresh_from_db()
        
        # Should have updated emotional intimacy
        assert relationship.emotional_intimacy_score > 0.0
        
        # Update interaction metrics
        relationship.update_interaction_metrics(
            conversation_duration=timedelta(minutes=3),
            emotion_intensity=emotion_context.emotion_intensity
        )
        
        assert relationship.total_interactions == 1
        assert relationship.average_emotion_intensity == 0.8