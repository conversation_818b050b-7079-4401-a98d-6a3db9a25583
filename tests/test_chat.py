import pytest
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from unittest.mock import patch, MagicMock
from chat.models import Conversation, Message, MessageReaction, ChatSession, VoiceSettings
from chat.services import ChatService

User = get_user_model()


@pytest.mark.django_db
class TestConversation:
    """Test conversation functionality"""
    
    def test_create_conversation(self, user):
        """Test creating a conversation"""
        conversation = Conversation.objects.create(
            user=user,
            title="Test Conversation"
        )
        assert conversation.title == "Test Conversation"
        assert conversation.user == user
        assert conversation.is_active is True
    
    def test_list_conversations_api(self, authenticated_client, user):
        """Test listing conversations via API"""
        # Create test conversations
        Conversation.objects.create(user=user, title="Conv 1")
        Conversation.objects.create(user=user, title="Conv 2")
        
        url = reverse('chat:conversation-list')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2
    
    def test_create_conversation_api(self, authenticated_client):
        """Test creating conversation via API"""
        url = reverse('chat:conversation-list')
        data = {'title': 'New Conversation'}
        
        response = authenticated_client.post(url, data)
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['title'] == 'New Conversation'
    
    def test_update_conversation_api(self, authenticated_client, user):
        """Test updating conversation via API"""
        conversation = Conversation.objects.create(user=user, title="Original")
        
        url = reverse('chat:conversation-detail', kwargs={'pk': conversation.id})
        data = {'title': 'Updated Title'}
        
        response = authenticated_client.patch(url, data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['title'] == 'Updated Title'


@pytest.mark.django_db
class TestMessage:
    """Test message functionality"""
    
    @pytest.fixture
    def conversation(self, user):
        """Create a test conversation"""
        return Conversation.objects.create(user=user, title="Test Conv")
    
    def test_create_message(self, user, conversation):
        """Test creating a message"""
        message = Message.objects.create(
            conversation=conversation,
            content="Hello, AI!",
            sender_type='user'
        )
        assert message.content == "Hello, AI!"
        assert message.sender_type == 'user'
        assert message.conversation == conversation
    
    def test_list_messages_api(self, authenticated_client, user, conversation):
        """Test listing messages via API"""
        # Create test messages
        Message.objects.create(conversation=conversation, content="User msg", sender_type='user')
        Message.objects.create(conversation=conversation, content="AI response", sender_type='ai')
        
        url = reverse('chat:conversation-messages', kwargs={'conversation_id': conversation.id})
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 2
    
    def test_create_message_api(self, authenticated_client, user, conversation):
        """Test creating message via API"""
        url = reverse('chat:message-list')
        data = {
            'conversation': conversation.id,
            'content': 'Test message',
            'sender_type': 'user'
        }
        
        response = authenticated_client.post(url, data)
        assert response.status_code == status.HTTP_201_CREATED
        assert response.data['content'] == 'Test message'


@pytest.mark.django_db
class TestMessageReactions:
    """Test message reaction functionality"""
    
    @pytest.fixture
    def message(self, user):
        """Create a test message"""
        conversation = Conversation.objects.create(user=user, title="Test")
        return Message.objects.create(
            conversation=conversation,
            content="Test message",
            sender_type='ai'
        )
    
    def test_create_reaction(self, user, message):
        """Test creating a message reaction"""
        reaction = MessageReaction.objects.create(
            user=user,
            message=message,
            reaction_type='like'
        )
        assert reaction.reaction_type == 'like'
        assert reaction.user == user
        assert reaction.message == message
    
    def test_toggle_reaction_api(self, authenticated_client, user, message):
        """Test toggling reaction via API"""
        url = reverse('chat:message-reaction', kwargs={'message_id': message.id})
        data = {'reaction_type': 'like'}
        
        # First request should create reaction
        response = authenticated_client.post(url, data)
        assert response.status_code == status.HTTP_200_OK
        assert MessageReaction.objects.filter(user=user, message=message).exists()
        
        # Second request should remove reaction
        response = authenticated_client.post(url, data)
        assert response.status_code == status.HTTP_200_OK
        assert not MessageReaction.objects.filter(user=user, message=message).exists()


@pytest.mark.django_db
class TestVoiceSettings:
    """Test voice settings functionality"""
    
    def test_create_voice_settings(self, user):
        """Test creating voice settings"""
        settings = VoiceSettings.objects.create(
            user=user,
            voice_name='alloy',
            speech_speed=1.0,
            auto_transcribe=True
        )
        assert settings.voice_name == 'alloy'
        assert settings.speech_speed == 1.0
        assert settings.auto_transcribe is True
    
    def test_get_voice_settings_api(self, authenticated_client, user):
        """Test getting voice settings via API"""
        VoiceSettings.objects.create(user=user, voice_name='nova')
        
        url = reverse('chat:voice-settings')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert response.data['voice_name'] == 'nova'
    
    def test_update_voice_settings_api(self, authenticated_client, user):
        """Test updating voice settings via API"""
        url = reverse('chat:voice-settings')
        data = {
            'voice_name': 'shimmer',
            'speech_speed': 1.2,
            'auto_transcribe': False
        }
        
        response = authenticated_client.patch(url, data)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['voice_name'] == 'shimmer'
        assert response.data['speech_speed'] == 1.2


@pytest.mark.django_db
class TestChatService:
    """Test chat service functionality"""
    
    @patch('openai.ChatCompletion.create')
    def test_generate_ai_response(self, mock_openai, user):
        """Test AI response generation"""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock(message=MagicMock(content="AI response"))]
        mock_openai.return_value = mock_response
        
        conversation = Conversation.objects.create(user=user, title="Test")
        chat_service = ChatService(user=user)
        
        # Since generate_response is async, we need to handle it properly
        import asyncio
        response = asyncio.run(chat_service.generate_response(str(conversation.id), "Hello"))
        
        assert response['text'] == "AI response"
        mock_openai.assert_called_once()
    
    @patch('openai.Audio.transcribe')
    def test_transcribe_audio(self, mock_transcribe, user, sample_audio_file):
        """Test audio transcription"""
        # Mock OpenAI transcription
        mock_response = MagicMock()
        mock_response.text = "Transcribed text"
        mock_transcribe.return_value = mock_response
        
        chat_service = ChatService(user=user)
        
        with open(sample_audio_file, 'rb') as audio_file:
            import asyncio
            import base64
            audio_data = base64.b64encode(audio_file.read()).decode()
            result = asyncio.run(chat_service.transcribe_audio(audio_data))
        
        assert result == "Transcribed text"
        mock_transcribe.assert_called_once()
    
    @patch('openai.Audio.speech.create')
    def test_generate_tts_audio(self, mock_tts, user):
        """Test TTS audio generation"""
        # Mock OpenAI TTS response
        mock_response = MagicMock()
        mock_response.content = b"fake audio data"
        mock_tts.return_value = mock_response
        
        chat_service = ChatService(user=user)
        import asyncio
        result = asyncio.run(chat_service._generate_audio("Hello world"))
        
        assert result is not None
        mock_tts.assert_called_once()


@pytest.mark.django_db
class TestChatSessions:
    """Test chat session functionality"""
    
    def test_create_chat_session(self, user):
        """Test creating chat session"""
        session = ChatSession.objects.create(
            user=user,
            channel_name='test-channel-123'
        )
        assert session.user == user
        assert session.channel_name == 'test-channel-123'
        assert session.is_active is True
    
    def test_list_chat_sessions_api(self, authenticated_client, user):
        """Test listing chat sessions via API"""
        ChatSession.objects.create(user=user, channel_name='test-channel-1')
        ChatSession.objects.create(user=user, channel_name='test-channel-2')
        
        url = reverse('chat:chat-sessions')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['results']) == 2


@pytest.mark.django_db
class TestAudioUpload:
    """Test audio upload functionality"""
    
    @patch('chat.services.ChatService.transcribe_audio')
    def test_audio_upload_api(self, mock_transcribe, authenticated_client, user, sample_audio_file):
        """Test audio upload and transcription via API"""
        mock_transcribe.return_value = "Transcribed text"
        
        url = reverse('chat:audio-upload')
        
        with open(sample_audio_file, 'rb') as audio_file:
            data = {'audio': audio_file}
            response = authenticated_client.post(url, data, format='multipart')
        
        assert response.status_code == status.HTTP_200_OK
        assert 'transcription' in response.data
        assert response.data['transcription'] == "Transcribed text"


@pytest.mark.django_db
class TestChatStats:
    """Test chat statistics functionality"""
    
    def test_chat_stats_api(self, authenticated_client, user):
        """Test getting chat statistics via API"""
        # Create test data
        conversation = Conversation.objects.create(user=user, title="Test")
        Message.objects.create(conversation=conversation, content="Test", sender_type='user')
        Message.objects.create(conversation=conversation, content="Response", sender_type='ai')
        
        url = reverse('chat:chat-stats')
        response = authenticated_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'total_conversations' in response.data
        assert 'total_messages' in response.data
        assert response.data['total_conversations'] >= 1
        assert response.data['total_messages'] >= 2


@pytest.mark.django_db
class TestMessageSearch:
    """Test message search functionality"""
    
    def test_search_messages_api(self, authenticated_client, user):
        """Test searching messages via API"""
        conversation = Conversation.objects.create(user=user, title="Test")
        Message.objects.create(
            conversation=conversation,
            content="This is a searchable message",
            sender_type='user'
        )
        Message.objects.create(
            conversation=conversation,
            content="Another message",
            sender_type='ai'
        )
        
        url = reverse('chat:message-search')
        response = authenticated_client.get(url, {'q': 'searchable'})
        
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['messages']) >= 1
        assert 'searchable' in response.data['messages'][0]['content'].lower()
