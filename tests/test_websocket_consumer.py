"""
Tests for enhanced WebSocket consumer with real-time streaming support.
"""
import pytest
import json
import base64
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.test import TransactionTestCase

from chat.consumers import ChatConsumer
from chat.models import Conversation, Message, StreamingSession, EmotionContext

User = get_user_model()


class TestEnhancedChatConsumer(TransactionTestCase):
    """Test enhanced chat consumer functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    async def test_connection_establishment(self):
        """Test WebSocket connection with streaming capabilities."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, subprotocol = await communicator.connect()
        assert connected
        
        # Should receive connection established message
        response = await communicator.receive_json_from()
        
        assert response['type'] == 'connection_established'
        assert response['streaming_enabled'] is True
        assert 'session_id' in response
        assert 'supported_message_types' in response
        
        await communicator.disconnect()
    
    async def test_text_message_handling(self):
        """Test enhanced text message handling."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        await communicator.receive_json_from()
        
        # Send text message
        await communicator.send_json_to({
            'type': 'text_message',
            'content': 'Hello AI companion!',
            'conversation_id': None,
            'timestamp': 1234567890000
        })
        
        # Should receive message received acknowledgment
        response = await communicator.receive_json_from()
        assert response['type'] == 'message_received'
        assert 'message_id' in response
        assert response['processing_started'] is True
        
        await communicator.disconnect()
    
    async def test_audio_chunk_handling(self):
        """Test audio chunk processing."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        await communicator.receive_json_from()
        
        # Create sample audio data
        sample_audio = b'\x00\x01' * 1000  # Simple audio data
        audio_b64 = base64.b64encode(sample_audio).decode('utf-8')
        
        # Send audio chunk
        await communicator.send_json_to({
            'type': 'audio_chunk',
            'data': audio_b64,
            'chunk_id': 'test_chunk_1',
            'is_final': False,
            'timestamp': 1234567890000
        })
        
        # Should receive transcription and/or emotion results
        # (Results depend on audio service implementation)
        
        await communicator.disconnect()
    
    async def test_emotion_feedback_handling(self):
        """Test emotion feedback processing."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        await communicator.receive_json_from()
        
        # Send emotion feedback
        await communicator.send_json_to({
            'type': 'emotion_feedback',
            'emotion_scores': {
                'joy': 0.8,
                'excitement': 0.6
            },
            'user_validation': True,
            'chunk_id': 'test_chunk_1'
        })
        
        # Should receive feedback acknowledgment
        response = await communicator.receive_json_from()
        assert response['type'] == 'emotion_feedback_received'
        assert response['chunk_id'] == 'test_chunk_1'
        
        await communicator.disconnect()
    
    async def test_message_validation(self):
        """Test message validation functionality."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        await communicator.receive_json_from()
        
        # Test empty text message validation
        await communicator.send_json_to({
            'type': 'text_message',
            'content': '',  # Empty content should fail
            'conversation_id': None
        })
        
        # Should receive error
        response = await communicator.receive_json_from()
        assert response['type'] == 'error'
        assert 'empty' in response['message'].lower()
        
        await communicator.disconnect()
    
    async def test_invalid_audio_data_validation(self):
        """Test validation of invalid audio data."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        await communicator.receive_json_from()
        
        # Send invalid audio chunk
        await communicator.send_json_to({
            'type': 'audio_chunk',
            'data': 'invalid_base64_data!@#',  # Invalid base64
            'chunk_id': 'test_chunk_1',
            'is_final': False
        })
        
        # Should receive error
        response = await communicator.receive_json_from()
        assert response['type'] == 'error'
        assert 'invalid' in response['message'].lower()
        
        await communicator.disconnect()
    
    async def test_heartbeat_handling(self):
        """Test connection heartbeat functionality."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        await communicator.receive_json_from()
        
        # Send heartbeat
        await communicator.send_json_to({
            'type': 'connection_heartbeat',
            'timestamp': 1234567890000
        })
        
        # Should receive heartbeat response
        response = await communicator.receive_json_from()
        assert response['type'] == 'heartbeat_response'
        assert 'timestamp' in response
        assert 'session_id' in response
        
        await communicator.disconnect()
    
    async def test_reconnection_handling(self):
        """Test reconnection request handling."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        await communicator.receive_json_from()
        
        # Send reconnection request
        await communicator.send_json_to({
            'type': 'reconnection_request',
            'reason': 'connection_lost'
        })
        
        # Should receive reconnection accepted
        response = await communicator.receive_json_from()
        assert response['type'] == 'reconnection_accepted'
        assert 'backoff_delay_seconds' in response
        assert 'attempt' in response
        
        await communicator.disconnect()
    
    async def test_unknown_message_type(self):
        """Test handling of unknown message types."""
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = self.user
        
        connected, _ = await communicator.connect()
        assert connected
        
        # Skip connection message
        await communicator.receive_json_from()
        
        # Send unknown message type
        await communicator.send_json_to({
            'type': 'unknown_message_type',
            'data': 'some data'
        })
        
        # Should receive error
        response = await communicator.receive_json_from()
        assert response['type'] == 'error'
        assert 'unknown' in response['message'].lower()
        
        await communicator.disconnect()
    
    async def test_anonymous_user_rejection(self):
        """Test that anonymous users are rejected."""
        from django.contrib.auth.models import AnonymousUser
        
        communicator = WebsocketCommunicator(ChatConsumer.as_asgi(), "/ws/chat/")
        communicator.scope["user"] = AnonymousUser()
        
        connected, _ = await communicator.connect()
        assert not connected  # Should be rejected
    
    def test_message_validation_methods(self):
        """Test message validation methods directly."""
        consumer = ChatConsumer()
        
        # Test valid text message
        valid, error = consumer.validate_message_data({
            'content': 'Hello world',
            'conversation_id': 'test-id'
        }, 'text_message')
        assert valid
        assert error == ""
        
        # Test invalid text message (empty)
        valid, error = consumer.validate_message_data({
            'content': '',
            'conversation_id': 'test-id'
        }, 'text_message')
        assert not valid
        assert 'empty' in error.lower()
        
        # Test valid audio chunk
        sample_audio = base64.b64encode(b'\x00\x01' * 100).decode('utf-8')
        valid, error = consumer.validate_message_data({
            'data': sample_audio,
            'chunk_id': 'test-chunk',
            'is_final': False
        }, 'audio_chunk')
        assert valid
        assert error == ""
        
        # Test invalid audio chunk (no data)
        valid, error = consumer.validate_message_data({
            'chunk_id': 'test-chunk',
            'is_final': False
        }, 'audio_chunk')
        assert not valid
        assert 'required' in error.lower()
    
    async def test_rate_limiting(self):
        """Test rate limiting functionality."""
        consumer = ChatConsumer()
        consumer.user = self.user
        
        # Test that initial requests are allowed
        for i in range(5):
            allowed = await consumer.check_rate_limit('text_message')
            assert allowed
        
        # Test that excessive requests are rate limited
        # (This test depends on the rate limit implementation)
        # For a more thorough test, we'd need to mock time or use a test-specific rate limiter


class TestMessageValidation:
    """Test message validation functionality."""
    
    def test_text_message_validation(self):
        """Test text message validation."""
        consumer = ChatConsumer()
        
        # Valid message
        valid, error = consumer.validate_message_data({
            'content': 'Hello AI!',
            'conversation_id': 'test-123'
        }, 'text_message')
        assert valid
        
        # Empty content
        valid, error = consumer.validate_message_data({
            'content': '',
            'conversation_id': 'test-123'
        }, 'text_message')
        assert not valid
        assert 'empty' in error.lower()
        
        # Too long content
        long_content = 'x' * 10001  # Over 10KB limit
        valid, error = consumer.validate_message_data({
            'content': long_content,
            'conversation_id': 'test-123'
        }, 'text_message')
        assert not valid
        assert 'too long' in error.lower()
    
    def test_audio_chunk_validation(self):
        """Test audio chunk validation."""
        consumer = ChatConsumer()
        
        # Valid audio chunk
        sample_audio = base64.b64encode(b'\x00\x01' * 1000).decode('utf-8')
        valid, error = consumer.validate_message_data({
            'data': sample_audio,
            'chunk_id': 'chunk-123',
            'is_final': False
        }, 'audio_chunk')
        assert valid
        
        # Missing audio data
        valid, error = consumer.validate_message_data({
            'chunk_id': 'chunk-123',
            'is_final': False
        }, 'audio_chunk')
        assert not valid
        assert 'required' in error.lower()
        
        # Invalid base64
        valid, error = consumer.validate_message_data({
            'data': 'invalid-base64!@#',
            'chunk_id': 'chunk-123',
            'is_final': False
        }, 'audio_chunk')
        assert not valid
        assert 'invalid' in error.lower()
        
        # Missing chunk ID
        valid, error = consumer.validate_message_data({
            'data': sample_audio,
            'is_final': False
        }, 'audio_chunk')
        assert not valid
        assert 'chunk id' in error.lower()
    
    def test_emotion_feedback_validation(self):
        """Test emotion feedback validation."""
        consumer = ChatConsumer()
        
        # Valid emotion feedback
        valid, error = consumer.validate_message_data({
            'emotion_scores': {'joy': 0.8, 'sadness': 0.2},
            'user_validation': True,
            'chunk_id': 'chunk-123'
        }, 'emotion_feedback')
        assert valid
        
        # Invalid emotion scores (not dict)
        valid, error = consumer.validate_message_data({
            'emotion_scores': 'not-a-dict',
            'user_validation': True,
            'chunk_id': 'chunk-123'
        }, 'emotion_feedback')
        assert not valid
        assert 'dictionary' in error.lower()
        
        # Invalid user validation (not bool)
        valid, error = consumer.validate_message_data({
            'emotion_scores': {'joy': 0.8},
            'user_validation': 'not-a-bool',
            'chunk_id': 'chunk-123'
        }, 'emotion_feedback')
        assert not valid
        assert 'boolean' in error.lower()


if __name__ == '__main__':
    pytest.main([__file__])