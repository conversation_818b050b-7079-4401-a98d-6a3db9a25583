"""
Tests for the adaptive content filtering system.
"""
import pytest
from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock

from chat.services.content_filter import ContentFilter, RelationshipProgressionTracker
from chat.models_realtime import UserRelationship
from authentication.models import User


class ContentFilterTestCase(TestCase):
    """Test cases for ContentFilter class."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.content_filter = ContentFilter()
    
    def test_content_filter_initialization(self):
        """Test ContentFilter initialization."""
        self.assertIsInstance(self.content_filter, ContentFilter)
        self.assertEqual(len(self.content_filter.CONTENT_LEVELS), 4)
        self.assertIn('explicit_sexual', self.content_filter.NSFW_PATTERNS)
    
    def test_get_user_relationship_creates_new(self):
        """Test that _get_user_relationship creates new relationship if none exists."""
        # Ensure no relationship exists
        self.assertFalse(UserRelationship.objects.filter(user=self.user).exists())
        
        relationship = self.content_filter._get_user_relationship(self.user)
        
        self.assertIsInstance(relationship, UserRelationship)
        self.assertEqual(relationship.user, self.user)
        self.assertEqual(relationship.relationship_level, 1)
        self.assertTrue(UserRelationship.objects.filter(user=self.user).exists())
    
    def test_get_user_relationship_returns_existing(self):
        """Test that _get_user_relationship returns existing relationship."""
        # Create existing relationship
        existing_relationship = UserRelationship.objects.create(
            user=self.user,
            relationship_level=2,
            total_interactions=10
        )
        
        relationship = self.content_filter._get_user_relationship(self.user)
        
        self.assertEqual(relationship.id, existing_relationship.id)
        self.assertEqual(relationship.relationship_level, 2)
        self.assertEqual(relationship.total_interactions, 10)
    
    def test_analyze_content_general(self):
        """Test content analysis for general content."""
        content = "Hello, how are you today? The weather is nice."
        analysis = self.content_filter._analyze_content(content)
        
        self.assertEqual(analysis['required_level'], 1)
        self.assertEqual(analysis['nsfw_score'], 0.0)
        self.assertEqual(len(analysis['categories']), 0)
    
    def test_analyze_content_romantic(self):
        """Test content analysis for romantic content."""
        content = "I really love talking to you and feel a deep connection."
        analysis = self.content_filter._analyze_content(content)
        
        self.assertGreater(analysis['required_level'], 1)
        self.assertGreater(analysis['nsfw_score'], 0.0)
        self.assertIn('emotional_intimate', analysis['categories'])
    
    def test_analyze_content_explicit(self):
        """Test content analysis for explicit content."""
        content = "I want to have sex with you tonight."
        analysis = self.content_filter._analyze_content(content)
        
        self.assertEqual(analysis['required_level'], 4)
        self.assertGreater(analysis['nsfw_score'], 0.0)
        self.assertIn('explicit_sexual', analysis['categories'])
    
    def test_filter_content_level_1_general(self):
        """Test filtering general content for level 1 user."""
        # Create level 1 relationship
        UserRelationship.objects.create(user=self.user, relationship_level=1)
        
        content = "Hello, how are you today?"
        is_allowed, filtered_content, filter_info = self.content_filter.filter_content(
            content, self.user
        )
        
        self.assertTrue(is_allowed)
        self.assertEqual(filtered_content, content)
        self.assertEqual(filter_info['relationship_level'], 1)
        self.assertEqual(filter_info['content_level_required'], 1)
    
    def test_filter_content_level_1_blocks_explicit(self):
        """Test that level 1 user is blocked from explicit content."""
        # Create level 1 relationship
        UserRelationship.objects.create(user=self.user, relationship_level=1)
        
        content = "I want to kiss you passionately."
        is_allowed, filtered_content, filter_info = self.content_filter.filter_content(
            content, self.user
        )
        
        self.assertFalse(is_allowed)
        self.assertNotEqual(filtered_content, content)
        self.assertEqual(filter_info['relationship_level'], 1)
        self.assertGreater(filter_info['content_level_required'], 1)
        # Check that it's one of the expected level 1 responses
        level_1_phrases = ["get to know each other better", "become closer friends", "build our friendship"]
        self.assertTrue(any(phrase in filtered_content for phrase in level_1_phrases))
    
    def test_filter_content_level_3_allows_intimate(self):
        """Test that level 3 user can access intimate content."""
        # Create level 3 relationship
        UserRelationship.objects.create(user=self.user, relationship_level=3)
        
        content = "I love you and want to share my deepest feelings."
        is_allowed, filtered_content, filter_info = self.content_filter.filter_content(
            content, self.user
        )
        
        self.assertTrue(is_allowed)
        self.assertEqual(filtered_content, content)
        self.assertEqual(filter_info['relationship_level'], 3)
    
    def test_filter_content_level_4_requires_consent(self):
        """Test that level 4 content requires explicit consent."""
        # Create level 4 relationship without consent
        UserRelationship.objects.create(
            user=self.user, 
            relationship_level=4,
            user_consent_flags={}
        )
        
        content = "Let's have sex tonight."
        is_allowed, filtered_content, filter_info = self.content_filter.filter_content(
            content, self.user
        )
        
        self.assertFalse(is_allowed)
        self.assertIn("explicit user consent", filter_info['filter_reason'])
    
    def test_filter_content_level_4_with_consent(self):
        """Test that level 4 content is allowed with consent."""
        # Create level 4 relationship with consent
        UserRelationship.objects.create(
            user=self.user, 
            relationship_level=4,
            user_consent_flags={'adult_content_consent': True}
        )
        
        content = "I want to explore our physical relationship."
        is_allowed, filtered_content, filter_info = self.content_filter.filter_content(
            content, self.user
        )
        
        self.assertTrue(is_allowed)
        self.assertEqual(filtered_content, content)
    
    def test_filter_content_emotional_context_blocking(self):
        """Test that inappropriate content is blocked during vulnerable emotional states."""
        # Create level 3 relationship
        UserRelationship.objects.create(user=self.user, relationship_level=3)
        
        content = "Let's talk about something sexual."
        context = {
            'emotion_context': {
                'primary_emotion': 'sadness',
                'emotion_intensity': 0.8
            }
        }
        
        is_allowed, filtered_content, filter_info = self.content_filter.filter_content(
            content, self.user, context
        )
        
        self.assertFalse(is_allowed)
        self.assertIn("vulnerable emotional state", filter_info['filter_reason'])
    
    def test_check_progression_eligibility_not_ready(self):
        """Test progression eligibility check when user is not ready."""
        # Create new level 1 relationship
        UserRelationship.objects.create(
            user=self.user,
            relationship_level=1,
            total_interactions=5,  # Less than required 10
            emotional_intimacy_score=0.1
        )
        
        eligible, reason, info = self.content_filter.check_progression_eligibility(self.user)
        
        self.assertFalse(eligible)
        self.assertIn("interactions", reason)
        self.assertEqual(info['current_level'], 1)
        self.assertEqual(info['next_level'], 2)
    
    def test_check_progression_eligibility_ready(self):
        """Test progression eligibility check when user is ready."""
        # Create relationship ready for progression
        UserRelationship.objects.create(
            user=self.user,
            relationship_level=1,
            total_interactions=15,  # More than required 10
            total_conversation_time=timezone.timedelta(hours=3),  # More than required 2
            emotional_intimacy_score=0.4  # More than required 0.3
        )
        
        eligible, reason, info = self.content_filter.check_progression_eligibility(self.user)
        
        self.assertTrue(eligible)
        self.assertEqual(reason, "Eligible for progression")
        self.assertEqual(info['current_level'], 1)
        self.assertEqual(info['next_level'], 2)
    
    def test_request_relationship_progression_success(self):
        """Test successful relationship progression."""
        # Create relationship ready for progression
        UserRelationship.objects.create(
            user=self.user,
            relationship_level=1,
            total_interactions=15,
            total_conversation_time=timezone.timedelta(hours=3),
            emotional_intimacy_score=0.4
        )
        
        success, message, info = self.content_filter.request_relationship_progression(self.user)
        
        self.assertTrue(success)
        self.assertIn("Progressed from level 1 to 2", message)
        self.assertEqual(info['old_level'], 1)
        self.assertEqual(info['new_level'], 2)
        
        # Verify relationship was updated
        relationship = UserRelationship.objects.get(user=self.user)
        self.assertEqual(relationship.relationship_level, 2)
    
    def test_request_relationship_progression_with_consent(self):
        """Test relationship progression to level 4 with explicit consent."""
        # Create level 3 relationship ready for progression
        UserRelationship.objects.create(
            user=self.user,
            relationship_level=3,
            total_interactions=150,
            total_conversation_time=timezone.timedelta(hours=30),
            emotional_intimacy_score=0.9
        )
        
        success, message, info = self.content_filter.request_relationship_progression(
            self.user, explicit_consent=True
        )
        
        # Debug output
        if not success:
            print(f"Progression failed: {message}")
            print(f"Info: {info}")
        
        self.assertTrue(success, f"Progression failed: {message}")
        self.assertEqual(info['new_level'], 4)
        self.assertTrue(info['explicit_consent'])
        
        # Verify consent was recorded
        relationship = UserRelationship.objects.get(user=self.user)
        self.assertTrue(relationship.user_consent_flags.get('adult_content_consent', False))
        self.assertTrue(relationship.explicit_progression_request)
    
    def test_get_content_guidelines(self):
        """Test getting content guidelines for user."""
        # Create level 2 relationship
        UserRelationship.objects.create(
            user=self.user,
            relationship_level=2,
            total_interactions=20,
            emotional_intimacy_score=0.5
        )
        
        guidelines = self.content_filter.get_content_guidelines(self.user)
        
        self.assertEqual(guidelines['current_level'], 2)
        self.assertEqual(guidelines['level_name'], 'Personal')
        self.assertIn('personal_life', guidelines['allowed_topics'])
        self.assertIn('sexual_content', guidelines['blocked_topics'])
        self.assertIn('total_interactions', guidelines['relationship_stats'])


class RelationshipProgressionTrackerTestCase(TestCase):
    """Test cases for RelationshipProgressionTracker class."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.tracker = RelationshipProgressionTracker()
    
    def test_update_interaction_metrics(self):
        """Test updating interaction metrics."""
        # Create initial relationship
        relationship = UserRelationship.objects.create(
            user=self.user,
            relationship_level=1,
            total_interactions=5,
            emotional_intimacy_score=0.2
        )
        
        interaction_data = {
            'duration': 300,  # 5 minutes in seconds
            'emotion_intensity': 0.6,
            'message_count': 10
        }
        
        metrics_info = self.tracker.update_interaction_metrics(self.user, interaction_data)
        
        # Verify metrics were updated
        relationship.refresh_from_db()
        self.assertEqual(relationship.total_interactions, 6)
        self.assertGreater(relationship.emotional_intimacy_score, 0.2)
        
        # Verify return info
        self.assertEqual(metrics_info['total_interactions'], 6)
        self.assertIn('conversation_time', metrics_info)
        self.assertIn('progression_eligible', metrics_info)
    
    def test_suggest_progression_eligible(self):
        """Test progression suggestion when user is eligible."""
        # Create relationship ready for progression
        UserRelationship.objects.create(
            user=self.user,
            relationship_level=1,
            total_interactions=15,
            total_conversation_time=timezone.timedelta(hours=3),
            emotional_intimacy_score=0.4
        )
        
        suggestion = self.tracker.suggest_progression(self.user)
        
        self.assertTrue(suggestion['should_suggest'])
        self.assertEqual(suggestion['current_level'], 1)
        self.assertEqual(suggestion['next_level'], 2)
        self.assertEqual(suggestion['next_level_name'], 'Personal')
        self.assertIn("becoming good friends", suggestion['message'])
        self.assertFalse(suggestion['requires_explicit_consent'])
    
    def test_suggest_progression_not_eligible(self):
        """Test progression suggestion when user is not eligible."""
        # Create relationship not ready for progression
        UserRelationship.objects.create(
            user=self.user,
            relationship_level=1,
            total_interactions=5,  # Not enough
            emotional_intimacy_score=0.1
        )
        
        suggestion = self.tracker.suggest_progression(self.user)
        
        self.assertFalse(suggestion['should_suggest'])
        self.assertIn('reason', suggestion)
        self.assertIn('progression_info', suggestion)
    
    def test_suggest_progression_level_4_requires_consent(self):
        """Test that progression to level 4 requires explicit consent."""
        # Create level 3 relationship ready for progression (but without consent)
        UserRelationship.objects.create(
            user=self.user,
            relationship_level=3,
            total_interactions=150,
            total_conversation_time=timezone.timedelta(hours=30),
            emotional_intimacy_score=0.9
        )
        
        suggestion = self.tracker.suggest_progression(self.user)
        
        # Should not suggest progression without consent, but should indicate it's available
        if suggestion['should_suggest']:
            self.assertEqual(suggestion['next_level'], 4)
            self.assertTrue(suggestion['requires_explicit_consent'])
            self.assertIn("explicit consent", suggestion['message'])
        else:
            # If not suggesting, it should be because consent is required
            self.assertIn("consent", suggestion['reason'])
    
    @patch('chat.services.content_filter.logger')
    def test_error_handling(self, mock_logger):
        """Test error handling in content filtering."""
        # Test with invalid user
        content_filter = ContentFilter()
        
        # This should handle the error gracefully
        is_allowed, filtered_content, filter_info = content_filter.filter_content(
            "test content", None  # Invalid user
        )
        
        self.assertFalse(is_allowed)
        self.assertIn("error", filter_info)
        mock_logger.error.assert_called()


class ContentFilterIntegrationTestCase(TestCase):
    """Integration tests for content filtering system."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.content_filter = ContentFilter()
        self.tracker = RelationshipProgressionTracker()
    
    def test_full_relationship_progression_flow(self):
        """Test complete relationship progression flow."""
        # Start with level 1 relationship
        relationship = UserRelationship.objects.create(user=self.user, relationship_level=1)
        
        # Simulate multiple interactions to build up metrics
        for i in range(15):
            interaction_data = {
                'duration': 600,  # 10 minutes
                'emotion_intensity': 0.6 + (i * 0.02),  # Start higher and gradually increase
                'message_count': 5
            }
            self.tracker.update_interaction_metrics(self.user, interaction_data)
        
        # Check if eligible for progression
        eligible, reason, info = self.content_filter.check_progression_eligibility(self.user)
        if not eligible:
            print(f"Not eligible: {reason}")
            print(f"Info: {info}")
        self.assertTrue(eligible)
        
        # Request progression
        success, message, prog_info = self.content_filter.request_relationship_progression(self.user)
        self.assertTrue(success)
        
        # Verify new level allows more content
        relationship.refresh_from_db()
        self.assertEqual(relationship.relationship_level, 2)
        
        # Test that previously blocked content is now allowed
        content = "I really care about you and want to share personal things."
        is_allowed, filtered_content, filter_info = self.content_filter.filter_content(
            content, self.user
        )
        self.assertTrue(is_allowed)
    
    def test_content_filtering_with_emotion_context(self):
        """Test content filtering with emotional context."""
        # Create level 2 relationship
        UserRelationship.objects.create(user=self.user, relationship_level=2)
        
        # Test normal emotional state - should allow personal content
        content = "I want to talk about my feelings for you."
        context = {
            'emotion_context': {
                'primary_emotion': 'joy',
                'emotion_intensity': 0.6
            }
        }
        
        is_allowed, filtered_content, filter_info = self.content_filter.filter_content(
            content, self.user, context
        )
        self.assertTrue(is_allowed)
        
        # Test vulnerable emotional state - should block inappropriate content
        content = "Let's talk about something intimate and sexual."
        context = {
            'emotion_context': {
                'primary_emotion': 'sadness',
                'emotion_intensity': 0.8
            }
        }
        
        is_allowed, filtered_content, filter_info = self.content_filter.filter_content(
            content, self.user, context
        )
        self.assertFalse(is_allowed)
        self.assertIn("vulnerable emotional state", filter_info['filter_reason'])