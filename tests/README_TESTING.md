# Testing Guide for Ellahai Backend

This document provides an overview of the testing structure and how to run tests for the <PERSON><PERSON> backend project.

## Test Structure

The test suite is organized as follows:

- `tests/`: Root directory for project-wide tests
  - `api/`: Tests for external API integrations (G<PERSON>q, Hume, etc.)
  - `services/`: Tests for service components
  - `utils/`: Tests for utility functions
- `chat/tests/`: Tests for the chat application
- `agents/tests/`: Tests for the agents application
- `authentication/tests/`: Tests for the authentication application
- `gamification/tests/`: Tests for the gamification application

## Test Categories

Tests are categorized using pytest markers:

- `unit`: Unit tests that don't require external dependencies
- `integration`: Integration tests that test multiple components together
- `slow`: Tests that take a long time to run
- `api`: Tests that require API access to external services
- `service`: Tests for service components

## Running Tests

### Using the Test Runner Script

The `run_tests.py` script provides a convenient way to run tests with various options:

```bash
# Run all tests
python run_tests.py

# Run tests in a specific directory
python run_tests.py tests/api

# Run tests with specific markers
python run_tests.py -m unit
python run_tests.py -m "unit and not slow"

# Skip certain test categories
python run_tests.py --skip-slow --skip-api

# Run with coverage
python run_tests.py --coverage --html
```

### Using pytest Directly

You can also use pytest directly:

```bash
# Run all tests
pytest

# Run tests in a specific directory
pytest tests/api

# Run tests with specific markers
pytest -m unit
pytest -m "unit and not slow"

# Run with coverage
coverage run -m pytest
coverage report
coverage html
```

## Writing Tests

When writing new tests:

1. Place them in the appropriate directory based on what they're testing
2. Use appropriate markers to categorize them
3. Follow the naming convention: `test_*.py` for test files
4. Use descriptive test function names that explain what's being tested

## Test Dependencies

Test dependencies are listed in `requirements-test.txt`. Make sure to install them:

```bash
pip install -r requirements-test.txt
```