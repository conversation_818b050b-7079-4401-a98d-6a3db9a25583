"""
Simplified functional tests for real-time AI companion system.
Tests core functionality without complex WebSocket integration.
"""

import asyncio
import json
import base64
import pytest
import os
import time
from unittest.mock import AsyncMock, patch, MagicMock
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model

from chat.services.audio_service import AudioProcessingService, AudioChunk
from chat.services.groq_service import GroqStreamingClient
from chat.services.hume_service import HumeEmotionClient, HumeTTSClient
from chat.services.content_filter import ContentFilter
from chat.models_realtime import UserRelationship, EmotionContext
from authentication.models import User

User = get_user_model()


class SimpleFunctionalTestSuite(TransactionTestCase):
    """Simplified functional test suite for core functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create relationship
        self.relationship = UserRelationship.objects.create(
            user=self.user,
            relationship_level=1,
            emotional_intimacy_score=0.2,
            total_interactions=5,
            user_consent_flags={'nsfw_content': False}
        )
    
    def test_content_filter_basic_functionality(self):
        """Test basic content filtering functionality."""
        content_filter = ContentFilter()
        
        # Test allowed content
        is_allowed, filtered_content, filter_info = content_filter.filter_content(
            content="Hello, how are you today?",
            user=self.user
        )
        self.assertTrue(is_allowed)
        
        # Test potentially filtered content
        is_allowed, filtered_content, filter_info = content_filter.filter_content(
            content="Let's discuss adult topics",
            user=self.user
        )
        # At level 1, this should be filtered
        self.assertFalse(is_allowed)
    
    def test_relationship_progression(self):
        """Test relationship level progression."""
        # Start at level 1
        self.assertEqual(self.relationship.relationship_level, 1)
        
        # Simulate interactions
        for i in range(10):
            self.relationship.total_interactions += 1
            self.relationship.emotional_intimacy_score += 0.05
            self.relationship.save()
        
        # Check if progression logic would trigger
        # (This would normally be handled by the system)
        if (self.relationship.total_interactions >= 10 and 
            self.relationship.emotional_intimacy_score >= 0.5):
            self.relationship.relationship_level = 2
            self.relationship.save()
        
        self.assertEqual(self.relationship.relationship_level, 2)
    
    def test_audio_processing_basic(self):
        """Test basic audio processing functionality."""
        # Test that AudioProcessingService can be instantiated
        audio_service = AudioProcessingService()
        self.assertIsNotNone(audio_service)
        
        # Test audio chunk creation
        chunk = AudioChunk(
            data=b'mock_audio_data',
            chunk_id='test-chunk',
            timestamp_ms=time.time() * 1000,
            user_id=str(self.user.id),
            is_final=True
        )
        
        self.assertEqual(chunk.chunk_id, 'test-chunk')
        self.assertEqual(chunk.user_id, str(self.user.id))
        self.assertTrue(chunk.is_final)
    
    def test_emotion_context_storage(self):
        """Test emotion context storage and retrieval."""
        # Create emotion context
        emotion_context = EmotionContext.objects.create(
            user=self.user,
            session_id='test-session',
            audio_emotions={'joy': 0.8, 'confidence': 0.7},
            text_emotions={'positive': 0.9},
            context_emotions={'primary': 'joy', 'intensity': 0.8},
            confidence_score=0.85
        )
        
        # Verify storage
        self.assertEqual(emotion_context.user, self.user)
        self.assertEqual(emotion_context.audio_emotions['joy'], 0.8)
        self.assertEqual(emotion_context.context_emotions['primary'], 'joy')
        
        # Test retrieval
        retrieved = EmotionContext.objects.filter(
            user=self.user,
            session_id='test-session'
        ).first()
        
        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.confidence_score, 0.85)
    
    def test_user_relationship_model(self):
        """Test user relationship model functionality."""
        # Test relationship creation
        self.assertIsNotNone(self.relationship)
        self.assertEqual(self.relationship.user, self.user)
        self.assertEqual(self.relationship.relationship_level, 1)
        
        # Test relationship updates
        self.relationship.total_interactions = 20
        self.relationship.emotional_intimacy_score = 0.6
        self.relationship.relationship_level = 2
        self.relationship.save()
        
        # Verify updates
        updated = UserRelationship.objects.get(user=self.user)
        self.assertEqual(updated.total_interactions, 20)
        self.assertEqual(updated.relationship_level, 2)
        self.assertAlmostEqual(updated.emotional_intimacy_score, 0.6, places=1)
    
    def test_content_filter_relationship_levels(self):
        """Test content filtering at different relationship levels."""
        content_filter = ContentFilter()
        
        test_content = "I find you very attractive"
        
        # Level 1 - should be filtered
        self.relationship.relationship_level = 1
        self.relationship.save()
        
        is_allowed, _, _ = content_filter.filter_content(content=test_content, user=self.user)
        self.assertFalse(is_allowed)
        
        # Level 2 - should be allowed
        self.relationship.relationship_level = 2
        self.relationship.save()
        
        # Clear cache to ensure fresh data
        from django.core.cache import cache
        cache.clear()
        
        is_allowed, _, _ = content_filter.filter_content(content=test_content, user=self.user)
        self.assertTrue(is_allowed)
    
    @pytest.mark.asyncio
    async def test_hume_tts_basic_functionality(self):
        """Test basic Hume TTS functionality."""
        mock_tts = AsyncMock(spec=HumeTTSClient)
        
        # Mock TTS streaming response
        async def mock_tts_stream():
            for i in range(3):
                yield base64.b64encode(f'audio_chunk_{i}'.encode()).decode()
                await asyncio.sleep(0.01)
        
        mock_tts.synthesize_streaming.return_value = mock_tts_stream()
        
        # Test TTS synthesis
        audio_chunks = []
        async for chunk in mock_tts.synthesize_streaming(
            text="Hello, this is a test",
            voice_settings={'voice_id': 'default'},
            emotion_context={'joy': 0.7}
        ):
            audio_chunks.append(chunk)
        
        # Verify we got audio chunks
        self.assertEqual(len(audio_chunks), 3)
        
        # Verify chunks are base64 encoded
        for chunk in audio_chunks:
            try:
                decoded = base64.b64decode(chunk)
                self.assertIsInstance(decoded, bytes)
            except Exception:
                self.fail(f"Audio chunk is not valid base64: {chunk}")
    
    def test_performance_metrics_model(self):
        """Test performance metrics model functionality."""
        from chat.models_realtime import PerformanceMetrics, StreamingSession
        
        import uuid
        
        # Create streaming session
        session_id = str(uuid.uuid4())
        session = StreamingSession.objects.create(
            user=self.user,
            session_id=session_id,
            websocket_id='ws-123'
        )
        
        # Create performance metrics
        metrics = PerformanceMetrics.objects.create(
            session=session,
            user=self.user,
            request_id='test-request-123',
            audio_processing_time=45.5,
            emotion_detection_time=30.2,
            llm_first_token_time=180.7,
            tts_first_chunk_time=95.3,
            total_response_time=420.8
        )
        
        # Verify metrics storage
        self.assertEqual(metrics.session.session_id, session_id)
        self.assertLess(metrics.total_response_time, 450)  # Under target
        self.assertLess(metrics.audio_processing_time, 50)  # Under target
        self.assertLess(metrics.llm_first_token_time, 200)  # Under target
        self.assertLess(metrics.tts_first_chunk_time, 100)  # Under target
    
    def test_audio_chunk_creation(self):
        """Test audio chunk data structure."""
        chunk = AudioChunk(
            data=b'test_audio_data',
            chunk_id='test-chunk-123',
            timestamp_ms=time.time() * 1000,
            user_id=str(self.user.id),
            is_final=True
        )
        
        self.assertEqual(chunk.chunk_id, 'test-chunk-123')
        self.assertEqual(chunk.user_id, str(self.user.id))
        self.assertTrue(chunk.is_final)
        self.assertIsInstance(chunk.data, bytes)
        self.assertGreater(chunk.timestamp_ms, 0)
    
    def test_content_filter_guidelines(self):
        """Test content filter guidelines functionality."""
        content_filter = ContentFilter()
        
        # Get guidelines for current user
        guidelines = content_filter.get_content_guidelines(self.user)
        
        self.assertIn('current_level', guidelines)
        self.assertIn('allowed_topics', guidelines)
        self.assertIn('blocked_topics', guidelines)
        self.assertEqual(guidelines['current_level'], 1)
        
        # Update relationship level and check guidelines change
        self.relationship.relationship_level = 2
        self.relationship.save()
        
        # Clear any cached relationship data
        from django.core.cache import cache
        cache.clear()
        
        updated_guidelines = content_filter.get_content_guidelines(self.user)
        self.assertEqual(updated_guidelines['current_level'], 2)
        
        # Level 2 should have more allowed topics than level 1
        level1_topics = set(guidelines['allowed_topics'])
        level2_topics = set(updated_guidelines['allowed_topics'])
        self.assertGreater(len(level2_topics), len(level1_topics))