"""
Tests for audio processing service.
"""
import pytest
import asyncio
import numpy as np
import wave
import io
import time
from unittest.mock import AsyncMock, MagicMock, patch
from django.test import TestCase

from chat.services.audio_service import (
    AudioChunk,
    TranscriptionResult,
    AudioProcessingResult,
    AudioBuffer,
    AudioQualityAssessment,
    AudioProcessingService,
)
from chat.services.audio_format import (
    AudioFormatHandler,
    validate_audio_format,
    convert_audio_format,
)
from chat.services.audio_service import (
    get_audio_service
)
from chat.services.hume_service import EmotionAnalysisResult, EmotionScore


class TestAudioChunk(TestCase):
    """Test AudioChunk dataclass."""
    
    def test_audio_chunk_creation(self):
        """Test creating audio chunk."""
        chunk = AudioChunk(
            data=b"test_audio_data",
            chunk_id="chunk_001",
            timestamp_ms=1000.0,
            sample_rate=48000,
            channels=1,
            is_final=False,
            user_id="user123"
        )
        
        assert chunk.data == b"test_audio_data"
        assert chunk.chunk_id == "chunk_001"
        assert chunk.timestamp_ms == 1000.0
        assert chunk.sample_rate == 48000
        assert chunk.channels == 1
        assert not chunk.is_final
        assert chunk.user_id == "user123"
    
    def test_audio_chunk_defaults(self):
        """Test default values."""
        chunk = AudioChunk(
            data=b"test",
            chunk_id="test",
            timestamp_ms=1000.0
        )
        
        assert chunk.sample_rate == 48000
        assert chunk.channels == 1
        assert not chunk.is_final
        assert chunk.user_id is None


class TestTranscriptionResult(TestCase):
    """Test TranscriptionResult dataclass."""
    
    def test_transcription_result_creation(self):
        """Test creating transcription result."""
        result = TranscriptionResult(
            text="Hello world",
            confidence=0.95,
            is_partial=False,
            processing_time_ms=150.0,
            chunk_id="chunk_001",
            timestamp_ms=1000.0
        )
        
        assert result.text == "Hello world"
        assert result.confidence == 0.95
        assert not result.is_partial
        assert result.processing_time_ms == 150.0
        assert result.chunk_id == "chunk_001"
        assert result.timestamp_ms == 1000.0


class TestAudioBuffer(TestCase):
    """Test AudioBuffer functionality."""
    
    def test_buffer_initialization(self):
        """Test buffer initialization."""
        buffer = AudioBuffer(
            max_buffer_size=5,
            target_chunk_duration_ms=500,
            sample_rate=48000
        )
        
        assert buffer.max_buffer_size == 5
        assert buffer.target_chunk_duration_ms == 500
        assert buffer.sample_rate == 48000
        assert buffer.target_chunk_size == 24000  # 48000 * 0.5
        assert len(buffer.buffer) == 0
        assert len(buffer.accumulated_data) == 0
    
    def test_add_small_chunk(self):
        """Test adding small chunk that doesn't trigger processing."""
        buffer = AudioBuffer(target_chunk_duration_ms=1000, sample_rate=48000)
        
        # Create small chunk (less than target size)
        small_audio = b"x" * 1000  # Much smaller than target
        chunk = AudioChunk(
            data=small_audio,
            chunk_id="small_001",
            timestamp_ms=1000.0
        )
        
        ready_chunks = buffer.add_chunk(chunk)
        
        # Should not return any ready chunks
        assert len(ready_chunks) == 0
        assert len(buffer.buffer) == 1
        assert len(buffer.accumulated_data) == 1000
    
    def test_add_large_chunk(self):
        """Test adding chunk that triggers processing."""
        buffer = AudioBuffer(target_chunk_duration_ms=100, sample_rate=48000)
        
        # Create large chunk (larger than target size)
        large_audio = b"x" * 20000  # Larger than target (9600 bytes)
        chunk = AudioChunk(
            data=large_audio,
            chunk_id="large_001",
            timestamp_ms=1000.0
        )
        
        ready_chunks = buffer.add_chunk(chunk)
        
        # Should return ready chunks
        assert len(ready_chunks) >= 1
        assert ready_chunks[0].chunk_id.startswith("aggregated_")
    
    def test_final_chunk_processing(self):
        """Test processing final chunk."""
        buffer = AudioBuffer(target_chunk_duration_ms=1000, sample_rate=48000)
        
        # Add some data first
        small_chunk = AudioChunk(
            data=b"x" * 1000,
            chunk_id="small_001",
            timestamp_ms=1000.0
        )
        buffer.add_chunk(small_chunk)
        
        # Add final chunk
        final_chunk = AudioChunk(
            data=b"y" * 500,
            chunk_id="final_001",
            timestamp_ms=2000.0,
            is_final=True
        )
        
        ready_chunks = buffer.add_chunk(final_chunk)
        
        # Should process final chunk
        assert len(ready_chunks) >= 1
        final_ready = [c for c in ready_chunks if c.is_final]
        assert len(final_ready) == 1
        assert final_ready[0].chunk_id.startswith("final_")
    
    def test_buffer_info(self):
        """Test buffer information retrieval."""
        buffer = AudioBuffer()
        
        info = buffer.get_buffer_info()
        
        assert 'buffer_count' in info
        assert 'accumulated_bytes' in info
        assert 'target_chunk_size' in info
        assert 'buffer_utilization' in info
        assert info['buffer_count'] == 0
        assert info['accumulated_bytes'] == 0


class TestAudioQualityAssessment(TestCase):
    """Test audio quality assessment."""
    
    def test_assess_empty_audio(self):
        """Test quality assessment with empty audio."""
        metrics = AudioQualityAssessment.assess_audio_quality(b"")
        
        assert metrics['quality_score'] == 0.0
        assert metrics['snr_estimate'] == 0.0
        assert metrics['volume_level'] == 0.0
    
    def test_assess_valid_audio(self):
        """Test quality assessment with valid audio."""
        # Create synthetic audio data (16-bit PCM)
        sample_rate = 48000
        duration = 0.1  # 100ms
        samples = int(sample_rate * duration)
        
        # Generate sine wave
        t = np.linspace(0, duration, samples, False)
        frequency = 440  # A4 note
        audio_signal = np.sin(2 * np.pi * frequency * t)
        
        # Convert to 16-bit PCM
        audio_int16 = (audio_signal * 32767).astype(np.int16)
        audio_bytes = audio_int16.tobytes()
        
        metrics = AudioQualityAssessment.assess_audio_quality(audio_bytes, sample_rate)
        
        assert 'quality_score' in metrics
        assert 'snr_estimate' in metrics
        assert 'volume_level' in metrics
        assert 'sample_count' in metrics
        
        # Should have reasonable quality for clean sine wave
        assert metrics['quality_score'] > 0.0
        assert metrics['volume_level'] > 0.0
        assert metrics['sample_count'] == samples
    
    def test_assess_noisy_audio(self):
        """Test quality assessment with noisy audio."""
        # Create noisy audio (random noise)
        samples = 4800  # 100ms at 48kHz
        noise = np.random.randint(-1000, 1000, samples, dtype=np.int16)
        audio_bytes = noise.tobytes()
        
        metrics = AudioQualityAssessment.assess_audio_quality(audio_bytes)
        
        # Noise should have lower quality score
        assert metrics['quality_score'] >= 0.0
        assert metrics['quality_score'] <= 1.0
        assert metrics['sample_count'] == samples


@pytest.fixture
def mock_groq_client():
    """Mock Groq client."""
    return AsyncMock()


@pytest.fixture
def mock_hume_client():
    """Mock Hume client."""
    return AsyncMock()


@pytest.fixture
def service(mock_groq_client, mock_hume_client):
    """Create test service."""
    return AudioProcessingService(
        groq_client=mock_groq_client,
        hume_client=mock_hume_client,
        max_workers=2
    )


@pytest.fixture
def sample_audio_chunk():
    """Create sample audio chunk."""
    # Generate 100ms of audio at 48kHz
    sample_rate = 48000
    duration = 0.1
    samples = int(sample_rate * duration)
    
    # Generate sine wave
    t = np.linspace(0, duration, samples, False)
    audio_signal = np.sin(2 * np.pi * 440 * t)  # 440Hz tone
    audio_int16 = (audio_signal * 16383).astype(np.int16)  # Moderate volume
    audio_bytes = audio_int16.tobytes()
    
    return AudioChunk(
        data=audio_bytes,
        chunk_id="test_chunk_001",
        timestamp_ms=time.time() * 1000,
        sample_rate=sample_rate,
        channels=1,
        user_id="test_user"
    )


class TestAudioProcessingService:
    
    def test_service_initialization(self, service):
        """Test service initialization."""
        assert service.groq_client is not None
        assert service.hume_client is not None
        assert service.executor is not None
        assert service.audio_buffer is not None
        assert service.processing_stats['total_chunks_processed'] == 0
    
    @pytest.mark.asyncio
    async def test_process_audio_chunk_parallel(self, service, mock_hume_client, sample_audio_chunk):
        """Test parallel audio processing."""
        # Mock emotion analysis result
        mock_emotion_result = EmotionAnalysisResult(
            primary_emotion="joy",
            emotion_intensity=0.8,
            emotion_valence=0.7,
            emotion_arousal=0.6,
            emotions=[EmotionScore("joy", 0.8)],
            confidence_score=0.9,
            processing_time_ms=50.0,
            source="audio",
            raw_data={}
        )
        mock_hume_client.analyze_audio_stream.return_value = mock_emotion_result
        
        # Process chunk
        results = []
        async for result in service.process_audio_chunk(
            sample_audio_chunk,
            enable_transcription=True,
            enable_emotion_detection=True,
            parallel_processing=True
        ):
            results.append(result)
        
        # Should get results (may be empty if chunk is too small for buffer)
        # This tests the processing pipeline structure
        assert isinstance(results, list)
    
    @pytest.mark.asyncio
    async def test_process_audio_chunk_sequential(self, service, mock_hume_client, sample_audio_chunk):
        """Test sequential audio processing."""
        # Mock emotion analysis result
        mock_emotion_result = EmotionAnalysisResult(
            primary_emotion="calm",
            emotion_intensity=0.3,
            emotion_valence=0.1,
            emotion_arousal=0.2,
            emotions=[EmotionScore("calm", 0.7)],
            confidence_score=0.8,
            processing_time_ms=75.0,
            source="audio",
            raw_data={}
        )
        mock_hume_client.analyze_audio_stream.return_value = mock_emotion_result
        
        # Process chunk sequentially
        results = []
        async for result in service.process_audio_chunk(
            sample_audio_chunk,
            enable_transcription=True,
            enable_emotion_detection=True,
            parallel_processing=False
        ):
            results.append(result)
        
        # Should get results
        assert isinstance(results, list)
    
    @pytest.mark.asyncio
    async def test_transcription_only(self, service, sample_audio_chunk):
        """Test transcription-only processing."""
        results = []
        async for result in service.process_audio_chunk(
            sample_audio_chunk,
            enable_transcription=True,
            enable_emotion_detection=False,
            parallel_processing=False
        ):
            results.append(result)
        
        # Should process without emotion detection
        assert isinstance(results, list)
    
    @pytest.mark.asyncio
    async def test_emotion_only(self, service, mock_hume_client, sample_audio_chunk):
        """Test emotion-only processing."""
        # Mock emotion analysis result
        mock_emotion_result = EmotionAnalysisResult(
            primary_emotion="neutral",
            emotion_intensity=0.5,
            emotion_valence=0.0,
            emotion_arousal=0.3,
            emotions=[EmotionScore("neutral", 0.6)],
            confidence_score=0.7,
            processing_time_ms=60.0,
            source="audio",
            raw_data={}
        )
        mock_hume_client.analyze_audio_stream.return_value = mock_emotion_result
        
        results = []
        async for result in service.process_audio_chunk(
            sample_audio_chunk,
            enable_transcription=False,
            enable_emotion_detection=True,
            parallel_processing=False
        ):
            results.append(result)
        
        # Should process without transcription
        assert isinstance(results, list)
    
    def test_performance_stats(self, service):
        """Test performance statistics."""
        # Initial stats
        stats = service.get_performance_stats()
        
        assert 'total_chunks_processed' in stats
        assert 'average_processing_time_ms' in stats
        assert 'parallel_processing_count' in stats
        assert 'buffer_info' in stats
        assert 'parallel_processing_ratio' in stats
        
        assert stats['total_chunks_processed'] == 0
        assert stats['parallel_processing_ratio'] == 0.0
    
    def test_health_status(self, service):
        """Test health status reporting."""
        health = service.get_health_status()
        
        assert 'status' in health
        assert 'health_score' in health
        assert 'average_processing_time_ms' in health
        assert 'target_processing_time_ms' in health
        assert 'total_chunks_processed' in health
        assert 'buffer_utilization' in health
        
        # Initial health should be good
        assert health['status'] == 'healthy'
        assert health['health_score'] == 1.0
        assert health['target_processing_time_ms'] == 50.0


class TestUtilityFunctions(TestCase):
    """Test utility functions."""
    
    def test_validate_audio_format_pcm(self):
        """Test audio format validation for PCM."""
        # Create raw PCM data
        audio_data = b"x" * 1000
        
        result = validate_audio_format(audio_data)
        
        assert result['valid'] is True
        assert result['format'] == 'pcm'
        assert result['channels'] == 1
        assert result['sample_rate'] == 48000
        assert result['sample_width'] == 2
    
    def test_validate_audio_format_wav(self):
        """Test audio format validation for WAV."""
        # Create a simple WAV file
        sample_rate = 44100
        duration = 0.1
        samples = int(sample_rate * duration)
        
        # Generate audio data
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t)
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        
        # Create WAV file in memory
        wav_buffer = io.BytesIO()
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        wav_data = wav_buffer.getvalue()
        
        result = validate_audio_format(wav_data)
        
        assert result['valid'] is True
        assert result['format'] == 'wav'
        assert result['channels'] == 1
        assert result['sample_rate'] == sample_rate
        assert result['sample_width'] == 2
        assert result['frame_count'] == samples
    
    def test_convert_audio_format(self):
        """Test audio format conversion."""
        input_audio = b"test_audio_data"
        
        # For now, conversion just returns input as-is
        result = convert_audio_format(input_audio, 48000, 1)
        
        assert result == input_audio
    
    def test_get_audio_service_singleton(self):
        """Test audio service singleton."""
        service1 = get_audio_service()
        service2 = get_audio_service()

        # Should return the same instance
        assert service1 is service2
        assert isinstance(service1, AudioProcessingService)


class TestGroqWhisperIntegration:
    """Test Groq Whisper integration."""

    @pytest.mark.asyncio
    async def test_prepare_audio_for_whisper(self):
        """Test audio preparation for Groq Whisper."""
        from chat.services.audio_service import AudioProcessingService

        # Create test audio chunk
        sample_rate = 48000
        duration = 0.1  # 100ms
        samples = int(sample_rate * duration)

        # Generate sine wave
        t = np.linspace(0, duration, samples, False)
        audio_signal = np.sin(2 * np.pi * 440 * t)
        audio_int16 = (audio_signal * 16383).astype(np.int16)
        audio_bytes = audio_int16.tobytes()

        chunk = AudioChunk(
            data=audio_bytes,
            chunk_id="test_whisper_001",
            timestamp_ms=time.time() * 1000,
            sample_rate=sample_rate,
            channels=1
        )

        # Create service instance
        service = AudioProcessingService()

        # Test audio preparation
        audio_file = service._prepare_audio_for_whisper(chunk)

        assert audio_file is not None
        assert hasattr(audio_file, 'name')
        assert audio_file.name.endswith('.wav')

        # Verify it's a valid WAV file
        audio_file.seek(0)
        wav_data = audio_file.read()
        assert wav_data.startswith(b'RIFF')  # WAV file signature
        assert b'WAVE' in wav_data[:12]  # WAV format identifier

        # Clean up
        audio_file.close()

    @pytest.mark.asyncio
    @patch('chat.services.audio_service.Groq')
    @patch.dict('os.environ', {'GROQ_API_KEY': 'test_key'})
    async def test_transcribe_audio_chunk_success(self, mock_groq_class):
        """Test successful transcription with Groq Whisper."""
        from chat.services.audio_service import AudioProcessingService

        # Mock Groq client and response
        mock_groq_instance = MagicMock()
        mock_groq_class.return_value = mock_groq_instance

        # Mock transcription response
        mock_transcription = MagicMock()
        mock_transcription.text = "Hello, this is a test transcription."
        mock_groq_instance.audio.transcriptions.create.return_value = mock_transcription

        # Create test audio chunk
        chunk = AudioChunk(
            data=b"fake_audio_data" * 100,  # Some audio data
            chunk_id="test_transcribe_001",
            timestamp_ms=time.time() * 1000,
            sample_rate=48000,
            channels=1,
            is_final=True
        )

        # Create service and test transcription
        service = AudioProcessingService()

        quality_metrics = {'quality_score': 0.8}
        result = await service._transcribe_audio_chunk(chunk, quality_metrics)

        # Verify result
        assert result is not None
        assert isinstance(result, TranscriptionResult)
        assert result.text == "Hello, this is a test transcription."
        assert result.chunk_id == "test_transcribe_001"
        assert result.confidence > 0.0
        assert not result.is_partial  # Should be final since chunk.is_final=True
        assert result.processing_time_ms > 0

        # Verify Groq was called correctly
        mock_groq_instance.audio.transcriptions.create.assert_called_once()
        call_args = mock_groq_instance.audio.transcriptions.create.call_args
        assert call_args[1]['model'] == 'whisper-large-v3-turbo'
        assert call_args[1]['response_format'] == 'verbose_json'
        assert call_args[1]['language'] == 'en'
        assert call_args[1]['temperature'] == 0.0

    @pytest.mark.asyncio
    @patch('chat.services.audio_service.Groq')
    @patch.dict('os.environ', {'GROQ_API_KEY': 'test_key'})
    async def test_transcribe_audio_chunk_error_fallback(self, mock_groq_class):
        """Test transcription error handling and fallback."""
        from chat.services.audio_service import AudioProcessingService

        # Mock Groq client to raise an exception
        mock_groq_instance = MagicMock()
        mock_groq_class.return_value = mock_groq_instance
        mock_groq_instance.audio.transcriptions.create.side_effect = Exception("API Error")

        # Create test audio chunk
        chunk = AudioChunk(
            data=b"fake_audio_data" * 100,
            chunk_id="test_error_001",
            timestamp_ms=time.time() * 1000,
            sample_rate=48000,
            channels=1
        )

        # Create service and test transcription
        service = AudioProcessingService()

        quality_metrics = {'quality_score': 0.6}
        result = await service._transcribe_audio_chunk(chunk, quality_metrics)

        # Should return fallback result
        assert result is not None
        assert isinstance(result, TranscriptionResult)
        assert result.text == "[Audio transcription unavailable]"
        assert result.confidence == 0.1  # Low confidence for fallback
        assert result.chunk_id == "test_error_001"


if __name__ == "__main__":
    # Run basic tests
    pytest.main([__file__, "-v"])