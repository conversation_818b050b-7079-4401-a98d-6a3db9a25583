#!/usr/bin/env python3
"""
Simple Memory Cache Performance Test
Tests the memory caching system performance improvements.
"""
import os
import sys
import django
import asyncio
import time

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from chat.services.memory_cache_service import memory_cache_service


async def test_memory_cache_performance():
    """Test memory cache performance."""
    print("🚀 Memory Cache Performance Test")
    print("=" * 50)
    
    # Test parameters
    user_id = "test_user_123"
    session_id = "test_session_456"
    
    # Test queries
    test_queries = [
        "What are my travel preferences?",
        "Tell me about my work",
        "What foods do I like?",
        "What are my hobbies?",
        "Do I have any health concerns?"
    ]
    
    print("⚡ Testing Memory Cache Service...")
    
    # Test preloading (this will use fallback since no real memories exist)
    print("   📥 Testing preload...")
    preload_start = time.time()
    preload_success = await memory_cache_service.preload_user_memories(
        user_id=user_id,
        session_id=session_id
    )
    preload_time = (time.time() - preload_start) * 1000
    print(f"   ✅ Preload completed in {preload_time:.1f}ms (success: {preload_success})")
    
    # Test cache queries
    print("   🔍 Testing cache queries...")
    total_query_time = 0
    
    for i, query in enumerate(test_queries, 1):
        print(f"   Query {i}: '{query[:30]}...'")
        
        start_time = time.time()
        
        # Search cached memories
        memories = await memory_cache_service.search_cached_memories(
            user_id=user_id,
            session_id=session_id,
            query=query,
            k=5
        )
        
        end_time = time.time()
        duration_ms = (end_time - start_time) * 1000
        total_query_time += duration_ms
        
        print(f"      ⚡ {duration_ms:.1f}ms | 📚 {len(memories)} memories")
    
    # Calculate average
    avg_query_time = total_query_time / len(test_queries)
    
    # Get cache stats
    stats = memory_cache_service.get_cache_stats()
    
    print("\n📊 PERFORMANCE RESULTS")
    print("=" * 50)
    print(f"⚡ Average Query Time:     {avg_query_time:.1f}ms")
    print(f"🎯 Target (≤10ms):         {'✅ YES' if avg_query_time <= 10 else '❌ NO'}")
    print(f"📈 Cache Hit Rate:         {stats.get('hit_rate_percent', 0):.1f}%")
    print(f"🔍 Cache Hits:             {stats.get('cache_hits', 0)}")
    print(f"❌ Cache Misses:           {stats.get('cache_misses', 0)}")
    
    # Test cache invalidation
    print("\n🧹 Testing cache cleanup...")
    cleanup_start = time.time()
    cleanup_success = await memory_cache_service.invalidate_user_cache(
        user_id=user_id,
        session_id=session_id
    )
    cleanup_time = (time.time() - cleanup_start) * 1000
    print(f"   ✅ Cache cleanup in {cleanup_time:.1f}ms (success: {cleanup_success})")
    
    print("\n🎉 Memory cache test completed!")
    
    # Return results for comparison
    return {
        'avg_query_time_ms': avg_query_time,
        'target_met': avg_query_time <= 10,
        'cache_stats': stats
    }


async def test_websocket_memory_integration():
    """Test WebSocket integration with memory caching."""
    print("\n🔌 Testing WebSocket Memory Integration...")
    
    try:
        from channels.testing import WebsocketCommunicator
        from channels.routing import URLRouter
        from django.urls import re_path
        from chat.consumers import ChatConsumer
        from authentication.models import User
        from asgiref.sync import sync_to_async
        
        # Get or create test user
        try:
            user = await sync_to_async(User.objects.get)(email="<EMAIL>")
        except User.DoesNotExist:
            user = await sync_to_async(User.objects.create_user)(
                username="<EMAIL>",
                email="<EMAIL>",
                password="testpass123"
            )
        
        # Create WebSocket application
        application = URLRouter([
            re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
        ])
        
        # Create communicator
        communicator = WebsocketCommunicator(application, "/ws/chat/")
        communicator.scope["user"] = user
        
        # Connect
        print("   🔗 Connecting WebSocket...")
        connected, _ = await communicator.connect()
        if not connected:
            print("   ❌ Failed to connect WebSocket")
            return {}
        
        print("   ✅ WebSocket connected")
        
        # Test message with memory context
        test_query = "Tell me about my preferences"
        
        print(f"   📤 Sending test message: '{test_query}'")
        start_time = time.time()
        
        # Send message
        import uuid
        await communicator.send_json_to({
            'type': 'text_message',
            'content': test_query,
            'conversation_id': str(uuid.uuid4())
        })
        
        # Collect responses
        response_received = False
        memory_retrieval_fast = True
        
        timeout = 15  # 15 seconds
        while (time.time() - start_time) < timeout:
            try:
                response = await asyncio.wait_for(
                    communicator.receive_json_from(),
                    timeout=3.0
                )
                
                response_type = response.get('type')
                
                if response_type == 'llm_response_chunk':
                    if response.get('is_final'):
                        total_response_time = (time.time() - start_time) * 1000
                        response_received = True
                        print(f"   ✅ Response received in {total_response_time:.1f}ms")
                        break
                
            except asyncio.TimeoutError:
                print("   ⏰ Response timeout")
                break
        
        # Disconnect
        await communicator.disconnect()
        
        result = {
            'websocket_connected': True,
            'response_received': response_received,
            'memory_cache_integrated': True
        }
        
        print(f"   📊 WebSocket test result: {result}")
        return result
        
    except Exception as e:
        print(f"   ❌ Error testing WebSocket integration: {e}")
        return {'error': str(e)}


async def main():
    """Run the memory cache tests."""
    # Test memory cache performance
    cache_results = await test_memory_cache_performance()
    
    # Test WebSocket integration
    websocket_results = await test_websocket_memory_integration()
    
    print("\n🏆 FINAL SUMMARY")
    print("=" * 50)
    
    if cache_results:
        print(f"⚡ Memory Cache Performance: {cache_results['avg_query_time_ms']:.1f}ms avg")
        print(f"🎯 Performance Target Met:  {'✅ YES' if cache_results['target_met'] else '❌ NO'}")
    
    if websocket_results and not websocket_results.get('error'):
        print(f"🔌 WebSocket Integration:    {'✅ SUCCESS' if websocket_results.get('response_received') else '⚠️ PARTIAL'}")
    
    print("\n🎉 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
