#!/usr/bin/env python3
"""
Hume Emotion Accuracy Test
Tests emotion detection accuracy using correct Hume 48 Speech Prosody emotions.
"""
import os
import sys
import django
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service

# Audio library path
AUDIO_LIBRARY_DIR = Path("expressive_audio_library")

# <PERSON>'s exact 48 Speech Prosody emotions
HUME_PROSODY_EMOTIONS = [
    "Admiration", "Adoration", "Aesthetic Appreciation", "Amusement", "Anger", "Annoyance", 
    "Anxiety", "Awe", "Awkwardness", "Boredom", "Calmness", "Concentration", "Confusion", 
    "Contemplation", "Contempt", "Contentment", "Craving", "Desire", "Determination", 
    "Disappointment", "Disapproval", "Disgust", "Distress", "Doubt", "Ecstasy", 
    "Embarrassment", "Empathic Pain", "Enthusiasm", "Entrancement", "Envy", "Excitement", 
    "Fear", "Gratitude", "Guilt", "Horror", "Interest", "Joy", "Love", "Nostalgia", 
    "Pain", "Pride", "Realization", "Relief", "Romance", "Sadness", "Sarcasm", 
    "Satisfaction", "Shame", "Surprise (negative)", "Surprise (positive)", "Sympathy", 
    "Tiredness", "Triumph"
]


async def setup_test_user() -> User:
    """Setup test user."""
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    return user


def get_emotion_families() -> Dict[str, List[str]]:
    """Get emotion families for tolerance matching based on Hume's 48 emotions."""
    return {
        # Negative emotion families
        "Sadness": ["Sadness", "Distress", "Disappointment", "Empathic Pain"],
        "Anger": ["Anger", "Annoyance", "Contempt", "Disapproval"],
        "Fear": ["Fear", "Anxiety", "Horror"],
        "Embarrassment": ["Embarrassment", "Awkwardness", "Shame"],
        
        # Positive emotion families  
        "Joy": ["Joy", "Contentment", "Satisfaction", "Amusement", "Ecstasy"],
        "Excitement": ["Excitement", "Enthusiasm", "Triumph"],
        "Gratitude": ["Gratitude", "Admiration", "Love"],
        "Pride": ["Pride", "Triumph", "Satisfaction"],
        
        # Surprise families
        "Surprise (positive)": ["Surprise (positive)", "Awe", "Excitement"],
        "Surprise (negative)": ["Surprise (negative)", "Fear", "Anxiety"],
        
        # Calm families
        "Calmness": ["Calmness", "Relief", "Contentment"],
        "Relief": ["Relief", "Calmness", "Satisfaction"],
        
        # Cognitive families
        "Contemplation": ["Contemplation", "Concentration", "Interest"],
        "Determination": ["Determination", "Concentration"],
    }


def map_test_to_hume_emotions(test_emotion: str) -> List[str]:
    """Map our test emotions to expected Hume emotions."""
    mapping = {
        "joy": ["Joy"],
        "excitement": ["Excitement"],
        "gratitude": ["Gratitude"],
        "sadness": ["Sadness"],
        "distress": ["Distress"],
        "disappointment": ["Disappointment"],
        "anger": ["Anger"],
        "frustration": ["Anger", "Annoyance"],
        "annoyance": ["Annoyance"],
        "fear": ["Fear"],
        "anxiety": ["Anxiety"],
        "panic": ["Fear", "Anxiety"],
        "surprise": ["Surprise (positive)", "Surprise (negative)"],
        "shock": ["Surprise (negative)", "Fear"],
        "awe": ["Awe"],
        "calmness": ["Calmness"],
        "relief": ["Relief"],
        "determination": ["Determination"],
        "contemplation": ["Contemplation"],
        "embarrassment": ["Embarrassment"],
        "pride": ["Pride"],
    }
    return mapping.get(test_emotion, [test_emotion])


def calculate_accuracy(results: List[Dict], use_families: bool = False) -> Dict:
    """Calculate accuracy metrics."""
    if not results:
        return {"exact": 0, "family": 0, "total": 0}
    
    exact_matches = 0
    family_matches = 0
    total = len(results)
    
    emotion_families = get_emotion_families()
    
    for result in results:
        if not result.get('detected_emotion'):
            continue
            
        detected = result['detected_emotion']
        expected_list = result.get('expected_emotions', [])
        
        # Check exact match
        if detected in expected_list:
            exact_matches += 1
            family_matches += 1
        elif use_families:
            # Check family match
            for expected in expected_list:
                if expected in emotion_families:
                    if detected in emotion_families[expected]:
                        family_matches += 1
                        break
                # Also check reverse - if detected emotion has a family
                for family_name, family_emotions in emotion_families.items():
                    if detected == family_name and expected in family_emotions:
                        family_matches += 1
                        break
    
    return {
        "exact": exact_matches,
        "family": family_matches,
        "total": total,
        "exact_percent": (exact_matches / total * 100) if total > 0 else 0,
        "family_percent": (family_matches / total * 100) if total > 0 else 0
    }


async def test_all_emotions():
    """Test all emotions in our library."""
    print("🎯 HUME EMOTION ACCURACY TEST")
    print("=" * 60)
    print("Testing with Hume's exact 48 Speech Prosody emotions")
    
    user = await setup_test_user()
    session_id = f"hume_accuracy_{int(time.time())}"
    user_id = str(user.id)
    
    # Get all audio files
    audio_files = sorted(AUDIO_LIBRARY_DIR.glob("*.mp3"))
    
    if not audio_files:
        print("❌ No audio files found")
        return []
    
    results = []
    
    for i, audio_file in enumerate(audio_files):
        # Extract emotion from filename
        emotion_label = audio_file.stem.split('_', 1)[1] if '_' in audio_file.stem else audio_file.stem
        expected_emotions = map_test_to_hume_emotions(emotion_label)
        
        print(f"\n🎵 Test {i+1}/{len(audio_files)}: {emotion_label}")
        print(f"   Expected: {expected_emotions}")
        
        # Load and analyze audio
        try:
            with open(audio_file, 'rb') as f:
                audio_data = f.read()
        except Exception as e:
            print(f"   ❌ Failed to load: {e}")
            continue
        
        chunk_id = f"{session_id}_test_{i}"
        start_time = time.time()
        
        result = await expression_measurement_service.analyze_audio_chunk(
            audio_data, chunk_id, session_id, user_id
        )
        
        analysis_time = (time.time() - start_time) * 1000
        
        if result:
            detected = result.dominant_emotion
            confidence = result.confidence
            
            # Check if it's an exact match
            is_exact = detected in expected_emotions
            
            # Check if it's a family match
            emotion_families = get_emotion_families()
            is_family = False
            for expected in expected_emotions:
                if expected in emotion_families and detected in emotion_families[expected]:
                    is_family = True
                    break
            
            accuracy_icon = "✅" if is_exact else "🟡" if is_family else "❌"
            match_type = "EXACT" if is_exact else "FAMILY" if is_family else "MISS"
            
            print(f"   {accuracy_icon} Detected: {detected} ({confidence:.2f}) - {match_type}")
            print(f"   ⏱️  Processing: {analysis_time:.1f}ms")
            
            # Show top 5 emotions
            print(f"   📊 Top 5:")
            for j, emotion in enumerate(result.emotions[:5]):
                name = emotion.get('name', 'Unknown')
                score = emotion.get('score', 0)
                match_indicator = "✓" if name in expected_emotions else " "
                print(f"      {j+1}. {name}: {score:.3f} {match_indicator}")
            
            results.append({
                'emotion_label': emotion_label,
                'expected_emotions': expected_emotions,
                'detected_emotion': detected,
                'confidence': confidence,
                'is_exact': is_exact,
                'is_family': is_family,
                'analysis_time_ms': analysis_time,
                'top_emotions': result.emotions[:5]
            })
        else:
            print(f"   ❌ No detection - {analysis_time:.1f}ms")
            results.append({
                'emotion_label': emotion_label,
                'expected_emotions': expected_emotions,
                'detected_emotion': None,
                'confidence': 0,
                'is_exact': False,
                'is_family': False,
                'analysis_time_ms': analysis_time
            })
        
        await asyncio.sleep(0.3)
    
    return results


async def main():
    """Run comprehensive Hume emotion accuracy test."""
    print("🎯 COMPREHENSIVE HUME EMOTION ACCURACY TEST")
    print("=" * 70)
    
    # Test all emotions
    results = await test_all_emotions()
    
    if not results:
        print("❌ No results to analyze")
        return
    
    # Calculate accuracy metrics
    accuracy = calculate_accuracy(results, use_families=True)
    
    print(f"\n🏆 ACCURACY ANALYSIS")
    print("=" * 70)
    
    print(f"📊 Overall Results:")
    print(f"   Total tests: {accuracy['total']}")
    print(f"   Exact matches: {accuracy['exact']}/{accuracy['total']} ({accuracy['exact_percent']:.1f}%)")
    print(f"   Family matches: {accuracy['family']}/{accuracy['total']} ({accuracy['family_percent']:.1f}%)")
    
    # Breakdown by confidence
    high_conf = [r for r in results if r.get('confidence', 0) > 0.6]
    med_conf = [r for r in results if 0.3 < r.get('confidence', 0) <= 0.6]
    low_conf = [r for r in results if 0 < r.get('confidence', 0) <= 0.3]
    
    print(f"\n📈 Confidence Breakdown:")
    print(f"   High confidence (>0.6): {len(high_conf)} tests")
    print(f"   Medium confidence (0.3-0.6): {len(med_conf)} tests")
    print(f"   Low confidence (≤0.3): {len(low_conf)} tests")
    
    # Show best and worst performers
    exact_matches = [r for r in results if r.get('is_exact')]
    family_matches = [r for r in results if r.get('is_family') and not r.get('is_exact')]
    
    if exact_matches:
        print(f"\n✅ Exact Matches ({len(exact_matches)}):")
        for match in exact_matches:
            print(f"   {match['emotion_label']} → {match['detected_emotion']} ({match['confidence']:.2f})")
    
    if family_matches:
        print(f"\n🟡 Family Matches ({len(family_matches)}):")
        for match in family_matches:
            print(f"   {match['emotion_label']} → {match['detected_emotion']} ({match['confidence']:.2f})")
    
    # Performance metrics
    avg_time = sum(r['analysis_time_ms'] for r in results) / len(results)
    print(f"\n⏱️  Performance:")
    print(f"   Average processing time: {avg_time:.1f}ms")
    
    # Final assessment
    print(f"\n🎯 PRODUCTION ASSESSMENT:")
    if accuracy['exact_percent'] >= 50:
        print("   ✅ EXCELLENT - High exact match accuracy")
    elif accuracy['family_percent'] >= 60:
        print("   ✅ GOOD - Strong family match accuracy")
    elif accuracy['family_percent'] >= 40:
        print("   ⚠️ FAIR - Moderate accuracy, monitor closely")
    else:
        print("   ❌ POOR - Needs improvement")
    
    print(f"\n✨ Hume emotion accuracy test completed!")
    print(f"System tested against Hume's exact 48 Speech Prosody emotions.")


if __name__ == "__main__":
    asyncio.run(main())
