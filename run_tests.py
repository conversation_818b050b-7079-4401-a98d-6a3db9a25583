#!/usr/bin/env python
"""
Test runner script for ellahai-backend.
"""
import os
import sys
import argparse
import subprocess

def run_tests(args):
    """Run tests with specified options."""
    # Base command
    cmd = ["pytest"]
    
    # Add verbosity
    if args.verbose:
        cmd.append("-v")
    
    # Add test path if specified
    if args.path:
        cmd.append(args.path)
    
    # Add markers if specified
    if args.markers:
        marker_expr = " and ".join(args.markers)
        cmd.extend(["-m", marker_expr])
    
    # Add exclusion markers
    exclude_markers = []
    if args.skip_slow:
        exclude_markers.append("slow")
    if args.skip_integration:
        exclude_markers.append("integration")
    if args.skip_api:
        exclude_markers.append("api")
    
    if exclude_markers:
        exclude_expr = " or ".join(exclude_markers)
        cmd.extend(["-m", f"not ({exclude_expr})"])
    
    # Add coverage if requested
    if args.coverage:
        cmd = ["coverage", "run", "-m"] + cmd
    
    # Add failfast if requested
    if args.failfast:
        cmd.append("-xvs")
    
    # Print command
    print(f"Running: {' '.join(cmd)}")
    
    # Run the tests
    result = subprocess.run(cmd)
    
    # Generate coverage report if requested
    if args.coverage and result.returncode == 0:
        subprocess.run(["coverage", "report"])
        if args.html:
            subprocess.run(["coverage", "html"])
            print("\nHTML coverage report generated in htmlcov/index.html")
    
    return result.returncode

def main():
    """Parse arguments and run tests."""
    parser = argparse.ArgumentParser(description="Run ellahai-backend tests")
    
    parser.add_argument("path", nargs="?", help="Test path to run")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    parser.add_argument("-m", "--markers", nargs="+", help="Only run tests with these markers")
    parser.add_argument("--skip-slow", action="store_true", help="Skip slow tests")
    parser.add_argument("--skip-integration", action="store_true", help="Skip integration tests")
    parser.add_argument("--skip-api", action="store_true", help="Skip tests requiring API access")
    parser.add_argument("--coverage", action="store_true", help="Run with coverage")
    parser.add_argument("--html", action="store_true", help="Generate HTML coverage report")
    parser.add_argument("-x", "--failfast", action="store_true", help="Stop on first failure")
    
    args = parser.parse_args()
    
    return run_tests(args)

if __name__ == "__main__":
    sys.exit(main())