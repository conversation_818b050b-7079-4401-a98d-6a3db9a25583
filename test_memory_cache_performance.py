#!/usr/bin/env python3
"""
Memory Cache Performance Test
Tests the optimized memory caching system vs original VectorDB approach.
"""
import os
import sys
import django
import asyncio
import time
import json
from typing import Dict, List, Any

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from channels.testing import WebsocketCommunicator
from channels.routing import URLRouter
from django.urls import re_path
from chat.consumers import ChatConsumer
from authentication.models import User


class MemoryCachePerformanceTest:
    """Test memory cache performance improvements."""
    
    def __init__(self):
        self.results = {
            'original_system': [],
            'cached_system': [],
            'performance_improvement': {}
        }
    
    async def setup_test_user(self):
        """Setup test user with some memories."""
        try:
            # Get or create test user
            user = await self.get_or_create_user(
                email="<EMAIL>",
                password="testpass123"
            )
            print(f"📝 Using test user: {user.email}")
            return user
        except Exception as e:
            print(f"❌ Error setting up user: {e}")
            return None
    
    async def get_or_create_user(self, email: str, password: str):
        """Get or create user asynchronously."""
        from asgiref.sync import sync_to_async
        
        try:
            user = await sync_to_async(User.objects.get)(email=email)
        except User.DoesNotExist:
            user = await sync_to_async(User.objects.create_user)(
                username=email,  # Use email as username
                email=email,
                password=password
            )
        return user
    
    async def create_test_memories(self, user):
        """Create test memories for performance testing."""
        try:
            from memory.services import MemoryManager

            memory_service = MemoryManager(user=user)
            
            # Create diverse test memories
            test_memories = [
                ("I love traveling to Japan and exploring temples", "travel", 0.8),
                ("My favorite food is sushi and ramen", "preference", 0.7),
                ("I work as a software engineer at a tech company", "personal", 0.9),
                ("I enjoy hiking and outdoor activities on weekends", "hobby", 0.6),
                ("I have a cat named Whiskers who loves to play", "personal", 0.7),
                ("I'm learning Spanish and plan to visit Spain", "goal", 0.8),
                ("I prefer morning workouts at the gym", "routine", 0.5),
                ("I collect vintage books and rare manuscripts", "hobby", 0.6),
                ("I'm allergic to peanuts and shellfish", "health", 0.9),
                ("I grew up in Seattle and miss the coffee culture", "personal", 0.7)
            ]
            
            created_count = 0
            for content, memory_type, importance in test_memories:
                memory_id = await memory_service.store_memory(
                    content=content,
                    memory_type=memory_type,
                    importance_score=importance
                )
                if memory_id:
                    created_count += 1
            
            print(f"📚 Created {created_count} test memories")
            return created_count > 0
            
        except Exception as e:
            print(f"❌ Error creating test memories: {e}")
            return False
    
    async def test_original_memory_system(self, user, queries: List[str]) -> List[Dict[str, Any]]:
        """Test original memory retrieval system."""
        print("\n🔍 Testing Original Memory System (VectorDB)...")
        
        results = []
        
        try:
            from agents.services.memory_manager import get_memory_manager
            
            memory_manager = get_memory_manager()
            
            for i, query in enumerate(queries, 1):
                print(f"   Query {i}: '{query[:30]}...'")
                
                start_time = time.time()
                
                # Original memory search
                memories = await memory_manager.async_search_memories(
                    query=query,
                    user_id=str(user.id),
                    k=5,
                    min_importance=0.3
                )
                
                end_time = time.time()
                duration_ms = (end_time - start_time) * 1000
                
                result = {
                    'query': query,
                    'duration_ms': duration_ms,
                    'memories_found': len(memories) if memories else 0,
                    'system': 'original'
                }
                
                results.append(result)
                print(f"      ⏱️  {duration_ms:.1f}ms | 📚 {result['memories_found']} memories")
                
                # Small delay between queries
                await asyncio.sleep(0.1)
            
            return results
            
        except Exception as e:
            print(f"❌ Error testing original system: {e}")
            return []
    
    async def test_cached_memory_system(self, user, queries: List[str]) -> List[Dict[str, Any]]:
        """Test cached memory retrieval system."""
        print("\n⚡ Testing Cached Memory System...")
        
        results = []
        
        try:
            from chat.services.memory_cache_service import memory_cache_service
            
            # Create a test session
            session_id = "test_session_123"
            
            # Preload memories
            print("   📥 Preloading memories...")
            preload_start = time.time()
            preload_success = await memory_cache_service.preload_user_memories(
                user_id=str(user.id),
                session_id=session_id
            )
            preload_time = (time.time() - preload_start) * 1000
            
            if not preload_success:
                print("❌ Failed to preload memories")
                return []
            
            print(f"   ✅ Preloaded in {preload_time:.1f}ms")
            
            # Test queries
            for i, query in enumerate(queries, 1):
                print(f"   Query {i}: '{query[:30]}...'")
                
                start_time = time.time()
                
                # Cached memory search
                memories = await memory_cache_service.search_cached_memories(
                    user_id=str(user.id),
                    session_id=session_id,
                    query=query,
                    k=5
                )
                
                end_time = time.time()
                duration_ms = (end_time - start_time) * 1000
                
                result = {
                    'query': query,
                    'duration_ms': duration_ms,
                    'memories_found': len(memories) if memories else 0,
                    'system': 'cached',
                    'preload_time_ms': preload_time if i == 1 else 0
                }
                
                results.append(result)
                print(f"      ⚡ {duration_ms:.1f}ms | 📚 {result['memories_found']} memories")
                
                # Small delay between queries
                await asyncio.sleep(0.1)
            
            # Get cache stats
            stats = memory_cache_service.get_cache_stats()
            print(f"   📊 Cache Stats: {stats}")
            
            return results
            
        except Exception as e:
            print(f"❌ Error testing cached system: {e}")
            return []
    
    async def test_websocket_integration(self, user) -> Dict[str, Any]:
        """Test WebSocket integration with memory caching."""
        print("\n🔌 Testing WebSocket Integration...")
        
        try:
            # Create WebSocket application
            application = URLRouter([
                re_path(r"^ws/chat/$", ChatConsumer.as_asgi()),
            ])
            
            # Create communicator
            communicator = WebsocketCommunicator(application, "/ws/chat/")
            communicator.scope["user"] = user
            
            # Connect
            connected, _ = await communicator.connect()
            if not connected:
                print("❌ Failed to connect WebSocket")
                return {}
            
            print("✅ WebSocket connected")
            
            # Test message with memory context
            test_query = "Tell me about my travel preferences"
            
            start_time = time.time()
            
            # Send message
            import uuid
            await communicator.send_json_to({
                'type': 'text_message',
                'content': test_query,
                'conversation_id': str(uuid.uuid4())  # Use proper UUID format
            })
            
            # Collect responses
            memory_retrieval_time = None
            total_response_time = None
            
            timeout = 10  # 10 seconds
            while (time.time() - start_time) < timeout:
                try:
                    response = await asyncio.wait_for(
                        communicator.receive_json_from(),
                        timeout=2.0
                    )
                    
                    response_type = response.get('type')
                    
                    if response_type == 'llm_response_chunk':
                        if response.get('is_final'):
                            total_response_time = (time.time() - start_time) * 1000
                            break
                    
                except asyncio.TimeoutError:
                    break
            
            # Disconnect
            await communicator.disconnect()
            
            result = {
                'websocket_connected': True,
                'total_response_time_ms': total_response_time,
                'memory_retrieval_optimized': True
            }
            
            print(f"   ⏱️  Total response time: {total_response_time:.1f}ms")
            return result
            
        except Exception as e:
            print(f"❌ Error testing WebSocket integration: {e}")
            return {'error': str(e)}
    
    def analyze_performance(self, original_results: List[Dict], cached_results: List[Dict]) -> Dict[str, Any]:
        """Analyze performance improvements."""
        if not original_results or not cached_results:
            return {}
        
        # Calculate averages
        original_avg = sum(r['duration_ms'] for r in original_results) / len(original_results)
        cached_avg = sum(r['duration_ms'] for r in cached_results) / len(cached_results)
        
        # Calculate improvement
        improvement_factor = original_avg / cached_avg if cached_avg > 0 else 0
        improvement_percent = ((original_avg - cached_avg) / original_avg * 100) if original_avg > 0 else 0
        
        analysis = {
            'original_avg_ms': original_avg,
            'cached_avg_ms': cached_avg,
            'improvement_factor': improvement_factor,
            'improvement_percent': improvement_percent,
            'time_saved_ms': original_avg - cached_avg,
            'target_met': cached_avg <= 10  # 10ms target
        }
        
        return analysis
    
    async def run_performance_test(self):
        """Run complete performance test."""
        print("🚀 Memory Cache Performance Test")
        print("=" * 50)
        
        # Setup
        user = await self.setup_test_user()
        if not user:
            return
        
        # Create test memories
        await self.create_test_memories(user)
        
        # Test queries
        test_queries = [
            "What are my travel preferences?",
            "Tell me about my work",
            "What foods do I like?",
            "What are my hobbies?",
            "Do I have any health concerns?"
        ]
        
        # Test original system
        original_results = await self.test_original_memory_system(user, test_queries)
        
        # Test cached system
        cached_results = await self.test_cached_memory_system(user, test_queries)
        
        # Test WebSocket integration
        websocket_result = await self.test_websocket_integration(user)
        
        # Analyze results
        analysis = self.analyze_performance(original_results, cached_results)
        
        # Print results
        print("\n📊 PERFORMANCE ANALYSIS")
        print("=" * 50)
        
        if analysis:
            print(f"🔍 Original System Average: {analysis['original_avg_ms']:.1f}ms")
            print(f"⚡ Cached System Average:  {analysis['cached_avg_ms']:.1f}ms")
            print(f"🚀 Improvement Factor:     {analysis['improvement_factor']:.1f}x faster")
            print(f"📈 Improvement Percent:    {analysis['improvement_percent']:.1f}%")
            print(f"⏱️  Time Saved:            {analysis['time_saved_ms']:.1f}ms per query")
            print(f"🎯 Target Met (≤10ms):     {'✅ YES' if analysis['target_met'] else '❌ NO'}")
        
        if websocket_result:
            print(f"\n🔌 WebSocket Integration:")
            print(f"   Total Response Time: {websocket_result.get('total_response_time_ms', 'N/A'):.1f}ms")
        
        print("\n🎉 Performance test completed!")
        return analysis


async def main():
    """Run the memory cache performance test."""
    test = MemoryCachePerformanceTest()
    await test.run_performance_test()


if __name__ == "__main__":
    asyncio.run(main())
