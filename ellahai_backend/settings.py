"""
Django settings for ellahai_backend project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from pathlib import Path
import environ
import os

# Initialize environment variables
env = environ.Env(
    DEBUG=(bool, True)
)

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# Take environment variables from .env file
environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY', default='django-insecure-4@ty!b*l+4)@8pe--wfb=*4a6u5*h4qslcn8ugrec$i#!8$l-k')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env('DEBUG', default=True)

ALLOWED_HOSTS = env.list('ALLOWED_HOSTS', default=[
    'localhost',
    '127.0.0.1',
    '*.trycloudflare.com',  # Allow Cloudflare tunnel domains
    '*.cloudflareaccess.com',  # Allow Cloudflare Access domains
])

# Testing configuration
ENABLE_DUMMY_TOKEN = env('ENABLE_DUMMY_TOKEN', default=DEBUG)  # Enable dummy token in debug mode


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    
    # Third party apps
    "rest_framework",
    "rest_framework.authtoken",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",
    "corsheaders",
    "channels",
    
    # Local apps
    "authentication",
    "chat",
    "memory",
    "agents",
    "gamification",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "ellahai_backend.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "ellahai_backend.wsgi.application"
ASGI_APPLICATION = "ellahai_backend.asgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# Use SQLite for testing if no DB_NAME is provided or if it ends with .sqlite3
if not env('DB_NAME') or env('DB_NAME').endswith('.sqlite3'):
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / (env('DB_NAME', default='db.sqlite3')),
        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': env('DB_NAME'),
            'USER': env('DB_USER'),
            'PASSWORD': env('DB_PASSWORD'),
            'HOST': env('DB_HOST', default='localhost'),
            'PORT': env('DB_PORT', default='5432'),
        }
    }


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Custom User Model
AUTH_USER_MODEL = 'authentication.User'

# Django REST Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'EXCEPTION_HANDLER': 'ellahai_backend.utils.custom_exception_handler',
}

# CORS settings
CORS_ALLOWED_ORIGINS = env.list('CORS_ALLOWED_ORIGINS', default=[
    "http://localhost:3000",  # Flutter web dev server
    "http://127.0.0.1:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
])

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# Channels (WebSocket) configuration
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [(env('REDIS_HOST', default='127.0.0.1'), env.int('REDIS_PORT', default=6379))],
        },
    },
}

# Celery Configuration (for background tasks)
CELERY_BROKER_URL = env('CELERY_BROKER_URL', default='redis://localhost:6379/0')
CELERY_RESULT_BACKEND = env('CELERY_RESULT_BACKEND', default='redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Redis Cache Configuration
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{env('REDIS_HOST', default='127.0.0.1')}:{env.int('REDIS_PORT', default=6379)}/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "SOCKET_CONNECT_TIMEOUT": 5,
            "SOCKET_TIMEOUT": 5,
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
        }
    }
}

# Caching Configuration
CACHING_ENABLED = env.bool('CACHING_ENABLED', default=True)
CACHE_TIMEOUTS = {
    'user_profile': env.int('CACHE_TIMEOUT_USER_PROFILE', default=3600),  # 1 hour
    'emotion_context': env.int('CACHE_TIMEOUT_EMOTION_CONTEXT', default=300),  # 5 minutes
    'memory_embeddings': env.int('CACHE_TIMEOUT_MEMORY_EMBEDDINGS', default=86400),  # 24 hours
    'frequent_queries': env.int('CACHE_TIMEOUT_FREQUENT_QUERIES', default=1800),  # 30 minutes
    'llm_responses': env.int('CACHE_TIMEOUT_LLM_RESPONSES', default=3600),  # 1 hour
    'api_connection': env.int('CACHE_TIMEOUT_API_CONNECTION', default=600),  # 10 minutes
}

# OpenAI API Configuration
OPENAI_API_KEY = env('OPENAI_API_KEY', default='')

# Groq API Configuration for Real-time LLM
GROQ_API_KEY = env('GROQ_API_KEY', default='')
GROQ_BASE_URL = env('GROQ_BASE_URL', default='https://api.groq.com')
GROQ_DEFAULT_MODEL = env('GROQ_DEFAULT_MODEL', default='llama-3.3-70b-versatile')
GROQ_MAX_RETRIES = env.int('GROQ_MAX_RETRIES', default=3)
GROQ_TIMEOUT = env.float('GROQ_TIMEOUT', default=30.0)
GROQ_CONNECTION_POOL_SIZE = env.int('GROQ_CONNECTION_POOL_SIZE', default=10)

# Hume AI Configuration for Emotion Detection and TTS
HUME_API_KEY = env('HUME_API_KEY', default='')
HUME_TIMEOUT = env.float('HUME_TIMEOUT', default=30.0)
HUME_DEFAULT_VOICE = env('HUME_DEFAULT_VOICE', default='')
HUME_ENABLE_PROSODY = env.bool('HUME_ENABLE_PROSODY', default=True)
HUME_ENABLE_LANGUAGE = env.bool('HUME_ENABLE_LANGUAGE', default=True)

# Circuit Breaker Configuration
HUME_CIRCUIT_BREAKER_FAILURE_THRESHOLD = env.int('HUME_CIRCUIT_BREAKER_FAILURE_THRESHOLD', default=10)
HUME_CIRCUIT_BREAKER_RECOVERY_TIMEOUT = env.int('HUME_CIRCUIT_BREAKER_RECOVERY_TIMEOUT', default=30)
GROQ_CIRCUIT_BREAKER_FAILURE_THRESHOLD = env.int('GROQ_CIRCUIT_BREAKER_FAILURE_THRESHOLD', default=10)
GROQ_CIRCUIT_BREAKER_RECOVERY_TIMEOUT = env.int('GROQ_CIRCUIT_BREAKER_RECOVERY_TIMEOUT', default=60)

# Emotion Critic Service Configuration - Memory Control
EMOTION_CRITIC_ENABLE_MEMORY = env.bool('EMOTION_CRITIC_ENABLE_MEMORY', default=False)  # DISABLED by default to prevent noise
EMOTION_CRITIC_MEMORY_DEBUG = env.bool('EMOTION_CRITIC_MEMORY_DEBUG', default=False)   # Debug memory integration

# Performance targets for real-time AI companion
PERFORMANCE_TARGET_RESPONSE_TIME_MS = env.int('PERFORMANCE_TARGET_RESPONSE_TIME_MS', default=450)
PERFORMANCE_TARGET_FIRST_TOKEN_MS = env.int('PERFORMANCE_TARGET_FIRST_TOKEN_MS', default=200)
PERFORMANCE_TARGET_TTS_FIRST_CHUNK_MS = env.int('PERFORMANCE_TARGET_TTS_FIRST_CHUNK_MS', default=100)

# Detailed performance targets (in milliseconds)
PERFORMANCE_TARGETS = {
    'total_response_time': env.int('PERFORMANCE_TARGET_RESPONSE_TIME_MS', default=450),
    'audio_processing_time': env.int('PERFORMANCE_TARGET_AUDIO_PROCESSING_MS', default=50),
    'emotion_detection_time': env.int('PERFORMANCE_TARGET_EMOTION_DETECTION_MS', default=100),
    'llm_first_token_time': env.int('PERFORMANCE_TARGET_FIRST_TOKEN_MS', default=200),
    'tts_first_chunk_time': env.int('PERFORMANCE_TARGET_TTS_FIRST_CHUNK_MS', default=100),
    'memory_retrieval_time': env.int('PERFORMANCE_TARGET_MEMORY_RETRIEVAL_MS', default=10),  # Reduced from 50ms with caching
    'transcription_time': env.int('PERFORMANCE_TARGET_TRANSCRIPTION_MS', default=100),
}

# Memory Cache Configuration
MEMORY_CACHE_SESSION_TIMEOUT = env.int('MEMORY_CACHE_SESSION_TIMEOUT', default=3600)  # 1 hour
MEMORY_CACHE_MAX_MEMORIES = env.int('MEMORY_CACHE_MAX_MEMORIES', default=100)  # Max memories per user session
MEMORY_CACHE_SIMILARITY_THRESHOLD = env.float('MEMORY_CACHE_SIMILARITY_THRESHOLD', default=0.7)
MEMORY_CACHE_PRELOAD_ENABLED = env.bool('MEMORY_CACHE_PRELOAD_ENABLED', default=True)  # Enable preloading on connect

# Performance monitoring configuration
PERFORMANCE_ALERT_THRESHOLD = env.float('PERFORMANCE_ALERT_THRESHOLD', default=0.8)
PERFORMANCE_MONITORING_ENABLED = env.bool('PERFORMANCE_MONITORING_ENABLED', default=True)
PERFORMANCE_MONITORING_INTERVAL = env.int('PERFORMANCE_MONITORING_INTERVAL', default=300)  # 5 minutes

# Memory/Vector Store Configuration
VECTOR_STORE_PERSIST_DIRECTORY = env('VECTOR_STORE_PERSIST_DIRECTORY', default=str(BASE_DIR / 'vector_store_db'))
EMBEDDING_MODEL = env('EMBEDDING_MODEL', default='text-embedding-3-small')

# Audio Processing
AUDIO_UPLOAD_PATH = env('AUDIO_UPLOAD_PATH', default=str(BASE_DIR / 'media' / 'audio'))
MAX_AUDIO_FILE_SIZE = env.int('MAX_AUDIO_FILE_SIZE', default=10 * 1024 * 1024)  # 10MB

# Debug Audio Configuration
DEBUG_SAVE_AUDIO_CHUNKS = env.bool('DEBUG_SAVE_AUDIO_CHUNKS', default=False)
DEBUG_AUDIO_PATH = env('DEBUG_AUDIO_PATH', default=str(BASE_DIR / 'debug_audio'))

# Backblaze B2 Storage Configuration
USE_B2_STORAGE = env.bool('USE_B2_STORAGE', default=False)
B2_ACCESS_KEY = env('B2_ACCESS_KEY', default='')
B2_SECRET_KEY = env('B2_SECRET_KEY', default='')
B2_BUCKET_NAME = env('B2_BUCKET_NAME', default='')
B2_REGION = env('B2_REGION', default='us-west-002')
B2_CUSTOM_DOMAIN = env('B2_CUSTOM_DOMAIN', default=None)
B2_FILE_OVERWRITE = env.bool('B2_FILE_OVERWRITE', default=False)
B2_DEFAULT_ACL = env('B2_DEFAULT_ACL', default='private')

# Configure storage backend based on settings
if USE_B2_STORAGE:
    # Add django-storages to INSTALLED_APPS
    if 'storages' not in INSTALLED_APPS:
        INSTALLED_APPS.append('storages')
    
    # Configure default file storage
    DEFAULT_FILE_STORAGE = 'chat.storage.BackblazeB2Storage'
    
    # Media files configuration with B2
    MEDIA_URL = env('MEDIA_URL', default='https://s3.{}.backblazeb2.com/{}/'.format(B2_REGION, B2_BUCKET_NAME))
else:
    # Use local file system for storage
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
    MEDIA_URL = '/media/'

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'ellahai_backend': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Create logs directory if it doesn't exist
os.makedirs(BASE_DIR / 'logs', exist_ok=True)
os.makedirs(BASE_DIR / 'media' / 'audio', exist_ok=True)

# Create debug audio directory if debugging is enabled
if DEBUG_SAVE_AUDIO_CHUNKS:
    os.makedirs(DEBUG_AUDIO_PATH, exist_ok=True)

# JWT Settings
from datetime import timedelta

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=env.int('JWT_ACCESS_TOKEN_LIFETIME_MINUTES', default=60)),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=env.int('JWT_REFRESH_TOKEN_LIFETIME_DAYS', default=7)),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'JTI_CLAIM': 'jti',
    'SLIDING_TOKEN_REFRESH_EXP_CLAIM': 'refresh_exp',
    'SLIDING_TOKEN_LIFETIME': timedelta(minutes=env.int('JWT_SLIDING_TOKEN_LIFETIME_MINUTES', default=60)),
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=env.int('JWT_SLIDING_TOKEN_REFRESH_LIFETIME_DAYS', default=7)),
}
