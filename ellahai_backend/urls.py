"""
URL configuration for el<PERSON><PERSON>_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

urlpatterns = [
    path("admin/", admin.site.urls),
    path('api/auth/', include('authentication.urls')),
    path('api/user/', include('authentication.user_urls')),  # User profile endpoints
    path('api/chat/', include('chat.urls')),
    path('api/memory/', include('memory.urls')),
    path('api/gamification/', include('gamification.urls')),
    path('api/agents/', include('agents.urls', namespace='agents')),

    # Unity Integration Aliases (for frontend compatibility)
    # These endpoints redirect to the actual implementation in agents app
    path('api/unity/', include('agents.unity_urls', namespace='unity')),

    # Shop System Aliases (for frontend compatibility)
    path('api/shop/', include('gamification.shop_urls', namespace='shop')),

    # Conversations aliases (for frontend compatibility)
    path('api/', include('chat.conversation_aliases')),

    # Additional gamification aliases for documented endpoints
    path('api/', include('ellahai_backend.api_aliases')),
]
