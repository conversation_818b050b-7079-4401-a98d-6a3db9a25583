"""
API endpoint aliases for frontend compatibility.

These endpoints provide aliases to match the exact API structure documented
in BACKEND_INTEGRATION_READINESS.md while redirecting to actual implementations.
"""

from django.urls import path
from django.http import HttpResponseRedirect
from django.urls import reverse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def unlock_achievement_alias(request):
    """Unlock achievement (alias to gamification endpoint)"""
    from gamification.views import unlock_achievement
    return unlock_achievement(request)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def daily_rewards_alias(request):
    """Get daily rewards (alias to gamification endpoint)"""
    from gamification.views import DailyRewardListView
    view = DailyRewardListView.as_view()
    return view(request)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def claim_daily_reward_alias(request):
    """Claim daily reward (alias to gamification endpoint)"""
    from gamification.views import claim_daily_reward
    return claim_daily_reward(request)


urlpatterns = [
    # Achievement endpoints
    path('achievements/unlock/', unlock_achievement_alias, name='unlock-achievement-alias'),
    
    # Daily rewards endpoints
    path('daily-rewards/', daily_rewards_alias, name='daily-rewards-alias'),
    path('daily-rewards/claim/', claim_daily_reward_alias, name='claim-daily-reward-alias'),
]
