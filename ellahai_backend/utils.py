from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status

def custom_exception_handler(exc, context):
    """
    Custom exception handler for DRF that formats errors consistently.
    """
    # Call REST framework's default exception handler first
    response = exception_handler(exc, context)
    
    # If response is None, there was an unhandled exception
    if response is None:
        return Response(
            {
                "error": {
                    "code": "server_error",
                    "message": "An unexpected error occurred",
                    "details": str(exc) if str(exc) else "No details available"
                }
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
    # Format the response to match our error structure
    if isinstance(response.data, dict):
        if "detail" in response.data:
            # Handle DRF's default 'detail' error
            error_message = response.data["detail"]
            error_code = get_error_code_from_status(response.status_code)
            
            response.data = {
                "error": {
                    "code": error_code,
                    "message": error_message,
                    "details": {}
                }
            }
        elif any(isinstance(response.data[field], list) for field in response.data):
            # Handle validation errors
            details = {}
            for field, errors in response.data.items():
                if isinstance(errors, list):
                    details[field] = errors[0] if errors else "Invalid value"
                else:
                    details[field] = str(errors)
            
            response.data = {
                "error": {
                    "code": "validation_error",
                    "message": "Validation failed",
                    "details": details
                }
            }
    
    return response

def get_error_code_from_status(status_code):
    """
    Map HTTP status codes to error codes.
    """
    error_codes = {
        400: "bad_request",
        401: "authentication_failed",
        403: "permission_denied",
        404: "resource_not_found",
        405: "method_not_allowed",
        406: "not_acceptable",
        409: "conflict",
        429: "rate_limited",
        500: "server_error",
    }
    
    return error_codes.get(status_code, "unknown_error")