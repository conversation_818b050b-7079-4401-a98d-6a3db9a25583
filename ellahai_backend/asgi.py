"""
ASGI config for ellahai_backend project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.security.websocket import AllowedHostsOriginValidator
from django.conf import settings

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "ellahai_backend.settings")

# Initialize Django ASGI application early to ensure the AppRegistry
# is populated before importing code that may import ORM models.
django_asgi_app = get_asgi_application()

from chat.routing import websocket_urlpatterns
from chat.middleware import JWTAuthMiddleware

class DevelopmentOriginValidator:
    """Custom origin validator that's more permissive during development."""

    def __init__(self, application):
        self.application = application

    async def __call__(self, scope, receive, send):
        # In debug mode, be more permissive with origins
        if settings.DEBUG:
            # Get origin header
            origin_header = None
            host_header = None
            for name, value in scope.get('headers', []):
                if name == b'origin':
                    origin_header = value.decode()
                elif name == b'host':
                    host_header = value.decode()

            print(f"DEBUG: Origin: {origin_header}, Host: {host_header}")

            # Allow connections without origin header (common for direct WebSocket connections)
            if not origin_header:
                print("DEBUG: No origin header, allowing connection")
                return await self.application(scope, receive, send)

            # Allow localhost, 127.0.0.1, and Cloudflare tunnel domains
            allowed_patterns = [
                'localhost',
                '127.0.0.1',
                '.trycloudflare.com',
                '.cloudflareaccess.com',
                '.ngrok.io',  # Also allow ngrok for development
            ]

            is_allowed = any(pattern in origin_header for pattern in allowed_patterns)
            if not is_allowed:
                print(f"DEBUG: Rejecting origin: {origin_header}")
                await send({"type": "websocket.close", "code": 4003})
                return
            else:
                print(f"DEBUG: Allowing origin: {origin_header}")

        return await self.application(scope, receive, send)

# Use custom validator in debug mode, standard validator in production
if settings.DEBUG:
    websocket_app = DevelopmentOriginValidator(
        JWTAuthMiddleware(
            URLRouter(websocket_urlpatterns)
        )
    )
else:
    websocket_app = AllowedHostsOriginValidator(
        JWTAuthMiddleware(
            URLRouter(websocket_urlpatterns)
        )
    )

application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": websocket_app,
})
