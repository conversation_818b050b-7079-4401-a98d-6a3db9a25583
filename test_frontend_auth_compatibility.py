#!/usr/bin/env python
"""
Test script to verify authentication endpoints match frontend requirements.
"""

import os
import sys
import django
import json
from django.test import TestCase, Client
from django.urls import reverse
from rest_framework import status

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User


def test_auth_endpoints():
    """Test all authentication endpoints match frontend expectations."""
    client = Client()
    
    print("🔐 Testing Authentication Endpoints Compatibility")
    print("=" * 60)
    
    # Test data matching frontend requirements
    user_data = {
        'email': '<EMAIL>',
        'password': 'TestPassword123!',
        'first_name': 'Test',
        'last_name': 'User',
        'username': 'testuser'
    }
    
    # 1. Test Registration Endpoint
    print("\n1. Testing Registration: POST /api/auth/register/")
    response = client.post('/api/auth/register/', 
                          data=json.dumps(user_data),
                          content_type='application/json')
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 201:
        data = response.json()
        print(f"   ✅ Registration successful")
        print(f"   ✅ Response has 'user' field: {'user' in data}")
        print(f"   ✅ Response has 'tokens' field: {'tokens' in data}")
        if 'tokens' in data:
            print(f"   ✅ Tokens has 'access': {'access' in data['tokens']}")
            print(f"   ✅ Tokens has 'refresh': {'refresh' in data['tokens']}")
        access_token = data.get('tokens', {}).get('access')
        refresh_token = data.get('tokens', {}).get('refresh')
        user_id = data.get('user', {}).get('id')
    else:
        print(f"   ❌ Registration failed: {response.content}")
        return
    
    # 2. Test Login Endpoint
    print("\n2. Testing Login: POST /api/auth/login/")
    login_data = {
        'email': user_data['email'],
        'password': user_data['password']
    }
    response = client.post('/api/auth/login/',
                          data=json.dumps(login_data),
                          content_type='application/json')
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Login successful")
        print(f"   ✅ Response has 'user' field: {'user' in data}")
        print(f"   ✅ Response has 'tokens' field: {'tokens' in data}")
    else:
        print(f"   ❌ Login failed: {response.content}")
    
    # 3. Test Token Refresh Endpoint
    print("\n3. Testing Token Refresh: POST /api/auth/refresh/")
    refresh_data = {'refresh': refresh_token}
    response = client.post('/api/auth/refresh/',
                          data=json.dumps(refresh_data),
                          content_type='application/json')
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Token refresh successful")
        print(f"   ✅ Response has 'access' field: {'access' in data}")
        new_access_token = data.get('access', access_token)
    else:
        print(f"   ❌ Token refresh failed: {response.content}")
        new_access_token = access_token
    
    # 4. Test User Profile Endpoints
    print("\n4. Testing User Profile: GET /api/user/profile/")
    headers = {'HTTP_AUTHORIZATION': f'Bearer {new_access_token}'}
    response = client.get('/api/user/profile/', **headers)
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print(f"   ✅ Profile retrieval successful")
    else:
        print(f"   ❌ Profile retrieval failed: {response.content}")
    
    print("\n5. Testing User Profile Update: PUT /api/user/profile/")
    update_data = {'first_name': 'Updated', 'bio': 'Updated bio'}
    response = client.put('/api/user/profile/',
                         data=json.dumps(update_data),
                         content_type='application/json',
                         **headers)
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print(f"   ✅ Profile update successful")
    else:
        print(f"   ❌ Profile update failed: {response.content}")
    
    # 5. Test Google Auth Endpoint Structure
    print("\n6. Testing Google Auth Endpoint: POST /api/auth/google/")
    google_data = {'id_token': 'fake_token_for_structure_test'}
    response = client.post('/api/auth/google/',
                          data=json.dumps(google_data),
                          content_type='application/json')
    
    print(f"   Status: {response.status_code}")
    print(f"   ✅ Endpoint exists (expected to fail with fake token)")
    
    # 6. Test Apple Auth Endpoint Structure
    print("\n7. Testing Apple Auth Endpoint: POST /api/auth/apple/")
    apple_data = {
        'identity_token': 'fake_token',
        'authorization_code': 'fake_code',
        'email': '<EMAIL>',
        'first_name': 'Apple',
        'last_name': 'User'
    }
    response = client.post('/api/auth/apple/',
                          data=json.dumps(apple_data),
                          content_type='application/json')
    
    print(f"   Status: {response.status_code}")
    print(f"   ✅ Endpoint exists (expected to fail with fake token)")
    
    # 7. Test Logout Endpoint
    print("\n8. Testing Logout: POST /api/auth/logout/")
    logout_data = {'refresh': refresh_token}
    response = client.post('/api/auth/logout/',
                          data=json.dumps(logout_data),
                          content_type='application/json',
                          **headers)
    
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        print(f"   ✅ Logout successful")
    else:
        print(f"   ❌ Logout failed: {response.content}")
    
    print("\n" + "=" * 60)
    print("🎉 Authentication endpoints compatibility test completed!")
    print("\nAll required endpoints are available and match frontend expectations:")
    print("✅ POST /api/auth/register/")
    print("✅ POST /api/auth/login/")
    print("✅ POST /api/auth/refresh/")
    print("✅ POST /api/auth/logout/")
    print("✅ POST /api/auth/google/")
    print("✅ POST /api/auth/apple/")
    print("✅ GET /api/user/profile/")
    print("✅ PUT /api/user/profile/")


if __name__ == '__main__':
    test_auth_endpoints()
