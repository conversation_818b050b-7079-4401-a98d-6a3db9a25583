#!/usr/bin/env python3
"""
Simple test to validate the enhanced system is working.
"""
import asyncio
import os
import sys
import time

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')

import django
django.setup()

from agents.services.langgraph_orchestrator import LangGraphOrchestrator, Domain


class MockUser:
    def __init__(self, personality, companion_name):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = "TestUser"
        self.id = f"test_{personality}"


async def test_basic_functionality():
    """Test basic functionality of the enhanced system."""
    print("🧪 Simple System Validation Test")
    print("=" * 40)
    
    # Test 1: Basic orchestrator initialization
    print("\n1. Testing orchestrator initialization...")
    try:
        user = MockUser('caringFriend', 'Ella')
        orchestrator = LangGraphOrchestrator(user=user)
        print("   ✅ Orchestrator initialized successfully")
    except Exception as e:
        print(f"   ❌ Initialization failed: {e}")
        return
    
    # Test 2: Personality prompt generation
    print("\n2. Testing personality prompt generation...")
    try:
        prompt = orchestrator._get_personality_prompt(user)
        if 'Ella' in prompt and 'caring' in prompt.lower():
            print("   ✅ Personality prompt generated correctly")
        else:
            print("   ⚠️ Personality prompt may not be working correctly")
    except Exception as e:
        print(f"   ❌ Personality prompt failed: {e}")
    
    # Test 3: LLM routing logic
    print("\n3. Testing LLM routing logic...")
    try:
        simple_query = "Hello, how are you?"
        complex_query = "Analyze the pros and cons of remote work"
        
        simple_needs_complex = orchestrator._needs_complex_reasoning(simple_query, Domain.GENERAL)
        complex_needs_complex = orchestrator._needs_complex_reasoning(complex_query, Domain.BUSINESS)
        
        if not simple_needs_complex and complex_needs_complex:
            print("   ✅ LLM routing logic working correctly")
            print(f"      Simple query → Groq: {not simple_needs_complex}")
            print(f"      Complex query → OpenAI: {complex_needs_complex}")
        else:
            print("   ⚠️ LLM routing may need adjustment")
            print(f"      Simple query → Complex: {simple_needs_complex}")
            print(f"      Complex query → Complex: {complex_needs_complex}")
    except Exception as e:
        print(f"   ❌ LLM routing test failed: {e}")
    
    # Test 4: Quick response test
    print("\n4. Testing quick response (timeout after 10s)...")
    try:
        start_time = time.time()
        response_received = False
        
        async def timeout_test():
            nonlocal response_received
            async for chunk in orchestrator.process_query(
                user_input="Hello!",
                user_id="test_user",
                streaming=True
            ):
                response_received = True
                print(f"   ✅ Response received in {(time.time() - start_time)*1000:.1f}ms")
                break
        
        # Run with timeout
        await asyncio.wait_for(timeout_test(), timeout=10.0)
        
        if not response_received:
            print("   ⚠️ No response received within timeout")
            
    except asyncio.TimeoutError:
        print("   ⚠️ Response timed out after 10 seconds")
    except Exception as e:
        print(f"   ❌ Response test failed: {e}")
    
    # Test 5: Multiple personalities
    print("\n5. Testing multiple personalities...")
    personalities = ['caringFriend', 'playfulCompanion', 'wiseMentor']
    
    for personality in personalities:
        try:
            user = MockUser(personality, f"Test{personality}")
            orchestrator = LangGraphOrchestrator(user=user)
            prompt = orchestrator._get_personality_prompt(user)
            
            if user.ai_companion_name in prompt:
                print(f"   ✅ {personality}: Prompt contains companion name")
            else:
                print(f"   ⚠️ {personality}: Prompt may not contain companion name")
                
        except Exception as e:
            print(f"   ❌ {personality}: Failed - {e}")
    
    print("\n" + "=" * 40)
    print("🎯 Basic validation complete!")
    print("\nIf you see mostly ✅ marks, the enhanced system is working correctly.")
    print("The system now supports:")
    print("• Groq + OpenAI dual-LLM architecture")
    print("• Personality-based responses")
    print("• Intelligent LLM routing")
    print("• Enhanced error handling")


if __name__ == "__main__":
    asyncio.run(test_basic_functionality())
