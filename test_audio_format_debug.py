#!/usr/bin/env python3
"""
Audio Format Debug Test
Debug what's happening with our audio files and Hume AI.
"""
import os
import sys
import django
import asyncio
import base64
from pathlib import Path

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service

# Audio library path
AUDIO_LIBRARY_DIR = Path("expressive_audio_library")


async def setup_test_user() -> User:
    """Setup test user."""
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    return user


async def debug_audio_processing():
    """Debug audio processing with detailed logging."""
    print("🔍 AUDIO FORMAT DEBUG TEST")
    print("=" * 60)
    
    user = await setup_test_user()
    session_id = f"debug_{int(asyncio.get_event_loop().time())}"
    user_id = str(user.id)
    
    # Get first audio file
    audio_files = list(AUDIO_LIBRARY_DIR.glob("*.mp3"))
    
    if not audio_files:
        print("❌ No audio files found")
        return
    
    test_file = audio_files[0]
    print(f"🎵 Testing file: {test_file.name}")
    
    # Load and analyze audio
    try:
        with open(test_file, 'rb') as f:
            audio_data = f.read()
        
        print(f"📊 Audio file info:")
        print(f"   Size: {len(audio_data):,} bytes ({len(audio_data)/1024:.1f} KB)")
        print(f"   First 20 bytes: {audio_data[:20].hex()}")
        
        # Check if it's a valid MP3
        if audio_data.startswith(b'ID3') or audio_data[0:2] == b'\xff\xfb':
            print(f"   ✅ Valid MP3 format detected")
        else:
            print(f"   ⚠️ Unexpected audio format")
        
        # Try base64 encoding (what Hume expects)
        audio_b64 = base64.b64encode(audio_data).decode('utf-8')
        print(f"   Base64 length: {len(audio_b64):,} characters")
        print(f"   Base64 sample: {audio_b64[:50]}...")
        
        # Test with expression service
        print(f"\n🧪 Testing with expression service...")
        
        chunk_id = f"{session_id}_debug_test"
        result = await expression_measurement_service.analyze_audio_chunk(
            audio_data, chunk_id, session_id, user_id
        )
        
        if result:
            print(f"   ✅ SUCCESS! Detected: {result.dominant_emotion} ({result.confidence:.2f})")
            print(f"   Top 3 emotions:")
            for i, emotion in enumerate(result.emotions[:3]):
                print(f"      {i+1}. {emotion.get('name', 'Unknown')}: {emotion.get('score', 0):.3f}")
        else:
            print(f"   ❌ No emotion detected")
            
            # Check service stats for more info
            stats = expression_measurement_service.get_performance_stats()
            print(f"   Service stats:")
            print(f"      Success rate: {stats.get('success_rate_percent', 0):.1f}%")
            print(f"      Failed requests: {stats.get('failed_requests', 0)}")
            print(f"      Total requests: {stats.get('total_requests', 0)}")
        
    except Exception as e:
        print(f"❌ Error processing audio: {e}")
        import traceback
        traceback.print_exc()


async def test_simple_wav_creation():
    """Test creating a simple WAV file using Python."""
    print(f"\n🎵 CREATING SIMPLE WAV FILE TEST")
    print("=" * 60)
    
    try:
        import wave
        import numpy as np
        
        # Create a simple sine wave WAV file
        sample_rate = 16000  # 16kHz as Hume prefers
        duration = 2.0  # 2 seconds
        frequency = 440  # A4 note
        
        # Generate sine wave
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        wave_data = np.sin(frequency * 2 * np.pi * t)
        
        # Convert to 16-bit integers
        wave_data = (wave_data * 32767).astype(np.int16)
        
        # Save as WAV
        test_wav_file = Path("test_simple.wav")
        
        with wave.open(str(test_wav_file), 'w') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 2 bytes = 16 bits
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(wave_data.tobytes())
        
        print(f"✅ Created simple WAV file: {test_wav_file}")
        print(f"   Duration: {duration} seconds")
        print(f"   Sample rate: {sample_rate} Hz")
        print(f"   File size: {test_wav_file.stat().st_size} bytes")
        
        # Test this WAV file with Hume
        user = await setup_test_user()
        session_id = f"wav_test_{int(asyncio.get_event_loop().time())}"
        user_id = str(user.id)
        
        with open(test_wav_file, 'rb') as f:
            wav_data = f.read()
        
        print(f"\n🧪 Testing simple WAV with Hume...")
        
        result = await expression_measurement_service.analyze_audio_chunk(
            wav_data, f"{session_id}_wav_test", session_id, user_id
        )
        
        if result:
            print(f"   ✅ WAV SUCCESS! Detected: {result.dominant_emotion} ({result.confidence:.2f})")
        else:
            print(f"   ❌ WAV also failed - may be a service configuration issue")
        
        # Clean up
        test_wav_file.unlink()
        
    except ImportError:
        print("❌ NumPy not available for WAV creation")
    except Exception as e:
        print(f"❌ Error creating WAV: {e}")


async def check_service_configuration():
    """Check if the expression service is properly configured."""
    print(f"\n⚙️ SERVICE CONFIGURATION CHECK")
    print("=" * 60)
    
    service = expression_measurement_service
    
    print(f"Service info:")
    print(f"   API key configured: {'✅' if service.api_key else '❌'}")
    print(f"   WebSocket URL: {service.websocket_url}")
    print(f"   Max connections: {service.max_connections}")
    print(f"   Connection pool size: {len(service._connection_pool)}")
    
    # Check if we can connect to Hume
    try:
        import websockets
        import ssl
        
        print(f"\n🔗 Testing WebSocket connection...")
        
        # Test connection (without sending data)
        ssl_context = ssl.create_default_context()
        
        async with websockets.connect(
            service.websocket_url,
            extra_headers={"X-Hume-Api-Key": service.api_key},
            ssl=ssl_context,
            ping_interval=None
        ) as websocket:
            print(f"   ✅ WebSocket connection successful")
            
            # Send a simple test message
            test_message = {
                "models": {"prosody": {}},
                "data": base64.b64encode(b"test").decode('utf-8')
            }
            
            await websocket.send(str(test_message).replace("'", '"'))
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"   ✅ Received response from Hume")
                print(f"   Response preview: {response[:100]}...")
            except asyncio.TimeoutError:
                print(f"   ⚠️ No response received (timeout)")
            
    except Exception as e:
        print(f"   ❌ WebSocket connection failed: {e}")


async def main():
    """Run comprehensive audio format debugging."""
    print("🔍 COMPREHENSIVE AUDIO FORMAT DEBUG")
    print("=" * 70)
    
    # Test 1: Debug current audio processing
    await debug_audio_processing()
    
    # Test 2: Check service configuration
    await check_service_configuration()
    
    # Test 3: Try simple WAV creation
    await test_simple_wav_creation()
    
    print(f"\n💡 DEBUG INSIGHTS:")
    print(f"   - Check if MP3 format is supported by Hume")
    print(f"   - Verify WebSocket connection and API key")
    print(f"   - Consider converting to WAV format")
    print(f"   - Test with different audio encoding")
    
    print(f"\n✨ Audio format debug completed!")


if __name__ == "__main__":
    asyncio.run(main())
