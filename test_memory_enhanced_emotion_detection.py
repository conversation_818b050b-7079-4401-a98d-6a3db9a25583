#!/usr/bin/env python3
"""
Memory-Enhanced Emotion Detection Test
Tests the actor-critic system with memory-informed emotion detection.
"""
import os
import sys
import django
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple
from difflib import SequenceMatcher
import numpy as np

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service
from chat.services.emotion_critic_service import emotion_critic_service
from create_memory_enhanced_test_users import MemoryEnhancedTestUserCreator


def get_comprehensive_emotion_families() -> Dict[str, List[str]]:
    """Get comprehensive emotion families for accuracy calculation."""
    return {
        "joy": ["Joy", "Happiness", "Excitement", "Enthusiasm", "Satisfaction", "Contentment"],
        "excitement": ["Excitement", "Joy", "Enthusiasm", "Anticipation", "Thrill"],
        "satisfaction": ["Satisfaction", "Contentment", "Joy", "Pride", "Accomplishment"],
        "anger": ["Anger", "Rage", "Fury", "Annoyance", "Irritation", "Frustration"],
        "frustration": ["Frustration", "Annoyance", "Anger", "Irritation"],
        "sadness": ["Sadness", "Sorrow", "Grief", "Disappointment", "Distress"],
        "disappointment": ["Disappointment", "Sadness", "Dissatisfaction"],
        "distress": ["Distress", "Anguish", "Sadness", "Anxiety"],
        "fear": ["Fear", "Terror", "Anxiety", "Worry", "Panic"],
        "anxiety": ["Anxiety", "Worry", "Fear", "Nervousness"],
        "panic": ["Panic", "Terror", "Fear", "Anxiety"],
        "interest": ["Interest", "Curiosity", "Fascination"],
        "curiosity": ["Curiosity", "Interest", "Wonder"],
        "concentration": ["Concentration", "Focus", "Contemplation"],
        "contemplation": ["Contemplation", "Reflection", "Concentration"],
        "realization": ["Realization", "Understanding", "Insight"],
        "confusion": ["Confusion", "Bewilderment", "Uncertainty"],
        "pride": ["Pride", "Accomplishment", "Satisfaction", "Triumph"],
        "triumph": ["Triumph", "Victory", "Pride", "Success"],
        "determination": ["Determination", "Resolve", "Persistence"],
        "gratitude": ["Gratitude", "Thankfulness", "Appreciation"],
        "love": ["Love", "Affection", "Adoration"],
        "calmness": ["Calmness", "Serenity", "Peace", "Tranquility"],
        "relief": ["Relief", "Ease", "Comfort"],
        "contentment": ["Contentment", "Satisfaction", "Peace"],
        "embarrassment": ["Embarrassment", "Shame", "Awkwardness"],
        "shame": ["Shame", "Embarrassment", "Guilt"],
        "hope": ["Hope", "Optimism", "Anticipation"],
        "empathy": ["Empathy", "Compassion", "Understanding"],
        "anticipation": ["Anticipation", "Expectation", "Hope"]
    }


async def test_memory_enhanced_emotion_detection():
    """Test memory-enhanced emotion detection with realistic user profiles."""
    
    print("🧠 MEMORY-ENHANCED EMOTION DETECTION TEST")
    print("=" * 70)
    
    # Create memory-enhanced test users
    creator = MemoryEnhancedTestUserCreator()
    test_users = await creator.create_test_user_profiles()
    
    print(f"\n🎯 Testing with {len(test_users)} memory-enhanced users")
    
    # Load extended conversation scenarios
    metadata_file = Path("conversation_audio_library/conversation_metadata.json")
    if not metadata_file.exists():
        print(f"❌ Metadata not found: {metadata_file}")
        return []
    
    with open(metadata_file, 'r') as f:
        metadata = json.load(f)
    
    scenarios = metadata['scenarios']
    extended_scenarios = [s for s in scenarios if len(s['interactions']) >= 10]
    
    print(f"📊 Testing {len(extended_scenarios)} extended scenarios with memory context")
    
    all_results = []
    user_comparisons = []
    
    # Test each user with extended scenarios
    for user_data in test_users:
        user = user_data["user"]
        profile = user_data["profile"]
        user_id = user_data["user_id"]
        
        print(f"\n👤 TESTING USER: {profile['name']} ({profile['personality']})")
        print(f"   Emotional patterns: {profile['emotional_patterns']}")
        
        user_results = []
        
        # Select appropriate scenario based on user profile
        scenario_mapping = {
            "anxious_achiever": "job_interview_journey",
            "optimistic_social": "job_interview_journey",
            "emotionally_sensitive": "family_reconciliation_journey",
            "emotionally_balanced": "job_interview_journey",
            "creative_perfectionist": "creative_breakthrough_journey",
            "family_oriented_healer": "family_reconciliation_journey"
        }

        scenario_name = scenario_mapping.get(profile['personality'], 'job_interview_journey')
        scenario = next((s for s in extended_scenarios if s['scenario'] == scenario_name), None)
        
        if scenario:
            print(f"\n🎭 Testing scenario: {scenario['scenario']}")
            print(f"   Emotional arc: {scenario.get('emotional_arc', 'N/A')}")

            session_id = f"memory_test_{user_id}_{int(time.time())}"
            scenario_results = []

            # Test more interactions for new scenarios (10 for extended testing)
            max_interactions = 10 if scenario['scenario'] in ['creative_breakthrough_journey', 'family_reconciliation_journey'] else 8
            interactions_to_test = scenario['interactions'][:max_interactions]
            
            for i, interaction in enumerate(interactions_to_test):
                stage = interaction['stage']
                text = interaction['text']
                expected_emotion = interaction['expected_emotion']
                
                # Load audio file
                audio_filename = f"{scenario['scenario']}_{i+1:02d}_{stage}.mp3"
                audio_path = Path("conversation_audio_library") / audio_filename
                
                if not audio_path.exists():
                    continue
                
                with open(audio_path, 'rb') as f:
                    audio_data = f.read()
                
                # Memory-enhanced emotion analysis
                chunk_id = f"{session_id}_interaction_{i+1}"
                start_time = time.time()
                
                result = await expression_measurement_service.analyze_audio_chunk(
                    audio_data, chunk_id, session_id, user_id, text=text
                )
                
                analysis_time = (time.time() - start_time) * 1000
                
                if result:
                    detected_emotion = result.dominant_emotion
                    confidence = result.confidence
                    
                    # Check accuracy with emotion families
                    exact_match = detected_emotion.lower() == expected_emotion.lower()
                    
                    emotion_families = get_comprehensive_emotion_families()
                    family_match = False
                    if expected_emotion in emotion_families:
                        family_emotions = [e.lower() for e in emotion_families[expected_emotion]]
                        family_match = detected_emotion.lower() in family_emotions
                    
                    match_type = "EXACT" if exact_match else "FAMILY" if family_match else "MISS"
                    match_icon = "✅" if exact_match else "🟡" if family_match else "❌"
                    
                    # Check if this emotion aligns with user's emotional patterns
                    pattern_match = detected_emotion.lower() in [p.lower() for p in profile['emotional_patterns']]
                    pattern_icon = "🎯" if pattern_match else "📊"
                    
                    print(f"   {i+1}. {match_icon} {pattern_icon} {expected_emotion} → {detected_emotion} ({confidence:.2f}) [{match_type}] - {analysis_time:.0f}ms")
                    
                    scenario_results.append({
                        'user_id': user_id,
                        'user_profile': profile['personality'],
                        'interaction': i + 1,
                        'stage': stage,
                        'expected_emotion': expected_emotion,
                        'detected_emotion': detected_emotion,
                        'confidence': confidence,
                        'exact_match': exact_match,
                        'family_match': family_match,
                        'pattern_match': pattern_match,
                        'analysis_time': analysis_time
                    })
                
                await asyncio.sleep(0.3)
            
            user_results.extend(scenario_results)

            # ===== TRAJECTORY-AWARE METRICS =====
            if scenario_results and len(scenario_results) > 0:
                emotion_families = get_comprehensive_emotion_families()

                # 1. Final-state accuracy
                expected_final = interactions_to_test[-1]['expected_emotion'].lower()
                pred_final = scenario_results[-1]['detected_emotion'].lower()

                final_exact = int(pred_final == expected_final)
                final_family = int(pred_final in [e.lower() for e in
                                emotion_families.get(expected_final, [])])

                # 2. Weighted accuracy (late turns matter more)
                weights = np.linspace(0.3, 1.0, len(scenario_results))  # older -> 0.3, latest -> 1.0
                w_exact = sum(w for w, r in zip(weights, scenario_results) if r['exact_match'])
                w_family = sum(w for w, r in zip(weights, scenario_results) if r['family_match'])
                weight_total = weights.sum()

                weighted_exact_acc = 100 * w_exact / weight_total if weight_total > 0 else 0
                weighted_family_acc = 100 * w_family / weight_total if weight_total > 0 else 0

                # 3. Arc similarity (emotional journey shape) - IMPROVED ENCODING
                def encode_emotion(emotion):
                    """Encode emotion to unique character for better arc similarity."""
                    emotion_map = {
                        'anger': 'A', 'anxiety': 'X', 'fear': 'F', 'sadness': 'S', 'joy': 'J',
                        'excitement': 'E', 'satisfaction': 'T', 'calmness': 'C', 'determination': 'D',
                        'concentration': 'O', 'contemplation': 'M', 'interest': 'I', 'curiosity': 'U',
                        'frustration': 'R', 'disappointment': 'P', 'relief': 'L', 'panic': 'N',
                        'resentment': 'G', 'empathy': 'Y', 'vulnerability': 'V', 'hope': 'H',
                        'love': 'W', 'gratitude': 'Q', 'pride': 'Z', 'triumph': 'B', 'despair': 'K',
                        'pain': '1', 'distress': '2', 'boredom': '3', 'tiredness': '4', 'nostalgia': '5',
                        'aesthetic appreciation': '6', 'surprise (positive)': '7', 'surprise (negative)': '8',
                        'admiration': '9', 'confusion': '0'
                    }
                    return emotion_map.get(emotion.lower(), emotion[0].upper())

                expected_seq = "".join([encode_emotion(i['expected_emotion']) for i in interactions_to_test])
                pred_seq = "".join([encode_emotion(r['detected_emotion']) for r in scenario_results])
                arc_sim = SequenceMatcher(None, expected_seq, pred_seq).ratio()  # 0-1

                # Display trajectory metrics
                print(f"\n   🏁 FINAL STATE:  {'✅' if final_exact else '🟡' if final_family else '❌'} "
                      f"{pred_final} vs {expected_final}")
                print(f"   📈 Weighted family accuracy (late>early): {weighted_family_acc:.1f}%")
                print(f"   🌀 Arc similarity: {arc_sim:.2f}")

                # Store trajectory metrics for later analysis
                trajectory_metrics = {
                    'final_exact': final_exact,
                    'final_family': final_family,
                    'weighted_exact_acc': weighted_exact_acc,
                    'weighted_family_acc': weighted_family_acc,
                    'arc_similarity': arc_sim,
                    'expected_sequence': expected_seq,
                    'predicted_sequence': pred_seq
                }

                # Add trajectory metrics to each result for aggregation
                for result in scenario_results:
                    result.update(trajectory_metrics)

            # Analyze user-specific performance
            if scenario_results:
                total = len(scenario_results)
                exact_matches = sum(1 for r in scenario_results if r['exact_match'])
                family_matches = sum(1 for r in scenario_results if r['family_match'])
                pattern_matches = sum(1 for r in scenario_results if r['pattern_match'])
                
                exact_accuracy = (exact_matches / total * 100) if total > 0 else 0
                family_accuracy = (family_matches / total * 100) if total > 0 else 0
                pattern_accuracy = (pattern_matches / total * 100) if total > 0 else 0
                
                print(f"\n   📊 USER PERFORMANCE:")
                print(f"      Exact accuracy: {exact_accuracy:.1f}% ({exact_matches}/{total})")
                print(f"      Family accuracy: {family_accuracy:.1f}% ({family_matches}/{total})")
                print(f"      Pattern alignment: {pattern_accuracy:.1f}% ({pattern_matches}/{total})")
                
                user_comparisons.append({
                    'user_profile': profile['personality'],
                    'emotional_patterns': profile['emotional_patterns'],
                    'exact_accuracy': exact_accuracy,
                    'family_accuracy': family_accuracy,
                    'pattern_accuracy': pattern_accuracy,
                    'total_interactions': total
                })
        
        all_results.extend(user_results)
    
    return all_results, user_comparisons


async def analyze_memory_enhanced_results(results: List[Dict], user_comparisons: List[Dict]):
    """Analyze memory-enhanced emotion detection results."""
    
    print(f"\n🏆 MEMORY-ENHANCED EMOTION DETECTION ANALYSIS")
    print("=" * 70)
    
    if not results:
        print("❌ No results to analyze")
        return
    
    # Overall performance
    total_interactions = len(results)
    exact_matches = sum(1 for r in results if r['exact_match'])
    family_matches = sum(1 for r in results if r['family_match'])
    pattern_matches = sum(1 for r in results if r['pattern_match'])

    overall_exact = (exact_matches / total_interactions * 100) if total_interactions > 0 else 0
    overall_family = (family_matches / total_interactions * 100) if total_interactions > 0 else 0
    overall_pattern = (pattern_matches / total_interactions * 100) if total_interactions > 0 else 0

    print(f"📊 Overall Memory-Enhanced Results:")
    print(f"   Total interactions: {total_interactions}")
    print(f"   Exact accuracy: {overall_exact:.1f}% ({exact_matches}/{total_interactions})")
    print(f"   Family accuracy: {overall_family:.1f}% ({family_matches}/{total_interactions})")
    print(f"   Pattern alignment: {overall_pattern:.1f}% ({pattern_matches}/{total_interactions})")

    # ===== TRAJECTORY-AWARE ANALYSIS =====
    # Aggregate trajectory metrics across all scenarios
    trajectory_results = [r for r in results if 'final_exact' in r]
    if trajectory_results:
        # Group by scenario (assuming each scenario has same trajectory metrics)
        scenarios_with_trajectory = {}
        for result in trajectory_results:
            scenario_key = f"{result['user_profile']}_{result.get('stage', 'unknown')}"
            if scenario_key not in scenarios_with_trajectory:
                scenarios_with_trajectory[scenario_key] = result

        # Calculate trajectory metrics
        final_exact_rate = sum(r['final_exact'] for r in scenarios_with_trajectory.values()) / len(scenarios_with_trajectory) * 100
        final_family_rate = sum(r['final_family'] for r in scenarios_with_trajectory.values()) / len(scenarios_with_trajectory) * 100
        avg_weighted_family = sum(r['weighted_family_acc'] for r in scenarios_with_trajectory.values()) / len(scenarios_with_trajectory)
        avg_arc_similarity = sum(r['arc_similarity'] for r in scenarios_with_trajectory.values()) / len(scenarios_with_trajectory)

        print(f"\n🎯 TRAJECTORY-AWARE METRICS:")
        print(f"   Final-state exact accuracy: {final_exact_rate:.1f}%")
        print(f"   Final-state family accuracy: {final_family_rate:.1f}%")
        print(f"   Weighted family accuracy (late>early): {avg_weighted_family:.1f}%")
        print(f"   Average arc similarity: {avg_arc_similarity:.2f}")

        # Show some example emotional journeys
        print(f"\n🌀 EMOTIONAL JOURNEY EXAMPLES:")
        for i, (scenario_key, result) in enumerate(list(scenarios_with_trajectory.items())[:3]):
            expected_seq = result['expected_sequence']
            predicted_seq = result['predicted_sequence']
            similarity = result['arc_similarity']
            print(f"   {i+1}. Expected: {expected_seq} → Predicted: {predicted_seq} (sim: {similarity:.2f})")
    else:
        print(f"\n⚠️ No trajectory metrics available in results")
    
    # User profile comparison
    print(f"\n👥 Performance by User Profile:")
    for comparison in user_comparisons:
        print(f"   🎭 {comparison['user_profile']}:")
        print(f"      Patterns: {comparison['emotional_patterns']}")
        print(f"      Family accuracy: {comparison['family_accuracy']:.1f}%")
        print(f"      Pattern alignment: {comparison['pattern_accuracy']:.1f}%")
        print(f"      Interactions: {comparison['total_interactions']}")
    
    # Memory enhancement impact
    baseline_accuracy = 48.1  # From previous extended scenario testing
    memory_improvement = overall_family - baseline_accuracy
    
    print(f"\n🧠 Memory Enhancement Impact:")
    print(f"   Baseline accuracy (no memory): {baseline_accuracy:.1f}%")
    print(f"   Memory-enhanced accuracy: {overall_family:.1f}%")
    print(f"   Improvement: {memory_improvement:+.1f}%")
    
    # Get critic performance stats
    try:
        async with emotion_critic_service:
            critic_stats = emotion_critic_service.get_performance_stats()
            
            print(f"\n🎯 Memory-Enhanced Critic Performance:")
            print(f"   Total evaluations: {critic_stats['total_evaluations']}")
            print(f"   Avg processing time: {critic_stats['avg_processing_time_ms']:.1f}ms")
            print(f"   Recent accuracy: {critic_stats['recent_accuracy']:.2f}")
            print(f"   Current weights: L:{critic_stats['current_weights']['language_weight']:.3f}, "
                  f"P:{critic_stats['current_weights']['prosody_weight']:.3f}")
    
    except Exception as e:
        print(f"   ⚠️ Critic stats unavailable: {e}")
    
    # Success assessment with trajectory-aware metrics
    print(f"\n🚀 MEMORY-ENHANCED SYSTEM ASSESSMENT:")

    target_improvement = 65.0  # Target: improve from 48.1% to 65%

    # Enhanced assessment including trajectory metrics
    trajectory_bonus = 0
    if trajectory_results:
        # Bonus points for good final-state and arc similarity
        if final_family_rate >= 70.0:
            trajectory_bonus += 5
        if avg_arc_similarity >= 0.7:
            trajectory_bonus += 3
        if avg_weighted_family >= overall_family + 5:  # Late-turn improvement
            trajectory_bonus += 2

    effective_score = overall_family + trajectory_bonus

    if effective_score >= target_improvement:
        print("   ✅ EXCELLENT - Memory enhancement target achieved!")
        print(f"   🎯 Exceeded 65% target with {overall_family:.1f}% accuracy")
        if trajectory_bonus > 0:
            print(f"   🌟 Trajectory bonus: +{trajectory_bonus}% for emotional journey accuracy")
    elif overall_family >= 55.0:
        print("   ✅ VERY GOOD - Significant memory enhancement impact")
        print(f"   🎯 Strong progress toward 65% target")
        if trajectory_bonus > 0:
            print(f"   🌟 Trajectory bonus: +{trajectory_bonus}% for emotional journey tracking")
    elif memory_improvement > 5.0:
        print("   ⚠️ GOOD - Clear memory enhancement benefit")
        print(f"   🎯 {memory_improvement:+.1f}% improvement shows memory value")
    else:
        print("   ❌ NEEDS OPTIMIZATION - Memory integration requires refinement")

    print(f"\n📋 Memory-Enhanced Insights:")
    print(f"   • Memory context provides {memory_improvement:+.1f}% accuracy improvement")
    print(f"   • Pattern alignment: {overall_pattern:.1f}% (emotional consistency)")
    if trajectory_results:
        print(f"   • Final-state accuracy: {final_family_rate:.1f}% (conversation endings)")
        print(f"   • Weighted accuracy: {avg_weighted_family:.1f}% (late-turn emphasis)")
        print(f"   • Arc similarity: {avg_arc_similarity:.2f} (emotional journey shape)")
    print(f"   • User-specific emotional profiles working")
    print(f"   • Memory-informed critic evaluation active")
    print(f"   • Ready for personalized emotion intelligence deployment")

    # Production recommendations based on trajectory analysis
    if trajectory_results:
        print(f"\n🔧 TRAJECTORY-BASED RECOMMENDATIONS:")
        if avg_arc_similarity < 0.6:
            print("   • Consider improving emotional journey consistency")
            print("   • Review memory decay parameters for better arc tracking")
        if final_family_rate < overall_family:
            print("   • Final-state detection needs attention")
            print("   • Strengthen session-end emotion aggregation")
        if avg_weighted_family > overall_family + 10:
            print("   • Excellent late-turn performance - memory working well")
            print("   • Consider emphasizing recent context even more")
        else:
            print("   • Emotional journey tracking performing well")
            print("   • Memory-enhanced trajectory detection validated")


async def main():
    """Run memory-enhanced emotion detection test."""
    
    print("🧠 MEMORY-ENHANCED EMOTION DETECTION SYSTEM")
    print("=" * 80)
    print("Testing actor-critic system with user memory integration")
    
    # Test memory-enhanced emotion detection
    results, user_comparisons = await test_memory_enhanced_emotion_detection()
    
    # Analyze results
    await analyze_memory_enhanced_results(results, user_comparisons)
    
    print(f"\n✨ Memory-enhanced emotion detection test completed!")
    print(f"🧠 Personalized emotional intelligence system validated!")


if __name__ == "__main__":
    asyncio.run(main())
