#!/usr/bin/env python3
"""
Comprehensive test runner for the fast response system.
Tests all scenarios mentioned: email, jokes, flirting, recommendations, research, etc.
"""
import asyncio
import os
import sys
import time
import unittest
from unittest.mock import Mock, patch, AsyncMock

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')

import django
django.setup()

from chat.services.fast_response_service import FastResponseService


class MockUser:
    """Mock user for testing."""
    def __init__(self, personality='caringFriend', companion_name='<PERSON>', first_name='TestUser'):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = first_name
        self.id = f"test_{personality}"


class ComprehensiveFastResponseTests(unittest.TestCase):
    """Comprehensive tests for all scenarios."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.users = {
            'caring': <PERSON><PERSON><PERSON><PERSON>('caringFriend', 'Ella'),
            'playful': <PERSON>ck<PERSON><PERSON>('playfulCompanion', 'Luna'),
            'wise': MockUser('wiseMentor', 'Sage'),
            'romantic': MockUser('romanticPartner', 'Alex'),
            'therapist': MockUser('supportiveTherapist', 'Dr. Hope')
        }
    
    def test_agent_processing_detection_comprehensive(self):
        """Test comprehensive agent processing detection for all scenarios."""
        print("\n🧪 Testing Agent Processing Detection")
        print("=" * 50)
        
        # Test cases: (query, should_need_agent, scenario_type)
        test_cases = [
            # Simple conversational (should NOT need agents)
            ("Hello, how are you?", False, "greeting"),
            ("Tell me a joke", False, "humor"),
            ("You look beautiful today", False, "flirting"),
            ("I love you", False, "romantic"),
            ("Good morning!", False, "greeting"),
            ("How was your day?", False, "casual"),
            ("Thanks for helping me", False, "gratitude"),
            
            # Complex tasks (SHOULD need agents)
            ("Can you help me send an email to my boss?", True, "email_task"),
            ("I need restaurant recommendations in San Francisco", True, "recommendations"),
            ("Can you review this research paper on AI?", True, "research_review"),
            ("I need deep research on climate change impacts", True, "deep_research"),
            ("Help me solve this complex math problem", True, "problem_solving"),
            ("Analyze the pros and cons of remote work", True, "analysis"),
            ("Create a business plan for my startup", True, "planning"),
            ("Research the latest trends in machine learning", True, "research"),
            ("Compare different investment strategies", True, "comparison"),
            ("Help me debug this Python code", True, "technical_help"),
        ]
        
        service = FastResponseService(user=self.users['caring'])
        
        results = {'correct': 0, 'total': 0, 'errors': []}
        
        for query, expected, scenario in test_cases:
            result = service._needs_agent_processing(query)
            results['total'] += 1
            
            if result == expected:
                results['correct'] += 1
                status = "✅"
            else:
                status = "❌"
                results['errors'].append((query, expected, result, scenario))
            
            print(f"  {status} {scenario:15} | Agent: {result:5} | '{query[:40]}...'")
        
        accuracy = (results['correct'] / results['total']) * 100
        print(f"\n📊 Accuracy: {results['correct']}/{results['total']} ({accuracy:.1f}%)")
        
        if results['errors']:
            print("\n❌ Errors:")
            for query, expected, actual, scenario in results['errors']:
                print(f"  {scenario}: Expected {expected}, got {actual} for '{query}'")
        
        # Assert high accuracy
        self.assertGreater(accuracy, 85, f"Agent detection accuracy too low: {accuracy:.1f}%")
    
    def test_personality_prompts_all_scenarios(self):
        """Test personality prompts for all user types."""
        print("\n🎭 Testing Personality Prompts")
        print("=" * 50)
        
        for personality, user in self.users.items():
            service = FastResponseService(user=user)
            
            # Test basic prompt
            prompt = service._build_fast_system_prompt(None, False)
            
            # Verify companion name and personality traits
            self.assertIn(user.ai_companion_name, prompt)
            self.assertIn(user.first_name, prompt)
            
            print(f"  ✅ {personality:15} | {user.ai_companion_name:10} | Prompt generated")
            
            # Test with emotion context
            emotion_prompt = service._build_fast_system_prompt(
                {'primary_emotion': 'happy', 'intensity': 0.8}, False
            )
            self.assertIn('happy', emotion_prompt)
            self.assertIn('0.8', emotion_prompt)
            
            # Test with agent processing needed
            agent_prompt = service._build_fast_system_prompt(None, True)
            self.assertIn('deeper analysis', agent_prompt)
    
    def test_performance_metrics_tracking(self):
        """Test performance metrics tracking."""
        print("\n⚡ Testing Performance Metrics")
        print("=" * 50)
        
        service = FastResponseService(user=self.users['caring'])
        
        # Simulate various response times
        test_times = [
            (300.0, 250.0),  # Fast response
            (400.0, 350.0),  # Medium response
            (500.0, 450.0),  # At target
            (600.0, 550.0),  # Slow response
        ]
        
        for total_time, first_chunk_time in test_times:
            service._update_performance_metrics(total_time, first_chunk_time)
        
        stats = service.get_performance_stats()
        
        print(f"  📊 Total requests: {stats['requests']}")
        print(f"  ⚡ Avg first chunk: {stats['avg_first_chunk_time_ms']:.1f}ms")
        print(f"  🎯 Target: {stats['target_first_chunk_ms']}ms")
        print(f"  📈 Performance ratio: {stats['performance_ratio']:.2f}")
        print(f"  ✅ Target met: {stats['target_met']}")
        
        # Verify calculations
        expected_avg = (250 + 350 + 450 + 550) / 4  # 400ms
        self.assertEqual(stats['avg_first_chunk_time_ms'], expected_avg)
        self.assertEqual(stats['requests'], 4)
        self.assertAlmostEqual(stats['performance_ratio'], 450/400, places=2)
    
    @patch('chat.services.fast_response_service.ChatGroq')
    def test_mock_response_flow(self, mock_groq):
        """Test the complete response flow with mocked Groq."""
        print("\n🔄 Testing Response Flow")
        print("=" * 50)
        
        # Mock Groq response
        mock_chunk = Mock()
        mock_chunk.content = "Hello! I'm doing great, thanks for asking. How are you today?"
        
        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]
        
        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm
        
        service = FastResponseService(user=self.users['caring'])
        
        async def test_flow():
            responses = []
            start_time = time.time()
            
            async for chunk in service.process_query_fast(
                user_input="Hello, how are you?",
                user_id="test_user",
                streaming=True
            ):
                responses.append(chunk)
            
            elapsed = (time.time() - start_time) * 1000
            
            # Analyze responses
            response_chunks = [r for r in responses if r['type'] == 'response_chunk']
            complete_responses = [r for r in responses if r['type'] == 'response_complete']
            agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']
            
            print(f"  📝 Response chunks: {len(response_chunks)}")
            print(f"  ✅ Complete responses: {len(complete_responses)}")
            print(f"  🤖 Agent processing: {len(agent_processing)}")
            print(f"  ⏱️ Total time: {elapsed:.1f}ms")
            
            # Assertions
            self.assertGreater(len(response_chunks), 0)
            self.assertEqual(len(complete_responses), 1)
            self.assertEqual(len(agent_processing), 0)  # Simple greeting shouldn't need agents
            
            complete_response = complete_responses[0]
            self.assertEqual(complete_response['source'], 'fast_groq')
            self.assertFalse(complete_response['needs_agent_processing'])
            self.assertTrue(complete_response['is_final'])
        
        # Run the async test
        asyncio.run(test_flow())
    
    def test_scenario_specific_routing(self):
        """Test routing for specific scenarios."""
        print("\n🎯 Testing Scenario-Specific Routing")
        print("=" * 50)
        
        scenarios = [
            # (query, expected_agent_needed, scenario_name)
            ("Send an <NAME_EMAIL> about the meeting", True, "Email Task"),
            ("What's a good restaurant near me?", True, "Recommendations"),
            ("Tell me a funny joke about cats", False, "Humor Request"),
            ("You're so sweet, I love talking to you", False, "Flirting/Romance"),
            ("Review this research paper on quantum computing", True, "Research Review"),
            ("I need comprehensive analysis of market trends", True, "Deep Research"),
            ("Help me solve this differential equation", True, "Math Problem"),
            ("Good night, sweet dreams", False, "Casual Goodbye"),
            ("Plan a vacation to Japan with detailed itinerary", True, "Complex Planning"),
            ("What's your favorite color?", False, "Simple Question"),
        ]
        
        service = FastResponseService(user=self.users['wise'])
        
        for query, expected, scenario_name in scenarios:
            result = service._needs_agent_processing(query)
            status = "✅" if result == expected else "❌"
            agent_text = "Agent" if result else "Fast"
            
            print(f"  {status} {scenario_name:20} | {agent_text:5} | '{query[:35]}...'")
            
            self.assertEqual(result, expected, 
                f"Scenario '{scenario_name}' failed: expected {expected}, got {result}")


def run_comprehensive_tests():
    """Run all comprehensive tests."""
    print("🚀 Running Comprehensive Fast Response System Tests")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(ComprehensiveFastResponseTests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if result.wasSuccessful():
        print("✅ ALL TESTS PASSED!")
        print("🎉 Fast Response System is ready for production!")
        print("\n🎯 Key Features Validated:")
        print("  • ⚡ Fast response routing (≤450ms target)")
        print("  • 🎭 Personality-based responses")
        print("  • 🤖 Intelligent agent processing detection")
        print("  • 📧 Email task handling")
        print("  • 😄 Humor and casual conversation")
        print("  • 💕 Flirting and romantic interactions")
        print("  • 🔍 Recommendation requests")
        print("  • 📚 Research and analysis tasks")
        print("  • 🧮 Problem-solving scenarios")
        print("  • 📊 Performance metrics tracking")
    else:
        print("❌ SOME TESTS FAILED")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        
        if result.failures:
            print("\n❌ Failures:")
            for test, traceback in result.failures:
                print(f"  {test}: {traceback}")
        
        if result.errors:
            print("\n💥 Errors:")
            for test, traceback in result.errors:
                print(f"  {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
