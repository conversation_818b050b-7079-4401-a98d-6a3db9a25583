#!/usr/bin/env python3
"""
Generate a fresh long-lived JWT token for testing.
"""
import os
import sys
import django

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from authentication.models import User
from datetime import datetime

def generate_fresh_token():
    print("🎫 Generating Fresh Long-Lived JWT Token")
    print("=" * 50)
    
    # Get or create test user
    try:
        user = User.objects.get(email='<EMAIL>')
        print(f"👤 Using user: {user.email}")
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword',
            first_name='Test',
            last_name='User'
        )
        print(f"👤 Created user: {user.email}")
    
    # Generate fresh tokens
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    refresh_token = str(refresh)
    
    # Get expiration info
    access_exp = datetime.fromtimestamp(refresh.access_token.payload['exp'])
    now = datetime.now()
    hours_valid = (access_exp - now).total_seconds() / 3600
    
    print(f"\n✅ Fresh tokens generated!")
    print(f"⏰ Valid for: {hours_valid:.1f} hours ({hours_valid/24:.1f} days)")
    print(f"🕐 Expires at: {access_exp}")
    
    print(f"\n🔑 ACCESS TOKEN (copy this for your frontend):")
    print(f"{access_token}")
    
    print(f"\n🔄 REFRESH TOKEN (for token refresh):")
    print(f"{refresh_token}")
    
    print(f"\n📋 WebSocket URL with token:")
    print(f"ws://localhost:8000/ws/chat/?token={access_token}")
    
    print(f"\n💡 Instructions:")
    print(f"1. Copy the ACCESS TOKEN above")
    print(f"2. Use it in your frontend authentication")
    print(f"3. This token will be valid for 30 days")
    print(f"4. If you still get expiration errors, restart your Django server")

if __name__ == "__main__":
    generate_fresh_token()
