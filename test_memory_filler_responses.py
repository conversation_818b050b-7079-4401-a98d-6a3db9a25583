#!/usr/bin/env python3
"""
Test memory-based filler responses in the fast response system.
Validates that the system uses personal memories to create engaging conversation
while processing complex requests in the background.
"""
import asyncio
import os
import sys
import time
from unittest.mock import Mock, patch, AsyncMock

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')

import django
django.setup()

from chat.services.fast_response_service import FastResponseService


class MockUser:
    """Mock user for testing."""
    def __init__(self, personality='caringFriend', companion_name='<PERSON>', first_name='TestUser'):
        self.selected_personality = personality
        self.ai_companion_name = companion_name
        self.first_name = first_name
        self.id = f"test_{personality}"


async def test_memory_based_filler_responses():
    """Test memory-based filler responses comprehensively."""
    print("🧠 Testing Memory-Based Filler Responses")
    print("=" * 60)
    
    user = MockUser('caringFriend', 'Ella')
    service = FastResponseService(user=user)
    
    if not service.memory_manager:
        print("❌ Memory manager not available - skipping memory tests")
        return False
    
    # Set up test memories
    print("\n📝 Setting up test memories...")
    
    test_memories = [
        {
            "text": "User works as a software engineer at a tech startup called InnovateTech",
            "type": "semantic_profile",
            "importance": 0.8,
            "personalness": 0.7,
            "actionability": 0.3
        },
        {
            "text": "User mentioned being excited about their new machine learning project last week",
            "type": "episodic_summary", 
            "importance": 0.7,
            "personalness": 0.8,
            "actionability": 0.6
        },
        {
            "text": "User enjoys hiking in the mountains and landscape photography",
            "type": "semantic_profile",
            "importance": 0.6,
            "personalness": 0.9,
            "actionability": 0.2
        },
        {
            "text": "Had a conversation about planning a trip to Japan for cherry blossom season",
            "type": "episodic_summary",
            "importance": 0.6,
            "personalness": 0.7,
            "actionability": 0.5
        },
        {
            "text": "User is learning to cook Italian cuisine and loves pasta dishes",
            "type": "semantic_profile",
            "importance": 0.5,
            "personalness": 0.8,
            "actionability": 0.3
        },
        {
            "text": "User mentioned feeling stressed about a work deadline last month",
            "type": "episodic_summary",
            "importance": 0.7,
            "personalness": 0.6,
            "actionability": 0.8
        }
    ]
    
    # Store memories
    for memory in test_memories:
        service.memory_manager.store_memory(
            text=memory["text"],
            memory_type=memory["type"],
            user_id="test_memory_user",
            importance_score=memory["importance"],
            personalness_score=memory["personalness"],
            actionability_score=memory["actionability"]
        )
    
    print(f"   ✅ Stored {len(test_memories)} test memories")
    
    # Test 1: Memory retrieval
    print("\n🔍 Test 1: Memory Retrieval")
    memories = service._get_contextual_memories("test_memory_user", limit=3)
    
    print(f"   📊 Retrieved {len(memories)} contextual memories")
    for i, memory in enumerate(memories, 1):
        mem_type = memory.get('memory_type', 'unknown')
        text = memory.get('text', '')[:50] + "..."
        importance = memory.get('importance_score', 0)
        print(f"   {i}. [{mem_type}] {text} (importance: {importance:.1f})")
    
    if len(memories) == 0:
        print("   ❌ No memories retrieved")
        return False
    else:
        print("   ✅ Memory retrieval working")
    
    # Test 2: System prompt enhancement
    print("\n📝 Test 2: System Prompt Enhancement")
    
    # Without agent processing (should not include memories)
    prompt_simple = service._build_fast_system_prompt(
        emotion_context=None,
        needs_agent=False,
        user_id="test_memory_user"
    )
    
    # With agent processing (should include memories)
    prompt_agent = service._build_fast_system_prompt(
        emotion_context=None,
        needs_agent=True,
        user_id="test_memory_user"
    )
    
    print(f"   📏 Simple prompt length: {len(prompt_simple)} chars")
    print(f"   📏 Agent prompt length: {len(prompt_agent)} chars")
    
    # Check for memory context in agent prompt
    has_memory_context = 'CONTEXTUAL MEMORIES' in prompt_agent
    has_examples = 'EXAMPLES of good filler responses' in prompt_agent
    
    print(f"   🧠 Contains memory context: {has_memory_context}")
    print(f"   💡 Contains filler examples: {has_examples}")
    
    if has_memory_context and has_examples:
        print("   ✅ System prompt enhancement working")
    else:
        print("   ❌ System prompt enhancement failed")
        return False
    
    # Test 3: Memory content in prompts
    print("\n🎯 Test 3: Memory Content Integration")
    
    # Check if specific memory content appears in prompt
    prompt_lower = prompt_agent.lower()
    memory_indicators = [
        'software', 'engineer', 'tech', 'startup',
        'hiking', 'photography', 'mountains',
        'machine learning', 'project',
        'japan', 'travel', 'trip',
        'cooking', 'italian', 'pasta'
    ]
    
    found_indicators = [indicator for indicator in memory_indicators if indicator in prompt_lower]
    
    print(f"   🔍 Memory indicators found: {len(found_indicators)}/{len(memory_indicators)}")
    print(f"   📋 Found: {', '.join(found_indicators[:5])}{'...' if len(found_indicators) > 5 else ''}")
    
    if len(found_indicators) >= 2:
        print("   ✅ Memory content successfully integrated")
    else:
        print("   ❌ Insufficient memory content integration")
        return False
    
    # Test 4: Mock response flow with memory context
    print("\n🔄 Test 4: Memory-Enhanced Response Flow")
    
    with patch('chat.services.fast_response_service.ChatGroq') as mock_groq:
        # Mock a response that could reference memories
        mock_chunk = Mock()
        mock_chunk.content = "I'll analyze that business plan for you! By the way, how's your machine learning project at InnovateTech going?"
        
        mock_stream = AsyncMock()
        mock_stream.__aiter__.return_value = [mock_chunk]
        
        mock_llm = Mock()
        mock_llm.astream.return_value = mock_stream
        mock_groq.return_value = mock_llm
        
        responses = []
        start_time = time.time()
        
        async for chunk in service.process_query_fast(
            user_input="Can you analyze this business plan and give me detailed feedback?",
            user_id="test_memory_user",
            streaming=True
        ):
            responses.append(chunk)
        
        elapsed = (time.time() - start_time) * 1000
        
        # Analyze responses
        response_chunks = [r for r in responses if r['type'] == 'response_chunk']
        complete_responses = [r for r in responses if r['type'] == 'response_complete']
        agent_processing = [r for r in responses if r['type'] == 'agent_processing_started']
        
        print(f"   ⚡ Response time: {elapsed:.1f}ms")
        print(f"   📝 Response chunks: {len(response_chunks)}")
        print(f"   ✅ Complete responses: {len(complete_responses)}")
        print(f"   🤖 Agent processing triggered: {len(agent_processing) > 0}")
        
        # Check that system prompt was called with memory context
        if mock_llm.astream.called:
            call_args = mock_llm.astream.call_args[0][0]
            system_message = call_args[0].content
            
            has_memory_in_call = 'CONTEXTUAL MEMORIES' in system_message
            print(f"   🧠 Memory context in LLM call: {has_memory_in_call}")
            
            if has_memory_in_call:
                print("   ✅ Memory-enhanced response flow working")
            else:
                print("   ❌ Memory context not passed to LLM")
                return False
        else:
            print("   ❌ LLM not called")
            return False
    
    # Test 5: Different query types
    print("\n🎭 Test 5: Different Query Types")
    
    query_tests = [
        ("Send an email to my boss about the project update", True, "Email Task"),
        ("What's a good restaurant for Italian food?", True, "Recommendation"),
        ("Tell me a joke about programming", False, "Humor"),
        ("Help me solve this complex math equation", True, "Problem Solving"),
        ("You're amazing, I love talking to you", False, "Romantic/Casual"),
        ("Research the latest AI trends for my presentation", True, "Research Task")
    ]
    
    for query, should_need_agent, query_type in query_tests:
        needs_agent = service._needs_agent_processing(query)
        status = "✅" if needs_agent == should_need_agent else "❌"
        
        if needs_agent:
            # Test memory integration for agent-requiring queries
            prompt = service._build_fast_system_prompt(
                emotion_context=None,
                needs_agent=True,
                user_id="test_memory_user"
            )
            has_memories = 'CONTEXTUAL MEMORIES' in prompt
            memory_status = "🧠" if has_memories else "📝"
        else:
            memory_status = "⚡"  # Fast response, no memories needed
        
        print(f"   {status} {memory_status} {query_type:15} | Agent: {needs_agent}")
    
    print("\n" + "=" * 60)
    print("🎉 Memory-Based Filler Response Tests Complete!")
    print("\n✅ All tests passed! The system now provides:")
    print("  • 🧠 Memory-enhanced filler responses")
    print("  • 🎯 Contextual conversation continuations")
    print("  • ⚡ Fast acknowledgment with personal touch")
    print("  • 🤖 Background processing with engaging wait experience")
    
    return True


async def test_memory_examples():
    """Test specific examples of memory-based filler responses."""
    print("\n💬 Testing Specific Memory-Based Examples")
    print("=" * 50)
    
    user = MockUser('wiseMentor', 'Sage')
    service = FastResponseService(user=user)
    
    if not service.memory_manager:
        print("❌ Memory manager not available")
        return
    
    # Add specific scenario memories
    scenarios = [
        {
            "memory": "User is preparing for a job interview at Google next week",
            "query": "Can you help me research Google's company culture?",
            "expected_filler": "job interview"
        },
        {
            "memory": "User's daughter just started college and is studying biology",
            "query": "Analyze this research paper on genetics for me",
            "expected_filler": "daughter"
        },
        {
            "memory": "User recently bought a new house and is renovating the kitchen",
            "query": "Help me create a budget plan for home improvements",
            "expected_filler": "kitchen renovation"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 Scenario {i}: {scenario['expected_filler']}")
        
        # Store the memory
        service.memory_manager.store_memory(
            text=scenario["memory"],
            memory_type="episodic_summary",
            user_id=f"scenario_user_{i}",
            importance_score=0.8,
            personalness_score=0.9,
            actionability_score=0.7
        )
        
        # Generate prompt with memory context
        prompt = service._build_fast_system_prompt(
            emotion_context=None,
            needs_agent=True,
            user_id=f"scenario_user_{i}"
        )
        
        # Check if the memory context is included
        prompt_lower = prompt.lower()
        memory_keywords = scenario["memory"].lower().split()
        found_keywords = [kw for kw in memory_keywords if len(kw) > 3 and kw in prompt_lower]
        
        print(f"   📝 Query: {scenario['query']}")
        print(f"   🧠 Memory: {scenario['memory']}")
        print(f"   🔍 Keywords found in prompt: {len(found_keywords)}")
        
        if len(found_keywords) >= 1:
            print(f"   ✅ Memory context successfully integrated")
        else:
            print(f"   ⚠️ Limited memory integration")


if __name__ == "__main__":
    async def main():
        success = await test_memory_based_filler_responses()
        await test_memory_examples()
        return success
    
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
