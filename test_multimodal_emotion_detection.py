#!/usr/bin/env python3
"""
Multi-Modal Emotion Detection Test
Tests the enhanced expression measurement service with both audio and text.
"""
import os
import sys
import django
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple

# Setup Django
sys.path.append('/Users/<USER>/dev/pythonprojects/ellahai-backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ellahai_backend.settings')
django.setup()

from authentication.models import User
from asgiref.sync import sync_to_async
from chat.services.expression_measurement_service import expression_measurement_service

# Test scenarios with audio and corresponding text
TEST_SCENARIOS = [
    {
        "name": "anger_multimodal",
        "audio_file": "expressive_audio_library/07_anger.mp3",
        "text": "This is absolutely unacceptable!",
        "expected_emotion": "anger"
    },
    {
        "name": "joy_multimodal",
        "audio_file": "expressive_audio_library/01_joy.mp3",
        "text": "I just got the best news ever!",
        "expected_emotion": "joy"
    },
    {
        "name": "anxiety_multimodal",
        "audio_file": "expressive_audio_library/11_anxiety.mp3",
        "text": "I'm so worried about tomorrow.",
        "expected_emotion": "anxiety"
    },
    {
        "name": "gratitude_multimodal",
        "audio_file": "expressive_audio_library/03_gratitude.mp3",
        "text": "Thank you so much for helping me.",
        "expected_emotion": "gratitude"
    },
    {
        "name": "disappointment_multimodal",
        "audio_file": "expressive_audio_library/06_disappointment.mp3",
        "text": "This didn't work out at all.",
        "expected_emotion": "disappointment"
    },
    {
        "name": "excitement_multimodal",
        "audio_file": "expressive_audio_library/02_excitement.mp3",
        "text": "This is going to be amazing!",
        "expected_emotion": "excitement"
    },
    {
        "name": "fear_multimodal",
        "audio_file": "expressive_audio_library/10_fear.mp3",
        "text": "I'm really scared about this.",
        "expected_emotion": "fear"
    },
    {
        "name": "pride_multimodal",
        "audio_file": "expressive_audio_library/21_pride.mp3",
        "text": "I'm really proud of this achievement.",
        "expected_emotion": "pride"
    }
]


async def setup_test_user() -> User:
    """Setup test user."""
    try:
        user = await sync_to_async(User.objects.get)(email="<EMAIL>")
    except User.DoesNotExist:
        user = await sync_to_async(User.objects.create_user)(
            username="<EMAIL>",
            email="<EMAIL>",
            password="testpass123"
        )
    return user


async def test_single_scenario(scenario: Dict, user_id: str) -> Dict:
    """Test a single multi-modal scenario."""
    print(f"\n🎭 TESTING: {scenario['name']}")
    print(f"   Audio: {scenario['audio_file']}")
    print(f"   Text: '{scenario['text']}'")
    print(f"   Expected: {scenario['expected_emotion']}")
    
    # Load audio file
    audio_path = Path(scenario['audio_file'])
    if not audio_path.exists():
        print(f"   ❌ Audio file not found: {audio_path}")
        return None
    
    try:
        with open(audio_path, 'rb') as f:
            audio_data = f.read()
    except Exception as e:
        print(f"   ❌ Failed to load audio: {e}")
        return None
    
    session_id = f"multimodal_test_{int(time.time())}"
    chunk_id = f"{session_id}_{scenario['name']}"
    
    # Test 1: Audio-only analysis
    print(f"\n   🎵 AUDIO-ONLY ANALYSIS:")
    start_time = time.time()
    
    audio_only_result = await expression_measurement_service.analyze_audio_chunk(
        audio_data, f"{chunk_id}_audio_only", session_id, user_id, text=None
    )
    
    audio_only_time = (time.time() - start_time) * 1000
    
    if audio_only_result:
        audio_match = audio_only_result.dominant_emotion.lower() == scenario['expected_emotion'].lower()
        match_icon = "✅" if audio_match else "❌"
        print(f"      {match_icon} Result: {audio_only_result.dominant_emotion} ({audio_only_result.confidence:.3f}) - {audio_only_time:.1f}ms")
        
        # Show top 3 emotions
        print(f"      📊 Top 3:")
        for i, emotion in enumerate(audio_only_result.emotions[:3]):
            name = emotion.get('name', 'Unknown')
            score = emotion.get('score', 0)
            print(f"         {i+1}. {name}: {score:.3f}")
    else:
        print(f"      ❌ No result - {audio_only_time:.1f}ms")
        audio_match = False
    
    # Test 2: Multi-modal analysis (audio + text)
    print(f"\n   🎭 MULTI-MODAL ANALYSIS (Audio + Text):")
    start_time = time.time()
    
    multimodal_result = await expression_measurement_service.analyze_audio_chunk(
        audio_data, f"{chunk_id}_multimodal", session_id, user_id, text=scenario['text']
    )
    
    multimodal_time = (time.time() - start_time) * 1000
    
    if multimodal_result:
        multimodal_match = multimodal_result.dominant_emotion.lower() == scenario['expected_emotion'].lower()
        match_icon = "✅" if multimodal_match else "❌"
        print(f"      {match_icon} Result: {multimodal_result.dominant_emotion} ({multimodal_result.confidence:.3f}) - {multimodal_time:.1f}ms")
        
        # Show top 3 emotions with modality breakdown if available
        print(f"      📊 Top 3:")
        for i, emotion in enumerate(multimodal_result.emotions[:3]):
            name = emotion.get('name', 'Unknown')
            score = emotion.get('score', 0)
            prosody_score = emotion.get('prosody_score', 'N/A')
            language_score = emotion.get('language_score', 'N/A')
            
            if prosody_score != 'N/A' and language_score != 'N/A':
                print(f"         {i+1}. {name}: {score:.3f} (prosody: {prosody_score:.3f}, language: {language_score:.3f})")
            else:
                print(f"         {i+1}. {name}: {score:.3f}")
    else:
        print(f"      ❌ No result - {multimodal_time:.1f}ms")
        multimodal_match = False
    
    # Compare results
    print(f"\n   📈 COMPARISON:")
    if audio_only_result and multimodal_result:
        confidence_improvement = multimodal_result.confidence - audio_only_result.confidence
        improvement_icon = "📈" if confidence_improvement > 0 else "📉" if confidence_improvement < 0 else "➡️"
        print(f"      {improvement_icon} Confidence change: {confidence_improvement:+.3f}")
        
        accuracy_improvement = multimodal_match and not audio_match
        if accuracy_improvement:
            print(f"      ✅ Multi-modal improved accuracy!")
        elif audio_match and multimodal_match:
            print(f"      ✅ Both methods accurate")
        elif not audio_match and not multimodal_match:
            print(f"      ⚠️ Both methods missed target emotion")
        else:
            print(f"      ⚠️ Audio-only was more accurate")
    
    return {
        'scenario_name': scenario['name'],
        'expected_emotion': scenario['expected_emotion'],
        'audio_only': {
            'emotion': audio_only_result.dominant_emotion if audio_only_result else None,
            'confidence': audio_only_result.confidence if audio_only_result else 0,
            'match': audio_match if audio_only_result else False,
            'time_ms': audio_only_time
        },
        'multimodal': {
            'emotion': multimodal_result.dominant_emotion if multimodal_result else None,
            'confidence': multimodal_result.confidence if multimodal_result else 0,
            'match': multimodal_match if multimodal_result else False,
            'time_ms': multimodal_time
        },
        'improvement': {
            'confidence_delta': (multimodal_result.confidence - audio_only_result.confidence) if (multimodal_result and audio_only_result) else 0,
            'accuracy_improved': multimodal_match and not audio_match if (multimodal_result and audio_only_result) else False
        }
    }


async def test_multimodal_emotion_detection():
    """Test multi-modal emotion detection across scenarios."""
    print("🎯 MULTI-MODAL EMOTION DETECTION TEST")
    print("=" * 70)
    print("Testing audio + text vs audio-only emotion detection")
    
    user = await setup_test_user()
    user_id = str(user.id)
    
    results = []
    
    for scenario in TEST_SCENARIOS:
        try:
            result = await test_single_scenario(scenario, user_id)
            if result:
                results.append(result)
        except Exception as e:
            print(f"❌ Error testing {scenario['name']}: {e}")
            continue
        
        # Small delay between tests
        await asyncio.sleep(0.5)
    
    return results


def analyze_multimodal_improvements(results: List[Dict]) -> Dict:
    """Analyze improvements from multi-modal detection."""
    if not results:
        return {}
    
    total_tests = len(results)
    audio_only_correct = sum(1 for r in results if r['audio_only']['match'])
    multimodal_correct = sum(1 for r in results if r['multimodal']['match'])
    
    confidence_improvements = [r['improvement']['confidence_delta'] for r in results if r['improvement']['confidence_delta'] != 0]
    accuracy_improvements = sum(1 for r in results if r['improvement']['accuracy_improved'])
    
    avg_confidence_improvement = sum(confidence_improvements) / len(confidence_improvements) if confidence_improvements else 0
    
    return {
        'total_tests': total_tests,
        'audio_only_accuracy': (audio_only_correct / total_tests * 100) if total_tests > 0 else 0,
        'multimodal_accuracy': (multimodal_correct / total_tests * 100) if total_tests > 0 else 0,
        'accuracy_improvement': ((multimodal_correct - audio_only_correct) / total_tests * 100) if total_tests > 0 else 0,
        'avg_confidence_improvement': avg_confidence_improvement,
        'scenarios_with_accuracy_improvement': accuracy_improvements
    }


async def main():
    """Run comprehensive multi-modal emotion detection test."""
    print("🎯 COMPREHENSIVE MULTI-MODAL EMOTION DETECTION TEST")
    print("=" * 80)
    
    # Test all scenarios
    results = await test_multimodal_emotion_detection()
    
    if not results:
        print("❌ No results to analyze")
        return
    
    # Analyze improvements
    analysis = analyze_multimodal_improvements(results)
    
    print(f"\n🏆 MULTI-MODAL ANALYSIS RESULTS")
    print("=" * 80)
    
    print(f"📊 Overall Performance:")
    print(f"   Total tests: {analysis['total_tests']}")
    print(f"   Audio-only accuracy: {analysis['audio_only_accuracy']:.1f}%")
    print(f"   Multi-modal accuracy: {analysis['multimodal_accuracy']:.1f}%")
    print(f"   Accuracy improvement: {analysis['accuracy_improvement']:+.1f}%")
    print(f"   Avg confidence improvement: {analysis['avg_confidence_improvement']:+.3f}")
    print(f"   Scenarios with accuracy improvement: {analysis['scenarios_with_accuracy_improvement']}")
    
    # Show detailed results
    print(f"\n📈 Detailed Results:")
    for result in results:
        name = result['scenario_name']
        expected = result['expected_emotion']
        
        audio_emotion = result['audio_only']['emotion']
        audio_conf = result['audio_only']['confidence']
        audio_match = "✅" if result['audio_only']['match'] else "❌"
        
        multi_emotion = result['multimodal']['emotion']
        multi_conf = result['multimodal']['confidence']
        multi_match = "✅" if result['multimodal']['match'] else "❌"
        
        conf_delta = result['improvement']['confidence_delta']
        improvement_icon = "📈" if conf_delta > 0 else "📉" if conf_delta < 0 else "➡️"
        
        print(f"   {name}:")
        print(f"      Expected: {expected}")
        print(f"      Audio-only: {audio_emotion} ({audio_conf:.3f}) {audio_match}")
        print(f"      Multi-modal: {multi_emotion} ({multi_conf:.3f}) {multi_match}")
        print(f"      {improvement_icon} Confidence Δ: {conf_delta:+.3f}")
    
    # Final assessment
    print(f"\n🎯 PRODUCTION ASSESSMENT:")
    if analysis['accuracy_improvement'] > 10:
        print("   ✅ EXCELLENT - Multi-modal provides significant improvement")
    elif analysis['accuracy_improvement'] > 0:
        print("   ✅ GOOD - Multi-modal provides measurable improvement")
    elif analysis['avg_confidence_improvement'] > 0.05:
        print("   ⚠️ FAIR - Multi-modal improves confidence")
    else:
        print("   ❌ NEEDS WORK - Multi-modal shows limited benefit")
    
    print(f"\n✨ Multi-modal emotion detection test completed!")
    print(f"Tested {analysis['total_tests']} scenarios with audio + text analysis.")


if __name__ == "__main__":
    asyncio.run(main())
