# Authentication System - Frontend Compatibility

## ✅ Authentication Endpoints Setup Complete

The backend authentication system has been configured to match your Flutter frontend requirements exactly.

### 🔐 Available Endpoints

#### Authentication Endpoints
- **POST** `/api/auth/register/` - User registration
- **POST** `/api/auth/login/` - User login  
- **POST** `/api/auth/refresh/` - Token refresh
- **POST** `/api/auth/logout/` - User logout
- **POST** `/api/auth/google/` - Google OAuth authentication
- **POST** `/api/auth/apple/` - Apple Sign In authentication

#### User Management Endpoints  
- **GET** `/api/user/profile/` - Get user profile
- **PUT** `/api/user/profile/` - Update user profile

### 📋 Request/Response Formats

#### Registration Request
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "username": "johndo<PERSON>"
}
```

#### Registration/Login Response
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "username": "johndoe",
    "first_name": "John",
    "last_name": "Doe",
    // ... other user fields
  },
  "tokens": {
    "access": "jwt_access_token",
    "refresh": "jwt_refresh_token"
  },
  "message": "Registration successful"
}
```

#### Token Refresh Request
```json
{
  "refresh": "jwt_refresh_token"
}
```

#### Token Refresh Response
```json
{
  "access": "new_jwt_access_token"
}
```

#### Google Auth Request
```json
{
  "id_token": "google_id_token"
}
```

#### Apple Auth Request
```json
{
  "identity_token": "apple_identity_token",
  "authorization_code": "apple_auth_code",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe"
}
```

#### Logout Request
```json
{
  "refresh": "jwt_refresh_token"
}
```

### 🔧 Key Features Implemented

1. **JWT Token Authentication**
   - Access tokens for API authentication
   - Refresh tokens for token renewal
   - Automatic token blacklisting on logout

2. **Social Authentication**
   - Google OAuth integration
   - Apple Sign In integration
   - Automatic user creation for new social logins

3. **User Profile Management**
   - Complete user profile retrieval
   - Profile updates with validation
   - User preferences management

4. **Security Features**
   - Password validation
   - Token expiration handling
   - Secure token storage recommendations

5. **Session Management**
   - User session tracking
   - Device type detection
   - IP address logging

### 🎯 Frontend Integration Notes

Your Flutter `AuthService` class is fully compatible with this backend setup:

1. **Token Storage**: The backend returns tokens in the exact format your frontend expects
2. **Automatic Refresh**: The interceptor in your frontend will work seamlessly with `/api/auth/refresh/`
3. **Error Handling**: 401 responses trigger automatic token refresh as expected
4. **Social Auth**: Google and Apple endpoints accept the exact parameters your frontend sends

### 🔄 Token Refresh Flow

1. Frontend makes authenticated request with access token
2. If token expired (401 response), frontend automatically calls `/api/auth/refresh/`
3. Backend returns new access token
4. Frontend retries original request with new token
5. If refresh fails, user is logged out

### 📱 User Profile Fields

The user model includes all fields your frontend needs:
- Basic info (name, email, username)
- Preferences (language, timezone, voice settings)
- AI companion settings (personality, name)
- Privacy settings (data collection, personalization)
- Gamification data (environments, outfits, pets)

### ✅ Testing

All endpoints have been tested and are working correctly:
- User registration creates accounts with proper validation
- Login returns JWT tokens in expected format
- Token refresh works with existing refresh tokens
- Profile endpoints require authentication and work properly
- Social auth endpoints are structured correctly

### 🚀 Next Steps

Your Flutter app should now be able to:
1. Register new users
2. Login existing users  
3. Automatically refresh tokens
4. Access protected endpoints
5. Manage user profiles
6. Handle social authentication

The authentication system is production-ready and matches your frontend requirements exactly.
