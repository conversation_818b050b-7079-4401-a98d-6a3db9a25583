from django.test import TestCase
from django.test import TestCase
from django.contrib.auth import get_user_model
from gamification.models import UserProgress, Wallet
from gamification.serializers import UserProgressSerializer, WalletSerializer

User = get_user_model()


class UserProgressSerializerTests(TestCase):
    """Test the UserProgressSerializer."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.progress = UserProgress.objects.create(
            user=self.user,
            xp=50,
            level=2,
            hearts=10,
            messages_count=25,
            voice_messages_count=5,
            achievements=["achievement1", "achievement2"],
            total_time_spent=3600,
            xp_to_next_level=200
        )
        self.serializer = UserProgressSerializer(instance=self.progress)
    
    def test_contains_expected_fields(self):
        """Test that the serializer contains the expected fields."""
        data = self.serializer.data
        expected_fields = [
            'xp', 'level', 'hearts', 'messages_count', 'voice_messages_count',
            'achievements', 'total_time_spent', 'xp_to_next_level',
            'progress_percentage', 'created_at', 'updated_at'
        ]
        self.assertEqual(set(data.keys()), set(expected_fields))
    
    def test_progress_percentage_calculation(self):
        """Test that progress_percentage is calculated correctly."""
        data = self.serializer.data
        # 50 XP out of 200 XP to next level = 25%
        self.assertEqual(data['progress_percentage'], 25.0)
    
    def test_zero_xp_to_next_level(self):
        """Test that progress_percentage is 100 when xp_to_next_level is 0."""
        self.progress.xp_to_next_level = 0
        self.progress.save()
        serializer = UserProgressSerializer(instance=self.progress)
        data = serializer.data
        self.assertEqual(data['progress_percentage'], 100)


class WalletSerializerTests(TestCase):
    """Test the WalletSerializer."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.wallet = Wallet.objects.create(
            user=self.user,
            balance=100,
            lifetime_earnings=150
        )
        self.serializer = WalletSerializer(instance=self.wallet)
    
    def test_contains_expected_fields(self):
        """Test that the serializer contains the expected fields."""
        data = self.serializer.data
        expected_fields = [
            'balance', 'lifetime_earnings', 'created_at', 'updated_at'
        ]
        self.assertEqual(set(data.keys()), set(expected_fields))