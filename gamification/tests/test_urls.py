from django.test import TestCase
from django.test import TestCase
from django.urls import reverse, resolve
from gamification import views


class GamificationUrlsTests(TestCase):
    """Test the gamification app URLs."""
    
    def test_user_level_url(self):
        """Test the user-level URL."""
        url = reverse('gamification:user-level')
        self.assertEqual(url, '/api/gamification/level/')
        self.assertEqual(resolve(url).func.view_class, views.UserLevelView)
    
    def test_user_progress_url(self):
        """Test the user-progress URL."""
        url = reverse('gamification:user-progress')
        self.assertEqual(url, '/api/gamification/progress/')
        if hasattr(views, 'UserProgressView'):
            self.assertEqual(resolve(url).func.view_class, views.UserProgressView)
        else:
            self.assertEqual(resolve(url).func.view_class, views.UserLevelView)
    
    def test_achievement_list_url(self):
        """Test the achievement-list URL."""
        url = reverse('gamification:achievement-list')
        self.assertEqual(url, '/api/gamification/achievements/')
        self.assertEqual(resolve(url).func.view_class, views.AchievementListView)
    
    def test_user_achievement_list_url(self):
        """Test the user-achievement-list URL."""
        url = reverse('gamification:user-achievement-list')
        self.assertEqual(url, '/api/gamification/achievements/user/')
        self.assertEqual(resolve(url).func.view_class, views.UserAchievementListView)
    
    def test_unlock_achievement_url(self):
        """Test the unlock-achievement URL."""
        url = reverse('gamification:unlock-achievement')
        self.assertEqual(url, '/api/gamification/achievements/unlock/')
        # The function is wrapped by api_view decorator, so it's name is 'view'
        self.assertEqual(resolve(url).func.__name__, 'view')
    
    def test_achievement_progress_url(self):
        """Test the achievement-progress URL."""
        url = reverse('gamification:achievement-progress')
        self.assertEqual(url, '/api/gamification/achievements/progress/')
    
    def test_user_wallet_url(self):
        """Test the user-wallet URL."""
        url = reverse('gamification:user-wallet')
        self.assertEqual(url, '/api/gamification/wallet/')
        self.assertEqual(resolve(url).func.view_class, views.UserWalletView)
    
    def test_wallet_url(self):
        """Test the wallet URL."""
        url = reverse('gamification:wallet')
        self.assertEqual(url, '/api/gamification/wallet/new/')
        if hasattr(views, 'WalletView'):
            self.assertEqual(resolve(url).func.view_class, views.WalletView)
        else:
            self.assertEqual(resolve(url).func.view_class, views.UserWalletView)
    
    def test_shop_item_list_url(self):
        """Test the shop-item-list URL."""
        url = reverse('gamification:shop-item-list')
        self.assertEqual(url, '/api/gamification/shop/')
        self.assertEqual(resolve(url).func.view_class, views.ShopItemListView)
    
    def test_purchase_item_url(self):
        """Test the purchase-item URL."""
        url = reverse('gamification:purchase-item')
        self.assertEqual(url, '/api/gamification/shop/purchase/')
    
    def test_preview_item_url(self):
        """Test the preview-item URL."""
        url = reverse('gamification:preview-item', args=['00000000-0000-0000-0000-000000000000'])
        self.assertEqual(url, '/api/gamification/shop/preview/00000000-0000-0000-0000-000000000000/')
    
    def test_user_inventory_url(self):
        """Test the user-inventory URL."""
        url = reverse('gamification:user-inventory')
        self.assertEqual(url, '/api/gamification/inventory/')
        self.assertEqual(resolve(url).func.view_class, views.UserInventoryListView)
    
    def test_inventory_summary_url(self):
        """Test the inventory-summary URL."""
        url = reverse('gamification:inventory-summary')
        self.assertEqual(url, '/api/gamification/inventory/summary/')
    
    def test_equip_item_url(self):
        """Test the equip-item URL."""
        url = reverse('gamification:equip-item', args=['00000000-0000-0000-0000-000000000000'])
        self.assertEqual(url, '/api/gamification/inventory/equip/00000000-0000-0000-0000-000000000000/')
    
    def test_user_daily_streak_url(self):
        """Test the user-daily-streak URL."""
        url = reverse('gamification:user-daily-streak')
        self.assertEqual(url, '/api/gamification/streak/')
        self.assertEqual(resolve(url).func.view_class, views.UserDailyStreakView)
    
    def test_daily_reward_list_url(self):
        """Test the daily-reward-list URL."""
        url = reverse('gamification:daily-reward-list')
        self.assertEqual(url, '/api/gamification/daily-rewards/')
        self.assertEqual(resolve(url).func.view_class, views.DailyRewardListView)
    
    def test_daily_reward_status_url(self):
        """Test the daily-reward-status URL."""
        url = reverse('gamification:daily-reward-status')
        self.assertEqual(url, '/api/gamification/daily-rewards/status/')
    
    def test_claim_daily_reward_url(self):
        """Test the claim-daily-reward URL."""
        url = reverse('gamification:claim-daily-reward')
        self.assertEqual(url, '/api/gamification/daily-rewards/claim/')
    
    def test_gamification_stats_url(self):
        """Test the gamification-stats URL."""
        url = reverse('gamification:gamification-stats')
        self.assertEqual(url, '/api/gamification/stats/')
    
    def test_leaderboard_url(self):
        """Test the leaderboard URL."""
        url = reverse('gamification:leaderboard')
        self.assertEqual(url, '/api/gamification/leaderboard/')