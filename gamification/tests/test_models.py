from django.test import TestCase
from django.test import TestCase
from django.contrib.auth import get_user_model
from gamification.models import UserProgress, Wallet

User = get_user_model()


class UserProgressModelTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.progress = UserProgress.objects.create(user=self.user)

    def test_user_progress_creation(self):
        """Test that a UserProgress instance can be created"""
        self.assertEqual(self.progress.user, self.user)
        self.assertEqual(self.progress.xp, 0)
        self.assertEqual(self.progress.level, 1)
        self.assertEqual(self.progress.hearts, 0)
        self.assertEqual(self.progress.messages_count, 0)
        self.assertEqual(self.progress.voice_messages_count, 0)
        self.assertEqual(self.progress.achievements, [])
        self.assertEqual(self.progress.total_time_spent, 0)
        self.assertEqual(self.progress.xp_to_next_level, 100)

    def test_add_xp(self):
        """Test adding XP and leveling up"""
        # Add 50 XP (not enough to level up)
        self.progress.add_xp(50)
        self.assertEqual(self.progress.xp, 50)
        self.assertEqual(self.progress.level, 1)
        
        # Add 60 more XP (enough to level up)
        self.progress.add_xp(60)
        self.assertEqual(self.progress.level, 2)
        self.assertEqual(self.progress.xp, 10)  # 110 - 100 = 10
        
        # Check that xp_to_next_level has been updated
        self.assertGreater(self.progress.xp_to_next_level, 100)

    def test_increment_message_count(self):
        """Test incrementing message count"""
        self.progress.increment_message_count()
        self.assertEqual(self.progress.messages_count, 1)
        self.assertEqual(self.progress.voice_messages_count, 0)
        
        self.progress.increment_message_count(is_voice=True)
        self.assertEqual(self.progress.messages_count, 2)
        self.assertEqual(self.progress.voice_messages_count, 1)

    def test_add_time_spent(self):
        """Test adding time spent"""
        self.progress.add_time_spent(60)
        self.assertEqual(self.progress.total_time_spent, 60)
        
        self.progress.add_time_spent(120)
        self.assertEqual(self.progress.total_time_spent, 180)

    def test_add_achievement(self):
        """Test adding achievements"""
        self.progress.add_achievement("achievement1")
        self.assertEqual(self.progress.achievements, ["achievement1"])
        
        # Adding the same achievement again should return False
        result = self.progress.add_achievement("achievement1")
        self.assertFalse(result)
        self.assertEqual(self.progress.achievements, ["achievement1"])
        
        # Adding a new achievement should work
        result = self.progress.add_achievement("achievement2")
        self.assertTrue(result)
        self.assertEqual(self.progress.achievements, ["achievement1", "achievement2"])


class WalletModelTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        self.wallet = Wallet.objects.create(user=self.user)

    def test_wallet_creation(self):
        """Test that a Wallet instance can be created"""
        self.assertEqual(self.wallet.user, self.user)
        self.assertEqual(self.wallet.balance, 0)
        self.assertEqual(self.wallet.lifetime_earnings, 0)

    def test_add_currency(self):
        """Test adding currency to wallet"""
        self.wallet.add_currency(100)
        self.assertEqual(self.wallet.balance, 100)
        self.assertEqual(self.wallet.lifetime_earnings, 100)
        
        self.wallet.add_currency(50)
        self.assertEqual(self.wallet.balance, 150)
        self.assertEqual(self.wallet.lifetime_earnings, 150)

    def test_spend_currency(self):
        """Test spending currency from wallet"""
        # Add some currency first
        self.wallet.add_currency(100)
        
        # Spend less than balance
        result = self.wallet.spend_currency(50)
        self.assertTrue(result)
        self.assertEqual(self.wallet.balance, 50)
        
        # Try to spend more than balance
        result = self.wallet.spend_currency(100)
        self.assertFalse(result)
        self.assertEqual(self.wallet.balance, 50)  # Balance unchanged
        
        # Spend exact balance
        result = self.wallet.spend_currency(50)
        self.assertTrue(result)
        self.assertEqual(self.wallet.balance, 0)