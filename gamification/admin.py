from django.contrib import admin
from .models import (
    UserProgress, UserLevel, Achievement, UserAchievement, 
    Wallet, UserWallet, ShopItem, UserInventory, 
    DailyReward, Pet, UserDailyStreak
)

@admin.register(UserProgress)
class UserProgressAdmin(admin.ModelAdmin):
    list_display = ('user', 'level', 'xp', 'hearts', 'messages_count')
    search_fields = ('user__username', 'user__email')

@admin.register(Achievement)
class AchievementAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'rarity', 'xp_reward', 'currency_reward')
    list_filter = ('category', 'rarity', 'is_active')
    search_fields = ('name', 'description')

@admin.register(UserAchievement)
class UserAchievementAdmin(admin.ModelAdmin):
    list_display = ('user', 'achievement', 'is_completed', 'unlocked_at')
    list_filter = ('is_completed',)
    search_fields = ('user__username', 'achievement__name')

@admin.register(Wallet)
class WalletAdmin(admin.ModelAdmin):
    list_display = ('user', 'balance', 'lifetime_earnings')
    search_fields = ('user__username', 'user__email')

@admin.register(ShopItem)
class ShopItemAdmin(admin.ModelAdmin):
    list_display = ('name', 'item_type', 'price', 'level_requirement', 'relationship_level_requirement', 'is_active')
    list_filter = ('item_type', 'is_active', 'is_featured', 'is_limited')
    search_fields = ('name', 'description')

@admin.register(UserInventory)
class UserInventoryAdmin(admin.ModelAdmin):
    list_display = ('user', 'item', 'acquired_at', 'is_equipped')
    list_filter = ('is_equipped',)
    search_fields = ('user__username', 'item__name')

@admin.register(DailyReward)
class DailyRewardAdmin(admin.ModelAdmin):
    list_display = ('day', 'xp_reward', 'currency_reward', 'item_reward', 'is_bonus_day')
    list_filter = ('is_bonus_day',)

@admin.register(Pet)
class PetAdmin(admin.ModelAdmin):
    list_display = ('name', 'pet_type', 'behavior', 'is_active')
    list_filter = ('pet_type', 'behavior', 'is_active')
    search_fields = ('name',)

@admin.register(UserDailyStreak)
class UserDailyStreakAdmin(admin.ModelAdmin):
    list_display = ('user', 'current_streak', 'longest_streak', 'last_login_date')
    search_fields = ('user__username', 'user__email')

# Register legacy models with simpler admin interfaces
admin.site.register(UserLevel)
admin.site.register(UserWallet)
