from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
import uuid

User = get_user_model()


class UserProgress(models.Model):
    """User progress tracking including XP, level, hearts, and message counts"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='progress')
    xp = models.IntegerField(default=0)
    level = models.IntegerField(default=1)
    hearts = models.IntegerField(default=0)
    messages_count = models.IntegerField(default=0)
    voice_messages_count = models.IntegerField(default=0)
    achievements = models.JSONField(default=list)
    total_time_spent = models.IntegerField(default=0)  # in seconds
    xp_to_next_level = models.IntegerField(default=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'gamification_user_progress'
        verbose_name = 'User Progress'
        verbose_name_plural = 'User Progress'
    
    def __str__(self):
        return f"{self.user.username} - Level {self.level}"
    
    def add_xp(self, amount):
        """Add XP and handle level ups"""
        self.xp += amount
        
        # Check for level up
        while self.xp >= self.xp_to_next_level:
            self.xp -= self.xp_to_next_level
            self.level += 1
            self.xp_to_next_level = self._calculate_xp_for_level(self.level + 1)
        
        self.save()
        return self.level
    
    def _calculate_xp_for_level(self, level):
        """Calculate XP required for a specific level"""
        return 100 * (level ** 1.5)  # Exponential scaling
    
    def increment_message_count(self, is_voice=False):
        """Increment message count and optionally voice message count"""
        self.messages_count += 1
        if is_voice:
            self.voice_messages_count += 1
        self.save(update_fields=['messages_count', 'voice_messages_count', 'updated_at'])
    
    def add_time_spent(self, seconds):
        """Add time spent in the app"""
        self.total_time_spent += seconds
        self.save(update_fields=['total_time_spent', 'updated_at'])
    
    def add_achievement(self, achievement_id):
        """Add an achievement to the user's achievements list"""
        if achievement_id not in self.achievements:
            self.achievements.append(achievement_id)
            self.save(update_fields=['achievements', 'updated_at'])
            return True
        return False


class UserLevel(models.Model):
    """User level and XP tracking (Legacy model - use UserProgress instead)"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='level')
    current_level = models.PositiveIntegerField(default=1)
    current_xp = models.PositiveIntegerField(default=0)
    total_xp = models.PositiveIntegerField(default=0)
    xp_to_next_level = models.PositiveIntegerField(default=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'gamification_user_level'
        verbose_name = 'User Level'
        verbose_name_plural = 'User Levels'
    
    def __str__(self):
        return f"{self.user.username} - Level {self.current_level}"
    
    def add_xp(self, amount):
        """Add XP and handle level ups"""
        self.current_xp += amount
        self.total_xp += amount
        
        # Check for level up
        while self.current_xp >= self.xp_to_next_level:
            self.current_xp -= self.xp_to_next_level
            self.current_level += 1
            self.xp_to_next_level = self._calculate_xp_for_level(self.current_level + 1)
        
        self.save()
        return self.current_level
    
    def _calculate_xp_for_level(self, level):
        """Calculate XP required for a specific level"""
        return 100 * (level ** 1.5)  # Exponential scaling


class Achievement(models.Model):
    """Achievement definitions"""
    CATEGORY_CHOICES = [
        ('chat', 'Chat'),
        ('memory', 'Memory'),
        ('social', 'Social'),
        ('milestone', 'Milestone'),
        ('special', 'Special'),
    ]
    
    RARITY_CHOICES = [
        ('common', 'Common'),
        ('rare', 'Rare'),
        ('epic', 'Epic'),
        ('legendary', 'Legendary'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField()
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    rarity = models.CharField(max_length=20, choices=RARITY_CHOICES, default='common')
    icon = models.CharField(max_length=100, blank=True)  # Icon identifier
    xp_reward = models.PositiveIntegerField(default=0)
    currency_reward = models.PositiveIntegerField(default=0)
    requirement_type = models.CharField(max_length=50)  # e.g., 'message_count', 'days_active'
    requirement_value = models.PositiveIntegerField()  # Target value
    is_hidden = models.BooleanField(default=False)  # Hidden until unlocked
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'gamification_achievement'
        verbose_name = 'Achievement'
        verbose_name_plural = 'Achievements'
    
    def __str__(self):
        return self.name


class UserAchievement(models.Model):
    """User's unlocked achievements"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='achievements')
    achievement = models.ForeignKey(Achievement, on_delete=models.CASCADE)
    unlocked_at = models.DateTimeField(auto_now_add=True)
    progress = models.PositiveIntegerField(default=0)  # Current progress towards achievement
    is_completed = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'gamification_user_achievement'
        verbose_name = 'User Achievement'
        verbose_name_plural = 'User Achievements'
        unique_together = ['user', 'achievement']
    
    def __str__(self):
        return f"{self.user.username} - {self.achievement.name}"
    
    def update_progress(self, value):
        """Update achievement progress and check completion"""
        self.progress = value
        if self.progress >= self.achievement.requirement_value and not self.is_completed:
            self.is_completed = True
            self.unlocked_at = timezone.now()
            # Award XP and currency
            if hasattr(self.user, 'progress'):
                self.user.progress.add_xp(self.achievement.xp_reward)
            elif hasattr(self.user, 'level'):
                self.user.level.add_xp(self.achievement.xp_reward)
                
            if hasattr(self.user, 'wallet'):
                self.user.wallet.add_currency(self.achievement.currency_reward)
        self.save()


class Wallet(models.Model):
    """User's virtual currency wallet"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='wallet')
    balance = models.IntegerField(default=0)
    lifetime_earnings = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'gamification_wallet'
        verbose_name = 'Wallet'
        verbose_name_plural = 'Wallets'
    
    def __str__(self):
        return f"{self.user.username} - {self.balance} coins"
    
    def add_currency(self, amount):
        """Add currency to wallet"""
        self.balance += amount
        self.lifetime_earnings += amount
        self.save(update_fields=['balance', 'lifetime_earnings', 'updated_at'])
        return self.balance
    
    def spend_currency(self, amount):
        """Spend currency from wallet"""
        if self.balance >= amount:
            self.balance -= amount
            self.save(update_fields=['balance', 'updated_at'])
            return True
        return False


class UserWallet(models.Model):
    """User's virtual currency wallet (Legacy model - use Wallet instead)"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='legacy_wallet')
    balance = models.PositiveIntegerField(default=0)  # Virtual currency balance
    total_earned = models.PositiveIntegerField(default=0)
    total_spent = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'gamification_user_wallet'
        verbose_name = 'User Wallet'
        verbose_name_plural = 'User Wallets'
    
    def __str__(self):
        return f"{self.user.username} - {self.balance} coins"
    
    def add_currency(self, amount):
        """Add currency to wallet"""
        self.balance += amount
        self.total_earned += amount
        self.save()
    
    def spend_currency(self, amount):
        """Spend currency from wallet"""
        if self.balance >= amount:
            self.balance -= amount
            self.total_spent += amount
            self.save()
            return True
        return False


class ShopItem(models.Model):
    """Shop items that users can purchase"""
    ITEM_TYPE_CHOICES = [
        ('environment', 'Environment'),
        ('outfit', 'Outfit'),
        ('accessory', 'Accessory'),
        ('companion', 'Companion'),
        ('pet', 'Pet'),
    ]
    
    RARITY_CHOICES = [
        ('common', 'Common'),
        ('rare', 'Rare'),
        ('epic', 'Epic'),
        ('legendary', 'Legendary'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.IntegerField()
    item_type = models.CharField(max_length=50, choices=ITEM_TYPE_CHOICES)
    image_url = models.URLField(default='https://placeholder.com/item.png')
    preview_data = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    is_limited = models.BooleanField(default=False)
    stock_quantity = models.IntegerField(null=True, blank=True)
    level_requirement = models.IntegerField(default=1)
    relationship_level_requirement = models.IntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'gamification_shop_item'
        verbose_name = 'Shop Item'
        verbose_name_plural = 'Shop Items'
    
    def __str__(self):
        return self.name
    
    def can_purchase(self, user):
        """Check if user can purchase this item"""
        if not self.is_active:
            return False, "Item is not available"
        
        if hasattr(user, 'progress') and user.progress.level < self.level_requirement:
            return False, f"Requires level {self.level_requirement}"
        
        if hasattr(user, 'wallet') and user.wallet.balance < self.price:
            return False, "Insufficient funds"
        
        if self.is_limited and self.stock_quantity is not None and self.stock_quantity <= 0:
            return False, "Out of stock"
        
        # Check if user already owns this item
        if self.user_inventories.filter(user=user).exists():
            return False, "Already owned"
        
        # Check relationship level requirement
        if hasattr(user, 'relationship') and user.relationship.level < self.relationship_level_requirement:
            return False, f"Requires relationship level {self.relationship_level_requirement}"
        
        return True, "Can purchase"


class UserInventory(models.Model):
    """User's purchased items inventory"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='inventory_items')
    item = models.ForeignKey(ShopItem, on_delete=models.CASCADE, related_name='user_inventories')
    acquired_at = models.DateTimeField(auto_now_add=True)
    is_equipped = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'gamification_user_inventory'
        verbose_name = 'User Inventory'
        verbose_name_plural = 'User Inventories'
        unique_together = ['user', 'item']
    
    def __str__(self):
        return f"{self.user.username} - {self.item.name}"


class DailyReward(models.Model):
    """Daily login rewards"""
    day = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(30)])
    xp_reward = models.PositiveIntegerField(default=0)
    currency_reward = models.PositiveIntegerField(default=0)
    item_reward = models.ForeignKey(ShopItem, on_delete=models.SET_NULL, null=True, blank=True)
    is_bonus_day = models.BooleanField(default=False)  # Special rewards on certain days
    
    class Meta:
        db_table = 'gamification_daily_reward'
        verbose_name = 'Daily Reward'
        verbose_name_plural = 'Daily Rewards'
        unique_together = ['day']
    
    def __str__(self):
        return f"Day {self.day} Reward"


class Pet(models.Model):
    """Pet companions that users can own"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    pet_type = models.CharField(max_length=50)
    behavior = models.CharField(max_length=50)
    sound = models.CharField(max_length=50)
    image_url = models.URLField(default='https://placeholder.com/pet.png')
    animation_data = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'gamification_pet'
        verbose_name = 'Pet'
        verbose_name_plural = 'Pets'
    
    def __str__(self):
        return f"{self.name} ({self.pet_type})"


class UserDailyStreak(models.Model):
    """User's daily login streak tracking"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='daily_streak')
    current_streak = models.PositiveIntegerField(default=0)
    longest_streak = models.PositiveIntegerField(default=0)
    last_login_date = models.DateField(null=True, blank=True)
    last_reward_claimed = models.DateField(null=True, blank=True)
    total_logins = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'gamification_user_daily_streak'
        verbose_name = 'User Daily Streak'
        verbose_name_plural = 'User Daily Streaks'
    
    def __str__(self):
        return f"{self.user.username} - {self.current_streak} day streak"
    
    def update_streak(self):
        """Update streak based on current date"""
        today = timezone.now().date()
        
        if self.last_login_date is None:
            # First login
            self.current_streak = 1
            self.total_logins = 1
        elif self.last_login_date == today:
            # Already logged in today
            return self.current_streak
        elif self.last_login_date == today - timezone.timedelta(days=1):
            # Consecutive day
            self.current_streak += 1
            self.total_logins += 1
        else:
            # Streak broken
            self.current_streak = 1
            self.total_logins += 1
        
        if self.current_streak > self.longest_streak:
            self.longest_streak = self.current_streak
        
        self.last_login_date = today
        self.save()
        return self.current_streak
