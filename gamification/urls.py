from django.urls import path
from . import views

app_name = 'gamification'

urlpatterns = [
    # User level and XP
    path('level/', views.UserLevelView.as_view(), name='user-level'),
    path('progress/', views.UserProgressView.as_view() if hasattr(views, 'UserProgressView') else views.UserLevelView.as_view(), name='user-progress'),
    
    # Achievements
    path('achievements/', views.AchievementListView.as_view(), name='achievement-list'),
    path('achievements/user/', views.UserAchievementListView.as_view(), name='user-achievement-list'),
    path('achievements/unlock/', views.unlock_achievement, name='unlock-achievement'),
    path('achievements/progress/', views.achievement_progress, name='achievement-progress'),
    
    # Wallet and currency
    path('wallet/', views.UserWalletView.as_view(), name='user-wallet'),
    path('wallet/new/', views.WalletView.as_view() if hasattr(views, 'WalletView') else views.UserWalletView.as_view(), name='wallet'),
    path('wallet/add-currency/', views.add_currency, name='add-currency'),
    
    # Shop and inventory
    path('shop/', views.ShopItemListView.as_view(), name='shop-item-list'),
    path('shop/purchase/', views.purchase_item, name='purchase-item'),
    path('shop/preview/<uuid:item_id>/', views.preview_item, name='preview-item'),
    path('inventory/', views.UserInventoryListView.as_view(), name='user-inventory'),
    path('inventory/summary/', views.inventory_summary, name='inventory-summary'),
    path('inventory/equip/<uuid:item_id>/', views.equip_item, name='equip-item'),
    
    # Daily rewards and streaks
    path('streak/', views.UserDailyStreakView.as_view(), name='user-daily-streak'),
    path('daily-rewards/', views.DailyRewardListView.as_view(), name='daily-reward-list'),
    path('daily-rewards/status/', views.daily_reward_status, name='daily-reward-status'),
    path('daily-rewards/claim/', views.claim_daily_reward, name='claim-daily-reward'),
    
    # Statistics and leaderboard
    path('stats/', views.gamification_stats, name='gamification-stats'),
    path('leaderboard/', views.leaderboard, name='leaderboard'),
]
