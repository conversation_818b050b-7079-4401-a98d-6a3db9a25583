from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from django.db.models import Count, Q

from .models import (
    UserLevel, Achievement, UserAchievement, UserWallet,
    ShopItem, UserInventory, DailyReward, UserDailyStreak,
    UserProgress, Wallet, Pet
)
from .serializers import (
    UserLevelSerializer, AchievementSerializer, UserAchievementSerializer,
    UserWalletSerializer, ShopItemSerializer, UserInventorySerializer,
    DailyRewardSerializer, UserDailyStreakSerializer, PurchaseItemSerializer,
    GamificationStatsSerializer, UserProgressSerializer, WalletSerializer
)


class UserLevelView(generics.RetrieveAPIView):
    """Get user's current level and XP information"""
    serializer_class = UserLevelSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        level, created = UserLevel.objects.get_or_create(user=self.request.user)
        return level


class AchievementListView(generics.ListAPIView):
    """List all available achievements"""
    serializer_class = AchievementSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # Show only non-hidden achievements or completed ones
        user_completed = UserAchievement.objects.filter(
            user=self.request.user, is_completed=True
        ).values_list('achievement_id', flat=True)
        
        queryset = Achievement.objects.filter(
            Q(is_hidden=False) | Q(id__in=user_completed),
            is_active=True
        )
        
        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)
            
        # Filter by rarity
        rarity = self.request.query_params.get('rarity')
        if rarity:
            queryset = queryset.filter(rarity=rarity)
            
        # Filter by completion status
        completed = self.request.query_params.get('completed')
        if completed is not None:
            is_completed = completed.lower() == 'true'
            if is_completed:
                queryset = queryset.filter(id__in=user_completed)
            else:
                queryset = queryset.exclude(id__in=user_completed)
        
        return queryset.order_by('category', 'rarity', 'name')


class UserAchievementListView(generics.ListAPIView):
    """List user's achievements with progress"""
    serializer_class = UserAchievementSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = UserAchievement.objects.filter(
            user=self.request.user
        ).select_related('achievement')
        
        # Filter by category
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(achievement__category=category)
            
        # Filter by completion status
        completed = self.request.query_params.get('completed')
        if completed is not None:
            is_completed = completed.lower() == 'true'
            queryset = queryset.filter(is_completed=is_completed)
            
        # Sort options
        sort_by = self.request.query_params.get('sort_by', 'unlocked_at')
        if sort_by == 'progress':
            queryset = queryset.order_by('-progress')
        elif sort_by == 'name':
            queryset = queryset.order_by('achievement__name')
        else:
            queryset = queryset.order_by('-unlocked_at')
            
        return queryset


class UserWalletView(generics.RetrieveAPIView):
    """Get user's wallet information"""
    serializer_class = UserWalletSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        wallet, created = UserWallet.objects.get_or_create(user=self.request.user)
        return wallet


class ShopItemListView(generics.ListAPIView):
    """List all shop items"""
    serializer_class = ShopItemSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = ShopItem.objects.filter(is_active=True)
        
        # Filter by item type
        item_type = self.request.query_params.get('type')
        if item_type:
            queryset = queryset.filter(item_type=item_type)
        
        # Filter by featured items
        featured = self.request.query_params.get('featured')
        if featured and featured.lower() == 'true':
            queryset = queryset.filter(is_featured=True)
        
        # Filter by user level
        user_progress = getattr(self.request.user, 'progress', None)
        if user_progress:
            queryset = queryset.filter(level_requirement__lte=user_progress.level)
        
        # Sort options
        sort_by = self.request.query_params.get('sort_by', 'price')
        sort_order = self.request.query_params.get('sort_order', 'asc')
        
        if sort_by == 'price':
            order_field = 'price'
        elif sort_by == 'level':
            order_field = 'level_requirement'
        elif sort_by == 'name':
            order_field = 'name'
        else:
            order_field = 'price'
            
        if sort_order.lower() == 'desc':
            order_field = f'-{order_field}'
            
        return queryset.order_by(order_field)


class UserInventoryListView(generics.ListAPIView):
    """List user's inventory items"""
    serializer_class = UserInventorySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        queryset = UserInventory.objects.filter(
            user=self.request.user
        ).select_related('item')
        
        # Filter by item type
        item_type = self.request.query_params.get('type')
        if item_type:
            queryset = queryset.filter(item__item_type=item_type)
            
        # Filter by equipped status
        equipped = self.request.query_params.get('equipped')
        if equipped is not None:
            is_equipped = equipped.lower() == 'true'
            queryset = queryset.filter(is_equipped=is_equipped)
            
        return queryset.order_by('-acquired_at')


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def purchase_item(request):
    """Purchase a shop item"""
    serializer = PurchaseItemSerializer(data=request.POST, context={'request': request})
    
    if not serializer.is_valid():
        # Check if the error is about already owning the item
        if 'item_id' in serializer.errors and any('Already owned' in str(error) for error in serializer.errors['item_id']):
            return Response({'error': 'Already owned'}, status=status.HTTP_400_BAD_REQUEST)
        # Check if the error is about insufficient funds
        if 'item_id' in serializer.errors and any('Insufficient funds' in str(error) for error in serializer.errors['item_id']):
            return Response({'error': 'Insufficient funds'}, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    item_id = serializer.validated_data['item_id']
    item = get_object_or_404(ShopItem, id=item_id, is_active=True)
    user = request.user
    
    try:
        with transaction.atomic():
            # Get or create user wallet (use new Wallet model)
            wallet, _ = Wallet.objects.get_or_create(user=user)
            
            # Double-check purchase eligibility
            can_purchase, message = item.can_purchase(user)
            if not can_purchase:
                return Response({'error': message}, status=status.HTTP_400_BAD_REQUEST)
            
            # Process purchase
            if not wallet.spend_currency(item.price):
                return Response({'error': 'Insufficient funds'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Add to inventory
            inventory_item, created = UserInventory.objects.get_or_create(
                user=user,
                item=item
            )
            
            if not created:
                # Item already owned, return error
                return Response({'error': 'Already owned'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Update stock for limited items
            if item.is_limited and item.stock_quantity is not None:
                item.stock_quantity -= 1
                item.save()
            
            # Update user model if needed based on item type
            if item.item_type == 'environment':
                if not user.owned_environments:
                    user.owned_environments = []
                if str(item.id) not in user.owned_environments:
                    user.owned_environments.append(str(item.id))
                    user.save(update_fields=['owned_environments'])
            elif item.item_type == 'outfit':
                if not user.owned_outfits:
                    user.owned_outfits = []
                if str(item.id) not in user.owned_outfits:
                    user.owned_outfits.append(str(item.id))
                    user.save(update_fields=['owned_outfits'])
            elif item.item_type == 'pet':
                if not user.owned_pets:
                    user.owned_pets = []
                if str(item.id) not in user.owned_pets:
                    user.owned_pets.append(str(item.id))
                    user.save(update_fields=['owned_pets'])
            
            return Response({
                'message': 'Item purchased successfully',
                'item': ShopItemSerializer(item, context={'request': request}).data,
                'wallet_balance': wallet.balance
            }, status=status.HTTP_200_OK)
    
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def equip_item(request, item_id):
    """Equip/unequip an inventory item"""
    try:
        with transaction.atomic():
            inventory_item = UserInventory.objects.get(
                user=request.user,
                item__id=item_id
            )
            
            # Toggle equipped status
            inventory_item.is_equipped = not inventory_item.is_equipped
            
            # If equipping, unequip other items of the same type
            if inventory_item.is_equipped:
                # Unequip other items of the same type first
                UserInventory.objects.filter(
                    user=request.user,
                    item__item_type=inventory_item.item.item_type
                ).exclude(id=inventory_item.id).update(is_equipped=False)
                
                # Update user's selected environment or outfit if applicable
                user = request.user
                if inventory_item.item.item_type == 'environment':
                    user.selected_environment = str(inventory_item.item.id)
                    user.save(update_fields=['selected_environment'])
                elif inventory_item.item.item_type == 'outfit':
                    # Assuming there's a field for selected outfit
                    if hasattr(user, 'selected_outfit'):
                        user.selected_outfit = str(inventory_item.item.id)
                        user.save(update_fields=['selected_outfit'])
            
            # Save the item after all other operations
            inventory_item.save()
        
        # Refresh from database to ensure we have the latest state
        inventory_item.refresh_from_db()
        
        return Response({
            'message': f"Item {'equipped' if inventory_item.is_equipped else 'unequipped'}",
            'is_equipped': inventory_item.is_equipped
        })
    
    except UserInventory.DoesNotExist:
        return Response({'error': 'Item not found in inventory'}, status=status.HTTP_404_NOT_FOUND)


class UserDailyStreakView(generics.RetrieveAPIView):
    """Get user's daily streak information"""
    serializer_class = UserDailyStreakSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        streak, created = UserDailyStreak.objects.get_or_create(user=self.request.user)
        # Update streak on access
        streak.update_streak()
        return streak


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def claim_daily_reward(request):
    """Claim daily login reward"""
    user = request.user
    today = timezone.now().date()
    
    try:
        # Get or create streak
        streak, _ = UserDailyStreak.objects.get_or_create(user=user)
        
        # Check if already claimed today
        if streak.last_reward_claimed == today:
            return Response({'error': 'Daily reward already claimed'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Update streak
        current_streak = streak.update_streak()
        
        # Get reward for current streak day (cap at 30)
        reward_day = min(current_streak, 30)
        
        try:
            daily_reward = DailyReward.objects.get(day=reward_day)
        except DailyReward.DoesNotExist:
            return Response({'error': 'No reward configured for this day'}, status=status.HTTP_404_NOT_FOUND)
        
        with transaction.atomic():
            # Award XP
            if daily_reward.xp_reward > 0:
                # Use UserProgress instead of UserLevel
                progress, _ = UserProgress.objects.get_or_create(user=user)
                progress.add_xp(daily_reward.xp_reward)
            
            # Award currency
            if daily_reward.currency_reward > 0:
                # Use Wallet instead of UserWallet
                wallet, _ = Wallet.objects.get_or_create(user=user)
                wallet.add_currency(daily_reward.currency_reward)
            
            # Award item
            if daily_reward.item_reward:
                # Check if user already has this item
                inventory_item, created = UserInventory.objects.get_or_create(
                    user=user,
                    item=daily_reward.item_reward
                )
                
                # Update user model if needed based on item type
                if created:
                    if daily_reward.item_reward.item_type == 'environment':
                        if not user.owned_environments:
                            user.owned_environments = []
                        if str(daily_reward.item_reward.id) not in user.owned_environments:
                            user.owned_environments.append(str(daily_reward.item_reward.id))
                            user.save(update_fields=['owned_environments'])
                    elif daily_reward.item_reward.item_type == 'outfit':
                        if not user.owned_outfits:
                            user.owned_outfits = []
                        if str(daily_reward.item_reward.id) not in user.owned_outfits:
                            user.owned_outfits.append(str(daily_reward.item_reward.id))
                            user.save(update_fields=['owned_outfits'])
                    elif daily_reward.item_reward.item_type == 'pet':
                        if not user.owned_pets:
                            user.owned_pets = []
                        if str(daily_reward.item_reward.id) not in user.owned_pets:
                            user.owned_pets.append(str(daily_reward.item_reward.id))
                            user.save(update_fields=['owned_pets'])
            
            # Mark reward as claimed
            streak.last_reward_claimed = today
            streak.save()
        
        return Response({
            'message': 'Daily reward claimed successfully',
            'reward': DailyRewardSerializer(daily_reward).data,
            'streak': current_streak
        })
    
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def gamification_stats(request):
    """Get comprehensive gamification statistics for user"""
    user = request.user
    
    # Get or create user gamification objects
    level, _ = UserLevel.objects.get_or_create(user=user)
    wallet, _ = UserWallet.objects.get_or_create(user=user)
    streak, _ = UserDailyStreak.objects.get_or_create(user=user)
    
    # Calculate achievements stats
    achievements_count = UserAchievement.objects.filter(user=user).count()
    completed_achievements_count = UserAchievement.objects.filter(
        user=user, is_completed=True
    ).count()
    
    # Calculate inventory count
    inventory_count = UserInventory.objects.filter(user=user).count()
    
    # Calculate user rank based on total XP
    rank = UserLevel.objects.filter(total_xp__gt=level.total_xp).count() + 1
    total_users = UserLevel.objects.count()
    
    stats_data = {
        'level_info': level,
        'wallet_info': wallet,
        'streak_info': streak,
        'achievements_count': achievements_count,
        'completed_achievements_count': completed_achievements_count,
        'inventory_count': inventory_count,
        'rank': rank,
        'total_users': total_users
    }
    
    serializer = GamificationStatsSerializer(stats_data)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def leaderboard(request):
    """Get leaderboard of top users by XP"""
    limit = min(int(request.query_params.get('limit', 50)), 100)  # Cap at 100
    
    top_users = UserLevel.objects.select_related('user').order_by('-total_xp')[:limit]
    
    leaderboard_data = []
    for i, user_level in enumerate(top_users, 1):
        leaderboard_data.append({
            'rank': i,
            'username': user_level.user.username,
            'level': user_level.current_level,
            'total_xp': user_level.total_xp,
            'is_current_user': user_level.user == request.user
        })
    
    return Response({
        'leaderboard': leaderboard_data,
        'total_users': UserLevel.objects.count()
    })

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def preview_item(request, item_id):
    """Preview a shop item before purchase"""
    try:
        item = ShopItem.objects.get(id=item_id, is_active=True)
        
        # Check if user meets level requirements to preview
        user_progress = getattr(request.user, 'progress', None)
        if user_progress and user_progress.level < item.level_requirement:
            return Response(
                {'error': f'Requires level {item.level_requirement} to preview'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Return preview data
        return Response({
            'item': ShopItemSerializer(item, context={'request': request}).data,
            'preview_data': item.preview_data
        })
        
    except ShopItem.DoesNotExist:
        return Response({'error': 'Item not found'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def inventory_summary(request):
    """Get a summary of user's inventory by item type"""
    user = request.user
    
    # Get inventory items grouped by type
    inventory_by_type = {}
    inventory_items = UserInventory.objects.filter(user=user).select_related('item')
    
    # Get all item types from the database
    item_types = set(ShopItem.objects.values_list('item_type', flat=True).distinct())
    
    for item_type in item_types:
        items = [item for item in inventory_items if item.item.item_type == item_type]
        inventory_by_type[item_type] = {
            'count': len(items),
            'equipped': next((str(item.item.id) for item in items if item.is_equipped), None),
            'items': UserInventorySerializer(
                [item for item in items], 
                many=True, 
                context={'request': request}
            ).data
        }
    
    # Get currently equipped items
    equipped_items = {}
    for item in inventory_items.filter(is_equipped=True):
        equipped_items[item.item.item_type] = UserInventorySerializer(
            item, context={'request': request}
        ).data
    
    return Response({
        'inventory_by_type': inventory_by_type,
        'equipped_items': equipped_items,
        'total_items': inventory_items.count()
    })

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def unlock_achievement(request):
    """Unlock an achievement for the user"""
    # Handle both JSON and form data
    if hasattr(request, 'data'):
        achievement_id = request.data.get('achievement_id')
        progress_value = request.data.get('progress_value')
    else:
        achievement_id = request.POST.get('achievement_id')
        progress_value = request.POST.get('progress_value')
    
    if not achievement_id:
        return Response({'error': 'Achievement ID is required'}, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        achievement = Achievement.objects.get(id=achievement_id, is_active=True)
    except Achievement.DoesNotExist:
        return Response({'error': 'Achievement not found'}, status=status.HTTP_404_NOT_FOUND)
    
    user = request.user
    
    # Check if user already has this achievement
    user_achievement, created = UserAchievement.objects.get_or_create(
        user=user,
        achievement=achievement
    )
    
    # If already completed, return early
    if user_achievement.is_completed:
        response_data = {
            'message': 'Achievement already unlocked',
            'achievement': UserAchievementSerializer(user_achievement).data
        }
        return Response(response_data)
    
    # Update progress if provided
    if progress_value is not None:
        try:
            progress_value = int(progress_value)
            
            # Check if this will complete the achievement
            if progress_value >= achievement.requirement_value and not user_achievement.is_completed:
                # Manually award XP and currency since update_progress might not do it
                if hasattr(user, 'progress'):
                    user.progress.add_xp(achievement.xp_reward)
                
                if hasattr(user, 'wallet'):
                    user.wallet.add_currency(achievement.currency_reward)
                
                # Mark as completed
                user_achievement.progress = progress_value
                user_achievement.is_completed = True
                user_achievement.unlocked_at = timezone.now()
                user_achievement.save()
                
                # Add achievement to user's progress achievements list
                if hasattr(user, 'progress'):
                    user.progress.add_achievement(str(achievement.id))
            else:
                # Just update progress
                user_achievement.progress = progress_value
                user_achievement.save()
        except (ValueError, TypeError):
            return Response({'error': 'Invalid progress value'}, status=status.HTTP_400_BAD_REQUEST)
    else:
        # If no progress value provided, complete the achievement
        user_achievement.progress = achievement.requirement_value
        user_achievement.is_completed = True
        user_achievement.unlocked_at = timezone.now()
        user_achievement.save()
        
        # Award XP and currency
        if hasattr(user, 'progress'):
            user.progress.add_xp(achievement.xp_reward)
        
        if hasattr(user, 'wallet'):
            user.wallet.add_currency(achievement.currency_reward)
    
    # Add achievement to user's progress achievements list
    if hasattr(user, 'progress') and user_achievement.is_completed:
        user.progress.add_achievement(str(achievement.id))
    
    # Create a response with explicit renderer
    response_data = {
        'message': 'Achievement progress updated',
        'is_completed': user_achievement.is_completed,
        'achievement': UserAchievementSerializer(user_achievement).data
    }
    
    # Create a response with explicit renderer
    response = Response(response_data)
    response.accepted_renderer = JSONRenderer()
    response.accepted_media_type = "application/json"
    response.renderer_context = {}
    
    return response


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def achievement_progress(request):
    """Get user's achievement progress summary"""
    user = request.user
    
    # Get total achievements count
    total_achievements = Achievement.objects.filter(is_active=True).count()
    
    # Get user's completed achievements
    user_achievements = UserAchievement.objects.filter(
        user=user
    ).select_related('achievement')
    
    completed_achievements = user_achievements.filter(is_completed=True).count()
    
    # Calculate completion percentage
    completion_percentage = (completed_achievements / total_achievements * 100) if total_achievements > 0 else 0
    
    # Get achievements by category
    categories = dict(Achievement.CATEGORY_CHOICES)
    achievements_by_category = {}
    
    for category_key, category_name in categories.items():
        category_achievements = Achievement.objects.filter(category=category_key, is_active=True)
        category_completed = user_achievements.filter(
            achievement__category=category_key, 
            is_completed=True
        ).count()
        
        achievements_by_category[category_key] = {
            'name': category_name,
            'total': category_achievements.count(),
            'completed': category_completed,
            'percentage': (category_completed / category_achievements.count() * 100) 
                          if category_achievements.count() > 0 else 0
        }
    
    # Get next achievements to unlock (in progress)
    in_progress_achievements = user_achievements.filter(
        is_completed=False
    ).order_by('-progress')[:5]
    
    # Get recently unlocked achievements
    recent_unlocked = user_achievements.filter(
        is_completed=True
    ).order_by('-unlocked_at')[:5]
    
    return Response({
        'total_achievements': total_achievements,
        'completed_achievements': completed_achievements,
        'completion_percentage': completion_percentage,
        'achievements_by_category': achievements_by_category,
        'in_progress_achievements': UserAchievementSerializer(in_progress_achievements, many=True).data,
        'recent_unlocked': UserAchievementSerializer(recent_unlocked, many=True).data
    })


class DailyRewardListView(generics.ListAPIView):
    """List all daily rewards"""
    serializer_class = DailyRewardSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return DailyReward.objects.all().order_by('day')


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def daily_reward_status(request):
    """Get user's daily reward status"""
    user = request.user
    today = timezone.now().date()
    
    # Get or create streak
    streak, _ = UserDailyStreak.objects.get_or_create(user=user)
    
    # Update streak (but don't save yet)
    current_streak = streak.current_streak
    if streak.last_login_date != today:
        if streak.last_login_date is None or streak.last_login_date == today - timezone.timedelta(days=1):
            # First login or consecutive day
            current_streak = streak.current_streak + 1 if streak.last_login_date else 1
        else:
            # Streak broken
            current_streak = 1
    
    # Check if reward can be claimed
    can_claim = streak.last_reward_claimed != today
    
    # Get current day's reward
    reward_day = min(current_streak, 30)
    try:
        current_reward = DailyReward.objects.get(day=reward_day)
        current_reward_data = DailyRewardSerializer(current_reward).data
    except DailyReward.DoesNotExist:
        current_reward_data = None
    
    # Get next 7 days rewards
    next_rewards = []
    for day in range(reward_day, min(reward_day + 7, 31)):
        try:
            reward = DailyReward.objects.get(day=day)
            next_rewards.append({
                'day': day,
                'reward': DailyRewardSerializer(reward).data,
                'is_current': day == reward_day
            })
        except DailyReward.DoesNotExist:
            pass
    
    return Response({
        'current_streak': current_streak,
        'longest_streak': streak.longest_streak,
        'can_claim_today': can_claim,
        'last_claimed': streak.last_reward_claimed,
        'current_reward': current_reward_data,
        'upcoming_rewards': next_rewards,
        'days_until_bonus': next((r['day'] - reward_day for r in next_rewards if r['reward']['is_bonus_day']), None)
    })


class UserProgressView(generics.RetrieveAPIView):
    """Get user's progress information"""
    serializer_class = UserProgressSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        progress, created = UserProgress.objects.get_or_create(user=self.request.user)
        return progress


class WalletView(generics.RetrieveAPIView):
    """Get user's wallet information (new model)"""
    serializer_class = WalletSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        wallet, created = Wallet.objects.get_or_create(user=self.request.user)
        return wallet


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def relationship_gates(request):
    """Get relationship gates for shop items"""
    from chat.models import UserRelationship

    try:
        # Get user's current relationship level
        relationship = UserRelationship.objects.filter(user=request.user).first()
        current_level = relationship.relationship_level if relationship else 1

        # Get shop items with relationship requirements
        gated_items = ShopItem.objects.filter(
            relationship_level_required__gt=0,
            is_active=True
        ).values(
            'id', 'name', 'category', 'relationship_level_required',
            'price', 'currency_type'
        )

        # Organize by relationship level
        gates = {}
        for item in gated_items:
            level = item['relationship_level_required']
            if level not in gates:
                gates[level] = {
                    'level': level,
                    'unlocked': current_level >= level,
                    'items': []
                }
            gates[level]['items'].append(item)

        return Response({
            'status': 'success',
            'current_relationship_level': current_level,
            'relationship_gates': list(gates.values()),
            'total_gated_items': len(gated_items)
        })

    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'Failed to retrieve relationship gates: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def add_currency(request):
    """Add currency to user's wallet"""
    try:
        amount = request.data.get('amount')
        currency_type = request.data.get('currency_type', 'coins')
        reason = request.data.get('reason', 'Manual addition')

        if not amount or amount <= 0:
            return Response({
                'status': 'error',
                'message': 'Valid amount is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get or create user's wallet
        wallet, created = Wallet.objects.get_or_create(user=request.user)

        # Add currency based on type
        if currency_type == 'coins':
            wallet.add_currency(amount)
        elif currency_type == 'gems':
            wallet.gems += amount
            wallet.save()
        else:
            return Response({
                'status': 'error',
                'message': 'Invalid currency type. Use "coins" or "gems"'
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'status': 'success',
            'message': f'Added {amount} {currency_type} to wallet',
            'wallet': WalletSerializer(wallet).data,
            'reason': reason
        })

    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'Failed to add currency: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)