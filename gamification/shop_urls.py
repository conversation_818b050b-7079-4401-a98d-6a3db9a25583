"""
Shop System URL aliases for frontend compatibility.

These URLs provide the exact endpoints documented in BACKEND_INTEGRATION_READINESS.md
while redirecting to the actual implementation in the gamification app.
"""

from django.urls import path
from . import views

app_name = 'shop'

urlpatterns = [
    # Shop System Endpoints (as documented in BACKEND_INTEGRATION_READINESS.md)
    path('items/', views.ShopItemListView.as_view(), name='items'),
    path('items/<str:category>/', views.ShopItemListView.as_view(), name='items_by_category'),
    path('purchase/', views.purchase_item, name='purchase'),
    path('preview/<uuid:item_id>/', views.preview_item, name='preview'),
    path('relationship-gates/', views.relationship_gates, name='relationship_gates'),
]
