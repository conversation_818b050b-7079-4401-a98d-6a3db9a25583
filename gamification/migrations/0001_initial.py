# Generated by Django 4.2.7 on 2025-07-19 04:34

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Achievement",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField()),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("chat", "Chat"),
                            ("memory", "Memory"),
                            ("social", "Social"),
                            ("milestone", "Milestone"),
                            ("special", "Special"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "rarity",
                    models.CharField(
                        choices=[
                            ("common", "Common"),
                            ("rare", "Rare"),
                            ("epic", "Epic"),
                            ("legendary", "Legendary"),
                        ],
                        default="common",
                        max_length=20,
                    ),
                ),
                ("icon", models.CharField(blank=True, max_length=100)),
                ("xp_reward", models.PositiveIntegerField(default=0)),
                ("currency_reward", models.PositiveIntegerField(default=0)),
                ("requirement_type", models.CharField(max_length=50)),
                ("requirement_value", models.PositiveIntegerField()),
                ("is_hidden", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Achievement",
                "verbose_name_plural": "Achievements",
                "db_table": "gamification_achievement",
            },
        ),
        migrations.CreateModel(
            name="ShopItem",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField()),
                (
                    "item_type",
                    models.CharField(
                        choices=[
                            ("avatar_customization", "Avatar Customization"),
                            ("chat_theme", "Chat Theme"),
                            ("voice_pack", "Voice Pack"),
                            ("emote", "Emote"),
                            ("badge", "Badge"),
                            ("boost", "XP Boost"),
                            ("special", "Special Item"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "rarity",
                    models.CharField(
                        choices=[
                            ("common", "Common"),
                            ("rare", "Rare"),
                            ("epic", "Epic"),
                            ("legendary", "Legendary"),
                        ],
                        default="common",
                        max_length=20,
                    ),
                ),
                ("price", models.PositiveIntegerField()),
                ("icon", models.CharField(blank=True, max_length=100)),
                ("preview_image", models.CharField(blank=True, max_length=200)),
                ("is_limited", models.BooleanField(default=False)),
                ("stock_quantity", models.PositiveIntegerField(blank=True, null=True)),
                ("level_requirement", models.PositiveIntegerField(default=1)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Shop Item",
                "verbose_name_plural": "Shop Items",
                "db_table": "gamification_shop_item",
            },
        ),
        migrations.CreateModel(
            name="UserWallet",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("balance", models.PositiveIntegerField(default=0)),
                ("total_earned", models.PositiveIntegerField(default=0)),
                ("total_spent", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="wallet",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Wallet",
                "verbose_name_plural": "User Wallets",
                "db_table": "gamification_user_wallet",
            },
        ),
        migrations.CreateModel(
            name="UserLevel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("current_level", models.PositiveIntegerField(default=1)),
                ("current_xp", models.PositiveIntegerField(default=0)),
                ("total_xp", models.PositiveIntegerField(default=0)),
                ("xp_to_next_level", models.PositiveIntegerField(default=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="level",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Level",
                "verbose_name_plural": "User Levels",
                "db_table": "gamification_user_level",
            },
        ),
        migrations.CreateModel(
            name="UserDailyStreak",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("current_streak", models.PositiveIntegerField(default=0)),
                ("longest_streak", models.PositiveIntegerField(default=0)),
                ("last_login_date", models.DateField(blank=True, null=True)),
                ("last_reward_claimed", models.DateField(blank=True, null=True)),
                ("total_logins", models.PositiveIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="daily_streak",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Daily Streak",
                "verbose_name_plural": "User Daily Streaks",
                "db_table": "gamification_user_daily_streak",
            },
        ),
        migrations.CreateModel(
            name="UserInventory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("purchased_at", models.DateTimeField(auto_now_add=True)),
                ("is_equipped", models.BooleanField(default=False)),
                ("quantity", models.PositiveIntegerField(default=1)),
                (
                    "item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_inventories",
                        to="gamification.shopitem",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="inventory",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Inventory",
                "verbose_name_plural": "User Inventories",
                "db_table": "gamification_user_inventory",
                "unique_together": {("user", "item")},
            },
        ),
        migrations.CreateModel(
            name="UserAchievement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("unlocked_at", models.DateTimeField(auto_now_add=True)),
                ("progress", models.PositiveIntegerField(default=0)),
                ("is_completed", models.BooleanField(default=False)),
                (
                    "achievement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="gamification.achievement",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="achievements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Achievement",
                "verbose_name_plural": "User Achievements",
                "db_table": "gamification_user_achievement",
                "unique_together": {("user", "achievement")},
            },
        ),
        migrations.CreateModel(
            name="DailyReward",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "day",
                    models.PositiveIntegerField(
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(30),
                        ]
                    ),
                ),
                ("xp_reward", models.PositiveIntegerField(default=0)),
                ("currency_reward", models.PositiveIntegerField(default=0)),
                ("is_bonus_day", models.BooleanField(default=False)),
                (
                    "item_reward",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="gamification.shopitem",
                    ),
                ),
            ],
            options={
                "verbose_name": "Daily Reward",
                "verbose_name_plural": "Daily Rewards",
                "db_table": "gamification_daily_reward",
                "unique_together": {("day",)},
            },
        ),
    ]
