# Generated by Django 4.2.7 on 2025-07-21 21:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("gamification", "0003_alter_userwallet_user_wallet"),
    ]

    operations = [
        migrations.RenameField(
            model_name="userinventory",
            old_name="purchased_at",
            new_name="acquired_at",
        ),
        migrations.RemoveField(
            model_name="pet",
            name="created_at",
        ),
        migrations.RemoveField(
            model_name="pet",
            name="description",
        ),
        migrations.RemoveField(
            model_name="pet",
            name="level_requirement",
        ),
        migrations.RemoveField(
            model_name="pet",
            name="price",
        ),
        migrations.RemoveField(
            model_name="pet",
            name="rarity",
        ),
        migrations.RemoveField(
            model_name="shopitem",
            name="icon",
        ),
        migrations.RemoveField(
            model_name="shopitem",
            name="preview_image",
        ),
        migrations.RemoveField(
            model_name="shopitem",
            name="rarity",
        ),
        migrations.RemoveField(
            model_name="userinventory",
            name="quantity",
        ),
        migrations.AddField(
            model_name="pet",
            name="animation_data",
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name="pet",
            name="image_url",
            field=models.URLField(default="https://placeholder.com/pet.png"),
        ),
        migrations.AddField(
            model_name="shopitem",
            name="image_url",
            field=models.URLField(default="https://placeholder.com/item.png"),
        ),
        migrations.AddField(
            model_name="shopitem",
            name="is_featured",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="shopitem",
            name="preview_data",
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name="shopitem",
            name="relationship_level_requirement",
            field=models.IntegerField(default=1),
        ),
        migrations.AlterField(
            model_name="pet",
            name="behavior",
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name="pet",
            name="pet_type",
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name="pet",
            name="sound",
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name="shopitem",
            name="item_type",
            field=models.CharField(
                choices=[
                    ("environment", "Environment"),
                    ("outfit", "Outfit"),
                    ("accessory", "Accessory"),
                    ("companion", "Companion"),
                    ("pet", "Pet"),
                ],
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="shopitem",
            name="level_requirement",
            field=models.IntegerField(default=1),
        ),
        migrations.AlterField(
            model_name="shopitem",
            name="price",
            field=models.IntegerField(),
        ),
        migrations.AlterField(
            model_name="shopitem",
            name="stock_quantity",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="userinventory",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="inventory_items",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
