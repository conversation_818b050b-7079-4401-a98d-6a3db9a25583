# Generated by Django 4.2.7 on 2025-07-21 20:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("gamification", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Pet",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                (
                    "pet_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("cat", "Cat"),
                            ("dog", "Dog"),
                            ("bird", "<PERSON>"),
                            ("rabbit", "Rabbit"),
                            ("hamster", "Hamster"),
                            ("fish", "Fish"),
                            ("dragon", "Dragon"),
                            ("unicorn", "Unicorn"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "behavior",
                    models.Char<PERSON><PERSON>(
                        choices=[
                            ("playful", "Playful"),
                            ("calm", "Calm"),
                            ("energetic", "Energetic"),
                            ("sleepy", "Sleepy"),
                            ("curious", "Curious"),
                            ("loyal", "Loyal"),
                        ],
                        default="playful",
                        max_length=20,
                    ),
                ),
                ("sound", models.CharField(blank=True, max_length=50)),
                ("description", models.TextField(blank=True)),
                (
                    "rarity",
                    models.Char<PERSON>ield(
                        choices=[
                            ("common", "Common"),
                            ("rare", "Rare"),
                            ("epic", "Epic"),
                            ("legendary", "Legendary"),
                        ],
                        default="common",
                        max_length=20,
                    ),
                ),
                ("price", models.PositiveIntegerField(default=100)),
                ("level_requirement", models.PositiveIntegerField(default=1)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Pet",
                "verbose_name_plural": "Pets",
                "db_table": "gamification_pet",
            },
        ),
        migrations.CreateModel(
            name="UserProgress",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("xp", models.IntegerField(default=0)),
                ("level", models.IntegerField(default=1)),
                ("hearts", models.IntegerField(default=0)),
                ("messages_count", models.IntegerField(default=0)),
                ("voice_messages_count", models.IntegerField(default=0)),
                ("achievements", models.JSONField(default=list)),
                ("total_time_spent", models.IntegerField(default=0)),
                ("xp_to_next_level", models.IntegerField(default=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="progress",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Progress",
                "verbose_name_plural": "User Progress",
                "db_table": "gamification_user_progress",
            },
        ),
    ]
