from rest_framework import serializers
from .models import (
    UserLevel, Achievement, UserAchievement, UserWallet, 
    ShopItem, UserInventory, DailyReward, UserDailyStreak,
    UserProgress, Wallet
)


class UserLevelSerializer(serializers.ModelSerializer):
    """Serializer for user level and XP"""
    progress_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = UserLevel
        fields = [
            'current_level', 'current_xp', 'total_xp', 'xp_to_next_level',
            'progress_percentage', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_progress_percentage(self, obj):
        """Calculate progress percentage to next level"""
        if obj.xp_to_next_level == 0:
            return 100
        return (obj.current_xp / obj.xp_to_next_level) * 100


class AchievementSerializer(serializers.ModelSerializer):
    """Serializer for achievements"""
    
    class Meta:
        model = Achievement
        fields = [
            'id', 'name', 'description', 'category', 'rarity', 'icon',
            'xp_reward', 'currency_reward', 'requirement_type', 
            'requirement_value', 'is_hidden', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class UserAchievementSerializer(serializers.ModelSerializer):
    """Serializer for user achievements"""
    achievement = AchievementSerializer(read_only=True)
    progress_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = UserAchievement
        fields = [
            'achievement', 'unlocked_at', 'progress', 'is_completed',
            'progress_percentage'
        ]
        read_only_fields = ['unlocked_at']
    
    def get_progress_percentage(self, obj):
        """Calculate achievement progress percentage"""
        if obj.achievement.requirement_value == 0:
            return 100
        return min((obj.progress / obj.achievement.requirement_value) * 100, 100)


class UserWalletSerializer(serializers.ModelSerializer):
    """Serializer for user wallet"""
    
    class Meta:
        model = UserWallet
        fields = [
            'balance', 'total_earned', 'total_spent', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class ShopItemSerializer(serializers.ModelSerializer):
    """Serializer for shop items"""
    can_purchase = serializers.SerializerMethodField()
    purchase_status = serializers.SerializerMethodField()
    
    class Meta:
        model = ShopItem
        fields = [
            'id', 'name', 'description', 'item_type', 'price',
            'image_url', 'preview_data', 'is_active', 'is_featured',
            'is_limited', 'stock_quantity', 'level_requirement',
            'relationship_level_requirement', 'can_purchase', 
            'purchase_status', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def get_can_purchase(self, obj):
        """Check if current user can purchase this item"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            can_purchase, _ = obj.can_purchase(request.user)
            return can_purchase
        return False
    
    def get_purchase_status(self, obj):
        """Get purchase status message for current user"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            _, message = obj.can_purchase(request.user)
            return message
        return "Authentication required"


class UserInventorySerializer(serializers.ModelSerializer):
    """Serializer for user inventory"""
    item = ShopItemSerializer(read_only=True)
    
    class Meta:
        model = UserInventory
        fields = [
            'item', 'acquired_at', 'is_equipped'
        ]
        read_only_fields = ['acquired_at']


class DailyRewardSerializer(serializers.ModelSerializer):
    """Serializer for daily rewards"""
    item_reward = ShopItemSerializer(read_only=True)
    
    class Meta:
        model = DailyReward
        fields = [
            'day', 'xp_reward', 'currency_reward', 'item_reward', 'is_bonus_day'
        ]
        read_only_fields = ['day']


class UserDailyStreakSerializer(serializers.ModelSerializer):
    """Serializer for user daily streak"""
    can_claim_today = serializers.SerializerMethodField()
    next_reward = serializers.SerializerMethodField()
    
    class Meta:
        model = UserDailyStreak
        fields = [
            'current_streak', 'longest_streak', 'last_login_date',
            'last_reward_claimed', 'total_logins', 'can_claim_today',
            'next_reward', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_can_claim_today(self, obj):
        """Check if user can claim today's reward"""
        from django.utils import timezone
        today = timezone.now().date()
        return obj.last_reward_claimed != today
    
    def get_next_reward(self, obj):
        """Get next reward information"""
        try:
            next_day = min(obj.current_streak + 1, 30)  # Cap at 30 days
            reward = DailyReward.objects.get(day=next_day)
            return DailyRewardSerializer(reward).data
        except DailyReward.DoesNotExist:
            return None


class PurchaseItemSerializer(serializers.Serializer):
    """Serializer for purchasing shop items"""
    item_id = serializers.UUIDField()
    
    def validate_item_id(self, value):
        """Validate that the item exists and is purchasable"""
        try:
            item = ShopItem.objects.get(id=value, is_active=True)
        except ShopItem.DoesNotExist:
            raise serializers.ValidationError("Item not found or not available")
        
        user = self.context['request'].user
        can_purchase, message = item.can_purchase(user)
        if not can_purchase:
            raise serializers.ValidationError(message)
        
        return value


class UserProgressSerializer(serializers.ModelSerializer):
    """Serializer for user progress including XP, level, hearts, and message counts"""
    progress_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = UserProgress
        fields = [
            'xp', 'level', 'hearts', 'messages_count', 'voice_messages_count',
            'achievements', 'total_time_spent', 'xp_to_next_level',
            'progress_percentage', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
    
    def get_progress_percentage(self, obj):
        """Calculate progress percentage to next level"""
        if obj.xp_to_next_level == 0:
            return 100
        return (obj.xp / obj.xp_to_next_level) * 100


class WalletSerializer(serializers.ModelSerializer):
    """Serializer for user wallet (new model)"""
    
    class Meta:
        model = Wallet
        fields = [
            'balance', 'lifetime_earnings', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class GamificationStatsSerializer(serializers.Serializer):
    """Serializer for gamification statistics"""
    level_info = UserLevelSerializer()
    wallet_info = UserWalletSerializer()
    streak_info = UserDailyStreakSerializer()
    achievements_count = serializers.IntegerField()
    completed_achievements_count = serializers.IntegerField()
    inventory_count = serializers.IntegerField()
    rank = serializers.IntegerField()
    total_users = serializers.IntegerField()
