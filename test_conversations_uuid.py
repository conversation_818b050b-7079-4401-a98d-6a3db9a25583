#!/usr/bin/env python3
"""
Test script to validate conversations endpoints with proper UUID.
"""

import requests
import uuid

def test_endpoint(url, description):
    """Test a single API endpoint."""
    print(f"\n🔍 Testing {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
        elif response.status_code == 401:
            print("🔒 Authentication required (expected)")
        elif response.status_code == 404:
            print("⚠️ Not Found (expected for non-existent UUID)")
        else:
            print(f"⚠️ Unexpected status: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - is the server running?")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Test conversations endpoints with proper UUID."""
    base_url = "http://127.0.0.1:8000"
    test_uuid = str(uuid.uuid4())
    
    print("🚀 Testing Conversations Endpoints with UUID")
    print("=" * 45)
    print(f"Test UUID: {test_uuid}")
    
    # Test conversations endpoints
    endpoints = [
        ("/api/conversations/", "Conversations List (alias)"),
        ("/api/chat/conversations/", "Conversations List (original)"),
        (f"/api/conversations/{test_uuid}/", "Conversation Detail (alias)"),
        (f"/api/chat/conversations/{test_uuid}/", "Conversation Detail (original)"),
        (f"/api/conversations/{test_uuid}/messages/", "Conversation Messages (alias)"),
        (f"/api/chat/conversations/{test_uuid}/messages/", "Conversation Messages (original)"),
    ]
    
    for endpoint, description in endpoints:
        test_endpoint(f"{base_url}{endpoint}", description)
    
    print("\n" + "=" * 45)
    print("✅ Testing complete!")
    print("\nResults:")
    print("- 401 (Unauthorized): Endpoint exists, authentication required ✅")
    print("- 404 (Not Found): Endpoint exists but UUID doesn't exist in DB ✅")

if __name__ == "__main__":
    main()
